@echo off
echo Testando rotas da API...

echo.
echo 1. Testando rota de perfis:
curl -s http://localhost:3100/oizap/perfis
echo.

echo.
echo 2. Testando rota de telas:
curl -s http://localhost:3100/oizap/telas
echo.

echo.
echo 3. Criando perfil Administrador:
curl -s -X POST http://localhost:3100/oizap/perfis -H "Content-Type: application/json" -d "{\"nm_perfil\": \"Administrador\"}"
echo.

echo.
echo 4. Criando tela Cadastro de Perfil:
curl -s -X POST http://localhost:3100/oizap/telas -H "Content-Type: application/json" -d "{\"nm_tela\": \"Cadastro de Perfil\", \"ds_rota\": \"/cadPerfil\"}"
echo.

echo.
echo 5. Criando tela Cadastro de Tela:
curl -s -X POST http://localhost:3100/oizap/telas -H "Content-Type: application/json" -d "{\"nm_tela\": \"Cadastro de Tela\", \"ds_rota\": \"/cadTela\"}"
echo.

echo.
echo 6. Criando tela Associação Perfis x Telas:
curl -s -X POST http://localhost:3100/oizap/telas -H "Content-Type: application/json" -d "{\"nm_tela\": \"Associação Perfis x Telas\", \"ds_rota\": \"/cadPerfisTelas\"}"
echo.

echo.
echo 7. Associando usuário 87 ao perfil Administrador:
curl -s -X POST http://localhost:3100/oizap/usuarios/87/perfis -H "Content-Type: application/json" -d "{\"cd_perfil\": 1}"
echo.

echo.
echo 8. Associando telas ao perfil Administrador:
curl -s -X POST http://localhost:3100/oizap/perfis/associar -H "Content-Type: application/json" -d "{\"cd_perfil\": 1, \"telas\": [1, 2, 3]}"
echo.

echo.
echo Teste concluído!
pause 