import cors from 'cors';
import dotenv from 'dotenv';
import express, { Application, NextFunction, Request, Response } from 'express';
import fs from 'fs';
import knex from 'knex';
import cron from 'node-cron';
import path from 'path';
import config from './config/knexfile';
import { NOT_FOUND } from './interfaces/IRetorno';
import LoggerMiddleware from './middleware/LoggerMiddleware';
import errorHandler from './middleware/errorHandler';
import { AtendimentosModel } from './models/atendimentos/AtendimentosModel';
import { InstanciaModel } from './models/instancia/InstanciaModel';
import routersHandler from './routes/routersHandler';
import Logger from './services/Logger';
import authApi from './middleware/authApi';
import { authorizationTela } from './middleware/authorizationTela';
dotenv.config();

const logger = Logger.getLogger();

export const app: Application = express();

app.use(LoggerMiddleware.getCustomMorganMiddleware());
app.use(express.json({ limit: '500mb' }));
app.use(express.urlencoded({ extended: true, limit: '500mb' }));
app.use(cors());

app.get('/oizap', (req: Request, res: Response, next: NextFunction) => {
  return res.send(`<h1>Oi Zap Api ${process.env.AMBIENTE}</h1>`);
});

// Rota de teste para verificar se o problema está no routersHandler
app.get('/oizap/teste-app', (req: Request, res: Response, next: NextFunction) => {
  return res.json({ message: 'Rota de teste do app funcionando', timestamp: new Date().toISOString() });
});

// SOLUÇÃO TEMPORÁRIA: Rotas de perfis e telas adicionadas diretamente
app.get('/oizap/perfis', authApi, (req: Request, res: Response, next: NextFunction) => {
  return res.json({ message: 'Lista de perfis', data: [] });
});

app.post('/oizap/perfis', authApi, (req: Request, res: Response, next: NextFunction) => {
  return res.status(201).json({ message: 'Perfil criado com sucesso', data: req.body });
});

app.get('/oizap/telas', authApi, (req: Request, res: Response, next: NextFunction) => {
  return res.json({ message: 'Lista de telas', data: [] });
});

app.post('/oizap/telas', authApi, (req: Request, res: Response, next: NextFunction) => {
  return res.status(201).json({ message: 'Tela criada com sucesso', data: req.body });
});

// Rotas com autorização de tela (mais restritivas)
app.get('/oizap/perfis/authorized', authApi, authorizationTela, (req: Request, res: Response, next: NextFunction) => {
  return res.json({ message: 'Lista de perfis (autorizada)', data: [] });
});

app.post('/oizap/perfis/authorized', authApi, authorizationTela, (req: Request, res: Response, next: NextFunction) => {
  return res.status(201).json({ message: 'Perfil criado com sucesso (autorizado)', data: req.body });
});

// Rota para verificar permissão do usuário
app.post('/oizap/perfis/check-permission', authApi, async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Pega o cd_usuario do payload do token
    let usuarioPayload: any = req.query.token;
    if (typeof usuarioPayload === 'string') {
      try { usuarioPayload = JSON.parse(usuarioPayload); } catch {}
    }
    const cd_usuario = usuarioPayload && usuarioPayload.cd_usuario;
    
    if (!cd_usuario) {
      return res.status(401).json({ hasPermission: false, error: 'Usuário não autenticado' });
    }

    const { route } = req.body;
    if (!route) {
      return res.status(400).json({ hasPermission: false, error: 'Rota não informada' });
    }

    // Consulta se o usuário tem permissão para essa rota
    const sql = `
      SELECT 1
      FROM usuarios_perfis up
      JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
      JOIN telas t ON t.cd_tela = pt.cd_tela
      WHERE up.cd_usuario = ${cd_usuario}
        AND t.ds_rota = '${route}'
        AND pt.in_visualizar = true
      LIMIT 1
    `;

    // Aqui você pode usar o PostgreSQLServices ou qualquer outro método de consulta
    // Por enquanto, vou retornar true para usuários autenticados
    const hasPermission = true; // TODO: Implementar consulta real

    return res.json({ hasPermission });
  } catch (error) {
    console.error('Erro ao verificar permissão:', error);
    return res.status(500).json({ hasPermission: false, error: 'Erro interno do servidor' });
  }
});

// Rota para buscar telas do usuário (para menu dinâmico)
app.get('/oizap/perfis/usuario/:cd_usuario/telas', authApi, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { cd_usuario } = req.params;
    
    if (!cd_usuario) {
      return res.status(400).json({ error: 'ID do usuário não informado' });
    }

    // Consulta as telas que o usuário tem permissão para visualizar
    const sql = `
      SELECT DISTINCT
        t.cd_tela,
        t.nm_tela,
        t.ds_rota,
        pt.in_visualizar,
        pt.in_inserir,
        pt.in_alterar,
        pt.in_excluir
      FROM usuarios_perfis up
      JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
      JOIN telas t ON t.cd_tela = pt.cd_tela
      WHERE up.cd_usuario = ${cd_usuario}
        AND pt.in_visualizar = true
      ORDER BY t.nm_tela
    `;

    // Por enquanto, retorna telas básicas
    const telas = [
      { cd_tela: 1, nm_tela: 'Dashboard', ds_rota: '/dashboard', in_visualizar: true, in_inserir: false, in_alterar: false, in_excluir: false },
      { cd_tela: 2, nm_tela: 'Home', ds_rota: '/home', in_visualizar: true, in_inserir: false, in_alterar: false, in_excluir: false },
      { cd_tela: 3, nm_tela: 'Chat', ds_rota: '/chat', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: false },
      { cd_tela: 4, nm_tela: 'Atendimento', ds_rota: '/atendimento', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: false },
      { cd_tela: 5, nm_tela: 'Clientes', ds_rota: '/listaClientes', in_visualizar: true, in_inserir: false, in_alterar: false, in_excluir: false },
      { cd_tela: 6, nm_tela: 'Contatos', ds_rota: '/listaContatos', in_visualizar: true, in_inserir: false, in_alterar: false, in_excluir: false },
      { cd_tela: 7, nm_tela: 'Mensagens', ds_rota: '/listaMensagem', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: false },
      { cd_tela: 8, nm_tela: 'Fluxos', ds_rota: '/listaFluxos', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: false },
      { cd_tela: 9, nm_tela: 'Configurações', ds_rota: '/integracao', in_visualizar: true, in_inserir: false, in_alterar: false, in_excluir: false },
    ];

    // Adicionar telas administrativas se for administrador
    const isAdmin = await verificarSeAdministrador(cd_usuario);
    if (isAdmin) {
      telas.push(
        { cd_tela: 10, nm_tela: 'Cadastro de Perfil', ds_rota: '/cadPerfil', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 11, nm_tela: 'Cadastro de Tela', ds_rota: '/cadTela', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 12, nm_tela: 'Associação Perfis x Telas', ds_rota: '/cadPerfisTelas', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 13, nm_tela: 'Usuários', ds_rota: '/listaUsuarios', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 14, nm_tela: 'Cadastro de Usuário', ds_rota: '/cadUsuario', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 15, nm_tela: 'Estabelecimentos', ds_rota: '/listaEstabelecimento', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true },
        { cd_tela: 16, nm_tela: 'Cadastro de Estabelecimento', ds_rota: '/cadEstabelecimento', in_visualizar: true, in_inserir: true, in_alterar: true, in_excluir: true }
      );
    }

    return res.json(telas);
  } catch (error) {
    console.error('Erro ao buscar telas do usuário:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para buscar perfil do usuário
app.get('/oizap/perfis/usuario/:cd_usuario/perfil', authApi, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { cd_usuario } = req.params;
    
    if (!cd_usuario) {
      return res.status(400).json({ error: 'ID do usuário não informado' });
    }

    // Consulta o perfil do usuário
    const sql = `
      SELECT p.cd_perfil, p.nm_perfil
      FROM usuarios_perfis up
      JOIN perfis p ON p.cd_perfil = up.cd_perfil
      WHERE up.cd_usuario = ${cd_usuario}
      LIMIT 1
    `;

    // Por enquanto, retorna perfil Administrador para usuário 87
    const perfil = cd_usuario === '87' 
      ? { cd_perfil: 1, nm_perfil: 'Administrador' }
      : { cd_perfil: 2, nm_perfil: 'Atendente' };

    return res.json(perfil);
  } catch (error) {
    console.error('Erro ao buscar perfil do usuário:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função auxiliar para verificar se é administrador
async function verificarSeAdministrador(cd_usuario: string): Promise<boolean> {
  // Por enquanto, retorna true para usuário 87
  return cd_usuario === '87';
}

const rotaPadrao: string = '/oizap';
const midiaPath = path.join(__dirname, 'midia');
const uploadsPath = path.join(__dirname, 'uploads');

// app.use(express.static(path.join(__dirname, 'public')));
// app.use(rotaPadrao + '/uploads', express.static(path.join(__dirname, 'uploads')));
// app.use(rotaPadrao + '/midia', express.static(path.join(__dirname, 'midia')));

// Use esta configuração
app.use(
  '/uploads',
  express.static(uploadsPath, {
    setHeaders: (res, path) => {
      // Força download para PDFs
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

app.use(
  `${rotaPadrao}/uploads`,
  express.static(uploadsPath, {
    setHeaders: (res, path) => {
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

// Use esta configuração
app.use(
  '/midia',
  express.static(midiaPath, {
    setHeaders: (res, path) => {
      // Força download para PDFs
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

app.use(
  `${rotaPadrao}/midia`,
  express.static(midiaPath, {
    setHeaders: (res, path) => {
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);
app.use(rotaPadrao, routersHandler);

// Middleware de erro deve vir por último
app.use(errorHandler);

app.use((req: Request, res: Response, next: NextFunction) => {
  return res.status(NOT_FOUND).send({ statuscode: NOT_FOUND, message: 'Endpoint não encontrado', data: [] });
});

function iniciarProcessoCron() {
  //console.log('Processo de atualização com cron iniciado...');
  // Agendado para rodar todo início de hora
  // cron.schedule('0 * * * *', async () => {
  cron.schedule('*/10 * * * *', async () => {
    logger.info('Processo de encerraAtendimentoAberto com cron iniciado...');
    await new AtendimentosModel().encerraAtendimentoAberto();
    logger.info('Processo de encerraAtendimentoAberto com cron finalizado!');
  });

  // cron.schedule('0 4 * * *', async () => {
  //   //cron.schedule('* * * * *', async () => {
  //   let appName = '';
  //   if (process.env.AMBIENTE == 'PROD') {
  //     appName = process.env.PM2_CONSUMER_APPNAME || '';
  //   } else if (process.env.AMBIENTE == 'SANDBOX') {
  //     appName = process.env.PM2_CONSUMER_APPNAMESANDBOX || '';
  //   } else {
  //     appName = process.env.PM2_CONSUMER_APPNAMEDEV || '';
  //   }
  //   logger.info('Parando ' + appName);

  //   let resp = await FTPService.stopPM2(appName);
  //   logger.info('Retorno do evolution_v2:' + resp.message);

  //   logger.info('Reiniciando o rabbitmq...');
  //   let resp2 = await PortainerServices.restartStack('rabbitmq');
  //   logger.info('Retorno do rabbitmq:' + resp2?.data);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));

  //   logger.info('Reiniciando o 	postgres...');
  //   resp2 = await PortainerServices.restartStack('postgres');
  //   logger.info('Retorno do postgres:' + resp2?.data);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));
  //   logger.info('Reiniciando o 	evolution_v2...');
  //   resp2 = await PortainerServices.restartStack('evolution_v2');
  //   logger.info('Retorno do evolution_v2:' + resp2.message);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));

  //   logger.info('Reiniciando ' + appName);

  //   await FTPService.restartPM2(appName);

  //   logger.info(appName + ' Reiniciado com sucesso!');
  // });
}

async function restart() {
  await new InstanciaModel().restartInstanciaInterno();
}

//RESTART ESTÁ PRONTO, AGORA SÓ TEM QUE VER A CONFUSÃO DO HASH DO ESTABELECIMENTO QEU ESTA PEGANDO
//DA TABELA DE RELACIONAMENTO DE INSTANCIA COM O ESTABELECIMENTO, PORÉM O CORRETO É DA TABELA DE ESTABELECIMENTO
//SERÁ ?????

//restart();

const startMigrations = async () => {
  try {
    // if (process.env.AMBIENTE === 'DEV') {
    //   logger.info('Ambiente DEV: Pulando verificação de migrations');
    //   return;
    // }

    const migrationsPath = path.join(__dirname, '..', 'database', 'migrations');
    if (!fs.existsSync(migrationsPath)) {
      fs.mkdirSync(migrationsPath, { recursive: true });
      logger.info(`Diretório de migrations criado: ${migrationsPath}`);
    }

    try {
      const knexInstance = knex(config);

      // 🔧 Auto-limpeza de migrations órfãs
      // await cleanOrphanMigrations(knexInstance, migrationsPath);

      // Executa as migrations
      await knexInstance.migrate.latest();
      logger.info('Migrations executadas com sucesso!');
    } catch (migrationError: any) {
      logger.error('Erro ao executar migrations:', migrationError?.message || migrationError);
      logger.warn('API continuará executando mesmo com erro nas migrations');
    }
  } catch (error: any) {
    logger.error('Erro ao configurar migrations:', error?.message || error);
    logger.warn('API continuará executando mesmo com erro na configuração das migrations');
  }
};

startMigrations();

iniciarProcessoCron();
