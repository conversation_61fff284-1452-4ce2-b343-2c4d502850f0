import hosts from '@/utils/hosts';

//Administração

import listaUsuarios from '../views/administracao/usuarios/listaUsuarios.vue';
import cadUsuario from '../views/administracao/usuarios/cadUsuario.vue';
import cadEstabelecimento from '../views/administracao/estabelecimento/cadEstabelecimento.vue';
import listaEstabelecimento from '../views/administracao/estabelecimento/listaEstabelecimento.vue';
import listaVersao from '../views/administracao/versao/listaVersao.vue';
import listaAtualizaTabelas from '../views/administracao/atualizaTabelas/listaAtualizaTabelas.vue';
import estabelecimentos from '../views/administracao/estabelecimento/estabelecimentos.vue';
import cadPadraoSimples from '../views/administracao/padrao-simples/cadPadrao.vue';
import statusAutorizacao from '../views/administracao/statusAutorizacao.vue';
/*import listaGrupos from '../views/administracao/grupo-acesso/listaGrupos.vue';
import cadGrupo from '../views/administracao/grupo-acesso/cadGrupo.vue';

//ArthySis
import listaPaginas from '../views/administracao/paginas/listaPaginas.vue';
import cadPagina from '../views/administracao/paginas/cadPagina.vue';
*/
const routerAdministracao = [
    //Administração
    {
        path: `${hosts.app}` + '/listaUsuarios',
        name: 'listaUsuarios',
        component: listaUsuarios,
    },
    {
        path: `${hosts.app}` + '/cadUsuario/:codusuario?',
        name: 'cadUsuario',
        component: cadUsuario,
    },
    {
        path: `${hosts.app}/listaEstabelecimento`,
        name: 'listaEstabelecimento',
        component: listaEstabelecimento,
    },
    {
        path: `${hosts.app}/cadEstabelecimento`,
        name: 'cadEstabelecimento',
        component: cadEstabelecimento,
    },
    {
        path: `${hosts.app}/listaVersao`,
        name: 'listaVersao',
        component: listaVersao,
    },
    {
        path: `${hosts.app}/listaAtualizaTabelas`,
        name: 'listaAtualizaTabelas',
        component: listaAtualizaTabelas,
    },
    {
        path: `${hosts.app}/estabelecimentos`,
        name: 'estabelecimentos',
        component: estabelecimentos,
    },
    {
        path: `${hosts.app}/cad-padrao-simples`,
        name: 'cadPadraoSimples',
        component: cadPadraoSimples,
    },
    {
        path: `${hosts.app}/status-autorizacao`,
        name: 'statusAutorizacao',
        component: statusAutorizacao,
    },
    /* {
    path: `${hosts.app}` + '/listaGrupos',
    name: 'listaGrupos',
    component: listaGrupos,
  },
  {
    path: `${hosts.app}` + '/cadGrupo/:codgrupo?',
    name: 'cadGrupo',
    component: cadGrupo,
  },
  //ArthySis
  {
    path: `${hosts.app}` + '/listaPaginas',
    name: 'listaPaginas',
    component: listaPaginas,
  },
  {
    path: `${hosts.app}` + '/cadPagina/:codpagina?',
    name: 'cadPagina',
    component: cadPagina,
  },*/
];

export default routerAdministracao;
