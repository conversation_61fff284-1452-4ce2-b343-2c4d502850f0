import { Router } from 'express';
import { DisparosPixelController } from '../../controllers/campanhas/DisparosPixelController';

export const DisparosPixelRoutes = Router();

DisparosPixelRoutes.post('/disparos-pixel/v1', DisparosPixelController.criarDisparo);
//DisparosPixelRoutes.get('/cliques-pixel/v1', DisparosPixelController.capturarClique);
//DisparosPixelRoutes.post('/webhook-pixel/v1', DisparosPixelController.webhookWhatsapp);
DisparosPixelRoutes.get('/disparos-pixel/v1', DisparosPixelController.listarDisparos);
