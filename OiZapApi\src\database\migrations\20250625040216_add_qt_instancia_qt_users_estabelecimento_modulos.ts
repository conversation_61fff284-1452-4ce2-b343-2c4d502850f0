import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('estabelecimento_modulos migration started');
    let hasColumn = await knex.schema.hasColumn('estabelecimento_modulos', 'qt_instancias');

    if (!hasColumn) {
      await knex.schema.alterTable('estabelecimento_modulos', (table) => {
        table
          .integer('qt_instancias')
          .defaultTo(0)
          .notNullable()
          .comment('Quantidade de instâncias contratada no estabelecimento');
      });
    }

    hasColumn = await knex.schema.hasColumn('estabelecimento_modulos', 'qt_usuarios');

    if (!hasColumn) {
      await knex.schema.alterTable('estabelecimento_modulos', (table) => {
        table
          .integer('qt_usuarios')
          .defaultTo(0)
          .notNullable()
          .comment('Quantidade de usuários contratados no estabelecimento');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('estabelecimento_modulos', (table) => {
      table.dropColumn('qt_instancias');
      table.dropColumn('qt_usuarios');
    });
  } catch (error) {
    throw error;
  }
}
