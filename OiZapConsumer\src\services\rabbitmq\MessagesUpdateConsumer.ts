import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection, ConsumeMessage } from 'amqplib';
import { IMessageStatus } from '../../interfaces/IMessageStatus';
import Logger from '../../logs/Logger';
import { MessagesModel } from '../../models/MessagesModel';
import { Funcoes } from '../Funcoes';
const logger = Logger.getLogger();

export default class MessagesUpdateConsumer {
  private queueName: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private io: any;
  private messagesHandler: MessagesModel;
  private CONTINUE: boolean = false;

  constructor(queueName: string, io: any) {
    this.queueName = queueName;
    this.io = io;
    this.messagesHandler = new MessagesModel();
  }

  async connect() {
    try {
      let room = this.queueName;
      room = room.replace('.messages.update', '');
      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();

      await this.channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });

      this.channel.consume(this.queueName, async (message: ConsumeMessage | null) => {
        if (!this.CONTINUE) {
          return;
        }

        if (message !== null) {
          let messageContent = JSON.parse(message.content.toString());
          logger.debug('MessagesUpdateConsumer > messageContent: ' + JSON.stringify(messageContent));

          try {
            messageContent.data.status_message = Funcoes.trataStatus(messageContent?.data?.status);
            const messageUpdate: IMessageStatus = {
              event: messageContent.event,
              instance: messageContent.instance,
              message_id: messageContent.data.keyId == undefined ? messageContent.data.id : messageContent.data.keyId,
              messageId:
                messageContent.data.messageId == undefined ? messageContent.data.id : messageContent.data.messageId,
              keyId: messageContent.data.keyId == undefined ? messageContent.data.id : messageContent.data.keyId,
              remoteJid: messageContent.data.remoteJid,
              fromMe: messageContent.data.fromMe,
              participant: messageContent.data.participant,
              status: messageContent.data.status,
              instanceId:
                messageContent.data.instanceId == undefined ? messageContent.instance : messageContent.data.instanceId,
              tp_status: Funcoes.trataStatus(messageContent?.data?.status),
              server_url: messageContent.server_url,
              date_time: messageContent.date_time,
              sender: messageContent.sender,
              apikey: messageContent.apikey,
            };
            //console.log('messageContent at line 55 in rabbitmq/MessagesUpdateConsumer.ts:', messageContent);
            logger.debug('MessagesUpdateConsumer > messageUpdate: ' + JSON.stringify(messageUpdate));
            let respMessage = await this.messagesHandler.updateMessage(messageUpdate);
            logger.debug('MessagesUpdateConsumer > messageUpdate > respMessage: ' + JSON.stringify(respMessage));
            //console.log('respMessage at line 56 in rabbitmq/MessagesUpdateConsumer.ts:', respMessage);

            if (respMessage.statuscode === 200) {
              this.io.in(room).emit('messages.update', messageUpdate);
              if (this.channel) this.channel.ack(message);
            } else {
              logger.error(
                `MessagesUpdateConsumer > Error updateMessage message PostgreSQL ${JSON.stringify(respMessage)}`,
              );
              if (this.channel) this.channel.reject(message, false);
            }
          } catch (error) {
            logger.error('Error handling the message: ' + JSON.stringify(error));
            if (this.channel) this.channel.reject(message, false);
          }
        }
      });
    } catch (error) {
      logger.error('MessagesUpdateConsumer Error connecting to RabbitMQ:' + JSON.stringify(error));
    }
  }

  close() {
    if (this.connection) {
      this.connection.close();
    }
  }
}
