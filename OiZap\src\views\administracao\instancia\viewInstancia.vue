<template>
    <ShowLoading ref="loading" />

    <div class="p-4">
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-4 text-white">
            <div class="flex flex-col md:flex-row md:items-center">
                <!-- Grupo: Ícone + Texto + Estatísticas -->
                <div class="flex items-center space-x-6 mb-4 md:mb-0">
                    <!-- <PERSON><PERSON><PERSON> e Texto -->
                    <div class="flex items-center space-x-3">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                            <Building2Icon
                                class="w-6 h-6"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                            />
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Instância</h3>
                            <p class="text-blue-100 text-sm">Cadastro de instância.</p>
                        </div>
                    </div>
                </div>

                <!-- <PERSON><PERSON><PERSON> à direita -->
                <div class="ml-auto">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="router.push({ name: 'listaInstancias' })"
                    >
                        <ArrowLeftIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Voltar</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="box overflow-x-auto">
            <!-- <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
                <h2 class="text-lg font-medium mr-auto py-2">Instância</h2>
                <button @click="router.push({ name: 'listaInstancias' })" class="btn btn-primary-soft btn-sm">
                    Voltar
                </button>
            </div> -->

            <TabGroup class="py-4 px-1 md:px-2">
                <TabList
                    class="nav-pills rounded-md p-1 max-w-[340px] intro-y border border-slate-300 dark:border-darkmode-300 border-dashed"
                >
                    <Tab class="hover:bg-slate-800/10 mr-1 cursor-pointer flex justify-center items-center py-1.5 px-2">
                        <button class="w-max" as="button">Dados</button>
                    </Tab>
                    <Tab
                        class="hover:bg-slate-800/10 cursor-pointer flex justify-center items-center py-1.5 px-2"
                        v-show="modulos.in_api"
                    >
                        <!-- :class="{ disabled: !formData.id_instancia }" -->
                        <button class="w-max" as="button">Webhooks</button>
                    </Tab>
                    <Tab class="hover:bg-slate-800/10 cursor-pointer flex justify-center items-center py-1.5 px-2">
                        <!-- :class="{ disabled: true }" :class="{ disabled: !formData.id_instancia }" -->
                        <button class="w-max" as="button">Mensagens</button>
                    </Tab>
                </TabList>
                <TabPanels class="mt-2 bg-darkmode-700 intro-y">
                    <TabPanel class="box p-4 md:p-6 grid grid-cols-12">
                        <div class="col-span-12 lg:pr-4">
                            <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4">
                                <div>
                                    <h2 class="text-lg font-semibold">
                                        {{ !!formData.id_instancia ? 'Dados da Instância' : 'Nova instância' }}
                                    </h2>
                                    <p class="text-md text-slate-600 dark:text-slate-400">
                                        As informações contidas nesta página são pessoais. Tenha cuidado ao
                                        compartilhar.
                                    </p>
                                </div>
                                <div
                                    class="flex justify-around w-full md:w-fit sm:justify-between items-center gap-2 flex-wrap sm:flex-nowrap"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-sm flex items-center gap-1 border-none shadow-none pr-4"
                                        :class="{
                                            'btn-success-soft': formData.status_connection != 'Conectado',
                                            'btn-warning-soft': formData.status_connection == 'Conectando',
                                            'btn-danger-soft': formData.status_connection == 'Conectado',
                                        }"
                                        @click="abrirModalConexao"
                                        :disabled="!formData.id_instancia"
                                    >
                                        <PlugIcon
                                            v-if="formData.status_connection != 'Conectado'"
                                            class="w-5 h-5 mb-0.5"
                                        />
                                        <LoaderIcon
                                            v-if="formData.status_connection == 'Conectando'"
                                            class="w-5 h-5 mb-0.5"
                                        />
                                        <XIcon
                                            v-if="formData.status_connection == 'Conectado'"
                                            class="w-5 h-5 mb-0.5"
                                        />

                                        {{ formData.status_connection == 'Conectado' ? 'Desconectar' : 'Conectar' }}
                                    </button>
                                    <button
                                        type="button"
                                        class="btn btn-sm btn-outline-primary flex items-center gap-1 border-none shadow-none"
                                        @click="salvar"
                                    >
                                        <SaveIcon class="w-5 h-5 mb-0.5" />
                                        Salvar
                                    </button>

                                    <button
                                        v-if="formData.id_instancia"
                                        type="button"
                                        class="btn btn-sm btn-outline-danger flex items-center gap-1 border-none shadow-none"
                                        @click="abreModalRemover()"
                                    >
                                        <Trash2Icon class="w-5 h-5 mb-0.5" />
                                        Excluir
                                    </button>
                                </div>
                            </div>
                            <div class="grid grid-cols-12">
                                <div class="col-span-12">
                                    <div class="grid grid-cols-12 gap-4 mt-2 gap-y-4">
                                        <div class="col-span-12">
                                            <div class="flex items-center gap-1 pb-1">
                                                <DiamondIcon class="w-4 h-4" /> Nome da Instância
                                            </div>
                                            <input
                                                type="text"
                                                ref="inputID"
                                                class="form-control"
                                                v-model="formData.nome"
                                            />
                                        </div>

                                        <div class="col-span-12 lg:col-span-6">
                                            <div class="flex items-center gap-1 pb-1">
                                                <KeyIcon class="w-4 h-4" />
                                                ID da Instância
                                            </div>
                                            <Tippy tag="div" class="w-full relative cursor-pointer" content="ID">
                                                <input
                                                    type="text"
                                                    ref="inputID"
                                                    class="form-control"
                                                    :value="formData.nameinstance"
                                                    disabled
                                                />
                                                <CopyIcon
                                                    class="absolute right-2 top-0 bottom-0 my-auto w-4 h-4"
                                                    @click="copyID()"
                                                    :class="{ copied: IDCopiado == true }"
                                                />
                                            </Tippy>
                                        </div>

                                        <div class="col-span-12 lg:col-span-6">
                                            <div class="flex justify-between items-end pb-1">
                                                <div class="flex items-center gap-1">
                                                    <ShieldEllipsisIcon class="w-4 h-4" />
                                                    Token de Integração
                                                </div>
                                                <button
                                                    v-if="!!formData.id_instancia"
                                                    type="button"
                                                    class="text-sm hover:underline text-slate-600 dark:text-slate-400 hidden md:flex items-center gap-1"
                                                    @click="modalNovoToken = true"
                                                    v-show="modulos.in_api"
                                                >
                                                    <RotateCcwIcon class="w-3 h-3" /> Gerar novo Token
                                                </button>
                                            </div>

                                            <Tippy tag="div" class="w-full relative cursor-pointer" content="Token">
                                                <input
                                                    type="text"
                                                    ref="inputToken"
                                                    class="form-control"
                                                    :value="formData.nr_hash"
                                                    disabled
                                                />
                                                <CopyIcon
                                                    class="absolute right-2 top-0 bottom-0 my-auto w-4 h-4"
                                                    @click="copyHash()"
                                                    :class="{ copied: HASHCopiado == true }"
                                                />
                                            </Tippy>
                                            <div class="col-span-12 w-full flex justify-end">
                                                <button
                                                    v-if="!!formData.id_instancia"
                                                    type="button"
                                                    class="text-sm hover:underline text-slate-600 dark:text-slate-400 flex md:hidden items-center gap-1 mt-2"
                                                    @click="modalNovoToken = true"
                                                    v-show="modulos.in_api"
                                                >
                                                    <RotateCcwIcon class="w-3 h-3" /> Gerar novo Token
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabPanel>
                    <TabPanel v-show="modulos.in_api">
                        <div class="box p-4 md:p-6 pb-2">
                            <div class="w-full">
                                <div class="flex justify-between items-center mb-4 gap-x-1">
                                    <div>
                                        <div class="flex items-center gap-1.5 w-max">
                                            <h2 class="text-lg font-semibold">Webhooks</h2>
                                        </div>
                                        <p class="text-md text-slate-600 dark:text-slate-400">
                                            Configure seus webhooks e receba notificações em tempo real.
                                        </p>
                                    </div>
                                    <button
                                        type="button"
                                        class="btn btn-outline-primary flex items-center gap-1 border-none shadow-none"
                                        @click="salvarWebhooks"
                                    >
                                        <SaveIcon class="w-5 h-5 mb-0.5" />
                                        <span class="hidden sm:block"> Salvar </span>
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-x-4 gap-y-6 pt-4 pb-2">
                                <div class="col-span-12 lg:col-span-6">
                                    <div class="flex items-center gap-1 pb-1">
                                        <SendIcon class="w-4 h-4" />Ao enviar
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            type="text"
                                            class="form-control"
                                            placeholder="Insira a URL para receber os eventos"
                                            v-model="formDataWebhook.ds_webhook_enviar"
                                        />
                                        <Tippy content="Testar">
                                            <button
                                                class="btn btn-sm btn-outline-primary border-none shadow-md"
                                                @click="testarWebhook('ds_webhook_enviar', true)"
                                            >
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </div>
                                <div class="col-span-12 lg:col-span-6">
                                    <div class="flex items-center gap-1 pb-1">
                                        <SendIcon class="w-4 h-4 rotate-180" />Ao receber
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            type="text"
                                            class="form-control"
                                            placeholder="Insira a URL para receber os eventos"
                                            v-model="formDataWebhook.ds_webhook_receber"
                                        />
                                        <Tippy content="Testar">
                                            <button
                                                class="btn btn-sm btn-outline-primary border-none shadow-md"
                                                @click="testarWebhook('ds_webhook_receber', true)"
                                            >
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </div>
                                <div class="col-span-12 lg:col-span-6">
                                    <div class="flex items-center gap-1 pb-1">
                                        <CircleDotIcon class="w-4 h-4" />Presença no chat
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            type="text"
                                            class="form-control"
                                            placeholder="Insira a URL para receber os eventos"
                                            v-model="formDataWebhook.ds_webhook_presenca"
                                        />
                                        <Tippy content="Testar">
                                            <button
                                                class="btn btn-sm btn-outline-primary border-none shadow-md"
                                                @click="testarWebhook('ds_webhook_presenca', true)"
                                            >
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </div>
                                <div class="col-span-12 lg:col-span-6">
                                    <div class="flex items-center gap-1 pb-1">
                                        <CheckCheckIcon class="w-4 h-4" />Receber status de mensagem
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            type="text"
                                            class="form-control"
                                            placeholder="Insira a URL para receber os eventos"
                                            v-model="formDataWebhook.ds_webhook_status_mensagem"
                                        />
                                        <Tippy content="Testar">
                                            <button
                                                class="btn btn-sm btn-outline-primary border-none shadow-md"
                                                @click="testarWebhook('ds_webhook_status_mensagem', true)"
                                            >
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </div>
                                <div class="col-span-12 lg:col-span-6">
                                    <div class="flex items-center gap-1 pb-1">
                                        <CloudIcon class="w-4 h-4" />Ao conectar/desconectar
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            type="text"
                                            class="form-control"
                                            placeholder="Insira a URL para receber os eventos"
                                            v-model="formDataWebhook.ds_webhook_conectar"
                                        />
                                        <Tippy content="Testar">
                                            <button
                                                class="btn btn-sm btn-outline-primary border-none shadow-md"
                                                @click="testarWebhook('ds_webhook_conectar', true)"
                                            >
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </div>
                                <!-- <div class="col-span-12 lg:col-span-6">
                                <div class="flex items-center gap-1 pb-1">
                                    <CloudOffIcon class="w-4 h-4" />Ao desconectar
                                </div>
                                <div class="flex items-center gap-2">
                                    <input
                                        type="text"
                                        class="form-control"
                                        placeholder="Insira a URL para receber os eventos"
                                        v-model="formDataWebhook.ds_webhook_desconectar"
                                    />
                                    <Tippy content="Testar">
                                        <button
                                            class="btn btn-sm btn-outline-primary border-none shadow-md"
                                            @click="testarWebhook('ds_webhook_desconectar', true)"
                                        >
                                            <ZapIcon />
                                        </button>
                                    </Tippy>
                                </div>
                            </div> -->
                            </div>
                        </div>
                    </TabPanel>
                    <TabPanel class="box p-4 md:p-6">
                        <div class="w-full">
                            <div class="flex justify-between items-center mb-4 gap-x-1">
                                <div>
                                    <div class="flex items-center gap-1.5 w-max">
                                        <!-- <LinkIcon class="w-6 h-6 text-secondary" /> -->
                                        <h2 class="text-lg font-semibold">Mensagens</h2>
                                    </div>
                                    <p class="text-md text-slate-600 dark:text-slate-400">
                                        Configure as mensagens automáticas que serão enviadas para os clientes.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <AccordionGroup class="w-full mb-6 pt-4" :selectedIndex="selectedIndex">
                            <AccordionItem
                                class="w-full border border-slate-200 rounded-xl mb-4 h-min overflow-hidden"
                                @click="selectedIndex = 0"
                            >
                                <Accordion
                                    class="px-4 sm:px-6 hover:bg-slate-100 shadow-md bg-white dark:bg-darkmode-600 flex justify-between items-center font-medium h-fit"
                                >
                                    <div
                                        class="flex flex-col sm:flex-row justify-start items-start sm:items-center font-medium"
                                    >
                                        Mensagem de Saudação <strong>(Em breve...)</strong>
                                    </div>
                                    <div class="flex items-center gap-4">
                                        <div class="form-inline">
                                            <label class="forceLeft form-label !mr-2">Ativo :</label>
                                            <div class="form-check form-switch">
                                                <input
                                                    class="form-check-input"
                                                    type="checkbox"
                                                    v-model="formDataMensagem[0].ativo"
                                                    disabled
                                                    @click.stop="(e) => mudarStatusMensagem(e, 0)"
                                                />
                                            </div>
                                        </div>
                                        <ChevronDownIcon class="w-4 h-4" />
                                    </div>
                                </Accordion>
                                <AccordionPanel
                                    class="py-4 p-2 sm:p-4 grid grid-cols-12 shadow-inner bg-gray-50 dark:bg-darkmode-800"
                                >
                                    <div class="col-span-12 flex justify-between items-center">
                                        <div class="mb-4">
                                            <h3 class="font-medium">Edite sua mensagem padrão.</h3>
                                            <p class="text-sm text-slate-600 dark:text-slate-400">
                                                Mensagem enviada quando o cliente inicia uma conversa.
                                            </p>
                                        </div>
                                        <button
                                            type="button"
                                            class="btn btn-outline-primary flex items-center gap-1 border-none shadow-none"
                                            disabled
                                            @click="salvarMensagem(0)"
                                        >
                                            <SaveIcon class="w-5 h-5 mb-0.5" />
                                            <span class="hidden sm:block"> Salvar </span>
                                        </button>
                                    </div>
                                    <div class="col-span-12">
                                        <div class="form-control">
                                            <label class="flex items-center gap-1 pb-1">
                                                Mensagem:
                                                <button
                                                    @click="toggleEmojiPicker(0)"
                                                    :ref="refEmojiButtons[0]"
                                                    disabled
                                                    class="btn btn-secondary-soft w-25 h-6 -mt-1.5 ml-2 text-xs"
                                                >
                                                    😊 Emoji
                                                </button>
                                            </label>

                                            <textarea
                                                class="chat__box__input form-control h-40 resize-none focus:ring-0"
                                                placeholder="Informe a mensagem que enviaremos para nossos clientes"
                                                :ref="refMensagem[0]"
                                                key="0"
                                                v-model="formDataMensagem[0].ds_mensagem"
                                                @input="() => updateCursorPosition(0)"
                                                @click="() => updateCursorPosition(0)"
                                                disabled
                                            ></textarea>
                                        </div>
                                    </div>
                                </AccordionPanel>
                            </AccordionItem>
                            <AccordionItem
                                class="w-full border border-slate-200 rounded-xl mb-4 h-min overflow-hidden"
                                @click="selectedIndex = 1"
                            >
                                <Accordion
                                    class="px-4 sm:px-6 hover:bg-slate-100 shadow-md bg-white dark:bg-darkmode-600 flex justify-between items-center font-medium h-fit"
                                >
                                    <div
                                        class="flex flex-col sm:flex-row justify-start items-start sm:items-center font-medium"
                                    >
                                        Mensagem de Ausência <strong>(Em breve...)</strong>
                                    </div>
                                    <div class="flex items-center gap-4">
                                        <div class="form-inline">
                                            <label class="forceLeft form-label !mr-2">Ativo :</label>
                                            <div class="form-check form-switch">
                                                <input
                                                    class="form-check-input"
                                                    type="checkbox"
                                                    v-model="formDataMensagem[1].ativo"
                                                    @click.stop="(e) => mudarStatusMensagem(e, 1)"
                                                    disabled
                                                />
                                            </div>
                                        </div>
                                        <ChevronDownIcon class="w-4 h-4" />
                                    </div>
                                </Accordion>
                                <AccordionPanel
                                    class="py-4 p-2 sm:p-4 grid grid-cols-12 shadow-inner bg-gray-50 dark:bg-darkmode-800"
                                >
                                    <div class="col-span-12 flex justify-between items-center">
                                        <h3 class="font-medium">Edite sua mensagem padrão.</h3>
                                        <button
                                            type="button"
                                            class="btn btn-outline-primary flex items-center gap-1 border-none shadow-none"
                                            disabled
                                            @click="salvarMensagem(1)"
                                        >
                                            <SaveIcon class="w-5 h-5 mb-0.5" />
                                            <span class="hidden sm:block"> Salvar </span>
                                        </button>
                                    </div>
                                    <div class="col-span-12 pr-4">
                                        <div class="form-control">
                                            <label class="flex items-center gap-1 pb-1">
                                                Mensagem:
                                                <button
                                                    @click="toggleEmojiPicker(1)"
                                                    :ref="refEmojiButtons[1]"
                                                    class="btn btn-secondary-soft w-25 h-6 -mt-1.5 ml-2 text-xs"
                                                    disabled
                                                >
                                                    😊 Emoji
                                                </button>
                                            </label>

                                            <textarea
                                                class="chat__box__input form-control h-40 resize-none focus:ring-0"
                                                placeholder="Informe a mensagem que enviaremos para nossos clientes"
                                                :ref="refMensagem[1]"
                                                key="1"
                                                v-model="formDataMensagem[1].ds_mensagem"
                                                @input="() => updateCursorPosition(1)"
                                                @click="() => updateCursorPosition(1)"
                                                disabled
                                            ></textarea>
                                        </div>
                                    </div>
                                </AccordionPanel>
                            </AccordionItem>
                            <AccordionItem
                                class="w-full border border-slate-200 rounded-xl mb-4 h-min overflow-hidden"
                                @click="selectedIndex = 2"
                            >
                                <Accordion
                                    class="px-4 sm:px-6 hover:bg-slate-100 shadow-md bg-white dark:bg-darkmode-600 flex justify-between items-center font-medium h-fit"
                                >
                                    <div
                                        class="flex flex-col sm:flex-row justify-start items-start sm:items-center font-medium"
                                    >
                                        Mensagem Fora do Horário
                                        <span class="opacity-50 flex justify-start items-center gap-0.5 font-normal">
                                            <!-- <DotIcon class="hidden sm:block" /> -->
                                        </span>
                                    </div>
                                    <div class="flex items-center gap-4">
                                        <div class="form-inline">
                                            <label class="forceLeft form-label !mr-2">Ativo :</label>
                                            <div class="form-check form-switch">
                                                <input
                                                    class="form-check-input"
                                                    type="checkbox"
                                                    v-model="formDataMensagem[2].ativo"
                                                    @click.stop="(e) => mudarStatusMensagem(e, 2)"
                                                />
                                            </div>
                                        </div>
                                        <ChevronDownIcon class="w-4 h-4" />
                                    </div>
                                </Accordion>
                                <AccordionPanel
                                    class="py-4 p-2 sm:p-4 grid grid-cols-12 shadow-inner bg-gray-50 dark:bg-darkmode-800"
                                >
                                    <div class="col-span-12 flex justify-between items-center">
                                        <h3 class="font-medium">
                                            Gerencie seu horário de funcionamento, e inclua sua mensagem padrão.
                                        </h3>
                                        <button
                                            type="button"
                                            class="btn btn-outline-primary flex items-center gap-1 border-none shadow-none"
                                            @click="salvarMensagem(2)"
                                        >
                                            <SaveIcon class="w-5 h-5 mb-0.5" />
                                            <span class="hidden sm:block"> Salvar </span>
                                        </button>
                                    </div>
                                    <div class="col-span-8 border-r border-gray-300/50 pr-4">
                                        <div class="form-control">
                                            <label class="flex items-center gap-1 pb-1">
                                                Mensagem:
                                                <button
                                                    @click="toggleEmojiPicker(2)"
                                                    :ref="refEmojiButtons[2]"
                                                    class="btn btn-secondary-soft w-25 h-6 -mt-1.5 ml-2 text-xs"
                                                >
                                                    😊 Emoji
                                                </button>
                                            </label>

                                            <textarea
                                                class="chat__box__input form-control h-40 resize-none focus:ring-0"
                                                placeholder="Informe a mensagem que enviaremos para nossos clientes"
                                                :ref="refMensagem[2]"
                                                key="2"
                                                v-model="formDataMensagem[2].ds_mensagem"
                                                @input="() => updateCursorPosition(2)"
                                                @click="() => updateCursorPosition(2)"
                                            ></textarea>
                                        </div>
                                    </div>

                                    <div class="col-span-4 pl-4 flex flex-col">
                                        <div class="">
                                            <label class="flex justify-between items-center gap-1 pb-1">
                                                Dias de Funcionamento:

                                                <span
                                                    class="text-xs text-slate-500 dark:text-slate-400 cursor-pointer hover:underline"
                                                    @click.stop="
                                                        () => {
                                                            selecionarDias = !selecionarDias;
                                                            listaDias.forEach(
                                                                (dia) =>
                                                                    (dia.selecionado =
                                                                        dia.nome == 'Seg' && !selecionarDias)
                                                            );
                                                        }
                                                    "
                                                >
                                                    {{ selecionarDias ? 'Finalizar' : 'Selecionar' }}
                                                </span>
                                            </label>
                                            <div
                                                class="flex justify-between items-center rounded-lg p-1 border border-slate-300 dark:border-darkmode-300 border-dashed"
                                            >
                                                <div
                                                    class="btn cursor-pointer flex justify-center items-center py-1.5 px-2 mx-0.5 rounded-md w-full shadow-none"
                                                    :class="{
                                                        'btn-primary': dia.selecionado,
                                                        'btn-outline-secondary border-none':
                                                            !dia.selecionado && !selecionarDias,
                                                        ' !shadow-md border ': selecionarDias,
                                                    }"
                                                    v-for="dia in listaDias"
                                                    :key="dia.id"
                                                    @click="
                                                        () => {
                                                            if (selecionarDias) {
                                                                dia.selecionado = !dia.selecionado;
                                                            } else {
                                                                listaDias.forEach((dia) => (dia.selecionado = false));
                                                                dia.selecionado = true;
                                                            }
                                                        }
                                                    "
                                                >
                                                    {{ dia.nome }}
                                                </div>
                                            </div>
                                            <div class="box p-4 mt-2">
                                                <div class="flex items-center gap-2">
                                                    <input
                                                        type="time"
                                                        class="form-control form-"
                                                        placeholder="Horário de funcionamento"
                                                        @input="(e) => selecionarHora(e, 'inicio')"
                                                        :value="listaDias.find((dia) => dia.selecionado)?.hr_inicio"
                                                    />
                                                    <span>às</span>
                                                    <input
                                                        type="time"
                                                        class="form-control"
                                                        placeholder="Horário de funcionamento"
                                                        @input="(e) => selecionarHora(e, 'fim')"
                                                        :value="listaDias.find((dia) => dia.selecionado)?.hr_fim"
                                                    />
                                                </div>
                                            </div>
                                            <cite class="text-xs text-slate-500 dark:text-slate-400">
                                                * Clique em "Selecionar" para editar dias simultaneamente.
                                            </cite>
                                        </div>
                                    </div>
                                </AccordionPanel>
                            </AccordionItem>
                        </AccordionGroup>

                        <div class="box p-4 md:p-6 pb-2 mt-2">
                            <div class="w-full pr-4 pb-4">
                                <div class="flex justify-between items-center mb-4">
                                    <div>
                                        <div class="flex items-center gap-1.5 w-max">
                                            <h2 class="text-lg font-semibold">Configurações do WhatsApp</h2>
                                        </div>
                                        <p class="text-md text-slate-600 dark:text-slate-400">
                                            Ajuste as configurações do WhatsApp para automação de mensagens e chamadas.
                                        </p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-12 gap-x-4 gap-y-6">
                                    <div class="col-span-12 md:col-span-6 flex items-center gap-2">
                                        <div class="form-check form-switch">
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                v-model="formDataWebhook.in_rejeitar_chamadas"
                                                @change="onChangeRejeitaChamada($event)"
                                                disabled
                                            />
                                        </div>
                                        <label class="font-medium text-slate-600 dark:text-slate-400"
                                            >Rejeitar chamadas automaticamente? <strong>(Em breve...)</strong></label
                                        >
                                    </div>
                                    <div class="col-span-12 md:col-span-6 flex items-center gap-2">
                                        <div class="form-check form-switch">
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                v-model="formDataWebhook.in_marcar_como_lida"
                                                @change="onChangeMarcaLida($event)"
                                                disabled
                                            />
                                        </div>
                                        <label class="font-medium text-slate-600 dark:text-slate-400">
                                            Marcar mensagens como 'lida' automaticamente? <strong>(Em breve...)</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabPanel>
                </TabPanels>
            </TabGroup>
            <div v-if="showEmojiPicker" :style="emojiPickerStyles" class="emoji-picker-popup z-[1000]">
                <EmojiPicker @select="onSelectEmoji" />
            </div>
        </div>
    </div>

    <!-- BEGIN: Modal Conexao -->
    <Modal :show="modaltemContrato" @hidden="modaltemContrato = false">
        <ModalBody class="p-0">
            <div class="p-5 text-center">
                <SmileIcon class="w-16 h-16 text-success mx-auto mt-3" />
                <div class="text-3xl mt-5">Hum...</div>
                <div class="text-slate-500 mt-2">Precisamos saber qual/quais módulo(s) você irá utilizar</div>
            </div>
            <div class="px-5 pb-8 text-center">
                <button type="button" @click="modaltemContrato = false" class="btn w-24 btn-success border-dashed">
                    Ok
                </button>
            </div>
            <div class="p-5 text-center border-t border-slate-200/60 dark:border-darkmode-400">
                <span>Selecione o(s) módulo(s)</span>
            </div>
        </ModalBody>
    </Modal>
    <!-- END: Modal Conexao -->

    <!-- BEGIN: Modal Content -->
    <Modal :show="modaltemContrato" @hidden="modaltemContrato = false">
        <ModalBody class="p-0">
            <div class="p-5 text-center">
                <SmileIcon class="w-16 h-16 text-success mx-auto mt-3" />
                <div class="text-3xl mt-5">Hum...</div>
                <div class="text-slate-500 mt-2">Precisamos saber qual/quais módulo(s) você irá utilizar</div>
            </div>
            <div class="px-5 pb-8 text-center">
                <button type="button" @click="modaltemContrato = false" class="btn w-24 btn-success border-dashed">
                    Ok
                </button>
            </div>
            <div class="p-5 text-center border-t border-slate-200/60 dark:border-darkmode-400">
                <span>Selecione o(s) módulo(s)</span>
            </div>
        </ModalBody>
    </Modal>
    <!-- END: Modal Content -->

    <!-- BEGIN: Modal Conexao -->
    <modalConexao ref="modalRef" @conexao="mudancaConexao" />
    <!-- END: Modal Conexao -->

    <!-- Modal  confirma remover-->
    <Modal :show="modalRemoveInstancia" @hidden="fechaModalRemover">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Confirmação</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label>
                        Ao remover a instância <strong> {{ formData.nome }}</strong> , você irá perder todos os dados
                        relacionados com essa instância!
                    </label>
                </div>
            </div>
            <div class="text-slate-500 mt-2">
                Deseja remover a instância <strong class="text-danger">{{ formData.nome }} </strong> ?
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="fechaModalRemover" class="btn btn-outline-secondary w-24 mr-2">Não</button>
            <button type="button" class="btn btn-danger w-24" @click="remover()">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma remover -->

    <!-- Modal  confirma novo token-->
    <Modal :show="modalNovoToken" @hidden="modalNovoToken = false">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Gerar um novo token?</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label>
                        Ao gerar um novo token, o token atual será invalidado e você precisará atualizar o token em
                        todos os lugares que o utilizam.
                    </label>
                </div>
            </div>
            <div class="text-slate-500 mt-2">Deseja mesmo gerar um novo token?</div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="modalNovoToken = false" class="btn btn-outline-secondary w-24 mr-2">
                Não
            </button>
            <button type="button" class="btn btn-danger w-24" @click="gerarNovoToken">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma novo token -->

    <!-- Modal Gaveta de teste -->
    <Modal
        :show="modalTestarWebhook.estado"
        @hidden="modalTestarWebhook.estado = false"
        :key="modalTestarWebhook.ref"
        slideOver
    >
        <ModalHeader class="flex justify-between items-center font-medium">
            Testar Webhook
            <XIcon class="w-6 h-6 cursor-pointer" @click="modalTestarWebhook.estado = false" />
        </ModalHeader>
        <ModalBody class="px-4 py-8 flex flex-col justify-center items-center">
            <div class="w-full flex justify-between items-center">
                <div class="flex items-center gap-1">
                    <DotIcon class="w-4 h-4" style="stroke-width: 3.5" />
                    <h3>{{ labelWebhook[modalTestarWebhook.ref] }}</h3>
                </div>
                <button
                    class="btn btn-sm btn-primary-soft border-none flex items-center gap-1"
                    @click="testarWebhook(modalTestarWebhook.ref, false)"
                >
                    Testar
                    <ZapIcon class="w-4 h-4" />
                </button>
            </div>
            <div class="w-full mt-4">
                <CodeBlock
                    label="Webhook"
                    shadow="md"
                    :code="listaApiExemplos.find((ex) => ex.nm_retorno === apiExemploSelecionado)?.ds_exemplo || ''"
                    lang="json"
                    :editable="true"
                    :showEditButton="true"
                    @edited="
                        (code) => {
                            salvarExemploApiDocs(code, modalTestarWebhook.ref);
                        }
                    "
                />
            </div>
        </ModalBody>
    </Modal>
    <!-- END:Gaveta de teste  -->
</template>

<script setup>
    import { ref, reactive, onMounted, nextTick, onUnmounted, toRaw } from 'vue';
    import { useRouter, useRoute } from 'vue-router';
    import InstanciaModulosServices from '@/services/administracao/InstanciaModulosServices';
    import FluxoAtendimentoServices from '@/services/fluxos/FluxoAtendimentoServices';
    import MensagemHorariosServices from '@/services/fluxos/MensagemHorariosServices';
    import EstabelecimentoInstancias from '@/services/administracao/EstabelecimentoInstanciasServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import ApiDocsServices from '@/services/administracao/ApiDocsServices';
    import endpointTeste from '@/utils/enpointsTeste';
    import LoadIcon from '@/global-components/loading-icon/Main.vue';
    import CodeBlock from '@/components/code-block/Main.vue';
    import modalConexao from './modalConexao.vue';
    import EmojiPicker from 'vue3-emoji-picker';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';

    import { usePagina } from '@/stores/pagina';
    import hosts from '@/utils/hosts';

    import io from 'socket.io-client';

    //import WhatsAppServices from '@/services/whatsapp/WhatsAppServices';
    import logger from '../../../utils/logger';
    import { DotIcon } from 'lucide-vue-next';
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();

    //import AtendimentoServices from '@/services/chat/AtendimentoServices';
    //import { format } from 'crypto-js';

    const router = useRouter();
    const route = useRoute();
    let pagina = usePagina();

    let listaInstanciaModulos = ref([]);
    let listaInstancias = ref([]);

    let codUsuario = ref();
    // let qrCode = ref('');
    // let timeOutQRCode = ref(false);
    // let exibirLoading = ref(false);
    // let exibirLoadingQRCode = ref(false);
    // let exibirQRCode = ref(false);
    // let gerandoQRCode = ref(false);
    let inDesconectado = ref(true);
    let IDCopiado = ref(false);
    let HASHCopiado = ref(false);
    let intervalId = ref();
    let modaltemContrato = ref(false);
    let modalRemoveInstancia = ref(false);
    let selectedIndex = ref(-1);

    let modalNovoToken = ref(false);
    let modalTestarWebhook = ref({ estado: false, ref: '' });
    let modalRef = ref();
    // let nrTentativas = ref(0);
    // let showToken = ref(false);
    // let telefoneRef = ref();
    let listaApiExemplos = ref([]);
    let apiExemploSelecionado = ref('');
    let emojiPickerStyles = ref({});
    let showEmojiPicker = ref(false);
    let cursorPosition = ref(0);
    // let refMensagem0 = ref();
    // let refMensagem1 = ref();
    // let refMensagem2 = ref();
    let refMensagem = ref([ref(), ref(), ref()]);
    let refEmojiButtons = ref([ref(), ref(), ref()]);

    let mensagemEmEdicaoIndice = ref(null);
    let formDataMensagem = ref([
        {
            nr_controle: null,
            ds_titulo: 'Mensagem de Saudação',
            ds_mensagem: '',
            ativo: false,
            cad_horarios: false,
        },
        {
            nr_controle: null,
            ds_titulo: 'Mensagem de Ausência',
            ds_mensagem: '',
            ativo: false,
            cad_horarios: false,
        },
        {
            nr_controle: null,
            ds_titulo: 'Mensagem Fora do Horário',
            ds_mensagem: '',
            ativo: false,
            cad_horarios: true,
        },
    ]);

    const inputNome = ref();
    const estabelecimentosLiberado = ref();
    const inputHash = ref();
    const inputID = ref();
    const loading = ref();
    const modulos = reactive({ in_chat_pedidos: false, in_api: false, in_crm: false });

    const formData = reactive({
        id_instancia: undefined,
        nome: undefined,
        nameinstance: undefined,
        status: undefined,
        nr_telefone: undefined,
        in_stop_bot: undefined,
        nr_hash: undefined,
    });

    const formDataWebhook = reactive({
        ds_webhook_enviar: '',
        ds_webhook_receber: '',
        ds_webhook_presenca: '',
        ds_webhook_status_mensagem: '',
        ds_webhook_conectar: '',
        ds_webhook_desconectar: '',
        in_marcar_como_lida: false,
        in_rejeitar_chamadas: false,
    });

    const labelWebhook = ref({
        ds_webhook_enviar: 'Ao enviar',
        ds_webhook_receber: 'Ao receber',
        ds_webhook_presenca: 'Presença no chat',
        ds_webhook_status_mensagem: 'Receber status de mensagem',
        ds_webhook_conectar: 'Ao conectar',
        ds_webhook_desconectar: 'Ao desconectar',
    });

    const formDataTeste = reactive({
        nr_telefone: '',
    });

    const selecionarDias = ref(false);
    const listaDias = ref([
        { id: 0, nome: 'Dom', selecionado: false, hr_inicio: '', hr_fim: '' },
        { id: 1, nome: 'Seg', selecionado: true, hr_inicio: '', hr_fim: '' },
        { id: 2, nome: 'Ter', selecionado: false, hr_inicio: '', hr_fim: '' },
        { id: 3, nome: 'Qua', selecionado: false, hr_inicio: '', hr_fim: '' },
        { id: 4, nome: 'Qui', selecionado: false, hr_inicio: '', hr_fim: '' },
        { id: 5, nome: 'Sex', selecionado: false, hr_inicio: '', hr_fim: '' },
        { id: 6, nome: 'Sáb', selecionado: false, hr_inicio: '', hr_fim: '' },
    ]);

    let socket;

    if (import.meta.env.MODE === 'development') {
        socket = io.connect(hosts.webSocket, { transports: ['websocket'] });
    } else {
        socket = io.connect(hosts.hostConsumer, {
            rememberUpgrade: true,
            transports: ['websocket'],
            secure: true,
            enabledTransports: ['ws', 'wss'],
            rejectUnauthorized: false,
            path: hosts.webSocket,
        });
    }

    function selecionarHora({ target }, campo) {
        listaDias.value.forEach((dia) => {
            if (dia.selecionado) {
                dia['hr_' + campo] = target.value;
            }
        });
    }

    async function toggleEmojiPicker(index) {
        mensagemEmEdicaoIndice.value = index;
        showEmojiPicker.value = !showEmojiPicker.value;
        const emojiButton = refEmojiButtons.value[index];
        if (showEmojiPicker.value) {
            nextTick(() => {
                const buttonRect = emojiButton.value.getBoundingClientRect();
                emojiPickerStyles.value = {
                    position: 'absolute',
                    top: `${buttonRect.bottom - window.scrollY}px`,
                    // left: `0px`,
                };
            });
        }
    }
    async function onSelectEmoji(event) {
        const emoji = event.i;
        const textArea = refMensagem.value[mensagemEmEdicaoIndice.value].value;
        const text = formDataMensagem.value[mensagemEmEdicaoIndice.value].ds_mensagem;
        const title = formDataMensagem.value[mensagemEmEdicaoIndice.value].ds_titulo;
        const start = text.slice(0, cursorPosition.value);
        const end = text.slice(cursorPosition.value);

        formDataMensagem.value.forEach((item) => {
            if (item.ds_titulo === title) {
                item.ds_mensagem = start + emoji + end;
            }
        });
        showEmojiPicker.value = false;

        // Atualiza a posição do cursor após a inserção do emoji
        nextTick(() => {
            cursorPosition.value += emoji.length;
            textArea.setSelectionRange(cursorPosition.value, cursorPosition.value);
        });
    }

    async function updateCursorPosition(index) {
        const textArea = refMensagem.value[index].value;
        cursorPosition.value = textArea.selectionStart;
    }

    function abrirModalConexao() {
        modalRef.value?.abrir(formData);
    }

    function mudancaConexao(dados_instancia) {
        formData.status_connection = dados_instancia.status_connection;
    }

    async function copyID() {
        const inputField = inputID.value;
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField.value);
                IDCopiado.value = true;
                setTimeout(() => {
                    IDCopiado.value = false; // Remove a classe após um tempo
                }, 2000); // Tempo do efeito em milissegundos
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function copyHash() {
        const inputField = inputHash.value;
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField.value);
                HASHCopiado.value = true;
                setTimeout(() => {
                    HASHCopiado.value = false; // Remove a classe após um tempo
                }, 2000); //
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function carregaInstanciaModulos() {
        let filtros = {
            id_instancia: formData.id_instancia,
            //cd_usuario: parseInt(codUsuario.value),
        };

        listaInstanciaModulos.value = [];

        //console.log('filtros at line 333:', filtros);
        const result = await InstanciaModulosServices.listar(filtros);

        if (result.statuscode == 200) {
            listaInstanciaModulos.value = result.data;

            //console.log('listaInstanciaModulos.value:', listaInstanciaModulos.value);
            //toast.success('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            // toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
    }

    async function gerarNovoToken() {
        loading.value.show();
        modalNovoToken.value = false;
        try {
            const { id_instancia } = formData;
            const dadosEstabelecimento = JSON.parse(localStorage.getItem('estabelecimentos'));
            if (!dadosEstabelecimento) {
                return toast.error('Não foi possível gerar um novo token!');
            }
            const cd_estabelecimento = dadosEstabelecimento[0].cd_estabelecimento;
            const nr_conrole = dadosEstabelecimento[0].nr_conrole;

            const result = await EstabelecimentoInstancias.alterar({
                id_instancia: Number(id_instancia),
                cd_estabelecimento,
                nr_conrole,
            });
            if (result.statuscode == 200) {
                toast.success('Novo token gerado com sucesso!');
                formData.nr_hash = result.data[0].nr_hash;
            } else {
                toast.error(result.message);
            }
        } catch (err) {
            console.error('Erro ao gerar novo token: ', err.message);
            toast.error(err.message);
        } finally {
            loading.value.hide();
        }
    }

    function obterHash() {
        const obj = localStorage.getItem('estabelecimentos');
        // console.log('obj at line 678 in instancia/viewInstancia.vue:', obj);
        const estabelecimentos = JSON.parse(obj);
        return estabelecimentos[0].nr_hash;
    }

    async function contratar() {
        // let modulos = listaInstanciaModulos.value.filter((modulo) => modulo.in_contratado == true);
        //console.log('modulos at line 354:', modulos);

        let modulos = listaInstanciaModulos.value.map((modulo) => {
            return {
                id_instancia: modulo.id_instancia,
                cd_modulo: modulo.cd_modulo,
                nm_modulo: modulo.nm_modulo,
                vl_modulo: modulo.vl_modulo,
                ds_funcionalidade: modulo.ds_funcionalidade,
                tp_situacao: modulo.in_contratado ? 'Contratado' : 'Não Contratado',
                in_contratado: modulo.in_contratado,
            };
        });

        const result = await InstanciaModulosServices.incluir(modulos);

        if (result.statuscode == 200) {
            toast.success('Módulo contratado com sucesso!');
        } else if (result.statuscode == 404) {
            // toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
    }

    async function remover() {
        loading.value.show();
        modalRemoveInstancia.value = false;
        const filtro = {
            id_instancia: formData.id_instancia,
            instance: formData.nameinstance,
            nr_hash: formData.nr_hash,
            cd_estabelecimento: formData.cd_estabelecimento,
        };

        const result = await InstanciaServices.remover(filtro);
        // console.log('result at line 427 in instancia/viewInstancia.vue:', result);

        if (result.statuscode == 200) {
            toast.success('Instância removida com sucesso!');

            router.push({ name: 'listaInstancias' });
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    async function testarWebhook(ref, modal) {
        // console.log(modal);

        if (modal) {
            apiExemploSelecionado.value = ref;
            modalTestarWebhook.value.ref = ref;
            modalTestarWebhook.value.estado = true;
            return;
        }

        if (!formDataWebhook[ref]) {
            toast.warning('Informe a URL do Webhook!');
            return;
        }

        try {
            const result = await endpointTeste.testar('POST', formDataWebhook[ref], undefined, {
                status: 'success',
                mensagem: 'Testando Webhook do OiZap --> ' + labelWebhook.value[ref],
            });

            toast.success('Enviado!');
        } catch {
            toast.error('Erro ao enviar requisição!');
        }
    }

    function abreModalRemover() {
        modalRemoveInstancia.value = true;
    }

    function fechaModalRemover() {
        modalRemoveInstancia.value = false;
    }

    async function carregaInstancia() {
        loading.value.show();
        let filtros = {
            id_instancia: formData.id_instancia,
            in_verificastatus: true,
        };

        listaInstancias.value = [];
        const result = await InstanciaServices.listar(filtros);
        // console.log('result at line 506 in instancia/viewInstancia.vue:', result);

        if (result.statuscode == 200) {
            const instancia = result.data[0];
            //formData.id_instancia = instancia.id_instancia;
            formData.nameinstance = instancia.nameinstance;
            formData.nome = instancia.nome;
            formData.nr_telefone = instancia.nr_telefone;
            formData.status = instancia.status;

            //wait carregaInstanciaModulos();

            //formData.status_connection = trataStatus(instancia.status_connection);
            formData.status_connection = instancia.status_connection;
            formData.in_stop_bot = instancia.in_stop_bot;
            formData.nr_hash = instancia.nr_hash;
            inDesconectado.value = formData.status_connection == 'Conectado' ? false : true;
            // console.log('inDesconectado.value at line 399:', inDesconectado.value);

            socket.emit('joinInstancia', formData.nameinstance);

            formDataWebhook.ds_webhook_conectar = instancia.ds_webhook_conectar;
            formDataWebhook.ds_webhook_desconectar = instancia.ds_webhook_desconectar;
            formDataWebhook.ds_webhook_enviar = instancia.ds_webhook_enviar;
            formDataWebhook.ds_webhook_presenca = instancia.ds_webhook_presenca;
            formDataWebhook.ds_webhook_receber = instancia.ds_webhook_receber;
            formDataWebhook.ds_webhook_status_mensagem = instancia.ds_webhook_status_mensagem;
            formDataWebhook.in_marcar_como_lida = !!instancia.in_marcar_como_lida;
            formDataWebhook.in_rejeitar_chamadas = !!instancia.in_rejeitar_chamadas;

            // console.log('listaInstanciaModulos.value:', listaInstanciaModulos.value);
            //toast.success('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            // toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    const onChangeContratado = (modulo, event) => {
        modulo.in_contratado = event.target.checked;
    };

    const focusNome = async () => {
        await nextTick();

        const input = inputNome.value;

        if (input && input.focus) {
            input.focus();
        }
    };

    const stopInterval = () => {
        if (intervalId.value) {
            //console.log('stopInterval');
            clearInterval(intervalId.value);
            intervalId.value = null;
        }
    };

    function mudarExemplo(code, ref) {
        if (!listaApiExemplos.value.find((ex) => ex.nm_retorno === ref)) {
            listaApiExemplos.value.push({
                tp_retorno: 'WebHooks',
                nm_retorno: ref,
                ds_exemplo: code,
            });
        }
        listaApiExemplos.value.find((ex) => ex.nm_retorno === ref).ds_exemplo = code;
    }

    async function salvarExemploApiDocs(code, ref) {
        // console.log(code);

        try {
            loading.value.show();
            const dadosExemplo = {
                tp_retorno: 'WebHooks',
                nm_retorno: ref,
                ds_exemplo: code,
            };
            const result = await ApiDocsServices.alterarExemplo(dadosExemplo);
            if (result.statuscode == 200) {
                toast.success('Exemplo salvo com sucesso!');
                mudarExemplo(code, ref);
                return;
            } else if (result.statuscode == 404) {
                const result = await ApiDocsServices.incluirExemplo(dadosExemplo);
                if (result.statuscode != 200) {
                    toast.warning(result.message);
                } else {
                    toast.success('Exemplo salvo com sucesso!');
                    mudarExemplo(code, ref);
                    return;
                }
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            console.log('Erro ao salvar exemplo: ' + error.message);
        } finally {
            loading.value.hide();
        }
    }

    async function listarApiDocsExemplos() {
        const result = await ApiDocsServices.listarExemplos({ tp_retorno: 'WebHooks' });
        if (result.statuscode == 200) {
            //console.log('result at line 555 in instancia/viewInstancia.vue:', result.data[0]);
            listaApiExemplos.value = result.data;
            // console.log(result.data);
        } else if (result.statuscode == 404) {
            // toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
    }

    async function listarHorarios() {
        loading.value.show();
        const findControle = formDataMensagem.value.find((item) => item.nr_controle != null);
        let filtros = {
            nr_contfluxo: findControle ? findControle.nr_controle : null,
        };

        const result = await MensagemHorariosServices.listar(filtros);
        if (result.statuscode == 200) {
            result.data.forEach((item) => {
                const dia = listaDias.value.find((d) => d.id == item.nr_diasemana);
                if (dia) {
                    dia.hr_inicio = item.hr_inicial;
                    dia.hr_fim = item.hr_final;
                }
            });
        } else if (result.statuscode == 404) {
            // toast.warning('Horarios: ' + result.message);
        } else {
            toast.error('Horarios: ' + result.message);
        }
        loading.value.hide();
    }

    async function listarMensagens() {
        loading.value.show();
        let filtros = {
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            tp_mensagem: 'API',
        };

        const result = await FluxoAtendimentoServices.listar(filtros);
        if (result.statuscode == 200) {
            formDataMensagem.value.forEach((item) => {
                const mensagem = result.data.find((msg) => msg.ds_titulo === item.ds_titulo);
                if (mensagem) {
                    item.nr_controle = mensagem.nr_controle;
                    item.ds_mensagem = mensagem.ds_mensagem;
                    item.ativo = mensagem.in_ativa;
                }
            });

            await listarHorarios();
        } else if (result.statuscode == 404) {
            // toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    async function salvar() {
        loading.value.show();

        let obj = {
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            nome: formData.nome,
            status: 'Ativo',
            in_stop_bot: true,
        };

        let result = {};

        if (!formData.id_instancia) result = await InstanciaServices.incluir(obj);
        else result = await InstanciaServices.alterar({ nome: obj.nome, id: formData.id_instancia });

        // console.log(' result.data at line 1408 in instancia/viewInstancia.vue:', result.data);
        if (result.statuscode == 200) {
            toast.success('Instância criada com sucesso!');
            // router.push({ name: 'viewInstancia', params: { idInstancia: result.data[0].id_instancia } });
            formData.id_instancia = result.data[0].id_instancia;
            formData.in_stop_bot = result.data[0].in_stop_bot;
            formData.nameinstance = result.data[0].nameinstance;
            formData.nome = result.data[0].nome;
            formData.nr_hash = result.data[0].nr_hash;
            formData.nr_telefone = result.data[0].nr_telefone;
            formData.status = result.data[0].status;
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    async function mudarStatusMensagem(e, index) {
        let reqObj = {
            nr_controle: formDataMensagem.value[index].nr_controle,
            in_ativa: e.target.checked,
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
        };
        if (e.target.checked && !formDataMensagem.value[index].ds_mensagem) {
            toast.warning('Adicione uma mensagem para ativá-la!');
            setTimeout(() => {
                formDataMensagem.value[index].ativo = false;
                e.target.checked = false;
                selectedIndex.value = index;
            }, 200);
            return;
        }

        //console.log('reqObj at line 1439 in instancia/viewInstancia.vue:', reqObj);
        let result = await FluxoAtendimentoServices.alterar(reqObj);
        if (result.statuscode == 200) {
            formDataMensagem.value[index].in_ativa = result.data[0].in_ativa;
            toast.success(result.message);
        } else if (result.statuscode == 409 || result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.erros);
        }
    }

    async function salvarMensagem(index) {
        // if (!formDataMensagem.value[index].ds_mensagem) {
        //     toast.warning('Mensagem é obrigatória!');
        //     return;
        // }

        loading.value.show();

        let reqObj = {
            tp_mensagem: 'API',
            ds_titulo: formDataMensagem.value[index].ds_titulo,
            ds_mensagem: formDataMensagem.value[index].ds_mensagem,
            in_ativa: formDataMensagem.value[index].ativo,
            cd_usucad: codUsuario.value,
            in_fluxopadrao: false,
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
        };

        let result;
        if (!formDataMensagem.value[index].nr_controle) {
            result = await FluxoAtendimentoServices.incluir(reqObj);
        } else {
            reqObj.nr_controle = formDataMensagem.value[index].nr_controle;
            result = await FluxoAtendimentoServices.alterar(reqObj);
        }

        if (result.statuscode == 200) {
            formDataMensagem.value[index].nr_controle = result.data[0].nr_controle;
            formDataMensagem.value[index].ds_titulo = result.data[0].ds_titulo;
            formDataMensagem.value[index].ds_mensagem = result.data[0].ds_mensagem;
            formDataMensagem.value[index].in_ativa = result.data[0].in_ativa;

            if (formDataMensagem.value[index].cad_horarios) {
                // adiciona os dias selecionados
                const errors = [];
                listaDias.value
                    .filter((dia) => dia.hr_inicio || dia.hr_fim)
                    .forEach(async (dia) => {
                        let reqHorario = {
                            nr_contfluxo: formDataMensagem.value[index].nr_controle,
                            nr_diasemana: dia.id,
                            ds_diasemana: dia.nome,
                            hr_inicial: !!dia.hr_inicio ? dia.hr_inicio : null,
                            hr_final: !!dia.hr_fim ? dia.hr_fim : null,
                        };
                        const resultHorario = await MensagemHorariosServices.incluirAlterar(reqHorario);
                        if (resultHorario.statuscode != 200) {
                            errors.push(resultHorario.message);
                        }
                    });
                if (errors.length > 0) {
                    toast.error(errors.join(', '));
                }

                if (selecionarDias.value) {
                    selecionarDias.value = !selecionarDias;
                    listaDias.value.forEach((dia) => (dia.selecionado = dia.nome == 'Seg'));
                }
            }

            toast.success(result.message);
        } else if (result.statuscode == 409 || result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.erros);
        }

        loading.value.hide();
    }

    async function salvarWebhooks() {
        loading.value.show();
        let result = await InstanciaServices.alterar({ id: formData.id_instancia, ...formDataWebhook });

        if (result.statuscode == 200) {
            const instancia = result.data[0];
            toast.success('Webhooks salvos com sucesso!');
            // router.push({ name: 'viewInstancia', params: { idInstancia: result.data[0].id_instancia } });

            formDataWebhook.ds_webhook_conectar = instancia.ds_webhook_conectar;
            formDataWebhook.ds_webhook_desconectar = instancia.ds_webhook_desconectar;
            formDataWebhook.ds_webhook_enviar = instancia.ds_webhook_enviar;
            formDataWebhook.ds_webhook_presenca = instancia.ds_webhook_presenca;
            formDataWebhook.ds_webhook_receber = instancia.ds_webhook_receber;
            formDataWebhook.ds_webhook_status_mensagem = instancia.ds_webhook_status_mensagem;
            formDataWebhook.in_marcar_como_lida = !!instancia.in_marcar_como_lida;
            formDataWebhook.in_rejeitar_chamadas = !!instancia.in_rejeitar_chamadas;
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    const onChangeRejeitaChamada = async (event) => {
        let result = await InstanciaServices.alterar({
            id: formData.id_instancia,
            in_rejeitar_chamadas: event.target.checked,
        });

        if (result.statuscode == 200) {
            toast.success('Configuração salva com sucesso!');

            formDataWebhook.in_rejeitar_chamadas = event.target.checked;
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
    };

    const onChangeMarcaLida = async (event) => {
        let result = await InstanciaServices.alterar({
            id: formData.id_instancia,
            in_marcar_como_lida: event.target.checked,
        });

        if (result.statuscode == 200) {
            toast.success('Configuração salva com sucesso!');

            formDataWebhook.in_marcar_como_lida = event.target.checked;
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
    };

    onMounted(async () => {
        pagina.pagename = 'Instância';
        pagina.description = 'Instâncias';
        pagina.module = 'Administração';

        formData.id_instancia = route.params.idInstancia;

        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;

        codUsuario.value = localStorage.getItem('codusuario');
        const estatabelecimentos = localStorage.getItem('estabelecimentos');
        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);

            if (!!formData.id_instancia) await carregaInstancia();

            await focusNome();

            // await eventosSocket();
        } else {
            toast.warning('Estabelecimento não liberado!');
        }
        await listarApiDocsExemplos();
        await listarMensagens();
    });

    onUnmounted(async () => {
        //console.log('onUnmounted', nrTentativas.value);
        //stopInterval();

        socket.emit('disconnectRoom', formData.nameinstance);
        // Desconectando o socket antes de desmontar o componente
        if (socket) {
            socket.disconnect();
            logger.debug('Socket desconectado:', socket.id);
        }
    });
</script>

<style scoped>
    .copied {
        animation: copyEffect 0.5s ease-in-out;
    }

    ::v-deep(.disabled) {
        pointer-events: none;
        opacity: 0.5;
    }

    ::v-deep(.nav-link.active > button) {
        font-weight: 600;
    }

    @keyframes copyEffect {
        0% {
            transform: scale(1);
            color: inherit;
        }
        50% {
            transform: scale(1.5);
            color: green;
        }
        100% {
            transform: scale(1);
            color: inherit;
        }
    }
</style>
