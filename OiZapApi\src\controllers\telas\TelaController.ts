import { Request, Response } from 'express';
import { TelaDB } from '../../data/telas/TelaDB';

export class TelaController {
  static async listarTelas(req: Request, res: Response) {
    try {
      const telas = await new TelaDB().listarTelas();
      return res.json(telas);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao listar telas' });
    }
  }
  static async criarTela(req: Request, res: Response) {
    try {
      const tela = await new TelaDB().criarTela(req.body);
      return res.status(201).json(tela);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao criar tela' });
    }
  }
  static async atualizarTela(req: Request, res: Response) {
    try {
      const { cd_tela } = req.params;
      const tela = await new TelaDB().atualizarTela(Number(cd_tela), req.body);
      return res.json(tela);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao atualizar tela' });
    }
  }
  static async deletarTela(req: Request, res: Response) {
    try {
      const { cd_tela } = req.params;
      await new TelaDB().deletarTela(Number(cd_tela));
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao deletar tela' });
    }
  }
} 