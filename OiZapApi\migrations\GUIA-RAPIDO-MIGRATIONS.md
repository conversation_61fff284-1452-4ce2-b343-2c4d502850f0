-- Migration: exemplo_operacoes_comuns
-- Created: 2025-01-07T00:00:00.000Z
-- Environment: ALL

-- ========================================
-- EXEMPLOS DE OPERAÇÕES COMUNS
-- ========================================

-- 1. CRIAR TABELA COM VERIFICAÇÃO
CREATE TABLE IF NOT EXISTS exemplo_produtos (
id SERIAL PRIMARY KEY,
nome VARCHAR(255) NOT NULL,
preco DECIMAL(10,2) NOT NULL,
categoria_id INTEGER,
ativo BOOLEAN DEFAULT TRUE,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. ADICIONAR COLUNA SE NÃO EXISTIR
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'exemplo_produtos' 
        AND column_name = 'descricao'
    ) THEN
        ALTER TABLE exemplo_produtos ADD COLUMN descricao TEXT;
    END IF;
END $$;

-- 3. CRIAR ÍNDICES
CREATE INDEX IF NOT EXISTS idx_exemplo_produtos_nome ON exemplo_produtos(nome);
CREATE INDEX IF NOT EXISTS idx_exemplo_produtos_categoria ON exemplo_produtos(categoria_id);
CREATE INDEX IF NOT EXISTS idx_exemplo_produtos_ativo ON exemplo_produtos(ativo);

-- 4. ADICIONAR CONSTRAINT UNIQUE
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'exemplo_produtos' 
        AND constraint_name = 'uq_exemplo_produtos_nome'
    ) THEN
        ALTER TABLE exemplo_produtos ADD CONSTRAINT uq_exemplo_produtos_nome UNIQUE (nome);
    END IF;
END $$;

-- 5. ADICIONAR FOREIGN KEY
DO $$
BEGIN
IF NOT EXISTS (
SELECT 1 FROM information_schema.table_constraints
WHERE table_name = 'exemplo_produtos'
AND constraint_name = 'fk_exemplo_produtos_categoria'
) THEN
-- Primeiro criar a tabela de categoria se não existir
CREATE TABLE IF NOT EXISTS categorias (
id SERIAL PRIMARY KEY,
nome VARCHAR(100) NOT NULL
);

        -- Depois adicionar a FK
        ALTER TABLE exemplo_produtos
        ADD CONSTRAINT fk_exemplo_produtos_categoria
        FOREIGN KEY (categoria_id) REFERENCES categorias(id);
    END IF;

END $$;

-- 6. INSERIR DADOS INICIAIS
INSERT INTO categorias (nome)
VALUES ('Eletrônicos'), ('Roupas'), ('Casa')
ON CONFLICT (nome) DO NOTHING;

-- 7. ATUALIZAR DADOS EXISTENTES
UPDATE exemplo_produtos
SET categoria_id = (SELECT id FROM categorias WHERE nome = 'Eletrônicos' LIMIT 1)
WHERE categoria_id IS NULL
AND nome ILIKE '%eletrônico%';

-- 8. RENOMEAR COLUNA (cuidado com dados existentes)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'exemplo_produtos' 
        AND column_name = 'descricao'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'exemplo_produtos' 
        AND column_name = 'descricao_completa'
    ) THEN
        ALTER TABLE exemplo_produtos RENAME COLUMN descricao TO descricao_completa;
    END IF;
END $$;
