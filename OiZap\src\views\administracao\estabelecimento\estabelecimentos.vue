<template>
    <ShowLoading ref="loading" />
    <div class="">
        <!-- <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Estabelecimentos</h2>
        </div> -->

        <div class="box p-4">
            <!-- Header da seção de módulos -->
            <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-4 text-white">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="flex items-center space-x-3 mb-4 md:mb-0">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                            <Building2Icon
                                class="w-6 h-6"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                            />
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Lista de Estabelecimentos</h3>
                            <p class="text-blue-100 text-sm">Lista de estabelecimentos cadastrados no sistema OiZap.</p>
                        </div>
                    </div>

                    <!-- Cards de estatística -->
                    <div class="flex space-x-4">
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center cursor-pointer"
                            @click="btnFitrarConexao('todos')"
                        >
                            <div class="text-2xl font-bold">{{ totalEstabelecimento }}</div>
                            <div class="text-xs text-blue-100">Total Estabelecimentos</div>
                        </div>
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center cursor-pointer"
                            @click="btnFitrarConexao('todos')"
                        >
                            <div class="text-2xl font-bold">{{ estatisticasConexao.totalinstancia }}</div>
                            <div class="text-xs text-blue-100">Total Instâncias</div>
                        </div>
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center cursor-pointer"
                            @click="btnFitrarConexao('conectados')"
                        >
                            <div class="text-2xl font-bold">{{ estatisticasConexao.conectado }}</div>
                            <div class="text-xs text-blue-100">Conectadas</div>
                        </div>
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center cursor-pointer"
                            @click="btnFitrarConexao('desconectados')"
                        >
                            <div class="text-2xl font-bold text-red-500">{{ estatisticasConexao.desconectado }}</div>
                            <div class="text-xs text-blue-100">Desconectadas</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto overflow-y-hidden">
                <div class="border-l border-r border-b border-slate-200 dark:border-darkmode-600 rounded-b-xl mb-2">
                    <div class="w-full p-4 pb-0">
                        <label class="form-label w-full flex flex-col sm:flex-row pb-0 mb-0 text-xs">Pesquisar</label>
                        <BaseInput
                            id="tabulator-html-filter-value"
                            type="text"
                            class="form-control p-1 mt-0 h-[2.7em]"
                            placeholder="Nome, Telefone ou Hash"
                            @input="
                                (event) => {
                                    tabulator.setFilter(customFilter, { value: event.target.value });
                                }
                            "
                        />
                    </div>
                    <div class="overflow-x-auto scrollbar-hidden">
                        <div id="tabulator" class="mt-5 mb-2 table-report table-report--tabulator"></div>
                    </div>
                </div>

                <!-- <div class="overflow-x-auto">
                    <table class="table table-report bg-gray-100 dark:bg-darkmode-800 px-4 rounded-md intro-y">
                        <thead class="">
                            <tr>
                                <th
                                    class="whitespace-nowrap text-slate-500 hover:bg-gray-200/60 hover:dark:bg-darkmode-600 rounded-md align-top"
                                    v-for="(coluna, index) in colunas"
                                    :key="index"
                                >
                                    <div>
                                        <div
                                            :class="`w-full flex justify-between items-center ${
                                                coluna.ativo ? 'text-primary' : 'text-slate-500'
                                            }`"
                                        >
                                            {{ coluna.label }}
                                            <button
                                                class="btn btn-sm btn-dark-soft bg-transparent shadow-none hover:bg-gray-400 hover:dark:bg-darkmode-400 border-none"
                                                v-if="coluna.label != 'Ações' && coluna.label != 'Foto'"
                                                @click="() => abrirFiltro(index)"
                                            >
                                                <EllipsisIcon class="w-4 h-4" />
                                            </button>

                                            <div
                                                v-if="coluna.label == 'Ações' || coluna.label == 'Foto'"
                                                class="w-[28px] h-[28px]"
                                            ></div>

                                            <button
                                                class="btn btn-sm btn-dark-soft bg-transparent border-none shadow-none hover:bg-gray-400 hover:dark:bg-darkmode-400"
                                                v-if="coluna.ordenacao"
                                                @click="ordenarPorStatus"
                                            >
                                                <ArrowDownIcon class="w-4 h-4 cursor-pointer" />
                                            </button>
                                        </div>
                                        <div
                                            v-if="filtroAbertoRef == index || !!filtros[coluna.chave]"
                                            class="mt-1 relative custom-input"
                                        >
                                            <SearchIconIcon class="w-4 h-4 absolute top-[25%]" />
                                            <input
                                                :ref="filtrosRefs[index]"
                                                type="text"
                                                class="!border-none form-control bg-transparent active:border-none focus:!border-none focus:!border-transparent pl-6"
                                                :placeholder="coluna.label"
                                                v-model="filtros[coluna.chave]"
                                                @input="({ target }) => pesquisarFiltro(coluna.chave, target.value)"
                                            />
                                        </div>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="listaEstabelecimentos.length" class="">
                            <tr
                                v-for="(estabelecimento, index) in listaEstabelecimentos"
                                :key="index"
                                class="cursor-pointer box [&>td]:!py-1 [&>td]:whitespace-nowrap hover:shadow-xl hover:scale-[1.01] transition-all duration-300"
                                @click="carregaEstabelecimento(estabelecimento)"
                            >
                                <td>
                                    <Tippy content="Clique aqui para conectar ao estabelecimento" class="tooltip w-fit">
                                        <div
                                            class="rounded-full p-0.5 w-fit"
                                            :class="
                                                estabelecimento.status_connection == 'Conectado'
                                                    ? 'border-solid border-2 border-green-600'
                                                    : ''
                                            "
                                        >
                                            <img
                                                v-if="estabelecimento.url_foto"
                                                alt="oizap"
                                                class="rounded-full w-8 h-8"
                                                :src="estabelecimento.url_foto"
                                                @click="carregaEstabelecimento(estabelecimento)"
                                            />
                                            <CircleUserIcon
                                                v-else
                                                class="rounded-full w-8 h-8 bg-slate-100 dark:text-slate-500 dark:bg-darkmode-800 text-slate-400"
                                            ></CircleUserIcon>
                                        </div>
                                    </Tippy>
                                </td>
                                <td>
                                    {{ estabelecimento.nm_estabelecimento }}
                                </td>
                                <td>
                                    {{ estabelecimento.telefone }}
                                </td>
                                <td>
                                    {{ estabelecimento.nameinstance }}
                                </td>
                                <td>
                                    {{ estabelecimento.nr_hash }}
                                </td>
                                <td>
                                    <div class="w-full flex justify-center items-center">
                                        <CloudIcon
                                            v-if="estabelecimento.in_habilitaproxy"
                                            class="w-4 h-4 relative mr-1 text-green-600"
                                            style="stroke-width: 2.5px"
                                        />
                                        <CloudOffIcon
                                            v-else
                                            class="w-4 h-4 relative mr-1 text-red-600"
                                            style="stroke-width: 2.5px"
                                        />
                                    </div>
                                </td>
                                <td class="">
                                    <div
                                        class="rounded text-xs text-white p-0.5 w-24 text-center"
                                        :class="{
                                            'bg-emerald-500': estabelecimento.status_connection == 'Conectado',
                                            'bg-red-500': estabelecimento.status_connection == 'Desconectado',
                                            'bg-orange-500':
                                                estabelecimento.status_connection != 'Desconectado' &&
                                                estabelecimento.status_connection != 'Conectado',
                                        }"
                                    >
                                        {{ estabelecimento.status_connection }}
                                    </div>
                                </td>
                                <td>
                                    <button
                                        type="button"
                                        class="btn btn-outline-primary text-xs w-24 h-6 ml-auto"
                                        @click.stop="abreModalConfiguracoes(estabelecimento)"
                                    >
                                        Configurações
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div> -->

                <!-- BEGIN: Limit Offset -->
                <div class="flex justify-between items-center">
                    <cite class="text-xs text-slate-500 dark:text-darkmode-400 p-4">
                        <span class="bg-green-500 w-2 h-2 rounded-full mr-0.5 inline-block align-baseline"></span>
                        Instâncias Conectadas
                        <span class="bg-red-500 w-2 h-2 rounded-full ml-2 mr-0.5 inline-block align-baseline"></span>
                        Instâncias Desconectadas
                    </cite>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN: Logar -->
    <Modal size="modal-xl" :show="modalConfig" @hidden="closeModalConfig()">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Estabelecimento</h2>
            <XIcon class="w-6 h-6 mr-0 cursor-pointer" @click="closeModalConfig()" />
        </ModalHeader>
        <ModalBody>
            <div class="box p-2">
                <div class="grid grid-cols-12 gap-1">
                    <div class="form-control col-span-6 gap-1">
                        <label for="validation-form-1" class="form-label w-20">Estabelecimento</label>

                        <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                v-model.trim="formData.nm_estabelecimento"
                                placeholder="Informe o Estabelecimento"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-6 gap-1">
                        <label for="validation-form-1" class="form-label w-20">CNPJ</label>

                        <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                v-model.trim="formData.nr_cnpj"
                                placeholder="Informe seu CNPJ"
                            />
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-12 gap-1">
                    <div class="form-control col-span-6 gap-1 mt-2">
                        <Tippy content="Link do Entrega do Chef" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <LinkIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.ds_hostentregadochef"
                                    placeholder="https://entregadochef.com/meunegocio"
                                />
                            </div>
                        </Tippy>
                    </div>
                    <!-- v-show="!isPesquisandoEndereco" -->
                    <div class="form-control col-span-6 gap-1 mt-2">
                        <Tippy content="Endereço" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <MapPinnedIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.ds_endereco"
                                    placeholder="Informe seu endereço"
                                    @input="focusSelectEndereco"
                                />
                            </div>
                        </Tippy>
                    </div>
                    <!-- <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full" v-show="isPesquisandoEndereco">
            <selPesquisaEndereco
              v-model="listaEnderecos"
              :mode="'single'"
              ref="selectEndereco"
              @emitEvent="selecionaEndereco"
              @keydown.esc="focusEndereco"
            />
          </div> -->
                </div>
                <div class="grid grid-cols-12 gap-1 mt-2">
                    <div class="col-span-6">
                        <Tippy content="Latitude" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <CompassIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.nr_latitude"
                                    placeholder="Informe a Latitude"
                                />
                            </div>
                        </Tippy>
                    </div>
                    <div class="col-span-6">
                        <Tippy content="Longitude" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <CompassIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.nr_longitude"
                                    placeholder="Informe a Longitude"
                                />
                            </div>
                        </Tippy>
                    </div>
                </div>
                <div class="grid grid-cols-12 gap-1 mt-2">
                    <div class="col-span-6">
                        <Tippy content="Valor de Entrega" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <TruckIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.vl_entrega"
                                    placeholder="Valor de Entrega"
                                />
                            </div>
                        </Tippy>
                    </div>
                    <div class="col-span-6">
                        <Tippy content="Hash Estabelecimento" class="tooltip w-5 h-5">
                            <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                <KeyRoundIcon class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0" />
                                <input
                                    type="text"
                                    class="form-control col-span-1 block pl-9"
                                    v-model.trim="formData.nr_hash"
                                    placeholder="Hash Estabelecimento"
                                    disabled="true"
                                />
                            </div>
                        </Tippy>
                    </div>
                </div>

                <div class="grid grid-cols-12 gap-1 mt-2">
                    <div class="col-span-6">
                        <div class="form-check mr-2 mt-2 sm:mt-0">
                            <div class="form-check form-switch">
                                <input
                                    class="form-check-input"
                                    type="checkbox"
                                    :value="formData.in_habilitaproxy"
                                    :checked="formData.in_habilitaproxy"
                                    @change="onChangeHabilitaProxy($event)"
                                />
                            </div>
                            <label class="ml-2 mt-2 forceLeft form-label self-center">Utiliza Proxy</label>
                        </div>
                        <label class="ml-2 mt-2 forceLeft form-label self-center text-xs text-slate-400 font-bold"
                            >Necessário desconectar e conectar a instância pelo OiZap
                            <a class="text-red-400">Cuidado!</a></label
                        >
                    </div>

                    <div class="col-span-6">
                        <div class="form-check mr-2 mt-2 sm:mt-0">
                            <div class="form-check form-switch">
                                <input
                                    class="form-check-input"
                                    type="checkbox"
                                    :value="formData.in_apidedicada"
                                    :checked="formData.in_apidedicada"
                                    @change="onChangeApidica($event)"
                                />
                            </div>
                            <label class="ml-2 mt-2 forceLeft form-label self-center">Api dedicada</label>
                        </div>

                        <div class="sm:ml-0 mt-3 sm:mt-0 relative">
                            <div class="flex items-center relative">
                                <!-- Ícone LinkIcon no lado esquerdo -->
                                <LinkIcon class="w-4 h-4 absolute left-3 z-10" />

                                <!-- Input com padding para os dois ícones -->
                                <input
                                    :disabled="!formData.in_apidedicada"
                                    type="text"
                                    class="form-control w-full pl-9 pr-12"
                                    v-model.trim="formData.ds_hostapi"
                                    placeholder="Link da Api dedicada"
                                />

                                <!-- Ícone CogIcon no lado direito -->
                                <div class="tooltip-container absolute right-3 top-2.5 transform -translate-y-1/2 z-10">
                                    <CogIcon
                                        :class="{
                                            'w-5 h-5 transition-colors': true,
                                            'cursor-pointer text-gray-600 hover:text-green-600':
                                                formData.in_apidedicada,
                                            'cursor-not-allowed text-gray-300 opacity-50': !formData.in_apidedicada,
                                        }"
                                        @click="handleCogClick()"
                                    />
                                    <div class="custom-tooltip" v-if="formData.in_apidedicada">
                                        {{ formData.ds_hostapi ? 'Recriar API dedicada' : 'Criar API dedicada' }}
                                    </div>
                                    <div class="custom-tooltip" v-else>Habilite a API dedicada primeiro</div>
                                </div>
                            </div>
                        </div>
                        <label class="ml-2 mt-2 forceLeft form-label self-center text-xs text-slate-400 font-bold"
                            >Necessário desconectar e conectar a instância pelo OiZap
                            <a class="text-red-400">Cuidado!</a>
                        </label>
                    </div>

                    <div class="col-span-6"></div>
                </div>

                <div class="grid grid-cols-6 mt-6">
                    <div class="col-span-6 ml-auto">
                        <button type="button" class="btn btn-success w-20 h-8" @click="salvar()">Salvar</button>
                    </div>
                </div>

                <!-- <div class="grid grid-cols-12 gap-1 mt-2">
                    <div class="col-span-6">
                        Finalizar atendimento não finalizados há mais de
                        <strong>{{ formData.hr_tempo_encerra_atendimento }} </strong> hora(s)
                        <VueSlider
                            v-model="formData.hr_tempo_encerra_atendimento"
                            :included="true"
                            :min="0"
                            :max="5"
                            :tooltip-formatter="'{value} hr(s)'"
                            :marks="true"
                        />
                    </div>

                    <div class="col-span-6 ml-2">
                        Tipo de Calculo da Pizza

                        <Multiselect
                            class="w-full z-50"
                            v-model="formData.tp_calculo"
                            placeholder="Calculo Pizza"
                            mode="single"
                            :close-on-select="false"
                            :searchable="true"
                            :createOption="false"
                            :options="listaCalculoPizza"
                            ref="selectCalculoPizza"
                            @emitEvent="selecionaCalculoPizza($event)"
                        />
                    </div>
                </div> -->

                <!-- Adicionar esta seção após o campo "Utiliza Proxy" -->
                <div class="mt-8">
                    <!-- Header da seção de módulos -->
                    <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-4 text-white">
                        <div class="flex flex-col md:flex-row md:items-center">
                            <!-- Grupo: Ícone + Texto + Estatísticas -->
                            <div class="flex items-center space-x-6 mb-4 md:mb-0">
                                <!-- Ícone e Texto -->
                                <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold">Módulos Contratados</h3>
                                        <p class="text-blue-100 text-sm">Gerencie os módulos do estabelecimento</p>
                                    </div>
                                </div>

                                <!-- Cards de estatística -->
                                <div class="flex space-x-4">
                                    <div
                                        class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center"
                                    >
                                        <div class="text-2xl font-bold">{{ modulosAtivos }}</div>
                                        <div class="text-xs text-blue-100">Ativos</div>
                                    </div>
                                    <div
                                        class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-3 text-center"
                                    >
                                        <div class="text-2xl font-bold">{{ listaEstabelecimentoModulos.length }}</div>
                                        <div class="text-xs text-blue-100">Total</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Botão à direita -->
                            <div class="ml-auto">
                                <!-- Aqui você pode adicionar um botão se necessário -->
                                <!-- <button class="btn btn-white btn-sm">Adicionar Módulo</button> -->
                            </div>
                        </div>
                    </div>

                    <!-- Container da tabela -->
                    <div class="bg-white rounded-b-xl shadow-lg overflow-hidden">
                        <!-- Tabela corrigida -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6"
                                        >
                                            Módulo
                                        </th>
                                        <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/6"
                                        >
                                            Funcionalidade
                                        </th>
                                        <!-- <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"
                                        >
                                            Valor
                                        </th> -->
                                        <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"
                                        >
                                            Situação
                                        </th>
                                        <!-- <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"
                                        >
                                            Vencimento
                                        </th> -->
                                        <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"
                                        >
                                            Contratado
                                        </th>
                                        <!-- <th
                                            scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"
                                        >
                                            Ações
                                        </th> -->
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr
                                        v-for="modulo in filteredModulos"
                                        :key="modulo.nr_controle"
                                        class="hover:bg-gray-50 transition-colors duration-200"
                                        :class="{ 'opacity-60': !modulo.in_contratado }"
                                    >
                                        <!-- Coluna 1: Módulo -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10">
                                                    <div
                                                        :class="[
                                                            'p-2 rounded-lg flex items-center justify-center text-white font-bold text-sm',
                                                            getModuleBadgeClass(modulo.cd_modulo),
                                                        ]"
                                                    >
                                                        {{ modulo.nm_modulo }}
                                                    </div>
                                                </div>
                                                <!-- <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ modulo.nm_modulo }}
                                                    </div>
                                                </div> -->
                                            </div>
                                        </td>

                                        <!-- Coluna 2: Funcionalidade -->
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 max-w-xs break-words">
                                                {{ modulo.ds_funcionalidade }}
                                            </div>
                                        </td>

                                        <!-- Coluna 3: Valor 
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                                            >
                                                R$ {{ formatCurrency(modulo.vl_modulo) }}
                                            </span>
                                        </td>-->

                                        <!-- Coluna 4: Situação -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <select
                                                v-model="modulo.tp_situacao"
                                                @change="atualizarSituacao(modulo)"
                                                class="text-sm rounded-full px-3 py-1 font-medium border-0 focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer"
                                                :class="{
                                                    'bg-green-100 text-green-800 focus:ring-green-500':
                                                        modulo.tp_situacao === 'Ativo',
                                                    'bg-red-100 text-red-800 focus:ring-red-500':
                                                        modulo.tp_situacao === 'Inativo',
                                                    'bg-yellow-100 text-yellow-800 focus:ring-yellow-500':
                                                        modulo.tp_situacao === 'Suspenso',
                                                }"
                                            >
                                                <option value="Ativo">Ativo</option>
                                                <option value="Inativo">Inativo</option>
                                                <option value="Suspenso">Suspenso</option>
                                            </select>
                                        </td>

                                        <!-- Coluna 5: Vencimento 
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <div class="text-gray-900 font-medium">
                                                {{ formatDate(modulo.dt_vencimento) }}
                                            </div>
                                            <div :class="getDaysLeftClass(modulo.dt_vencimento)" class="text-xs">
                                                {{ getDaysLeft(modulo.dt_vencimento) }}
                                            </div>
                                        </td>-->

                                        <!-- Coluna 6: Contratado -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    v-model="modulo.in_contratado"
                                                    @change="atualizarContratado(modulo)"
                                                    class="sr-only peer"
                                                />
                                                <div
                                                    class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                                                ></div>
                                            </label>
                                        </td>

                                        <!-- Coluna 7: Ações 
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex space-x-2 justify-end">
                                                <button
                                                    @click="editarModulo(modulo)"
                                                    class="inline-flex items-center p-2 border border-transparent rounded-lg text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                                    title="Editar módulo"
                                                >
                                                    <svg
                                                        class="w-4 h-4"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                        ></path>
                                                    </svg>
                                                </button>
                                                <button
                                                    @click="verDetalhes(modulo)"
                                                    class="inline-flex items-center p-2 border border-transparent rounded-lg text-green-600 hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                                                    title="Ver detalhes"
                                                >
                                                    <svg
                                                        class="w-4 h-4"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                                        ></path>
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                        ></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>-->
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Estado vazio -->
                            <div v-if="filteredModulos.length === 0" class="text-center py-12">
                                <svg
                                    class="mx-auto h-12 w-12 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    ></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum módulo encontrado</h3>
                                <p class="mt-1 text-sm text-gray-500">Tente ajustar os filtros de pesquisa.</p>
                            </div>
                        </div>

                        <!-- Loading state -->
                        <div
                            v-if="loadingModulos"
                            class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
                        >
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>
            </div>
        </ModalBody>
    </Modal>
    <!-- END: Logar -->
</template>

<script setup>
    import { ref, reactive, computed, onMounted } from 'vue';
    import { useRouter, useRoute } from 'vue-router';
    //import VersaoServices from '@/services/administracao/VersaoServices';
    import EstabelecimentoServices from '@/services/administracao/EstabelecimentoServices';
    import UsuariosEstabelecimentosServices from '@/services/administracao/UsuariosEstabelecimentosServices';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';
    import ProxyServices from '@/services/administracao/ProxyServices';
    import hosts from '@/utils/hosts';
    import { useToast } from '@/global-components/toastify/useToast';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import { useSimpleMenuStore } from '@/stores/simple-menu';
    import { useEstabelecimento } from '@/stores/estabelecimento';

    const toast = useToast();
    const totalEstabelecimento = ref(0);
    // import converters from '../../../utils/converters';
    // import logger from '../../../utils/logger';
    import { debounce } from 'lodash';
    import { Building2Icon } from 'lucide-vue-next';
    import { createIcons, icons } from 'lucide';
    //import { usePagina } from '@/stores/pagina';
    //import logger from '../../../utils/logger';

    //let pagina = usePagina();
    const router = useRouter();
    //const route = useRoute();
    //  const value = ref();

    //const isOpenFiltrosEst = ref(false);
    const tabulator = ref();
    const loading = ref();
    const codUsuario = ref();
    let listaEstabelecimentos = ref([]);
    let listaProxys = ref([]);
    let IDCopiado = ref(false);
    let HASHCopiado = ref(false);
    let modalConfig = ref(false);
    let isPesquisandoEndereco = ref(false);
    let botaoLimparFiltro = ref(false);
    let filtroAbertoRef = ref(null);
    const listaEstabelecimentoModulos = ref([]);

    const filtros = ref({
        nm_estabelecimento: '',
        telefone: '',
        nameinstance: '',
        nr_hash: '',
        in_habilitaproxy: '',
        status_connection: '',
        limit: 10,
        page: 1,
    });
    // const listaInProxy = ref([
    //     { value: '', label: 'Todos' },
    //     { value: false, label: 'Não utiliza Proxy' },
    //     { value: true, label: 'Utiliza Proxy' },
    // ]);
    const listaOffset = ref([20, 40, 50, 100]);
    const estabelecimentoSelecionado = ref();
    const hashCopiado = ref({});
    const telefoneCopiado = ref({});

    // const listaCalculoPizza = ref([
    //     { value: 1, label: 'Maior valor de sabor' },
    //     { value: 2, label: 'Por rateio de cada valor do sabor' },
    // ]);
    const formData = reactive({
        cd_estabelecimento: undefined,
        nr_hash: undefined,
        nm_estabelecimento: undefined,
        nr_cnpj: undefined,
        ds_endereco: undefined,
        nr_endereco: undefined,
        ds_bairro: undefined,
        ds_cidade: undefined,
        ds_pais: undefined,
        ds_uf: undefined,
        nr_latitude: undefined,
        nr_longitude: undefined,
        ds_hostentregadochef: undefined,
        vl_entrega: 0,
        in_habilitaproxy: false,
        host_proxy: undefined,
        porta_proxy: undefined,
        username_proxy: undefined,
        password_proxy: undefined,
        hr_tempo_encerra_atendimento: 0,
        tp_calculo: undefined,
        in_apidedicada: false,
        ds_hostapi: undefined,
    });

    const estabelecimentoStore = useEstabelecimento();
    const colunas = ref([
        { label: 'Foto', chave: 'url_foto', filtro: false, ordenacao: false, ativo: false },
        { label: 'Nome', chave: 'nm_estabelecimento', filtro: true, ordenacao: false, ativo: false },
        { label: 'Telefone', chave: 'telefone', filtro: true, ordenacao: false, ativo: false },
        { label: 'Instâncias', chave: 'on/off', filtro: true, ordenacao: false, ativo: false },
        { label: 'Proxy', chave: 'in_habilitaproxy', filtro: true, ordenacao: false, ativo: false },
        { label: 'Status', chave: 'status_connection', filtro: false, ordenacao: true, ativo: false },
        { label: 'Ações', chave: 'acoes', filtro: false, ordenacao: false, ativo: false },
    ]);
    const filtrosRefs = ref([ref(), ref(), ref(), ref(), ref(), ref(), ref(), ref()]);

    function initTabulator() {
        tabulator.value = new Tabulator('#tabulator', {
            data: listaEstabelecimentos.value,
            //rowHeight: 25,
            layout: 'fitColumns',
            responsiveLayout: 'collapse',
            placeholder: 'Nenhum registro encontrado',
            pagination: 'local',
            paginationSize: 100,
            rowHeight: 40,
            paginationSizeSelector: listaOffset.value,
            columns: [
                {
                    title: 'Foto',
                    field: 'url_foto',
                    width: 80,
                    hozAlign: 'center',
                    headerSort: false,
                    vertAlign: 'middle',
                    formatter: function (cell) {
                        const estabelecimento = cell.getData();

                        const a = dom(`
                                <div class="rounded-full w-fit">
                                    ${
                                        estabelecimento.url_foto
                                            ? `<img alt="oizap" class="rounded-full w-7 h-7" src="${estabelecimento.url_foto}" />`
                                            : `<i data-lucide="circle-user" class="rounded-full w-7 h-7 bg-slate-100 dark:text-slate-500 dark:bg-darkmode-800 text-slate-400"></i>`
                                    }
                                </div>
                            `);
                        return a[0];
                    },
                },
                {
                    title: 'Nome',
                    field: 'nm_estabelecimento',
                    headerHozAlign: 'left',
                    vertAlign: 'middle',
                    width: 350,
                    cellClick: function (e, cell) {
                        abrirFiltro(1);
                    },
                },
                {
                    title: 'Telefone',
                    field: 'telefone',
                    headerHozAlign: 'left',
                    vertAlign: 'middle',
                    width: 180,
                    formatter: function (cell) {
                        const estabelecimento = cell.getData();
                        const telefone = cell.getValue();

                        if (!telefone) return '';

                        const a = dom(`
                            <div class="flex items-center justify-between group">
                                <span class="text-sm font-medium">${telefone}</span>
                                <button
                                    class="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100 transition-opacity duration-200 copy-telefone-btn"
                                    title="Copiar telefone"
                                >
                                    <i data-lucide="copy" class="w-3 h-3 text-gray-500 hover:text-blue-600"></i>
                                </button>
                            </div>
                        `);

                        // Adicionar evento de clique para o botão de copiar
                        dom(a)
                            .find('.copy-telefone-btn')
                            .on('click', function (e) {
                                e.preventDefault();
                                e.stopPropagation();
                                copyTelefoneTabulator(telefone, estabelecimento.cd_estabelecimento);

                                // Feedback visual
                                const icon = dom(this).find('i');
                                icon.attr('data-lucide', 'check');
                                icon.addClass('text-green-600');

                                setTimeout(() => {
                                    icon.attr('data-lucide', 'copy');
                                    icon.removeClass('text-green-600');
                                    createIcons({
                                        icons,
                                        'stroke-width': 1.5,
                                        nameAttr: 'data-lucide',
                                    });
                                }, 2000);
                            });

                        return a[0];
                    },
                    cellClick: function (e, cell) {
                        // Verificar se o clique foi no botão de copiar
                        if (!e.target.closest('.copy-telefone-btn')) {
                            abrirFiltro(2);
                        }
                    },
                },
                {
                    title: 'Hash',
                    field: 'nr_hash',
                    vertAlign: 'middle',
                    width: 280,
                    formatter: function (cell) {
                        const estabelecimento = cell.getData();
                        const hash = cell.getValue();

                        if (!hash) return '';

                        // Truncar hash se for muito longo
                        const displayHash = hash;

                        const a = dom(`
                            <div class="flex items-center justify-between group">
                                <span class="text-sm font-mono text-gray-700 dark:text-gray-300" title="${hash}">${displayHash}</span>
                                <button
                                    class="ml-2 p-1 rounded  copy-hash-btn  transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700  group-hover:opacity-100"
                                    title="Copiar"
                                >
                                    <i data-lucide="copy" class="w-3 h-3 text-gray-500 hover:text-blue-600"></i>
                                </button>
                            </div>
                        `);

                        // Adicionar evento de clique para o botão de copiar
                        dom(a)
                            .find('.copy-hash-btn')
                            .on('click', function (e) {
                                e.preventDefault();
                                e.stopPropagation();
                                copyHashTabulator(hash, estabelecimento.cd_estabelecimento);

                                // Feedback visual
                                const icon = dom(this).find('i');
                                icon.attr('data-lucide', 'check');
                                icon.addClass('text-green-600');

                                setTimeout(() => {
                                    icon.attr('data-lucide', 'copy');
                                    icon.removeClass('text-green-600');
                                    createIcons({
                                        icons,
                                        'stroke-width': 1.5,
                                        nameAttr: 'data-lucide',
                                    });
                                }, 2000);
                            });

                        return a[0];
                    },
                },
                {
                    title: 'Instâncias',
                    field: 'instances_on/off',
                    width: 150,
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    formatter: function (cell) {
                        const data = cell.getData();
                        return `
                                <div class="flex items-center justify-center gap-2">
                                    <div class="flex items-center gap-1">
                                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                        <span class="text-sm font-semibold text-green-600">${data.instances_on}</span>
                                    </div>
                                    <div class="text-slate-400">/</div>
                                    <div class="flex items-center gap-1">
                                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                        <span class="text-sm font-semibold text-red-600">${data.instances_off}</span>
                                    </div>
                                </div>
                            `;
                    },
                },
                {
                    title: 'Proxy',
                    field: 'in_habilitaproxy',
                    width: 110,
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    formatter: function (cell) {
                        const isProxy = cell.getValue();
                        return `
                            <div class="flex items-center justify-center">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                                    isProxy
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                }">
                                    <div class="w-1.5 h-1.5 mr-1.5 rounded-full ${
                                        isProxy ? 'bg-green-400' : 'bg-gray-400'
                                    }"></div>
                                    ${isProxy ? 'Sim' : 'Não'}
                                </span>
                            </div>
                        `;
                    },
                },
                {
                    title: 'Api dedicada',
                    field: 'in_apidedicada',
                    width: 150,
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    formatter: function (cell) {
                        const isApidica = cell.getValue();
                        return `
                            <div class="flex items-center justify-center">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                                    isApidica
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                }">
                                    <div class="w-1.5 h-1.5 mr-1.5 rounded-full ${
                                        isApidica ? 'bg-green-400' : 'bg-gray-400'
                                    }"></div>
                                    ${isApidica ? 'Sim' : 'Não'}
                                </span>
                            </div>
                        `;
                    },
                },
                {
                    title: 'Ações',
                    field: 'acoes',
                    width: 140,
                    sorter: false,
                    headerSort: false,
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    formatter: function (cell) {
                        const estabelecimento = cell.getData();
                        const a = dom(`
                                <div class="flex items-center justify-center h-full">
                                    <button
                                        class="btn btn-success-soft btn-sm flex items-center px-4 py-2 h-6 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                                        href="javascript:;"
                                    >
                                        <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Configurações
                                    </button>
                                </div>
                            `);

                        dom(a)
                            .find('button')
                            .on('click', function (e) {
                                e.preventDefault();
                                e.stopPropagation();
                                abreModalConfiguracoes(estabelecimento);
                            });

                        return a[0];
                    },
                },
            ],
        });

        tabulator.value.on('rowClick', function (e, row) {
            // Verificar se o clique foi em um botão de copiar
            if (!e.target.closest('.copy-hash-btn') && !e.target.closest('.copy-telefone-btn')) {
                carregaEstabelecimento(row.getData());
            }
        });

        tabulator.value.on('renderComplete', function () {
            createIcons({
                icons,
                'stroke-width': 1.5,
                nameAttr: 'data-lucide',
            });
        });
    }

    // Função para copiar hash
    async function copyHashTabulator(hash, estabelecimentoId) {
        try {
            await navigator.clipboard.writeText(hash);
            hashCopiado.value[estabelecimentoId] = true;
            toast.showSuccessNotification('Hash copiado para área de transferência!');

            setTimeout(() => {
                hashCopiado.value[estabelecimentoId] = false;
            }, 2000);
        } catch (err) {
            console.error('Falha ao copiar hash: ', err);
            toast.showErrorNotification('Erro ao copiar hash');
        }
    }

    // Função para copiar telefone
    async function copyTelefoneTabulator(telefone, estabelecimentoId) {
        try {
            await navigator.clipboard.writeText(telefone);
            telefoneCopiado.value[estabelecimentoId] = true;
            toast.showSuccessNotification('Telefone copiado para área de transferência!');

            setTimeout(() => {
                telefoneCopiado.value[estabelecimentoId] = false;
            }, 2000);
        } catch (err) {
            console.error('Falha ao copiar telefone: ', err);
            toast.showErrorNotification('Erro ao copiar telefone');
        }
    }

    function customFilter(data, filterParams) {
        if (!!filterParams?.conexao) {
            if (filterParams.conexao === 'conectado') {
                return data.instances_on > 0;
            } else if (filterParams.conexao === 'desconectado') {
                return data.instances_off > 0;
            } else {
                return true; // Se não for 'conectado' ou 'desconectado', retorna todos
            }
        }

        if (!filterParams.value) {
            return true;
        }

        if (!isNaN(filterParams.value)) {
            return (
                data.telefone?.toString()?.includes(filterParams.value) ||
                data.nr_hash?.toString()?.includes(filterParams.value)
            );
        } else if (isNaN(filterParams.value)) {
            return (
                data.nm_estabelecimento?.toLowerCase().includes(filterParams.value.toLowerCase()) ||
                data.nr_hash?.toString()?.includes(filterParams.value)
            );
        } else {
            return false;
        }
    }

    function btnFitrarConexao(conexao) {
        switch (conexao) {
            case 'conectados':
                tabulator.value.setFilter(customFilter, { conexao: 'conectado' });
                break;
            case 'desconectados':
                tabulator.value.setFilter(customFilter, { conexao: 'desconectado' });
                break;
            default:
                tabulator.value.clearFilter();
                break;
        }
    }

    async function listarEstabelecimentos() {
        loading.value.show();

        listaEstabelecimentos.value = [];
        const result = await EstabelecimentoServices.listaInstanciasPorEstabelecimento();

        if (result.statuscode == 200) {
            listaEstabelecimentos.value = agruparEstabelecimentosComStatus(result.data);

            if (tabulator.value) {
                tabulator.value.setData(listaEstabelecimentos.value);
            } else {
                initTabulator();
            }
            totalEstabelecimento.value = listaEstabelecimentos.value.length;
            listaEstabelecimentos.value = result.data;
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
        loading.value.hide();
    }

    const estatisticasConexao = computed(() => {
        const stats = listaEstabelecimentos.value.reduce(
            (acc, estabelecimento) => {
                if (estabelecimento.status_connection === 'Conectado') {
                    acc.conectado++;
                } else {
                    acc.desconectado++;
                }
                acc.totalinstancia++;
                return acc;
            },
            { conectado: 0, desconectado: 0, total: 0, totalinstancia: 0 }
        );

        return stats;
    });

    async function listarEstabelecimentoModulos() {
        listaEstabelecimentoModulos.value = [];

        const result = await EstabelecimentoModulosServices.listar({ cd_estabelecimento: formData.cd_estabelecimento });
        //  console.log('🚀 ~ estabelecimentos.vue:519 ~ listarEstabelecimentoModulos ~ result:', result);
        if (result.statuscode == 200) {
            listaEstabelecimentoModulos.value = result.data;
        }
    }

    const pesquisarFiltro = debounce(async (chave, valor) => {
        if (chave != 'page' && chave != 'limit') {
            filtros.value.page = 1;
        }
        if (valor < 0) filtros.value[(chave = '')];
        else filtros.value[chave] = valor;
        await listarEstabelecimentos(filtros.value);

        botaoLimparFiltro.value = !Object.values(filtros.value).every(
            (value) => value === undefined && value === '' && value === 1 && value === 10
        );

        const colunaIndex = colunas.value.findIndex((coluna) => coluna.chave === chave);
        if (colunaIndex !== -1) {
            colunas.value[colunaIndex].ativo = true;
        }
    }, 500);

    function agruparEstabelecimentosComStatus(data) {
        const agrupado = {};

        data.forEach((item) => {
            const id = item.cd_estabelecimento;
            if (!agrupado[id]) {
                agrupado[id] = {
                    ...item,
                    instances_on: 0,
                    instances_off: 0,
                };
            }

            if (item.status_connection === 'Conectado') {
                agrupado[id].instances_on += 1;
            } else {
                agrupado[id].instances_off += 1;
            }
        });

        return Object.values(agrupado);
    }

    function abrirFiltro(index) {
        filtroAbertoRef.value == index ? (filtroAbertoRef.value = null) : (filtroAbertoRef.value = index);
        if (filtroAbertoRef.value == index) {
            setTimeout(() => {
                //console.log(filtrosRefs.value[index].value);

                if (filtrosRefs.value[index].value) filtrosRefs.value[index].value[0].focus();
            }, 400);
        }
    }

    function limparFiltros() {
        filtros.value = {
            nm_estabelecimento: '',
            telefone: '',
            nameinstance: '',
            nr_hash: '',
            in_habilitaproxy: '',
            status_connection: '',
            limit: 10,
            page: 1,
        };
        listarEstabelecimentos();
        botaoLimparFiltro.value = false;
        filtroAbertoRef.value = null;
        colunas.value.forEach((coluna) => (coluna.ativo = false));
    }

    function ordenarPorStatus() {
        listaEstabelecimentos.value.sort((a, b) => {
            if (a.status_connection == 'Conectado') return filtros.value.status_connection ? 1 : -1;
            if (a.status_connection == 'Desconectado') return filtros.value.status_connection ? -1 : 1;
            return 0;
        });
        filtros.value.status_connection = !filtros.value.status_connection;
    }

    async function carregaEstabelecimento(estabelecimento) {
        //console.log('🚀 ~ estabelecimentos.vue:1278 ~ carregaEstabelecimento ~ estabelecimento:', estabelecimento);
        loading.value.show();
        const filtro = {
            cd_estabelecimento: estabelecimento.cd_estabelecimento,
        };

        //console.log('🚀 ~ estabelecimentos.vue:1284 ~ carregaEstabelecimento ~ filtro:', filtro);
        const result = await UsuariosEstabelecimentosServices.listar(filtro);
        //console.log('🚀 ~ estabelecimentos.vue:1284 ~ carregaEstabelecimento ~ result:', result);

        if (result.statuscode == 200) {
            localStorage.setItem('estabelecimentos', JSON.stringify(result.data));

            // Salvar no store Pinia
            estabelecimentoStore.setEstabelecimento(estabelecimento);

            router.push({ path: `${hosts.app}/chat` });
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }

        loading.value.hide();
    }

    async function copyID(instancia) {
        const inputField = instancia;
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField.value);
                IDCopiado.value = true;
                toast.showSuccessNotification('Instância copiada!');
                setTimeout(() => {
                    IDCopiado.value = false; // Remove a classe após um tempo
                }, 2000); // Tempo do efeito em milissegundos
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function copyHash(hash) {
        const inputField = hash;
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField.value);
                HASHCopiado.value = true;
                toast.showSuccessNotification('Hash do Estabelecimento copiado!');
                setTimeout(() => {
                    HASHCopiado.value = false; // Remove a classe após um tempo
                }, 2000); // Tempo do efeito em milissegundos
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function abreModalConfiguracoes(estabelecimento) {
        estabelecimentoSelecionado.value = estabelecimento;
        loading.value.show();
        // await listarProxy();
        await consultaEstabelecimento(estabelecimento.cd_estabelecimento);
        modalConfig.value = true;
        loading.value.hide();
    }

    async function closeModalConfig() {
        modalConfig.value = false;
    }

    const focusSelectEndereco = async () => {
        let dsEndereco = formData.ds_endereco.trim();
        // console.log('dsEndereco:', dsEndereco);
        if (dsEndereco.length > 1) return;

        isPesquisandoEndereco.value = true;
        nextTick(async () => {
            if (selectEndereco.value) {
                const input = selectEndereco.value.$refs.input || selectEndereco.value.$el.querySelector('input');
                if (input) {
                    input.focus();
                }
            }
        });
    };

    const onChangeHabilitaProxy = (event) => {
        formData.in_habilitaproxy = event.target.checked;
        const findEstabelecimento = listaEstabelecimentos.value.find(
            (estab) => estab.cd_estabelecimento == estabelecimentoSelecionado.value.cd_estabelecimento
        );
        findEstabelecimento.in_habilitaproxy = event.target.checked;
    };

    const onChangeApidica = (event) => {
        formData.in_apidedicada = event.target.checked;
        const findEstabelecimento = listaEstabelecimentos.value.find(
            (estab) => estab.cd_estabelecimento == estabelecimentoSelecionado.value.cd_estabelecimento
        );
        findEstabelecimento.in_apidedicada = event.target.checked;
    };

    async function limpaFormData() {
        Object.keys(formData).forEach((key) => (formData[key] = undefined));
    }

    async function consultaEstabelecimento(cdEstabelecimento) {
        loading.value.show();
        let filtros = {
            cd_estabelecimento: cdEstabelecimento,
        };

        await limpaFormData();
        //disabled.value = true;

        let result = await EstabelecimentoServices.listar(filtros);
        // console.log('🚀 ~ estabelecimentos.vue:1461 ~ consultaEstabelecimento ~ result:', result);

        if (result.statuscode == 200) {
            // disabled.value = false;

            const data = result.data[0];

            formData.cd_estabelecimento = data.cd_estabelecimento;
            formData.nm_estabelecimento = data.nm_estabelecimento;
            formData.nr_cnpj = data.nr_cnpj;
            formData.ds_endereco = data.ds_endereco;
            formData.nr_endereco = data.nr_endereco;

            formData.ds_bairro = data.bairro;
            formData.nr_latitude = data.latitude;
            formData.nr_longitude = data.longitude;
            formData.ds_cidade = data.cidade;
            formData.ds_pais = data.pais;
            formData.ds_uf = data.uf;

            formData.nr_latitude = data.nr_latitude;
            formData.nr_longitude = data.nr_longitude;
            formData.ds_hostentregadochef = data.ds_hostentregadochef;
            formData.vl_entrega = data.vl_entrega;
            formData.nr_hash = data.nr_hash;
            formData.in_habilita_encerra_atendimento = data.in_habilita_encerra_atendimento;
            formData.in_habilitaproxy = data.in_habilitaproxy;
            formData.host_proxy = data.host_proxy;
            formData.porta_proxy = data.porta_proxy;
            formData.username_proxy = data.username_proxy;
            formData.password_proxy = data.password_proxy;
            formData.hr_tempo_encerra_atendimento = data.hr_tempo_encerra_atendimento;
            formData.tp_calculo = data.tp_calculo;

            formData.in_apidedicada = data.in_apidedicada;
            formData.ds_hostapi = data.ds_hostapi;

            await listarEstabelecimentoModulos();

            // if (formData.host_proxy != undefined) {
            //     listaProxys.value.push({
            //         label: formData.host_proxy,
            //         value: formData.host_proxy,
            //         username: formData.username_proxy,
            //         password: formData.password_proxy,
            //         port: formData.porta_proxy,
            //     });
            // }

            //toast.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
        loading.value.hide();
    }

    const handleCogClick = () => {
        // Só executa se a API dedicada estiver habilitada
        if (formData.in_apidedicada) {
            if (formData.ds_hostapi) {
                // Se já tem API, pergunta se quer recriar
                if (confirm('Deseja recriar a API dedicada? Isso irá gerar uma nova URL.')) {
                    criarApiDedicada();
                }
            } else {
                // Se não tem API, cria diretamente
                criarApiDedicada();
            }
        } else {
            toast.showWarningNotification('Habilite a API dedicada primeiro!');
        }
    };

    const criarApiDedicada = async () => {
        const result = await EstabelecimentoServices.criarApiDedicada({
            nm_estabelecimento: formData.nm_estabelecimento,
            cd_estabelecimento: formData.cd_estabelecimento,
        });
        console.log('🚀 ~ estabelecimentos.vue:1535 ~ criarApiDedicada ~ result:', result);

        if (result.statuscode == 200) {
            formData.ds_hostapi = result.data[0].ds_hostapi;
            toast.showSuccessNotification('Api dedicada criada com sucesso!');
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    };

    async function salvar() {
        //let modulos = listaModulos.value.filter((modulo) => modulo.in_contratado == true);
        // console.log('salvar', formData.cd_estabelecimento);
        let reqObj = {
            cd_estabelecimento: formData.cd_estabelecimento == undefined ? undefined : formData.cd_estabelecimento,
            cd_usuario: parseInt(codUsuario.value),
            nm_estabelecimento: formData.nm_estabelecimento,
            nr_cnpj: formData.nr_cnpj,
            ds_endereco: formData.ds_endereco,
            nr_endereco: formData.nr_endereco,
            ds_bairro: formData.ds_bairro,
            ds_cidade: formData.ds_cidade,
            ds_pais: formData.ds_pais,
            ds_uf: formData.ds_uf,
            nr_latitude: formData.nr_latitude,
            nr_longitude: formData.nr_longitude,
            ds_hostentregadochef: formData.ds_hostentregadochef,
            hr_tempo_encerra_atendimento: formData.hr_tempo_encerra_atendimento,
            in_habilitaproxy: formData.in_habilitaproxy,
            host_proxy: formData.host_proxy,
            porta_proxy: formData.porta_proxy,
            username_proxy: formData.username_proxy,
            password_proxy: formData.password_proxy,
            vl_entrega: formData.vl_entrega.replace(',', '.'),
            tp_calculo: formData.tp_calculo,
            in_apidedicada: formData.in_apidedicada,
            ds_hostapi: formData.ds_hostapi,
            // modulos: modulos,
        };

        let result;
        if (formData.cd_estabelecimento == undefined) {
            result = await EstabelecimentoServices.incluir(reqObj);
        } else {
            result = await EstabelecimentoServices.alterar(reqObj);
        }
        // console.log('EstabelecimentoServices', result);

        if (result.statuscode == 200) {
            const data = result.data[0];
            formData.cd_estabelecimento = data.cd_estabelecimento;
            formData.nr_hash = data.nr_hash;
            toast.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    async function listarProxy() {
        listaProxys.value = [];
        const result = await ProxyServices.listar();
        //console.log('result at line 648 in estabelecimento/estabelecimentos.vue:', result);

        if (result.statuscode == 200) {
            listaProxys.value = result.data.map((proxy) => ({
                label: proxy.proxy_address,
                value: proxy.proxy_address,
                username: proxy.username,
                password: proxy.password,
                port: proxy.port,
                valid: proxy.valid,
            }));

            //toast.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    // const selecionaProxy = async (event) => {
    //     if (event.event == 'Select') {
    //         formData.password_proxy = event.select.password;
    //         formData.porta_proxy = event.select.port;
    //         formData.username_proxy = event.select.username;
    //     } else if (event.event == 'Clear') {
    //         formData.password_proxy = undefined;
    //         formData.porta_proxy = undefined;
    //         formData.username_proxy = undefined;
    //     }
    // };

    // const selecionaCalculoPizza = async (event) => {
    //     if (event.event == 'Clear') {
    //         formData.tp_calculo = undefined;
    //     }
    // };

    // Adicionar refs para módulos
    const modulos = ref([]);
    const searchModulos = ref('');
    const loadingModulos = ref(false);

    // Computed para módulos filtrados
    const filteredModulos = computed(() => {
        if (!searchModulos.value) return listaEstabelecimentoModulos.value;

        return listaEstabelecimentoModulos.value.filter(
            (modulo) =>
                modulo.nm_modulo.toLowerCase().includes(searchModulos.value.toLowerCase()) ||
                modulo.ds_funcionalidade.toLowerCase().includes(searchModulos.value.toLowerCase())
        );
    });

    const modulosAtivos = computed(
        () => listaEstabelecimentoModulos.value.filter((m) => m.tp_situacao === 'Ativo' && m.in_contratado).length
    );

    // Funções para atualizar módulos
    const atualizarSituacao = async (modulo) => {
        await atualizaModulosContratados(modulo);
    };

    const atualizarContratado = async (modulo) => {
        await atualizaModulosContratados(modulo);
    };

    const atualizaModulosContratados = async (modulo) => {
        let valorModulo;
        if (typeof modulo.vl_modulo === 'string') {
            valorModulo = modulo.vl_modulo.replace(/\./g, '').replace(',', '.');
        } else {
            valorModulo = modulo.vl_modulo; // Já é number
        }
        const body = {
            nr_controle: modulo.nr_controle,
            cd_estabelecimento: formData.cd_estabelecimento,
            cd_modulo: modulo.cd_modulo,
            //vl_modulo: valorModulo,
            in_contratado: modulo.in_contratado ? true : false,
            tp_situacao: modulo.tp_situacao ? modulo.tp_situacao : 'Ativo',
        };

        let result;
        let msg;
        if (modulo.nr_controle == undefined) {
            result = await EstabelecimentoModulosServices.incluir(body);
            msg = 'Módulo contratado com sucesso!';
        } else {
            result = await EstabelecimentoModulosServices.alterar(body);
            msg = 'Módulo atualizado com sucesso!';
        }

        if (result.statuscode == 200) {
            const find = listaEstabelecimentoModulos.value.find((m) => m.nr_controle === modulo.nr_controle);
            if (find) {
                Object.assign(find, body);
            } else {
                listaEstabelecimentoModulos.value.push(body);
            }
            toast.showSuccessNotification(msg);
        } else if (result.statuscode == 404) {
        } else {
            toast.showErrorNotification(result.message);
        }
    };

    // // Utility functions
    // const formatCurrency = (value) => {
    //     return parseFloat(value).toLocaleString('pt-BR', {
    //         minimumFractionDigits: 2,
    //         maximumFractionDigits: 2,
    //     });
    // };

    // const formatDate = (dateString) => {
    //     return new Date(dateString).toLocaleDateString('pt-BR');
    // };

    // const getDaysLeft = (dateString) => {
    //     const today = new Date();
    //     const expiry = new Date(dateString);
    //     const diffTime = expiry - today;
    //     const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    //     if (diffDays < 0) return 'Vencido';
    //     if (diffDays === 0) return 'Vence hoje';
    //     if (diffDays === 1) return 'Vence amanhã';
    //     return `${diffDays} dias`;
    // };

    // const getDaysLeftClass = (dateString) => {
    //     const today = new Date();
    //     const expiry = new Date(dateString);
    //     const diffTime = expiry - today;
    //     const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    //     if (diffDays < 0) return 'text-red-600 font-bold';
    //     if (diffDays <= 7) return 'text-red-500 font-semibold';
    //     if (diffDays <= 30) return 'text-yellow-500';
    //     return 'text-green-600';
    // };

    const getModuleBadgeClass = (cdModulo) => {
        const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'];
        return colors[cdModulo % colors.length];
    };

    onMounted(async () => {
        codUsuario.value = localStorage.getItem('codusuario');
        await listarEstabelecimentos();
    });
</script>

<style scoped>
    .custom-table {
        border-collapse: separate !important;
        border-spacing: 0 0 !important;
    }
    .custom-table th,
    .custom-table td {
        padding: 12px;
        text-align: left;
    }
    .custom-table {
        border-bottom: none !important;
    }

    .custom-input {
        background-color: transparent;
        border: none;
        border-bottom: 2px solid #16a34a;
        display: flex;
        overflow: hidden;
        width: 0;
        transform: scaleY(0);
        animation: show-input 0.3s forwards;
    }
    @keyframes show-input {
        0% {
            transform: scaleY(0);
            width: 0;
        }
        100% {
            transform: scaleY(1);
            width: 100%;
        }
    }
    ::v-deep(.multiselect-clear) {
        display: none !important;
        width: 0 !important;
    }
    ::v-deep(.multiselect-single-label) {
        padding-right: 28px !important;
    }
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-input) {
        cursor: pointer !important;
    }

    .copied {
        animation: copyEffect 0.5s ease-in-out;
    }

    @keyframes copyEffect {
        0% {
            transform: scale(1);
            color: inherit;
        }
        50% {
            transform: scale(1.5);
            color: green;
        }
        100% {
            transform: scale(1);
            color: inherit;
        }
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: all 0.3s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
        transform: translateY(10px);
    }

    .fade-move {
        transition: transform 0.3s ease;
    }
    /* Animação para o efeito de cópia */
    .copy-effect {
        animation: copyPulse 0.6s ease-in-out;
    }

    @keyframes copyPulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.2);
            color: #10b981;
        }
        100% {
            transform: scale(1);
        }
    }

    /* Estilo para botões de copiar */
    .copy-hash-btn,
    .copy-telefone-btn {
        transition: all 0.2s ease;
    }

    .copy-hash-btn:hover,
    .copy-telefone-btn:hover {
        transform: scale(1.1);
    }

    /* Estilo para o hash truncado */
    .hash-display {
        font-family: 'Courier New', monospace;
        letter-spacing: 0.5px;
    }
    .tooltip-container {
        position: relative;
    }

    .custom-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #374151;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
        z-index: 99999;
        margin-bottom: 5px;
    }

    .custom-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: #374151;
    }

    .tooltip-container:hover .custom-tooltip {
        opacity: 1;
    }
</style>
