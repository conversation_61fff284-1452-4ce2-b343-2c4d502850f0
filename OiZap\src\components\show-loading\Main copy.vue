<template> 
<Modal  :show= showR @hidden="showR=false"  backdrop="static"  background="bg-loading" >
      <LoadingIcon icon="ball-triangle" color="#FFFFFF"/>   
</Modal>
</template>
<script setup>
import { reactive, toRefs, ref } from "vue";
var showR = ref(false);

defineExpose({
    show,hide
})
async function show(){   
  showR.value = true; 
}
async function hide(time){   
  console.log("hide metodo em"+ time) ;
  setTimeout(function () {
    console.log("hide");
    showR.value = false; 
     }, time); 
}
</script>
<style>
.bg-loading{
   position: absolute !important;
   top:50% !important;
   right: 40% !important;
   height:100px !important;
    width:100px !important;
    background-color:transparent !important; 
   box-shadow:none !important;
}
</style>