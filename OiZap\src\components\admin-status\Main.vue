<template>
  <div class="admin-status">
    <div class="intro-y box p-5">
      <h3 class="text-lg font-medium mb-4">Status do Sistema de Autorização</h3>
      
      <!-- Status do Usuário -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">Informações do Usuário</h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium">ID:</span> {{ userInfo.cd_usuario || 'N/A' }}
          </div>
          <div>
            <span class="font-medium">Nome:</span> {{ userInfo.nome || 'N/A' }}
          </div>
          <div>
            <span class="font-medium">Perfil:</span> 
            <span :class="getPerfilClass(userInfo.perfil)">
              {{ userInfo.perfil || 'N/A' }}
            </span>
          </div>
          <div>
            <span class="font-medium">Status:</span>
            <span :class="isAuthenticated ? 'text-green-600' : 'text-red-600'">
              {{ isAuthenticated ? 'Autenticado' : 'Não Autenticado' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Telas com Permissão -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">Telas com Permissão ({{ telasPermitidas.length }})</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          <div 
            v-for="tela in telasPermitidas" 
            :key="tela.cd_tela"
            class="p-2 bg-gray-50 rounded text-sm"
          >
            <div class="font-medium">{{ tela.nm_tela }}</div>
            <div class="text-gray-600">{{ tela.ds_rota }}</div>
            <div class="flex gap-1 mt-1">
              <span v-if="tela.in_visualizar" class="px-1 bg-blue-100 text-blue-800 text-xs rounded">Ver</span>
              <span v-if="tela.in_inserir" class="px-1 bg-green-100 text-green-800 text-xs rounded">Inserir</span>
              <span v-if="tela.in_alterar" class="px-1 bg-yellow-100 text-yellow-800 text-xs rounded">Alterar</span>
              <span v-if="tela.in_excluir" class="px-1 bg-red-100 text-red-800 text-xs rounded">Excluir</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Telas Administrativas -->
      <div class="mb-4" v-if="telasAdministrativas.length > 0">
        <h4 class="font-medium mb-2">Telas Administrativas ({{ telasAdministrativas.length }})</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div 
            v-for="tela in telasAdministrativas" 
            :key="tela.cd_tela"
            class="p-2 bg-red-50 border border-red-200 rounded text-sm"
          >
            <div class="font-medium text-red-800">{{ tela.nm_tela }}</div>
            <div class="text-red-600">{{ tela.ds_rota }}</div>
            <div class="text-xs text-red-500 mt-1">Apenas Administradores</div>
          </div>
        </div>
      </div>

      <!-- Ações -->
      <div class="flex gap-2">
        <button 
          @click="carregarPermissoes" 
          class="btn btn-primary"
          :disabled="loading"
        >
          {{ loading ? 'Carregando...' : 'Atualizar Permissões' }}
        </button>
        <button 
          @click="testarAcesso" 
          class="btn btn-secondary"
          :disabled="loading"
        >
          Testar Acesso
        </button>
      </div>

      <!-- Logs -->
      <div v-if="logs.length > 0" class="mt-4">
        <h4 class="font-medium mb-2">Logs de Acesso</h4>
        <div class="bg-gray-100 p-3 rounded text-sm max-h-40 overflow-y-auto">
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span :class="log.type === 'success' ? 'text-green-600' : 'text-red-600'">
              {{ log.message }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import MenuServices from '@/services/administracao/MenuServices'
import api from '@/utils/api'

const loading = ref(false)
const userInfo = ref({})
const telasPermitidas = ref([])
const logs = ref([])

const isAuthenticated = computed(() => {
  return !!localStorage.getItem('token')
})

const telasAdministrativas = computed(() => {
  return telasPermitidas.value.filter(tela => 
    tela.ds_rota.includes('cadPerfil') || 
    tela.ds_rota.includes('cadTela') || 
    tela.ds_rota.includes('cadPerfisTelas') ||
    tela.ds_rota.includes('listaUsuarios') ||
    tela.ds_rota.includes('cadUsuario') ||
    tela.ds_rota.includes('listaEstabelecimento') ||
    tela.ds_rota.includes('cadEstabelecimento')
  )
})

function getPerfilClass(perfil) {
  if (perfil === 'Administrador') return 'text-red-600 font-medium'
  if (perfil === 'Gerente') return 'text-orange-600 font-medium'
  return 'text-blue-600'
}

function addLog(message, type = 'info') {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // Manter apenas os últimos 10 logs
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

async function carregarPermissoes() {
  loading.value = true
  addLog('Carregando permissões do usuário...', 'info')
  
  try {
    const cd_usuario = localStorage.getItem('codusuario')
    if (!cd_usuario) {
      throw new Error('Usuário não autenticado')
    }

    // Carregar informações do usuário
    const userResponse = await api.get(`/perfis/usuario/${cd_usuario}/perfil`)
    userInfo.value = {
      cd_usuario,
      nome: localStorage.getItem('usuario'),
      perfil: userResponse.data?.nm_perfil
    }

    // Carregar telas permitidas
    const telasResponse = await api.get(`/perfis/usuario/${cd_usuario}/telas`)
    telasPermitidas.value = telasResponse.data

    addLog(`Permissões carregadas: ${telasPermitidas.value.length} telas`, 'success')
  } catch (error) {
    console.error('Erro ao carregar permissões:', error)
    addLog(`Erro ao carregar permissões: ${error.message}`, 'error')
  } finally {
    loading.value = false
  }
}

async function testarAcesso() {
  addLog('Testando acesso às telas administrativas...', 'info')
  
  const telasAdmin = ['/cadPerfil', '/cadTela', '/cadPerfisTelas']
  
  for (const tela of telasAdmin) {
    try {
      const hasPermission = await MenuServices.verificarPermissao(tela)
      const status = hasPermission ? 'PERMITIDO' : 'NEGADO'
      addLog(`Acesso a ${tela}: ${status}`, hasPermission ? 'success' : 'error')
    } catch (error) {
      addLog(`Erro ao testar ${tela}: ${error.message}`, 'error')
    }
  }
}

onMounted(() => {
  carregarPermissoes()
})
</script> 