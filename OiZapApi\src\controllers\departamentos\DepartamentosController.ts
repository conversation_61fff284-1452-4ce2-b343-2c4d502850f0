import {
  BAD_REQUEST,
  INTERNAL_SERVER_ERROR,
  parametrosInvalidos,
  erroInterno,
  sucesso,
} from '../../interfaces/IRetorno';
import { Request, Response } from 'express';
import { DepartamentosModel } from '../../models/departamentos/DepartamentosModel';

export class DepartamentosController {
  static async incluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.ds_departamento) {
        errors.push('O campo "ds_departamento" é obrigatório.');
      }
      if (!req.body.in_ativo) {
        errors.push('O campo "in_ativo" deve ser um booleano.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new DepartamentosModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listar(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new DepartamentosModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_departamento) {
        errors.push('O campo "cd_departamento" é obrigatório.');
      }
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new DepartamentosModel().alterar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async excluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.cd_departamento) {
        errors.push('O campo "cd_departamento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new DepartamentosModel().excluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
