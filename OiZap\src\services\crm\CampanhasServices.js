import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';
//const host = `${hosts.apiOiZap}/disparos-pixel`;
const host = `${hosts.apiOiZap}/campanhas`;
const CampanhasServices = {
    async incluir(req) {
        try {
            const response = await callApi('post', `${host}/v1`, undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async alterar(req) {
        try {
            // Se houver endpoint para alterar, ajuste aqui. Caso não exista, pode ser implementado no backend.
            const response = await callApi('put', `${host}/v1`, undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async remover(req) {
        try {
            const response = await callApi('delete', `${host}/v1`, undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listar(req) {
        try {
            const response = await callApi('get', `${host}/v1`, req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
};
export default CampanhasServices;
