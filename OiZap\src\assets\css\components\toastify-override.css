/* Toast container */
:root {
    --toastify-z-index: 9999;
}

.Toastify__toast-container {
    z-index: var(--toastify-z-index) !important;
}

.Toastify__toast {
    z-index: var(--toastify-z-index) !important;
}

/* Custom toast classes */
.custom-toast-class {
    z-index: var(--toastify-z-index) !important;
}

/* Confirm modal overlay */
.confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--toastify-z-index) - 1) !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confirm-toast-container {
    z-index: var(--toastify-z-index) !important;
    position: relative;
}

/* Para garantir que funcione com qualquer modal */
.modal-backdrop,
.modal,
.v-overlay,
.el-overlay {
    z-index: 8000 !important;
}
