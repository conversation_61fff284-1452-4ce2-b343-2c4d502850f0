import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    let hasColumn = await knex.schema.hasTable('campanhas');

    if (!hasColumn) {
      console.log('Tabela não existe, criando...');
      await knex.schema.createTable('campanhas', (table) => {
        table.increments('cd_campanha').primary();
        table.string('ds_campanha', 200).notNullable();
        table.string('tp_campanha', 30);
        table.string('ds_url', 300);
        table.text('ds_descricao');
        table.boolean('in_ativo').defaultTo(true);
        table.integer('cd_usucad').nullable();
        table.timestamp('dt_inicio').nullable();
        table.timestamp('dt_fim').nullable();
        table.timestamp('dt_cadastro').nullable();
        table.timestamps(true, true);
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.dropTable('campanhas');
  } catch (error) {
    throw error;
  }
}
