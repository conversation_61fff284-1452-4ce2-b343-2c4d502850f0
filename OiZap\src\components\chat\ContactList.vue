<template>
    <div class="h-full flex flex-col" v-show="inContatos">
        <!-- <PERSON><PERSON> de <PERSON> -->

        <div class="px-3 py-2 border-b border-gray-100 dark:border-darkmode-400">
            <div class="flex items-center space-x-2">
                <div class="relative flex-1">
                    <BaseInput
                        v-model="searchQuery"
                        placeholder="Buscar conversas..."
                        @input="debouncedSearch"
                        iconLeft="SearchIcon"
                        ref="searchInput"
                    />
                </div>

                <Tippy content="Nova conversa por número">
                    <BinaryIcon
                        class="w-6 h-6 ml-1 mr-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100 transition-opacity duration-200"
                        style="stroke-width: 2px"
                        @click="abrirModalConversa"
                    />
                </Tippy>
            </div>
        </div>

        <!-- Lista de contatos -->
        <div ref="scrollContainer" class="flex-1 overflow-y-auto" @scroll="handleScroll">
            <!-- Loading -->
            <div v-if="loading" class="flex items-center justify-center p-4">
                <LoadingIcon icon="bars" class="w-5 h-5 mr-2" />
                <span class="text-sm text-gray-600 dark:text-slate-400">Carregando contatos...</span>
            </div>

            <!-- Lista agrupada -->
            <div v-else-if="groupedContacts.length > 0">
                <div v-for="group in groupedContacts" :key="group.letter" class="mb-4">
                    <!-- Cabeçalho da letra -->
                    <div
                        class="sticky top-0 bg-gray-50 dark:bg-darkmode-500 px-4 py-2 border-b border-gray-200 dark:border-darkmode-400"
                    >
                        <h3 class="text-sm font-semibold text-gray-700 dark:text-slate-300 uppercase">
                            {{ group.letter }}
                        </h3>
                    </div>

                    <!-- Contatos do grupo -->
                    <div class="space-y-1">
                        <div
                            v-for="(contact, index) in group.contacts"
                            :key="contact.telefone"
                            @click="selectContact(contact, index)"
                            class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-darkmode-400 cursor-pointer transition-colors duration-200 border-b border-gray-100 dark:border-darkmode-300"
                        >
                            <!-- Avatar -->
                            <div class="flex-shrink-0 mr-3">
                                <div v-if="contact.url_profile_picture" class="w-10 h-10 rounded-full overflow-hidden">
                                    <img
                                        :src="contact.url_profile_picture"
                                        :alt="contact.push_name"
                                        class="w-full h-full object-cover"
                                        @error="handleImageError"
                                    />
                                </div>
                                <div
                                    v-else
                                    class="w-10 h-10 bg-gray-300 dark:bg-darkmode-400 rounded-full flex items-center justify-center"
                                >
                                    <span class="text-sm font-medium text-gray-600 dark:text-slate-300">
                                        {{ getInitials(contact.push_name) }}
                                    </span>
                                </div>
                            </div>

                            <!-- Informações do contato -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-slate-300 truncate">
                                        {{ contact.push_name || formatPhone(contact.telefone) }}
                                    </h4>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-slate-400">
                                    {{ formatPhone(contact.telefone) }}
                                </p>
                            </div>

                            <!-- Ações 
                            <div class="flex-shrink-0 ml-2">
                                <button
                                    @click.stop="startChat(contact)"
                                    class="p-2 text-gray-400 hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors"
                                    title="Iniciar conversa"
                                >
                                    <MessageCircleIcon class="w-5 h-5" />
                                </button>
                            </div>
                            -->
                        </div>
                    </div>
                </div>

                <!-- Carregando mais -->
                <div v-if="loadingMore" class="flex items-center justify-center p-4">
                    <LoadingIcon icon="bars" class="w-4 h-4 mr-2" />
                    <span class="text-xs text-gray-600 dark:text-slate-400">Carregando mais...</span>
                </div>
            </div>

            <!-- Estado vazio -->
            <div v-else class="flex flex-col items-center justify-center p-8 text-gray-500 dark:text-slate-400">
                <UserIcon class="w-12 h-12 mb-4 opacity-50" />
                <p class="text-sm font-medium">Nenhum contato encontrado</p>
                <p class="text-xs opacity-75">Tente ajustar sua busca</p>
            </div>
        </div>
    </div>

    <!-- BEGIN: Modal Content -->
    <Modal :show="modalNovaConversaNumero" @hidden="fechaModalConversa">
        <ModalHeader class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-slate-200">Iniciar conversa por número</h2>
            <button type="button" class="btn btn-icon btn-sm" @click="fechaModalConversa">
                <XIcon class="w-5 h-5 text-gray-500 dark:text-slate-400" />
            </button>
        </ModalHeader>
        <ModalBody class="p-4">
            <div class="grid grid-cols-12 gap-4">
                <div class="col-span-12">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"> Nome </label>

                    <BaseInput
                        ref="inputNome"
                        v-model="formData.nm_contato"
                        type="text"
                        placeholder="Informe o nome do contato (opcional)"
                        class="w-full"
                        maxlength="60"
                    />
                </div>
                <div class="col-span-12">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                        Telefone
                        <span class="text-red-500">*</span>
                    </label>

                    <BaseInput
                        v-model="formData.nr_telefone"
                        type="text"
                        placeholder="Informe o telefone do contato (DDD) XXXXX-XXXX"
                        class="w-full"
                        maxlength="60"
                    />
                </div>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-between">
            <button type="button" class="btn btn-danger-soft w-24" @click="fechaModalConversa">Cancelar</button>
            <button type="button" class="btn btn-primary-soft w-24" @click="iniciarConversa(true)">Iniciar</button>
        </ModalFooter>
    </Modal>
    <!-- END: Modal Content -->
</template>

<script setup>
    import { ref, computed, watch, nextTick, onMounted, reactive } from 'vue';
    import { debounce } from 'lodash';
    import converters from '@/utils/converters';

    // Props
    const props = defineProps({
        contacts: {
            type: Array,
            default: () => [],
        },
        loading: {
            type: Boolean,
            default: false,
        },
        loadingMore: {
            type: Boolean,
            default: false,
        },
        inContatos: {
            type: Boolean,
            default: false,
        },
        hasMore: {
            type: Boolean,
            default: true,
        },
    });

    // Emits
    const emit = defineEmits(['search', 'load-more', 'contact-selected', 'start-chat']);

    // Refs
    const searchQuery = ref('');
    const scrollContainer = ref(null);
    const searchInput = ref(null);
    let lastScrollTop = 0;
    let isLoadingMore = false;
    const modalNovaConversaNumero = ref(false);
    const inputNome = ref(null);
    const formData = reactive({
        nm_contato: undefined,
        nr_telefone: undefined,
    });

    // Computed
    const groupedContacts = computed(() => {
        if (!props.contacts.length) return [];

        // Agrupar contatos por primeira letra
        const groups = {};

        props.contacts.forEach((contact) => {
            const name = contact.push_name || contact.telefone;
            const firstLetter = name.charAt(0).toUpperCase();
            const letter = /^[A-Z]/.test(firstLetter) ? firstLetter : '#';

            if (!groups[letter]) {
                groups[letter] = [];
            }
            groups[letter].push(contact);
        });

        // Converter para array e ordenar
        return Object.keys(groups)
            .sort((a, b) => {
                if (a === '#') return 1;
                if (b === '#') return -1;
                return a.localeCompare(b);
            })
            .map((letter) => ({
                letter,
                contacts: groups[letter].sort((a, b) => {
                    const nameA = a.push_name || a.telefone;
                    const nameB = b.push_name || b.telefone;
                    return nameA.localeCompare(nameB);
                }),
            }));
    });

    // Methods
    const getInitials = (name) => {
        if (!name) return '?';
        return name
            .split(' ')
            .map((n) => n.charAt(0))
            .join('')
            .substring(0, 2)
            .toUpperCase();
    };

    const formatPhone = (phone) => {
        return converters.formatarTelefone(phone);
    };

    const handleImageError = (event) => {
        event.target.style.display = 'none';
        event.target.parentNode.innerHTML = `
        <div class="w-10 h-10 bg-gray-300 dark:bg-darkmode-400 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-gray-600 dark:text-slate-300">
                ${getInitials(event.target.alt)}
            </span>
        </div>
    `;
    };

    const selectContact = (contact, index) => {
        emit('contact-selected', contact, index);
    };

    // const startChat = (contact) => {
    //     emit('start-chat', contact);
    // };

    const abrirModalConversa = () => {
        modalNovaConversaNumero.value = true;
    };

    const iniciarConversa = (numero) => {
        // Validar se o telefone foi informado
        if (!formData.nr_telefone) {
            alert('Por favor, informe o telefone do contato.');
            return;
        }

        // Limpar a máscara do telefone e manter apenas números
        const telefoneNumeros = formData.nr_telefone.replace(/\D/g, '');

        if (telefoneNumeros.length < 10) {
            alert('Por favor, informe um telefone válido.');
            return;
        }

        // Criar objeto do contato
        const novoContato = {
            telefone: telefoneNumeros,
            push_name: formData.nm_contato || telefoneNumeros,
            url_profile_picture: null,
            numero: numero,
        };

        // Emitir evento para iniciar conversa
        emit('start-chat', novoContato);

        // Fechar modal e limpar formulário
        fechaModalConversa();
    };

    const limparFormulario = () => {
        formData.nm_contato = undefined;
        formData.nr_telefone = undefined;
    };

    const fechaModalConversa = () => {
        modalNovaConversaNumero.value = false;
        limparFormulario();
    };
    // Função de scroll SUPER SIMPLES
    const handleScroll = (event) => {
        const { scrollTop, scrollHeight, clientHeight } = event.target;

        // Verificar se chegou ao final
        if (scrollTop + clientHeight >= scrollHeight - 50 && props.hasMore && !props.loadingMore && !isLoadingMore) {
            // Salvar posição ANTES de disparar o load-more
            lastScrollTop = scrollTop;
            isLoadingMore = true;

            emit('load-more');
        }
    };

    // Quando novos contatos chegam, manter scroll na posição
    watch(
        () => props.contacts.length,
        async (newLength, oldLength) => {
            if (newLength > oldLength && isLoadingMore) {
                await nextTick();

                // Aguardar um pouquinho mais para o DOM se ajustar
                setTimeout(() => {
                    if (scrollContainer.value && lastScrollTop > 0) {
                        scrollContainer.value.scrollTop = lastScrollTop;
                        isLoadingMore = false;
                    }
                }, 50); // Aumentei o delay
            }
        }
    );

    // Reset quando termina de carregar
    watch(
        () => props.loadingMore,
        (newValue) => {
            if (!newValue) {
                isLoadingMore = false;
            }
        }
    );

    // Reset quando termina de carregar
    watch(
        () => props.inContatos,
        (newValue) => {
            searchQuery.value = null; // Resetar referência do input
        }
    );

    const debouncedSearch = debounce(() => {
        emit('search', searchQuery.value);
    }, 300);

    // Focar no input quando o componente for montado
    onMounted(async () => {
        searchQuery.value = '';
        await nextTick();
        if (props.inContatos) {
            focusSearchInput();
        }
    });

    // Função para focar no input
    const focusSearchInput = () => {
        if (searchInput.value) {
            try {
                // Tentar diferentes formas de acessar o input
                if (typeof searchInput.value.focus === 'function') {
                    searchInput.value.focus();
                } else if (searchInput.value.$el) {
                    const inputElement =
                        searchInput.value.$el.querySelector('input') || searchInput.value.$el.querySelector('textarea');
                    if (inputElement) {
                        inputElement.focus();
                    }
                }
            } catch (error) {
                console.log('Erro ao focar no input:', error);
            }
        }
    };

    // Também focar quando o componente se tornar visível (se estiver usando v-show)
    watch(
        () => props.contacts,
        () => {
            if (props.contacts.length === 0) {
                // Quando a lista estiver vazia, focar no input para facilitar a busca
                nextTick(() => {
                    focusSearchInput();
                });
            }
        }
    );
</script>

<style scoped>
    .overflow-y-auto {
        scroll-behavior: auto;
    }
</style>
