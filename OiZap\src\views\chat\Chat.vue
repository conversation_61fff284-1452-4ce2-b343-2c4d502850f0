<template>
    <ShowLoading ref="loading" />

    <div class="intro-y chat grid grid-cols-12 gap-2 max-height: calc(100vh)">
        <!-- SIDEBAR CONVERSAS - COLUNA ESQUERDA COMPLETA -->
        <div class="col-span-12 lg:col-span-4 xl:col-span-3 mt-1">
            <!-- Todo o conteúdo da sidebar aqui -->
            <div class="intro-y">
                <!-- BEGIN: Header Compacto -->
                <div
                    class="bg-white dark:bg-darkmode-600 rounded-lg shadow-sm border border-gray-200 dark:border-darkmode-400 mb-1"
                >
                    <!-- Header para Contatos (Nova Conversa) -->
                    <div
                        v-if="filtroStatus.contatos"
                        class="px-4 py-3 bg-gradient-to-br from-emerald-400 to-teal-600 text-white rounded-t-lg"
                    >
                        <div class="flex items-center justify-between">
                            <!-- Botão Voltar e Título -->
                            <div class="flex items-center space-x-3">
                                <button
                                    @click="voltarParaUltimaAba"
                                    class="p-2 hover:bg-emerald-600 rounded-full transition-colors"
                                    title="Voltar"
                                >
                                    <ArrowLeftIcon class="w-5 h-5" />
                                </button>
                                <h1 class="text-base font-semibold">Nova Conversa</h1>
                            </div>

                            <!-- Status Compacto (mesmo do header principal) -->
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <div
                                        :class="[
                                            'w-2 h-2 rounded-full',
                                            situacaoInstancias === 'ok'
                                                ? 'bg-green-400'
                                                : situacaoInstancias === 'alerta'
                                                ? 'bg-yellow-400'
                                                : 'bg-red-400',
                                        ]"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Header Principal Normal (quando não for contatos) -->
                    <div
                        v-else
                        class="px-4 py-3 bg-gradient-to-br from-emerald-400 to-teal-600 text-white rounded-t-lg"
                    >
                        <div class="flex items-center justify-between mb-2">
                            <h1 class="text-base font-semibold">OiZap Chat</h1>
                            <div class="flex items-center space-x-2">
                                <!-- Status Compacto -->
                                <div class="flex items-center space-x-1">
                                    <div
                                        :class="[
                                            'w-2 h-2 rounded-full',
                                            situacaoInstancias === 'ok'
                                                ? 'bg-green-400'
                                                : situacaoInstancias === 'alerta'
                                                ? 'bg-yellow-400'
                                                : 'bg-red-400',
                                        ]"
                                    ></div>
                                </div>

                                <!-- Dropdown Instâncias Compacto -->
                                <Dropdown
                                    v-show="listaInstancias.length > 1"
                                    :show="isOpenFiltroInstancia"
                                    @hidden="fechaFiltrosInstancia"
                                    placement="bottom-start"
                                >
                                    <DropdownToggle
                                        @click="isOpenFiltroInstancia = true"
                                        class="p-1 hover:bg-emerald-600 rounded transition-colors"
                                    >
                                        <LaptopIcon class="w-4 h-4" />
                                    </DropdownToggle>
                                    <DropdownMenu>
                                        <DropdownContent tag="div" class="p-3">
                                            <table class="table table-report">
                                                <thead>
                                                    <tr>
                                                        <th class="whitespace-nowrap text-xs">Instância</th>
                                                        <th class="text-center whitespace-nowrap text-xs">ChatBot</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        v-for="instancia in listaInstancias"
                                                        :key="instancia"
                                                        class="intro-x text-xs"
                                                    >
                                                        <td>
                                                            <div class="flex justify-left items-left">
                                                                <span class="flex items-center">
                                                                    <input
                                                                        id="checkbox-switch-1"
                                                                        class="form-check-input mr-1"
                                                                        type="checkbox"
                                                                        v-model="instancia.in_selecionado"
                                                                        @change="onSelecionado()"
                                                                    />
                                                                    <div class="w-5 h-5">
                                                                        <img
                                                                            alt="oizap"
                                                                            :src="instancia.image"
                                                                            style="box-shadow: 0px 0px 0px 0px"
                                                                        />
                                                                    </div>
                                                                    <span class="font-medium whitespace-nowrap">{{
                                                                        instancia.nome
                                                                    }}</span>
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td class="table-report__action">
                                                            <div
                                                                class="flex justify-center items-center cursor-pointer"
                                                            >
                                                                <span class="flex items-center mr-3">
                                                                    <div class="form-check form-switch">
                                                                        <input
                                                                            class="form-check-input"
                                                                            type="checkbox"
                                                                            :value="!instancia.in_stop_bot"
                                                                            :checked="!instancia.in_stop_bot"
                                                                            @change="onChangeBot(instancia, $event)"
                                                                        />
                                                                    </div>
                                                                    <label
                                                                        class="forceLeft form-label self-center ml-2 mt-1.5"
                                                                        >{{
                                                                            instancia.in_stop_bot ? 'Parado' : 'Ativo'
                                                                        }}</label
                                                                    >
                                                                </span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </DropdownContent>
                                    </DropdownMenu>
                                </Dropdown>
                            </div>
                        </div>

                        <div class="flex space-x-1">
                            <button
                                @click="consultaAtendimentos('Em Atendimento')"
                                :class="[
                                    'relative flex-1 py-2 px-2 rounded text-xs font-medium transition-all',
                                    filtroStatus.emAtendimento
                                        ? 'bg-white text-emerald-600 dark:bg-emerald-700 dark:text-emerald-200'
                                        : 'text-emerald-100 hover:text-white hover:bg-emerald-600',
                                ]"
                            >
                                <div class="flex items-center justify-center space-x-1">
                                    <span>{{ modulos.in_crm ? 'Fila' : 'Atendimento' }}</span>
                                    <div
                                        v-if="filtroStatus.qtEmAtendimento > 0"
                                        :class="[
                                            'text-xs px-1.5 py-0.5 rounded-full font-bold min-w-[18px] text-center',
                                            filtroStatus.emAtendimento
                                                ? 'bg-emerald-500 text-white  dark:bg-emerald-700 dark:text-emerald-200'
                                                : 'bg-yellow-400 text-gray-900',
                                        ]"
                                    >
                                        {{ filtroStatus.qtEmAtendimento > 99 ? '99+' : filtroStatus.qtEmAtendimento }}
                                    </div>
                                </div>
                            </button>

                            <button
                                @click="consultaAtendimentos('Em Andamento')"
                                :class="[
                                    'relative flex-1 py-2 px-2 rounded text-xs font-medium transition-all',
                                    filtroStatus.emAndamento
                                        ? 'bg-white text-emerald-600  dark:bg-emerald-700 dark:text-emerald-200'
                                        : 'text-emerald-100 hover:text-white hover:bg-emerald-600',
                                ]"
                            >
                                <div class="flex items-center justify-center space-x-1">
                                    <span>{{ modulos.in_crm ? 'Atendimento' : 'Andamento' }}</span>
                                    <div
                                        v-if="filtroStatus.qtEmAndamento > 0"
                                        :class="[
                                            'text-xs px-1.5 py-0.5 rounded-full font-bold min-w-[18px] text-center',
                                            filtroStatus.emAndamento
                                                ? 'bg-emerald-500 text-white dark:bg-emerald-700 dark:text-emerald-200'
                                                : 'bg-yellow-400 text-gray-900',
                                        ]"
                                    >
                                        {{ filtroStatus.qtEmAndamento > 99 ? '99+' : filtroStatus.qtEmAndamento }}
                                    </div>
                                </div>
                            </button>

                            <button
                                @click="consultaAtendimentos('Finalizados')"
                                :class="[
                                    'flex-1 py-2 px-2 rounded text-xs font-medium transition-all',
                                    ultimaAbaSelecionada === 'Finalizados'
                                        ? 'bg-white text-emerald-600  dark:bg-emerald-700 dark:text-emerald-200'
                                        : 'text-emerald-100 hover:text-white hover:bg-emerald-600',
                                ]"
                            >
                                Finalizados
                            </button>
                        </div>
                    </div>

                    <!-- Barra de Busca Compacta (só aparece quando NÃO for contatos) -->
                    <div
                        class="px-3 py-2 border-b border-gray-100 dark:border-darkmode-400"
                        v-if="!filtroStatus.contatos"
                    >
                        <div class="flex items-center space-x-2">
                            <div class="relative flex-1">
                                <BaseInput
                                    v-model="filtroNome"
                                    placeholder="Buscar conversas..."
                                    @input="filtrarChats"
                                    iconLeft="SearchIcon"
                                />
                            </div>

                            <Tippy content="Nova Conversa">
                                <MessageSquarePlusIcon
                                    class="w-5 h-5 ml-3 cursor-pointer"
                                    style="stroke-width: 2px"
                                    @click="consultaContatos(true)"
                                />
                            </Tippy>

                            <!-- Botão Filtros Compacto -->
                            <Dropdown :show="isOpenFiltros" @hidden="fechaFiltros" placement="bottom-start">
                                <DropdownToggle
                                    @click="isOpenFiltros = true"
                                    class="p-1.5 text-gray-600 dark:text-slate-400 hover:text-gray-800 dark:hover:text-slate-300 hover:bg-gray-100 dark:hover:bg-darkmode-400 rounded transition-colors"
                                >
                                    <ListFilterIcon class="w-4 h-4" />
                                </DropdownToggle>
                                <DropdownMenu>
                                    <DropdownContent tag="div" class="p-3">
                                        <div class="text-sm font-medium text-gray-700 dark:text-slate-300 mb-3">
                                            Filtros
                                        </div>

                                        <div
                                            class="flex items-center space-x-2 p-2 hover:bg-gray-50 dark:hover:bg-darkmode-400 rounded"
                                        >
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                v-model="inMsgNaoLida"
                                                @change="listaChats(true)"
                                            />
                                            <label class="text-sm text-gray-700 dark:text-slate-300 cursor-pointer"
                                                >Não Lidas</label
                                            >
                                        </div>

                                        <div class="mt-2 pt-2 border-t border-gray-100 dark:border-darkmode-400">
                                            <div class="text-xs text-gray-500 dark:text-slate-400 mb-2">Status:</div>
                                            <div
                                                v-for="status in listaSituacoes"
                                                :key="status"
                                                class="flex items-center space-x-2 p-1 hover:bg-gray-50 dark:hover:bg-darkmode-400 rounded"
                                            >
                                                <input
                                                    class="form-check-input"
                                                    type="checkbox"
                                                    @change="checkSituacao(status.tp_status)"
                                                />
                                                <label class="text-xs text-gray-700 dark:text-slate-300 cursor-pointer">
                                                    {{ status.tp_status }}
                                                </label>
                                            </div>
                                        </div>
                                    </DropdownContent>
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </div>
                </div>
                <!-- END: Header Compacto -->

                <!-- Lista de Conversas Compacta - APLICAR DARK MODE AQUI border-gray-200-->

                <div
                    class="bg-white dark:bg-darkmode-600 rounded-lg shadow-sm border border-gray-200 dark:border-darkmode-400"
                    :style="{
                        height: modulos.in_chat_pedidos
                            ? 'calc(90vh - 170px)'
                            : modulos.in_crm && !filtroStatus.contatos && chatAtivo?.nome != undefined
                            ? 'calc(90vh - 160px)'
                            : modulos.in_crm && filtroStatus.contatos
                            ? 'calc(90vh - 50px)'
                            : modulos.in_crm
                            ? 'calc(90vh - 130px)'
                            : 'calc(90vh - 170px)',
                    }"
                >
                    <!-- Estado de Carregamento - APLICAR DARK MODE -->
                    <div
                        v-show="loadingChats && !filtroStatus.contatos"
                        class="flex items-center justify-center p-3 text-gray-600 dark:text-slate-400"
                    >
                        <LoadingIcon icon="bars" class="w-4 h-4 mr-2" />
                        <span class="text-xs">
                            {{
                                filtroStatus.emAndamento
                                    ? `Carregando ${listaUltimosChats.length + 1}/${listaUltimosChats.length + limit}`
                                    : 'Carregando...'
                            }}
                        </span>
                    </div>

                    <!-- Estado Vazio - APLICAR DARK MODE -->

                    <div
                        v-show="listaUltimosChats.length == 0 && !loadingChats && !filtroStatus.contatos"
                        class="flex flex-col items-center justify-center p-6 text-gray-500 dark:text-slate-400 h-full"
                    >
                        <div
                            class="w-12 h-12 bg-gray-100 dark:bg-darkmode-400 rounded-full flex items-center justify-center mb-2"
                        >
                            <svg
                                class="w-6 h-6 text-gray-400 dark:text-slate-500"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7z"
                                />
                            </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-700 dark:text-slate-300">Nenhuma conversa</div>
                        <div class="text-xs text-gray-500 dark:text-slate-400">Sem atendimentos no momento</div>
                    </div>

                    <!-- ChatList Component para atendimentos -->
                    <ChatList
                        v-show="!filtroStatus.contatos"
                        :chats-nao-pedidos="chatsNaoPedidos"
                        :chats-pedidos-realizados="chatsPedidosRealizados"
                        :chat-ativo="chatAtivo"
                        :get-color-for-status="getColorForStatus"
                        :passou-dois-minutos="passouDoisMinutos"
                        :maior-dt-ultima-importacao="maiorDtUltimaImportacao"
                        @chat-selected="showChatBox"
                        @transferir="transferirAtendimento"
                        @scroll="onScroll"
                        :modulosOiZap="modulos"
                        :qtInstancias="listaInstancias.length"
                    />

                    <!-- ContactList Component para contatos -->
                    <ContactList
                        v-show="filtroStatus.contatos"
                        :inContatos="filtroStatus.contatos"
                        :contacts="listaContatos"
                        :loading="loadingContatos"
                        :loading-more="loadingMoreContatos"
                        :has-more="hasMoreContatos"
                        @search="searchContactos"
                        @load-more="loadMoreContatos"
                        @contact-selected="onContactSelected"
                        @start-chat="onStartChat"
                    />
                </div>
            </div>
        </div>

        <div
            class="intro-y col-span-12 box h-full flex flex-col mt-1 bg-gray-200 dark:bg-darkmode-600"
            :class="{
                'lg:col-span-4 2xl:col-span-4': inModuloPedido == true,
                'lg:col-span-8 2xl:col-span-9': inModuloPedido == false,
            }"
            :style="{
                'max-height': modulos.in_chat_pedidos
                    ? 'calc(90vh - 95px)'
                    : modulos.in_crm && !filtroStatus.contatos && chatAtivo?.nome != undefined
                    ? 'calc(90vh - 15px)'
                    : modulos.in_crm && filtroStatus.contatos
                    ? 'calc(100vh - 100px)'
                    : modulos.in_crm
                    ? 'calc(100vh - 70px)'
                    : 'calc(90vh - 95px)',
            }"
        >
            <!-- BEGIN: Chat Default (quando não há chat selecionado)  ,-->
            <div v-if="!chatBox" class="h-full">
                <ChatDefault
                    :situacao-instancias="situacaoInstancias"
                    :lista-instancias="listaInstancias"
                    @abrir-modal-conexao="abrirModalConexao"
                    :tamanho="{
                        height: modulos.in_chat_pedidos ? 'calc(90vh - 95px)' : 'calc(92vh)',
                    }"
                />
            </div>
            <!-- END: Chat Default -->

            <!-- BEGIN: Chat Ativo (quando há chat selecionado)   :style="modulos.in_chat_pedidos ? 'h-full' : 'height: calc(100vh)'"-->
            <div v-else class="h-full flex flex-col">
                <!-- Header do Chat -->
                <!--BEGIN: Topo Nome  style="background-color: #f0f2f5; flex-shrink: 0"-->
                <div
                    class="flex flex-col sm:flex-row border-b border-slate-200/60 dark:border-darkmode-400 px-3 py-2 box bg-gray-100"
                >
                    <div class="flex items-center">
                        <div class="w-12 h-12 flex-none image-fit mr-1">
                            <div
                                class="rounded-full p-0.5"
                                :class="
                                    chatAtivo?.online == undefined
                                        ? ''
                                        : chatAtivo?.online
                                        ? 'border-solid border-2 border-green-600'
                                        : ''
                                "
                            >
                                <img
                                    alt="oizap"
                                    class="rounded-full"
                                    :src="chatAtivo?.url_profile_picture"
                                    @error="handleImageError"
                                />
                            </div>
                        </div>
                        <div class="ml-3 mr-auto">
                            <div class="flex items-center font-medium text-sm">
                                {{ chatAtivo.nome }}
                                <Tippy
                                    v-show="
                                        !chatAtivo.ds_obscliente ||
                                        chatAtivo.ds_obscliente == '' ||
                                        chatAtivo.ds_obscliente == null
                                    "
                                    content="Observação do cliente"
                                    class="mr-1"
                                >
                                    <TextSearchIcon
                                        class="w-4 h-4 ml-3 mr-3 cursor-pointer"
                                        style="stroke-width: 2px"
                                        @click="abreModalObsCliente()"
                                    />
                                </Tippy>
                                <span class="text-xs text-green-700 ml-2">
                                    {{ chatAtivo.presence }}
                                </span>
                            </div>

                            <div class="flex items-center text-slate-500 text-xs whitespace-nowrap -mt-0.5">
                                {{ converters.formatarTelefone(chatAtivo.telefone) }}
                                <span class="text-xs text-slate-400 ml-2">
                                    {{ chatAtivo.nameinstance }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div
                        class="flex items-center sm:ml-auto mt-5 sm:mt-0 border-t sm:border-0 border-slate-200/60 pt-3 sm:pt-0 -mx-5 sm:mx-0 sm:px-0"
                    >
                        <button
                            v-if="chatAtivo.tp_etapachat == 'Falar com Atendente'"
                            type="button"
                            class="btn btn-primary-soft h-6 text-xs mr-5"
                            @click="atendido()"
                        >
                            <HeadsetIcon class="w-4 h-4 mr-1" />
                            Atendido
                        </button>
                        <button type="button" class="btn btn-dark-soft h-6 text-xs" @click="abrirModalEncerrar()">
                            <CheckIcon class="w-4 h-4 mr-1" />
                            Encerrar
                        </button>

                        <Dropdown class="ml-auto">
                            <DropdownToggle tag="a" href="javascript:;" class="w-5 h-5 text-slate-500">
                                <MoreVerticalIcon class="w-5 h-5" />
                            </DropdownToggle>
                            <DropdownMenu class="w-52">
                                <DropdownContent>
                                    <DropdownItem @click="situacaoBot(chatAtivo)">
                                        <BotOffIcon
                                            v-if="(bot.in_stop_bot && stopBot) || stopBot"
                                            class="w-5 h-5 mr-2"
                                        />
                                        <BotIcon v-else class="w-5 h-5 mr-2" />

                                        {{
                                            bot.in_stop_bot && stopBot
                                                ? 'ChatBot Ativado'
                                                : stopBot == true
                                                ? 'ChatBot Desativado'
                                                : 'ChatBot Ativado'
                                        }}
                                    </DropdownItem>
                                    <!-- <DropdownItem @click="situacaoMSGAut(chatAtivo)">
                                        <MessageSquareOffIcon
                                             v-if="(bot.in_stop_msg_automatica)"
                                            class="w-5 h-5 mr-2"
                                        />
                                        <MessageSquareIcon v-else class="w-5 h-5 mr-2" />

                                        {{
                                            bot.in_stop_msg_automatica
                                                ? 'Mensagens Automáticas Ativada'
                                                : 'Mensagens Automáticas Desativada'
                                        }}
                                    </DropdownItem> -->
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                </div>
                <!--END: Topo Nome-->

                <!-- Área das Mensagens -->
                <div
                    class="flex-1 overflow-y-auto relative messages-container"
                    ref="messagesContainer"
                    :style="[backgroundStyle]"
                >
                    <!-- Overlay para dar mais contraste (fixo) -->
                    <div
                        class="absolute inset-0 pointer-events-none z-0"
                        :style="{
                            background: darkMode
                                ? 'rgba(0, 0, 0, 0.15) !important'
                                : 'rgba(255, 255, 255, 0.2) !important',
                        }"
                    ></div>

                    <!-- Container das mensagens -->
                    <div class="relative z-10 p-4 space-y-2">
                        <!-- Placeholder quando não há mensagens -->
                        <div
                            v-if="!mensagens || mensagens.length === 0"
                            class="flex items-center justify-center h-full min-h-[400px]"
                        >
                            <div
                                class="text-center bg-white/90 dark:bg-darkmode-600/90 backdrop-blur-sm rounded-xl p-8 shadow-lg border border-white/20"
                            >
                                <div class="text-gray-500 dark:text-slate-400">
                                    <svg
                                        class="w-16 h-16 mx-auto mb-4 opacity-50"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                    <p class="font-semibold text-lg mb-2">Nenhuma mensagem ainda</p>
                                    <p class="text-sm opacity-75">As mensagens aparecerão aqui quando enviadas</p>
                                </div>
                            </div>
                        </div>
                        <!-- Botão Carregar Mais Mensagens -->
                        <div
                            v-else
                            class="flex mt-1 items-center rounded-lg bg-blue-50 dark:bg-gray-600 text-xs mx-auto mb-1 h-6 px-3 py-1.5 cursor-pointer w-[120px] transition-colors duration-200 hover:bg-blue-100"
                            :class="[darkMode ? ' text-slate-100' : '']"
                            @click="carregaAtendimentos()"
                        >
                            <RefreshCcwIcon class="w-4 h-4 mr-1" />
                            Carregar mais
                        </div>
                        <!-- Grupos de mensagens por atendimento -->
                        <template
                            v-for="group in groupedMessages"
                            :key="group.atendimento?.cd_atendimento || 'current'"
                        >
                            <!-- Cabeçalho do Atendimento -->

                            <div
                                v-if="group.atendimento"
                                class="mx-auto mb-1 px-3 py-1.5 bg-blue-50 dark:bg-gray-600 cursor-pointer w-full transition-colors duration-200 hover:bg-blue-100 rounded-lg"
                                @click="handleAtendimentoSelect(group.atendimento)"
                            >
                                <div class="flex items-center gap-3 text-xs">
                                    <ChevronRightIcon v-if="!group.atendimento.open" class="h-4 w-4" />
                                    <ChevronDownIcon v-else class="h-4 w-4" />
                                    <span :class="[darkMode ? 'text-slate-100' : 'text-gray-700']">
                                        Atendimento {{ group.atendimento.cd_atendimento }}
                                    </span>
                                    <span class="text-gray-500">
                                        {{
                                            group.atendimento.tp_status == 'Atendimento Finalizado'
                                                ? converters.date(
                                                      'DD/MM/YYYY HH:MM:SS',
                                                      group.atendimento.dt_finalizado
                                                  )
                                                : converters.date('DD/MM/YYYY HH:MM:SS', group.atendimento.dt_inicio)
                                        }}
                                    </span>
                                    <span
                                        class="px-2 py-0.5 rounded font-semibold"
                                        :class="getColorForStatus(group.atendimento.tp_status)"
                                    >
                                        {{ group.atendimento.tp_status }}
                                    </span>
                                    <Tippy
                                        content="Ver Pedido do Atendimento"
                                        class="tooltip h-4 w-4"
                                        v-if="modulos.in_chat_pedidos"
                                    >
                                        <PackageSearchIcon
                                            class="h-4 w-4"
                                            @click.stop="consultaPedidoAntedimento(group.atendimento)"
                                        />
                                    </Tippy>
                                </div>
                            </div>

                            <!-- Mensagens do Atendimento com Transition para evitar piscadas -->
                            <Transition name="fade" mode="out-in">
                                <div
                                    v-show="group.atendimento && group.atendimento.open"
                                    class="space-y-3 overflow-hidden"
                                >
                                    <!-- Use v-show em vez de v-for com v-if para manter os elementos na DOM -->
                                    <div v-if="!isLoadingMessages">
                                        <ChatMessages
                                            v-for="(mensagem, index) in group.messages"
                                            :key="`msg-${mensagem.id || index}-${mensagem.timestamp}`"
                                            :mensagens="mensagem"
                                            :hosts="hosts"
                                            :dark="darkMode"
                                            :show-avatar="shouldShowAvatar(mensagem, index)"
                                        />
                                    </div>
                                    <div v-else class="flex justify-center py-4">
                                        <div
                                            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"
                                        ></div>
                                    </div>
                                </div>
                            </Transition>

                            <!-- Mensagens atuais (sem atendimento específico) -->
                            <div v-if="!group.atendimento" class="space-y-3">
                                <ChatMessages
                                    v-for="(mensagem, index) in mensagens"
                                    :key="`current-msg-${mensagem.id || index}-${mensagem.timestamp}`"
                                    :mensagens="mensagem"
                                    :hosts="hosts"
                                    :dark="darkMode"
                                    :show-avatar="shouldShowAvatar(mensagem, index)"
                                />
                            </div>
                        </template>

                        <!-- Indicador de digitação -->
                        <div v-if="alguemDigitando" class="flex justify-start animate-pulse">
                            <div
                                class="bg-white dark:bg-darkmode-400 rounded-2xl px-4 py-3 shadow-sm max-w-xs border border-gray-200 dark:border-darkmode-300"
                            >
                                <div class="flex items-center space-x-2">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                        <div
                                            class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                            style="animation-delay: 0.1s"
                                        ></div>
                                        <div
                                            class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                            style="animation-delay: 0.2s"
                                        ></div>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-slate-400">digitando...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex-shrink-0 p-2">
                    <!-- Container estilo WhatsApp -->
                    <div
                        class="flex items-center gap-3 bg-white/95 dark:bg-darkmode-600/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-3"
                    >
                        <!-- Inputs de arquivo (mantidos ocultos) -->
                        <input
                            ref="fileInputDoc"
                            type="file"
                            id="fileInputDoc"
                            multiple
                            accept=".doc,.pdf,.xls,.csv,.txt"
                            style="display: none"
                            @change="handleDrop"
                        />
                        <input
                            ref="fileInput"
                            type="file"
                            id="fileInput"
                            multiple
                            accept=".jpg,.jpeg,.png,.ogg,.mp3,.mp4,.avi"
                            style="display: none"
                            @change="handleDrop"
                        />

                        <!-- Botões de ação -->
                        <div class="flex items-center gap-2">
                            <!-- Botão de anexo 
                            <button
                                class="p-2 hover:bg-gray-100 dark:hover:bg-darkmode-500 rounded-full transition-colors"
                                @click="$refs.fileInput.click()"
                            >
                                <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        fill-rule="evenodd"
                                        d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z"
                                        clip-rule="evenodd"
                                    />
                                </svg>
                            </button>
                            -->
                        </div>

                        <!-- Área do textarea -->
                        <div class="flex-1 relative">
                            <textarea
                                class="w-full bg-transparent border-0 focus:ring-0 focus:outline-none resize-none placeholder:text-gray-500 dark:placeholder:text-slate-400 text-sm leading-5"
                                rows="1"
                                ref="textAreaMensagem"
                                :placeholder="`Digite uma mensagem`"
                                v-model="messageInput"
                                @input="autoResizeTextarea"
                                @keydown="handleKeydown"
                                @keydown.esc="focusProduto"
                                @keydown.enter.prevent="sendMessage"
                                @dragover.prevent
                                @drop="handleDrop"
                                style="
                                    overflow-y: hidden;
                                    white-space: pre-wrap;
                                    word-wrap: break-word;
                                    resize: none;
                                    max-height: 200px;
                                    min-height: 30px;
                                    height: 30px;
                                "
                            ></textarea>

                            <!-- Sugestões de mensagens -->
                            <div v-if="dropdownVisible" class="suggestions-list" ref="suggestionsList">
                                <ul>
                                    <li
                                        v-for="(mensagem, index) in filteredMensagensAtalhos"
                                        :key="mensagem.nr_controle"
                                        @click="handleItemClick(mensagem)"
                                        @keydown.enter.prevent="handleItemClick(mensagem)"
                                        :class="{ highlighted: highlightedIndex === index }"
                                    >
                                        {{ mensagem.ds_titulo }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Botão de enviar -->
                        <button
                            class="p-2 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full transition-colors flex-shrink-0 ml-2"
                            @click="sendMessage"
                            :disabled="!messageInput.trim()"
                            :class="{ 'opacity-50 cursor-not-allowed': !messageInput.trim() }"
                        >
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"
                                />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Input de Mensagem -->
                <!-- <div class="bg-white dark:bg-darkmode-600 border-t border-gray-200 dark:border-darkmode-400 p-4">
                    <div class="flex items-center space-x-3">
                        <input
                            type="text"
                            placeholder="Digite sua mensagem..."
                            class="flex-1 px-4 py-2 border border-gray-300 dark:border-darkmode-400 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-darkmode-800 dark:text-slate-300"
                        />
                    </div>
                </div> -->
            </div>
            <!-- END: Chat Ativo -->
        </div>

        <!-- BEGIN: Pedido -->
        <div
            v-show="inModuloPedido && modulos.in_chat_pedidos"
            class="intro-y col-span-12"
            :class="{
                'lg:col-span-5 2xl:col-span-5': inModuloPedido == true,
            }"
        >
            <div class="chat__box box flex flex-col mt-1" style="height: 94vh !important">
                <Pedido
                    ref="compPedidoRef"
                    @eventPedidoFechado="PedidoFechado"
                    @eventReturnPedido="ReturnPedido"
                    @eventReturnCliente="ReturnCliente"
                    @eventReturnFocusMsg="ReturnFocusMsg"
                    :dark="darkMode"
                />
            </div>
            <!-- END:Pedido -->
        </div>
    </div>

    <!-- aqui    -->
    <!-- BEGIN:Atalho MSGs -->
    <div
        class="overflow-x-scroll mt-1.5 flex-1"
        style="max-width: 100%; max-height: 87%"
        v-show="chatAtivo && modulos.in_chat_pedidos"
    >
        <div class="flex space-x-1">
            <div v-for="(mensagem, index) in listaMensagensAtalhos" :key="index">
                <Tippy :content="`${mensagem.hint}`" class="tooltip h-8 w-8">
                    <div class="box p-2 flex items-center cursor-pointer text-xs zoom-in h-6">
                        <div class="ml-2 mr-auto w-16">
                            <div class="font-medium truncate" @click="handleItemClick(mensagem)">
                                {{ mensagem.ds_titulo }}
                            </div>
                        </div>
                    </div>
                </Tippy>
            </div>
        </div>
    </div>
    <!-- END:Atalho MSGs -->

    <Modal :show="modalAtalho" size="modal-xl" tabindex="-1" @hidden="fechaAtalhoOpcoes">
        <ModalBody style="background-color: #f9f9f9; border-radius: 7px; margin-top: -25px">
            <div class="box">
                <input
                    type="text"
                    class="form-control col-span-1 block pl-9"
                    placeholder="Pesquise a Mensagem"
                    v-model="inputAtalhoMensagem"
                    @input="filtraMensagensAtalho"
                    @keydown.down.prevent="moveSelection(1)"
                    @keydown.up.prevent="moveSelection(-1)"
                    @keydown.enter.prevent="selectMessage"
                    ref="textPesquisaMensagem"
                />
                <div class="overflow-y-scroll mt-1 flex-1" style="max-height: 90%">
                    <div class="grid grid-cols-12">
                        <div class="mt-1 col-span-12">
                            <div v-for="(mensagem, index) in listaMensagensAtalhos" :key="index">
                                <div
                                    class="box p-2 flex items-center"
                                    :class="{ 'bg-blue-100': index === selectedIndex }"
                                    @click="handleItemClick(mensagem)"
                                >
                                    <div class="ml-2 mr-auto">
                                        <div class="font-medium">{{ mensagem.ds_titulo }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ModalBody>
    </Modal>

    <!-- BEGIN: Modal Content -->
    <Modal :show="modalObsCliente" @hidden="fechaModalObsCliente">
        <ModalBody class="p-0">
            <div class="p-3 text-center">
                <textarea
                    class="chat__box__input form-control border border-gray-300 focus:ring-blue-300 focus:border-blue-300"
                    rows="10"
                    ref="textObsCliente"
                    :placeholder="`Observação do Cliente`"
                    v-model="dsObsCliente"
                    style="overflow-y: auto; white-space: pre-wrap; word-wrap: break-word; resize: none"
                ></textarea>
            </div>
        </ModalBody>
    </Modal>
    <!-- END: Modal Content -->

    <!-- BEGIN: Modal Conexao -->
    <modalConexao ref="modalConexaoRef" @conexao="mudancaConexao" :baseQRCode="qrCode" />
    <!-- END: Modal Conexao -->

    <mdTransferirAtendimento ref="modalTransAtendimentoRef" @transferenciaConfirmada="returnAtendimento" />
    <mdEncerrarAtendimento ref="modalEncerrarAtendimentoRef" @encerramentoConfirmado="returnEncerramento" />
</template>

<script setup>
    import { nextTick, onMounted, ref, watch, onUnmounted, computed, reactive } from 'vue';
    import io from 'socket.io-client';
    import hosts from '@/utils/hosts';
    import _ from 'lodash';
    import MessagesServices from '@/services/chat/MessagesServices';
    import AtendimentoServices from '@/services/chat/AtendimentoServices';
    import UploadServices from '@/services/configuracao/UploadServices';
    import WhatsAppServices from '@/services/whatsapp/WhatsAppServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import FluxoAtendimentoServices from '@/services/fluxos/FluxoAtendimentoServices';
    import ClientesServices from '@/services/pedidos/ClientesServices';
    //import ChatMessages from './ChatMessages.vue';
    import ChatMessages from '@/components/chat/ChatMessages.vue';
    import ChatList from '@/components/chat/ChatList.vue';
    import ChatDefault from '@/components/chat/ChatDefault.vue';
    import Pedido from '@/views/chat/Pedido.vue';
    import mdTransferirAtendimento from '@/views/chat/modalTransferirAtendimento.vue';
    import mdEncerrarAtendimento from '@/views/chat/modalEncerraAtendimento.vue';
    import { useBot } from '@/stores/bot';
    import axios from 'axios';
    import modalConexao from '@/views/administracao/instancia/modalConexao.vue';
    import converters from '../../utils/converters';
    import functions from '../../utils/functions';
    import imgWhatsOFF from '@/assets/images/whatsoff.png';
    import imgWhatsON from '@/assets/images/whatson.png';
    import { useDarkModeStore } from '@/stores/dark-mode';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';
    import ContatosServices from '@/services/pedidos/ContatosServices';
    import ContactList from '@/components/chat/ContactList.vue';

    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);
    const mensagens = ref([]);
    const alguemDigitando = ref(false);
    const messagesContainer = ref(null);
    import { usePagina } from '@/stores/pagina';
    import logger from '../../utils/logger';
    import backgroundImage from '@/assets/images/fundochat.png';
    import backgroundImagedark from '@/assets/images/fundochatdark.png';
    //import imageOiZap from '@/assets/images/logo.png';
    import notFoundImage from '@/assets/images/notfound.png';
    import { useToast } from '@/global-components/toastify/useToast';
    //import { now } from 'lodash';
    //import DepartamentosServices from '../../services/administracao/DepartamentosServices';
    import UsuariosServices from '../../services/administracao/UsuariosServices';
    const toast = useToast();

    let pagina = usePagina();
    const qrCode = ref('');

    // Adicionar novas refs para contatos
    const listaContatos = ref([]);
    const loadingContatos = ref(false);
    const loadingMoreContatos = ref(false);
    const hasMoreContatos = ref(true);
    const pageContatos = ref(0);
    const limitContatos = ref(50);
    const searchContatos = ref('');

    const modalTransAtendimentoRef = ref();
    const modalEncerrarAtendimentoRef = ref();
    let groupedMessages = ref([]);
    let mensagensRecebidas = ref([]);
    let atendimentos = ref([]);
    let novosAtendimentos = ref([]);
    let listaSituacoes = ref([]);
    //let messages = ref([]);
    let mensagem = ref('');
    let dsObsCliente = ref('');
    //let contactsFoto = ref('');
    let chatAtivo = ref();
    let listaUltimosChats = ref([]);
    let listaUltimosChatsOld = ref([]);
    let inMsgNaoLida = ref(false);
    let isFiltroChats = ref(false);
    //let filtroInstancias = ref([]);
    let stopBot = ref(false);
    let stopMSGAutomatica = ref(false);
    let isOpenFiltros = ref(false);
    let isOpenFiltroInstancia = ref(false);
    let bot = useBot();
    let inSituacaoBot = ref(false);
    //let qtTotalMessagesNotRead = ref(0);
    let modalAtalho = ref(false);
    let inputAtalhoMensagem = ref();
    //let statusAtendimento = ref(undefined);
    //let enviandoMensagem = ref(false);
    let reconectando = ref(false);
    const selectedAtendimento = ref();

    // No setup ou no seu script
    //const debouncedScroll = _.debounce(onScroll, 200);
    const modalObsCliente = ref(false);
    const messageInput = ref('');
    const dropdownVisible = ref(false);
    const dropdownVisibleButton = ref(false);
    const highlightedIndex = ref(-1);
    const textAreaMensagem = ref(null);
    const suggestionsList = ref(null);
    //const filterInput = ref('');
    const listaMensagensAtalhosOriginal = ref([]);
    const textPesquisaMensagem = ref(null);

    const showAttachmentMenu = ref(false);
    const showEmojiPicker = ref(false);
    const isLoadingMessages = ref(false);

    const usuarioDepartamentos = ref([]);

    const selectedIndex = ref(-1); // Armazena o índice selecionado

    //const dropdownMenu = ref(null);

    // let inFinalizaPedido = ref(false);
    let filtroNome = ref('');
    //let tpSituacaoAtendimentos = ref('');

    let inModuloPedido = ref(false);
    let isRecording = ref(false);
    let mediaRecorder = ref(null);
    let recordedChunks = ref([]);
    //let listaProdutos = ref([]);
    //let listaAdicionaisItem = ref([]);
    const cdEstabelecimento = ref();
    const nrHash = ref('');
    const codUsuario = ref();
    const nmUsuario = ref('');
    //let finalizarPedido = ref(false);
    //let listaAdicionais = ref([]);
    //let listaPedidoItens = ref([]);
    let recordingTime = ref(0);
    let timerAudio = ref(null);
    let timerAtendimento = ref(null);
    //let listaItemAdicionais = ref([]);
    //let inRetirada = ref(false);
    let instanciaSelecionada = ref([]);
    //let chatPedido = ref();
    const statusEmAtendimento = ref(
        `'Fila','Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente','Pedido Realizado'`
    );
    const statusEmAndamento = ref(`'Pedido Confirmado','Pedido Pronto','Saiu para Entrega'`);
    const statusEmAtendimentoCRM = ref(`'Fila','Transferido'`);
    const statusEmAndamentoCRM = ref(`'Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente'`);
    const statusFinalizadosCRM = ref(
        `'Atendimento Finalizado','Pedido Realizado','Pedido Confirmado','Pedido Pronto','Saiu para Entrega'`
    );
    const statusCRM = ref(`'Fila','Transferido','Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente'`);

    const contatosNaoLidos = ref(new Map());
    // Estrutura do mapa:
    // telefone_instance -> {
    //   qt: number,
    //   tp_status: string,
    //   tp_etapachat: string
    // }
    const ultimaAbaSelecionada = ref('Em Atendimento'); // padrão
    let filtroStatus = ref({
        emAtendimento: false,
        qtEmAtendimento: 0,
        finalizados: false,
        qtfinalizados: 0,
        emAndamento: false,
        qtEmAndamento: 0,
        contatos: false,
        status: `'Fila','Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente','Pedido Realizado'`,
        arStatus: ['Fila', 'Em Atendimento', 'Em Pedido', 'Em Pagamento', 'Falar com Atendente', 'Pedido Realizado'],
    });
    // Estrutura do cache melhorada
    let cacheAtendimentos = ref({
        emAtendimento: [],
        emAndamento: [],
    });

    let page = ref(0);
    const limit = ref(20);
    let pageAtendimentos = ref(0);
    const limitAtendimentos = ref(1);
    let loadingChats = ref(false);
    let hasMore = ref(true); // Para controlar se ainda há mais dados para carregar
    let inFiltraContatos = ref(false);
    const maiorDtUltimaImportacao = ref('');
    const compPedidoRef = ref(null);
    const previousColors = ref([]);
    const tpAmbiente = ref('');
    const chatBox = ref(false);
    const chatContainer = ref(null);
    const modulos = reactive({ in_chat_pedidos: false, in_api: false, in_crm: false });
    // const itemsContainer = ref(null);
    // const instance = ref();
    const loading = ref();
    const fileInput = ref();
    const fileInputDoc = ref();
    const estabelecimentosLiberado = ref([]);
    const modalConexaoRef = ref();
    const mimeTypes = {
        txt: 'text/plain',
        html: 'text/html',
        jpg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        json: 'application/json',
        pdf: 'application/pdf',
        csv: 'text/csv',
        // Adicione outros conforme necessário
    };
    let listaInstancias = ref([]);
    let listaMensagensAtalhos = ref([]);
    let situacaoInstancias = ref();
    let apiDesconectada = ref(false);

    //const socket = io.connect(hosts.apiConsumer, { transports: ['websocket'] });
    let socket;

    if (import.meta.env.MODE === 'development') {
        socket = io.connect(hosts.webSocket, { transports: ['websocket'] });
    } else {
        socket = io.connect(hosts.hostConsumer, {
            rememberUpgrade: true,
            transports: ['websocket'],
            secure: true,
            enabledTransports: ['ws', 'wss'],
            rejectUnauthorized: false,
            path: hosts.webSocket,
            reconnection: true, // Habilitar reconexão automática
            reconnectionAttempts: 5, // Tentativas máximas de reconexão
            reconnectionDelay: 2000, // Esperar 2 segundos antes de tentar reconectar
            reconnectionDelayMax: 5000, // Limite máximo de 5 segundos entre tentativas
            timeout: 20000, // Tempo limite de conexão em 20 segundos
            pingInterval: 10000, // 10s
            pingTimeout: 5000, // 5s
        });
    }
    const voltarParaUltimaAba = () => {
        //listaContatos.value = [];
        switch (ultimaAbaSelecionada.value) {
            case 'Em Atendimento':
                consultaAtendimentos('Em Atendimento');
                break;
            case 'Em Andamento':
                consultaAtendimentos('Em Andamento');
                break;
            case 'Finalizados':
                consultaAtendimentos('Finalizados');
                break;
            default:
                consultaAtendimentos('Em Atendimento');
        }
    };

    const playAudioWhats = () => {
        const nrAudios = import.meta.glob('@/assets/som/whatsapp.mp3', { eager: true });
        new Audio(Object.values(nrAudios)[0].default).play();
    };

    async function showChatBox(chat, index) {
        //
        limitAtendimentos.value = 1;
        if (modulos.in_chat_pedidos) {
            inModuloPedido.value = true;
        } else {
            inModuloPedido.value = false;
        }
        inSituacaoBot.value = true;
        mensagensRecebidas.value = [];
        mensagens.value = [];
        mensagem.value = '';

        //
        chatAtivo.value = chat;
        //

        chatAtivo.value.index = index;
        chatAtivo.value.in_novamsg = false;
        chatAtivo.value.online = undefined;

        let status;
        status = chat?.tp_status;
        if (!status) status = chat?.status_pedido;
        if (!status) status = chat?.tp_status_atendimento;

        if (filtroStatus.value.status && filtroStatus.value.status.includes(status)) {
            chatAtivo.value.naoRemove = false;
        } else {
            chatAtivo.value.naoRemove = true;
        }

        if (compPedidoRef.value && modulos.in_chat_pedidos) {
            compPedidoRef.value.selecionaCliente(chatAtivo.value);
        }
        stopBot.value = chat.in_stop_bot == false ? bot.in_stop_bot : chat.in_stop_bot;

        // if (filtroStatus.value.emAtendimento && chatAtivo.value.qt_messages_notread > 0) {
        //     filtroStatus.value.qtEmAtendimento = parseInt(filtroStatus.value.qtEmAtendimento) - 1;
        // } else if (filtroStatus.value.emAndamento && chatAtivo.value.qt_messages_notread > 0) {
        //     filtroStatus.value.qtEmAndamento = parseInt(filtroStatus.value.qtEmAndamento) - 1;
        // }
        //Quando está na Aba Em Atendimento selecionado e clica no chat, remove a contagem de mensagens não lidas
        //do chat que está sendo atendido com a etapa "Falar com Atendente"
        if (chat.tp_etapachat == 'Falar com Atendente' && filtroStatus.value.emAtendimento) {
            filtroStatus.value.qtEmAndamento = parseInt(filtroStatus.value.qtEmAndamento) - 1;
        }

        chat.qt_messages_notread = 0;
        pageAtendimentos.value = 0;

        atendimentos.value = [];
        groupedMessages.value = [];

        const resultCliente = await ClientesServices.listaClientePorAtendimento({
            cd_atendimento: chat.cd_atendimento,
            cd_estabelecimento: chat.cd_estabelecimento,
        });

        if (resultCliente.statuscode == 200) {
            const dataCliente = resultCliente.data[0];
            chatAtivo.value.ds_obscliente = dataCliente.ds_obscliente;
            chatAtivo.value.ds_endereco = dataCliente.ds_endereco;
            chatAtivo.value.nr_endereco = dataCliente.nr_endereco;
            chatAtivo.value.ds_bairro = dataCliente.ds_bairro;
            chatAtivo.value.ds_cidade = dataCliente.ds_cidade;
            chatAtivo.value.ds_uf = dataCliente.ds_uf;
            chatAtivo.value.ds_complemento = dataCliente.ds_complemento;
        }
        //
        await listaAtendimentos({ instance: chat.instance, telefone: chat.telefone });

        listMessages(chat, true);

        chatBox.value = true;
        await nextTick();
        await focusTextAreaMensagem();
    }

    async function fechaFiltrosInstancia() {
        isOpenFiltroInstancia.value = false;
    }

    const checkSituacao = (status) => {
        isFiltroChats.value = true;
        listaSituacoes.value.map((situacao) => {
            if (situacao.tp_status == status) {
                situacao.in_habilitado = !situacao.in_habilitado;
            }
        });
    };

    async function fechaFiltros() {
        if (!isFiltroChats.value) return;
        listaUltimosChats.value = [];
        loadingChats.value = false;
        hasMore.value = true;
        listaChats(true);
        isFiltroChats.value = false;
        isOpenFiltros.value = false;
    }

    async function alterar(instancia) {
        loading.value.show();
        let filtros = {
            id: instancia.id,
            in_stop_bot: instancia.in_stop_bot,
        };

        let result = await InstanciaServices.alterar(filtros);

        if (result.statuscode == 200) {
            if (instancia.in_stop_bot) {
                toast.warning('Bot parado!');
            } else {
                toast.success('Bot ativado!');
            }

            filtros = {
                instance: instancia.instance,
                in_stop_bot: instancia.in_stop_bot,
            };

            result = await AtendimentoServices.stopBotAtendimento(filtros);

            listaUltimosChats.value.forEach((element) => {
                if (element.instance == instancia.instance) {
                    element.in_stop_bot = instancia.in_stop_bot;
                }
            });
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    async function verificaStatusInstancias() {
        let instanciaOFF = false;
        let instanciaON = false;
        listaInstancias.value.forEach((instancia) => {
            if (instancia.status_connection == 'Desconectado') {
                instanciaOFF = true;
            }
            if (instancia.status_connection == 'Conectado') {
                instanciaON = true;
            }
        });

        if (instanciaOFF && instanciaON) {
            situacaoInstancias.value = 'alerta';
        } else if (!instanciaOFF && instanciaON) {
            situacaoInstancias.value = 'ok';
        } else if (!instanciaOFF && !instanciaON) {
            situacaoInstancias.value = 'parado';
        }
    }
    const onChangeBot = async (instancia, event) => {
        //
        instancia.in_stop_bot = !event.target.checked;
        await verificaStatusInstancias();
        const req = {
            id: instancia.id_instancia,
            instance: instancia.nameinstance,
            in_stop_bot: instancia.in_stop_bot,
        };
        await alterar(req);
    };
    const onSelecionado = async () => {
        await listaChats(false);
    };

    async function joinSala(instancia) {
        socket.emit('joinRoom', instancia);
    }
    // Crie uma função para fazer o upload do arquivo
    async function uploadFile(file) {
        //
        const formData = new FormData();
        formData.append('file', file);
        formData.append('ds_caminho', `/uploads/${chatAtivo.value.instance}/send/images`);
        //

        try {
            const response = await axios.post(hosts.api + '/atendimento/uploadArquivos/v1', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            console.error(error);
        }
    }
    async function openFileInput() {
        fileInputDoc.value.click();
    }
    async function openFileInputAudio() {
        fileInput.value.click();
    }
    async function handleDrop(event) {
        loading.value.show();
        event.preventDefault();
        const formData = new FormData(); // Criar FormData fora do loop
        formData.append('ds_pasta', 'send');
        formData.append('is_category', true);
        formData.append('cd_estabelecimento', chatAtivo.value.cd_estabelecimento);

        if (event?.dataTransfer?.items) {
            formData.append('instance', chatAtivo.value.instance);
            // Processar cada item solto
            for (let i = 0; i < event.dataTransfer.items.length; i++) {
                // Verificar se é um arquivoURL_IMAGENSFLUXOdotenv
                if (event.dataTransfer.items[i].kind === 'file') {
                    const file = event.dataTransfer.items[i].getAsFile();
                    formData.append('files', file); // Adicionar arquivo ao FormData
                }
            }
        } else if (event.target.files) {
            formData.append('instance', chatAtivo.value.instance);
            for (const file of event.target.files) {
                formData.append('files', file); // Adicionar arquivo ao FormData
            }
        }
        let result = await UploadServices.upload(formData);
        if (result.statuscode == 200) {
            for (const data of result.data) {
                for (const file of data.files) {
                    if (file.category == 'images') {
                        const respSendImage = await sendImage(file);
                    }
                }
            }
        }

        loading.value.hide();
    }
    function dateTime(messageTimestamp, type) {
        const date = new Date(messageTimestamp * 1000);
        date.setUTCHours(date.getUTCHours() - 3);
        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Adicionando 1 porque os meses começam em zero
        const day = String(date.getUTCDate()).padStart(2, '0');
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const seconds = String(date.getUTCSeconds()).padStart(2, '0');
        let resp;
        if (type == 'date') {
            resp = `${year}-${month}-${day}`;
        } else if (type == 'datetime') {
            resp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        if (type == 'now') {
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            resp = `${hours}:${minutes}:${seconds}`;
        } else {
            resp = `${hours}:${minutes}:${seconds}`;
        }
        return resp;
    }
    async function generateSHA1Hash(dataString) {
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(dataString);
        const hashBuffer = await crypto.subtle.digest('SHA-1', dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map((byte) => byte.toString(16).padStart(2, '0')).join('');
        return hashHex; // Obtém os primeiros 10 caracteres do hash como código único
    }
    async function novoAtendimento(mensagem, idunico) {
        let reqObj = {
            cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
            cd_atendente: codUsuario.value,
            instance: chatAtivo.value.instance,
            nr_telefone: chatAtivo.value.telefone,
            ds_nome: chatAtivo.value.push_name,
            ds_mensagem: chatAtivo.value.ds_mensagem,
            ds_foto: chatAtivo.value.url_profile_picture,
            cd_contato: chatAtivo.value.cd_contato,
            mensagem: mensagem,
            idunico: idunico,
            nr_hash: nrHash.value,
            cd_departamento:
                usuarioDepartamentos.value.length == 1 ? usuarioDepartamentos.value[0].cd_departamento : undefined,
        };
        //
        return await AtendimentoServices.novo(reqObj);
    }
    // async function sendMessage(event) {
    //     if (dropdownVisible.value) {
    //         return;
    //     }
    //     if (event.keyCode === 13 && event.ctrlKey) {
    //         document.execCommand('insertText', false, '\n');
    //         return;
    //     }

    //     const messageContent = messageInput.value; //.textContent;
    //     if (messageContent == '') return;

    //     const date = dateTime(new Date(), 'datetime');
    //     const dataString = `${date}${chatAtivo.value.telefone}`;
    //     //

    //     let idunico;
    //     idunico = await generateSHA1Hash(dataString);
    //     let dataCRM;
    //     let isNovoAtendimento = false;
    //     //let isCRM = false;
    //     if (chatAtivo.value.cd_atendimento == undefined || chatAtivo.value.cd_atendimento == null) {
    //         const respNovoAtend = await novoAtendimento(messageContent, idunico);
    //         if (respNovoAtend.statuscode != 200) {
    //             toast.error(respNovoAtend.message);

    //             return;
    //         }
    //         isNovoAtendimento = true;
    //         //isCRM = true;
    //         chatAtivo.value.cd_atendimento = respNovoAtend.data[0].cd_atendimento;
    //         chatAtivo.value.tp_status = 'Em Atendimento';
    //         chatAtivo.value.ds_hash = respNovoAtend.data[0].nr_hash;
    //         chatAtivo.value.nr_hash = nrHash.value;

    //         filtroStatus.value.emAtendimento = false;
    //         filtroStatus.value.emAndamento = true;
    //         filtroStatus.value.contatos = false;
    //         filtroStatus.value.status = statusEmAndamentoCRM.value;
    //         filtroStatus.value.arStatus = statusEmAndamentoCRM.value.split(',');
    //
    //     }

    //     let tpStatus = chatAtivo.value.tp_status;

    //     //console.logQ('🚀 ~ Chat.vue:1393 ~ sendMessage ~ data.chatAtivo.value:', chatAtivo.value);

    //     if (modulos.in_crm && !isNovoAtendimento && tpStatus == 'Fila') {
    //         // isCRM = true;
    //         dataCRM = {
    //             tp_status: tpStatus,
    //             cd_atendimento: chatAtivo.value.cd_atendimento,
    //             cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
    //             cd_atendente: codUsuario.value,
    //             nm_atendente: nmUsuario.value,
    //             ds_comentario: chatAtivo.value.ds_comentario,
    //         };
    //         //const resp =
    //         await AtendimentoServices.iniciar(dataCRM);
    //         //
    //     }

    //     if (tpStatus == 'Fila') {
    //         tpStatus = tpStatus == 'Fila' ? 'Iniciado' : chatAtivo.value.tp_status;
    //         chatAtivo.value.tp_status = 'Em Atendimento';
    //         if (dataCRM.tp_status == 'Fila') {
    //             dataCRM.tp_status = tpStatus;
    //         }

    //         filtroStatus.value.emAtendimento = false;
    //         filtroStatus.value.emAndamento = true;
    //     }

    //     let dataText = {
    //         origem: 'chat',
    //         telefone: chatAtivo.value.telefone,
    //         instance: chatAtivo.value.instance,
    //         mensagem: messageContent,
    //         event: 'messages.upsert',
    //         idunico: idunico,
    //         nr_hash: chatAtivo.value.nr_hash,
    //         //quando é enviado na fila e não direto
    //         //isso garante se o whatsapp estiver fora
    //         //e quando voltar ele manda
    //         in_rabbitmq: true,
    //         tp_etapachat: chatAtivo.value?.tp_etapachat,
    //         tp_situacao: chatAtivo.value?.tp_situacao,
    //         tp_status: chatAtivo.value?.tp_status,
    //         dataCRM: dataCRM,
    //     };

    //     messageInput.value = null; //.textContent = null;

    //     const now = new Date();
    //     const time = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

    //     let data = {
    //         origem: 'chat',
    //         message: 'Sucesso',
    //         event: 'messages.upsert',
    //         instance: chatAtivo.value.instance,
    //         nome: chatAtivo.value.nome,
    //         from_me: true,
    //         mensagem: messageContent,
    //         message_timestamp: now,
    //         horario: time,
    //         telefone: chatAtivo.value.telefone,
    //         message_id: undefined,
    //         remote_jid: chatAtivo.value.telefone + '@s.whatsapp.net',
    //         url_profile_picture: chatAtivo.value.url_profile_picture,
    //         type_message: 'textMessage',
    //         cd_atendimento: chatAtivo.value.cd_atendimento,
    //         ds_hash: chatAtivo.value.ds_hash,
    //         nr_hash: chatAtivo.value.nr_hash,
    //         idunico: idunico,
    //         tp_status_atendimento: tpStatus,
    //         cd_atendimento: chatAtivo.value.cd_atendimento,
    //         cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
    //         cd_atendente: codUsuario.value,
    //         nm_atendente: nmUsuario.value,
    //         // ds_comentario: chatAtivo.value.ds_comentario,
    //     };

    //
    //     await loadMessages(data);
    //     const result = listaUltimosChats.value.find((chat) => chat.telefone === chatAtivo.value.telefone);
    //     if (result) {
    //         //result.index = 0;
    //         result.mensagem = messageContent;
    //         result.message_id = data.message_id;
    //         result.message_timestamp = data.message_timestamp;
    //         result.horario = data.horario;
    //         result.in_delete = data.in_delete;
    //         result.nm_atendente = data.nm_atendente;
    //         result.ds_departamento = data.ds_departamento;
    //     }

    //     await sortChatsByTimestamp();
    //
    //     const resultMessage = await WhatsAppServices.sendMessage(dataText);
    //
    //     if (resultMessage.statuscode == 200) {
    //         const dataMessage = resultMessage.data[0];
    //
    //         let result = mensagens.value.find((chat) => chat.idunico == dataMessage.idunico);

    //         if (result) {
    //             result.status_message = 'Enviado';
    //             if (dataMessage.message_id) {
    //                 result.message_id = dataMessage.message_id;
    //             }
    //             // const index = mensagens.value.indexOf(result);
    //             // mensagens.value[index] = result;
    //         }
    //     }
    // }

    async function sendMessage(event) {
        if (dropdownVisible.value) {
            return;
        }
        if (event.keyCode === 13 && event.ctrlKey) {
            document.execCommand('insertText', false, '\n');
            return;
        }

        let messageContent;

        if (modulos.in_crm) {
            messageContent = `*${nmUsuario.value}:*\n${messageInput.value}`; //.textContent;
        } else {
            messageContent = messageInput.value.trim(); // Remover espaços em branco
        }

        if (messageContent == '') return;

        const date = dateTime(new Date(), 'datetime');
        const dataString = `${date}${chatAtivo.value.telefone}`;

        let idunico;
        idunico = await generateSHA1Hash(dataString);
        let dataCRM;
        let isNovoAtendimento = false;
        //let statusAnterior = chatAtivo.value.tp_status; // Salvar status anterior
        let respNovoAtend;
        if (chatAtivo.value.cd_atendimento == undefined || chatAtivo.value.cd_atendimento == null) {
            respNovoAtend = await novoAtendimento(messageContent, idunico);
            //
            if (respNovoAtend.statuscode != 200) {
                toast.error(respNovoAtend.message);
                //
                return;
            }
            isNovoAtendimento = true;
            chatAtivo.value.cd_atendimento = respNovoAtend.data[0].cd_atendimento;
            chatAtivo.value.tp_status = 'Em Atendimento';
            chatAtivo.value.ds_hash = respNovoAtend.data[0].nr_hash;
            chatAtivo.value.nr_hash = nrHash.value;
            chatAtivo.value.cd_departamento =
                usuarioDepartamentos.value.length == 1 ? usuarioDepartamentos.value[0].cd_departamento : undefined;
            chatAtivo.value.ds_departamento =
                usuarioDepartamentos.value.length == 1 ? usuarioDepartamentos.value[0].ds_departamento : undefined;

            filtroStatus.value.emAtendimento = false;
            filtroStatus.value.emAndamento = true;
            filtroStatus.value.contatos = false;
            filtroStatus.value.status = statusEmAndamentoCRM.value;
            filtroStatus.value.arStatus = statusEmAndamentoCRM.value.split(',');
        }

        let tpStatus = chatAtivo.value.tp_status;

        if (
            (modulos.in_crm && !isNovoAtendimento && tpStatus == 'Fila') ||
            (modulos.in_crm && isNovoAtendimento && tpStatus == 'Em Atendimento')
        ) {
            dataCRM = {
                tp_status: tpStatus,
                cd_atendimento: chatAtivo.value.cd_atendimento,
                cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
                cd_atendente: codUsuario.value,
                nm_atendente: nmUsuario.value,
                ds_comentario: chatAtivo.value.ds_comentario,
                cd_departamento:
                    usuarioDepartamentos.value.length == 1 ? usuarioDepartamentos.value[0].cd_departamento : undefined,
                ds_departamento:
                    usuarioDepartamentos.value.length == 1 ? usuarioDepartamentos.value[0].ds_departamento : undefined,
            };

            if (modulos.in_crm && !isNovoAtendimento && tpStatus == 'Fila') {
                await AtendimentoServices.iniciar(dataCRM);
            } else {
                dataCRM.tp_status = 'Iniciado';
            }
        }

        let mudouDeAba = false; // Flag para saber se mudou de aba

        if (tpStatus == 'Fila') {
            tpStatus = tpStatus == 'Fila' ? 'Iniciado' : chatAtivo.value.tp_status;
            chatAtivo.value.tp_status = 'Em Atendimento';
            if (dataCRM && dataCRM.tp_status == 'Fila') {
                dataCRM.tp_status = tpStatus;
            }

            filtroStatus.value.emAtendimento = false;
            filtroStatus.value.emAndamento = true;
            mudouDeAba = true; // Marcamos que mudou de aba
        }

        let dataText = {
            origem: 'chat',
            telefone: chatAtivo.value.telefone,
            instance: chatAtivo.value.instance,
            mensagem: messageContent,
            event: 'messages.upsert',
            idunico: idunico,
            nr_hash: chatAtivo.value.nr_hash,
            in_rabbitmq: true,
            from_me: true,
            nome: chatAtivo.value.nome,
            tp_etapachat: chatAtivo.value?.tp_etapachat,
            tp_situacao: chatAtivo.value?.tp_situacao,
            tp_status: chatAtivo.value?.tp_status,
            tp_status_atendimento: isNovoAtendimento ? 'Iniciado' : undefined,
            remote_jid: `${chatAtivo.value.telefone.nr_telefone}@s.whatsapp.net`,
            // cd_atendente: isNovoAtendimento ? codUsuario.value : undefined,
            // nm_atendente: isNovoAtendimento ? nmUsuario.value : undefined,
            // cd_atendimento: isNovoAtendimento ? respNovoAtend.statuscode == 200 ? respNovoAtend.data[0].cd_atendimento : undefined : undefined,
            // message_timestamp: localISOString,
            dataCRM: dataCRM,
        };

        // req.body = {
        //   event: 'messages.upsert',
        //   tp_status_atendimento: 'Iniciado',
        //   cd_atendente: body.cd_atendente,
        //   telefone: body.nr_telefone,
        //   instance: body.instance,
        //   cd_estabelecimento: body.cd_estabelecimento,
        //   cd_atendimento: resp.data[0].cd_atendimento,
        //   message_timestamp: localISOString,
        //   from_me: true,
        //   nome: body.ds_nome,
        //   mensagem: body.mensagem,
        //   idunico: body.idunico,
        //   remote_jid: `${body.nr_telefone}@s.whatsapp.net`,
        // };
        // await MessagesDB.incluir(req);

        // req.body = {
        //   cd_atendimento: resp.data[0].cd_atendimento,
        //   message_timestamp: localISOString,
        //   url_profile_picture: body.ds_foto,
        //   cd_estabelecimento: body.cd_estabelecimento,
        //   telefone: body.nr_telefone,
        //   from_me: true,
        //   nome: body.ds_nome,
        //   mensagem: body.mensagem,
        //   idunico: body.idunico,
        //   message_id: body.idunico,
        //   remote_jid: `${body.nr_telefone}@s.whatsapp.net`,
        //   event: 'messages.upsert',
        //   tp_status_atendimento: 'Inicio',
        //   cd_atendente: body.cd_atendente,
        //   instance: body.instance,
        // };
        // await MessagesDB.incluiUltimMensagem(req);

        messageInput.value = '';

        // Reset textarea height after sending message
        if (textAreaMensagem.value) {
            textAreaMensagem.value.style.height = '40px';
            textAreaMensagem.value.style.overflowY = 'hidden';
        }

        const now = new Date();
        const time = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

        let data = {
            origem: 'chat',
            message: 'Sucesso',
            event: 'messages.upsert',
            instance: chatAtivo.value.instance,
            nome: chatAtivo.value.nome,
            from_me: true,
            mensagem: messageContent,
            message_timestamp: now,
            horario: time,
            telefone: chatAtivo.value.telefone,
            message_id: undefined,
            remote_jid: chatAtivo.value.telefone + '@s.whatsapp.net',
            url_profile_picture: chatAtivo.value.url_profile_picture,
            type_message: 'textMessage',
            ds_hash: chatAtivo.value.ds_hash,
            nr_hash: chatAtivo.value.nr_hash,
            idunico: idunico,
            tp_status_atendimento: tpStatus,
            cd_atendimento: chatAtivo.value.cd_atendimento,
            cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
            cd_atendente: codUsuario.value,
            nm_atendente: nmUsuario.value,
            cd_departamento: chatAtivo.value.cd_departamento,
            ds_departamento: chatAtivo.value.ds_departamento,
        };

        if (!isNovoAtendimento) {
            await loadMessages(data);
        }

        // Atualizar o chat na lista atual
        const result = listaUltimosChats.value.find((chat) => chat.telefone === chatAtivo.value.telefone);
        if (result) {
            result.mensagem = messageContent;
            result.message_id = data.message_id;
            result.message_timestamp = data.message_timestamp;
            result.horario = data.horario;
            result.in_delete = data.in_delete;
            result.nm_atendente = data.nm_atendente;
            result.ds_departamento = data.ds_departamento;
            result.tp_status = chatAtivo.value.tp_status; // Atualizar status
        }

        // Se mudou de aba (de Fila para Em Atendimento), atualizar o cache e a lista
        if (modulos.in_crm && (mudouDeAba || isNovoAtendimento)) {
            // Atualizar o cache removendo da aba anterior e adicionando na nova
            if (result) {
                // Remove da aba "Em Atendimento" (Fila)
                const indexEmAtendimento = cacheAtendimentos.value.emAtendimento.findIndex(
                    (chat) => chat.telefone === result.telefone && chat.instance === result.instance
                );
                if (indexEmAtendimento !== -1) {
                    cacheAtendimentos.value.emAtendimento.splice(indexEmAtendimento, 1);
                }

                // Adiciona na aba "Em Andamento" (Atendimento)
                const chatAtualizado = { ...result };
                const existeEmAndamento = cacheAtendimentos.value.emAndamento.find(
                    (chat) => chat.telefone === result.telefone && chat.instance === result.instance
                );

                if (!existeEmAndamento) {
                    cacheAtendimentos.value.emAndamento.unshift(chatAtualizado);
                    // Ordena por timestamp
                    cacheAtendimentos.value.emAndamento.sort(
                        (a, b) => new Date(b.message_timestamp) - new Date(a.message_timestamp)
                    );
                }
            } else if (respNovoAtend.statuscode == 200) {
                // Se não encontrou o chat na lista, adiciona como novo
                let data = respNovoAtend.data[0];
                data.nome = chatAtivo.value.nome;
                data.url_profile_picture = data.ds_foto;
                data.mensagem = messageContent;
                data.telefone = data.nr_telefone;
                data.cd_atendente = codUsuario.value;
                data.nm_atendente = nmUsuario.value;
                const novoChat = { ...data, tp_status: 'Em Andamento' };

                cacheAtendimentos.value.emAndamento.unshift(novoChat);
            }

            // Recarregar a lista atual com os dados do cache de "Em Andamento"
            // pois agora estamos visualizando essa aba
            listaUltimosChats.value = [...cacheAtendimentos.value.emAndamento];
        }

        await sortChatsByTimestamp();

        const resultMessage = await WhatsAppServices.sendMessage(dataText);

        if (resultMessage.statuscode == 200) {
            const dataMessage = resultMessage.data[0];
            //
            let result = mensagens.value.find((chat) => chat.idunico == dataMessage.idunico);

            if (result) {
                result.status_message = 'Enviado';
                if (dataMessage.message_id) {
                    result.message_id = dataMessage.message_id;
                }
            }
        }

        if (isNovoAtendimento) {
            await showChatBox(data, 0);
        }
    }

    async function sendDocument(obj) {
        let idunico;

        const date = dateTime(new Date(), 'datetime');
        const dataString = `${date}${chatAtivo.value.telefone}`;

        idunico = await generateSHA1Hash(dataString);

        let dataText = {
            telefone: chatAtivo.value.telefone,
            instance: chatAtivo.value.instance,
            nm_arquivo: obj.ds_caminho + '/' + obj.file_name,
            mediatype: 'document',
            fileName: obj.file_name,
            caption: '',
            idunico: idunico,
            tp_etapachat: chatAtivo.value?.tp_etapachat,
            tp_situacao: chatAtivo.value?.tp_situacao,
            tp_status: chatAtivo.value?.tp_status,
        };

        let reqObj = {
            origem: 'bot',
            message: 'Sucesso',
            event: 'messages.upsert',
            instance: chatAtivo.value.instance,
            nome: chatAtivo.value.nome,
            from_me: true,
            mensagem: '',
            message_timestamp: new Date(),
            horario: dateTime(new Date(), 'now'),
            telefone: chatAtivo.value.telefone,
            message_id: undefined,
            remote_jid: chatAtivo.value.telefone + '@s.whatsapp.net',
            url_profile_picture: chatAtivo.value.url_profile_picture,
            type_message: 'documentMessage',
            mimetype: undefined,
            file_sha256: undefined,
            file_length: undefined,
            nr_height: undefined,
            nr_width: undefined,
            media_key: undefined,
            jpeg_thumbnail: undefined,
            file_name: obj.file_name,
            url_midia: obj.url_midia,
            cd_atendimento: chatAtivo.value.cd_atendimento,
            ds_hash: chatAtivo.value.ds_hash,
            idunico: idunico,
        };

        await loadMessages(reqObj);

        listaUltimosChats.value[chatAtivo.value.index].mensagem = '';
        listaUltimosChats.value[chatAtivo.value.index].message_id = reqObj.message_id;
        listaUltimosChats.value[chatAtivo.value.index].message_timestamp = reqObj.message_timestamp;
        listaUltimosChats.value[chatAtivo.value.index].horario = reqObj.horario;
        listaUltimosChats.value[chatAtivo.value.index].in_delete = reqObj.in_delete;

        await sortChatsByTimestamp();

        const resultMessage = await WhatsAppServices.sendDocument(dataText);

        //

        if (resultMessage.statuscode == 200) {
            const dataMessage = resultMessage.data[0];

            //

            let result = mensagens.value.find((chat) => chat.idunico == dataMessage.idunico);

            if (result) {
                result.status_message = 'Enviado';
                result.message_id = dataMessage.message_id;
                result.mimetype = dataMessage.mimetype;
                result.file_sha256 = dataMessage?.file_sha256;
                result.file_length = dataMessage?.file_length;
                result.media_key = dataMessage?.media_key;
                const index = mensagens.value.indexOf(result);
                mensagens.value[index] = result;
            }
        } else {
        }
    }
    async function sendAudio(obj) {
        let idunico;

        const date = dateTime(new Date(), 'datetime');
        const dataString = `${date}${chatAtivo.value.telefone}`;

        idunico = await generateSHA1Hash(dataString);

        let dataText = {
            telefone: chatAtivo.value.telefone,
            instance: chatAtivo.value.instance,
            nm_arquivo: obj.ds_caminho + '/' + obj.file_name,
            idunico: idunico,
            tp_etapachat: chatAtivo.value?.tp_etapachat,
            tp_situacao: chatAtivo.value?.tp_situacao,
            tp_status: chatAtivo.value?.tp_status,
        };

        let reqObj = {
            origem: 'bot',
            message: 'Sucesso',
            event: 'messages.upsert',
            instance: chatAtivo.value.instance,
            nome: chatAtivo.value.nome,
            from_me: true,
            mensagem: '',
            message_timestamp: new Date(),
            horario: dateTime(new Date(), 'now'),
            telefone: chatAtivo.value.telefone,
            message_id: undefined,
            remote_jid: chatAtivo.value.telefone + '@s.whatsapp.net',
            url_profile_picture: chatAtivo.value.url_profile_picture,
            type_message: 'audioMessage',
            mimetype: undefined,
            file_sha256: undefined,
            file_length: undefined,
            nr_height: undefined,
            nr_width: undefined,
            media_key: undefined,
            jpeg_thumbnail: undefined,
            file_name: obj.file_name,
            url_midia: obj.url_midia,
            cd_atendimento: chatAtivo.value.cd_atendimento,
            ds_hash: chatAtivo.value.ds_hash,
            idunico: idunico,
        };

        await loadMessages(reqObj);

        listaUltimosChats.value[chatAtivo.value.index].mensagem = '';
        listaUltimosChats.value[chatAtivo.value.index].message_id = reqObj.message_id;
        listaUltimosChats.value[chatAtivo.value.index].message_timestamp = reqObj.message_timestamp;
        listaUltimosChats.value[chatAtivo.value.index].horario = reqObj.horario;
        listaUltimosChats.value[chatAtivo.value.index].in_delete = reqObj.in_delete;

        await sortChatsByTimestamp();

        const resultMessage = await WhatsAppServices.sendAudio(dataText);

        //

        if (resultMessage.statuscode == 200) {
            /* if (resultMessage?.data.key?.id != '') {
                                 await incluiMessages(resultMessage.data, obj);
                               } else {
                               }*/

            const dataMessage = resultMessage.data[0];

            //

            let result = mensagens.value.find((chat) => chat.idunico == dataMessage.idunico);

            if (result) {
                result.status_message = 'Enviado';
                result.message_id = dataMessage.message_id;

                result.mimetype = dataMessage.mimetype;
                result.file_sha256 = dataMessage?.file_sha256;
                result.file_length = dataMessage?.file_length;
                result.media_key = dataMessage?.media_key;

                const index = mensagens.value.indexOf(result);
                mensagens.value[index] = result;
            }
        } else {
        }
    }
    async function sendImage(obj) {
        //
        let idunico;

        const date = dateTime(new Date(), 'datetime');
        const dataString = `${date}${chatAtivo.value.telefone}`;

        idunico = await generateSHA1Hash(dataString);

        let dataText = {
            telefone: chatAtivo.value.telefone,
            instance: chatAtivo.value.instance,
            nm_arquivo: obj.ds_caminho + '/' + obj.file_name,
            url: obj.url,
            idunico: idunico,
            event: 'messages.upsert',
            idunico: idunico,
            nr_hash: chatAtivo.value.nr_hash,
            in_rabbitmq: true,
            tp_etapachat: chatAtivo.value?.tp_etapachat,
            tp_situacao: chatAtivo.value?.tp_situacao,
            tp_status: chatAtivo.value?.tp_status,
        };

        let reqObj = {
            origem: 'bot',
            message: 'Sucesso',
            event: 'messages.upsert',
            instance: chatAtivo.value.instance,
            nome: chatAtivo.value.nome,
            from_me: true,
            mensagem: '',
            message_timestamp: new Date(),
            horario: dateTime(new Date(), 'now'),
            telefone: chatAtivo.value.telefone,
            message_id: undefined,
            remote_jid: chatAtivo.value.telefone + '@s.whatsapp.net',
            url_profile_picture: chatAtivo.value.url_profile_picture,
            type_message: 'imageMessage',
            mimetype: undefined,
            file_sha256: undefined,
            file_length: undefined,
            nr_height: undefined,
            nr_width: undefined,
            media_key: undefined,
            jpeg_thumbnail: undefined,
            file_name: obj.file_name,
            url_midia: obj.url_midia,
            cd_atendimento: chatAtivo.value.cd_atendimento,
            ds_hash: chatAtivo.value.ds_hash,
            idunico: idunico,
        };

        await loadMessages(reqObj);

        listaUltimosChats.value[chatAtivo.value.index].mensagem = '';
        listaUltimosChats.value[chatAtivo.value.index].message_id = reqObj.message_id;
        listaUltimosChats.value[chatAtivo.value.index].message_timestamp = reqObj.message_timestamp;
        listaUltimosChats.value[chatAtivo.value.index].horario = reqObj.horario;
        listaUltimosChats.value[chatAtivo.value.index].in_delete = reqObj.in_delete;

        await sortChatsByTimestamp();

        const resultMessage = await WhatsAppServices.sendImage(dataText);
        //

        if (resultMessage.statuscode == 200) {
            const dataMessage = resultMessage.data[0];
            let result = mensagens.value.find((chat) => chat.idunico == dataMessage.idunico);

            if (result) {
                result.status_message = 'Enviado';
                result.message_id = dataMessage.message_id;

                result.mimetype = dataMessage.mimetype;
                result.file_sha256 = dataMessage?.file_sha256;
                result.file_length = dataMessage?.file_length;

                result.media_key = dataMessage?.media_key;

                const index = mensagens.value.indexOf(result);
                mensagens.value[index] = result;
            }
        } else {
        }
    }

    function atualizaContadoresNaoLidos() {
        let qtEmAtendimento = 0;
        let qtEmAndamento = 0;

        contatosNaoLidos.value.forEach((info, key) => {
            if (info.qt > 0) {
                if (info.tp_etapachat === 'Falar com Atendente') {
                    // Se for "Falar com Atendente", verifica o status para contar em uma ou ambas as abas
                    if (['Pedido Confirmado', 'Pedido Pronto', 'Saiu para Entrega'].includes(info.tp_status)) {
                        qtEmAtendimento++;
                        qtEmAndamento++;
                    } else {
                        qtEmAtendimento++;
                    }
                } else {
                    if (modulos.in_crm) {
                        // Se for CRM, verifica o status para contar em uma ou ambas as abas
                        if (
                            statusEmAtendimentoCRM.value
                                .split(',')
                                .map((s) => s.replace(/'/g, '').trim())
                                .includes(info.tp_status)
                        ) {
                            qtEmAtendimento++;
                        } else if (
                            statusEmAndamentoCRM.value
                                .split(',')
                                .map((s) => s.replace(/'/g, '').trim())
                                .includes(info.tp_status)
                        ) {
                            qtEmAndamento++;
                        }
                    } else {
                        // Se não for CRM, conta normalmente

                        if (
                            [
                                'Fila',
                                'Em Atendimento',
                                'Em Pedido',
                                'Em Pagamento',
                                'Falar com Atendente',
                                'Pedido Realizado',
                            ].includes(info.tp_status)
                        ) {
                            qtEmAtendimento++;
                        } else if (
                            ['Pedido Confirmado', 'Pedido Pronto', 'Saiu para Entrega'].includes(info.tp_status)
                        ) {
                            qtEmAndamento++;
                        }
                    }
                }
            }
        });

        filtroStatus.value.qtEmAtendimento = qtEmAtendimento;
        filtroStatus.value.qtEmAndamento = qtEmAndamento;
    }

    async function listaChats(zeraPage) {
        inSituacaoBot.value = true;

        const lstSituacao = listaSituacoes.value
            .filter((status) => status.in_habilitado)
            .map((element) => `'${element.tp_status}'`)
            .join(',');

        let lstInstancias;
        if (listaInstancias.value) {
            lstInstancias = listaInstancias.value
                .filter((element) => element.in_selecionado)
                .map((element) => `'${element.nameinstance}'`)
                .join(',');
        }

        if (loadingChats.value || !hasMore.value) return; // Evita múltiplas requisições simultâneas
        loadingChats.value = true; // Ativa o card de carregamento
        await nextTick();
        if (!page.value) page.value = 0; // Inicializa a primeira página como 0
        // Calcular o offset baseado na página atual

        let filtros = {
            instance: lstInstancias,
            tp_status: lstSituacao ? lstSituacao : filtroStatus.value.status,
            in_mensagens_naolidas: inMsgNaoLida.value,
            //tp_chats: filtroStatus.value.emAtendimento ? 'emAtendimento' : 'finalizados',
            //limit: limit.value, // Limite de itens por página
            //offset: offset, // Quantidade de itens já carregados (offset)
        };

        if (filtroStatus.value.emAndamento) {
            if (zeraPage) {
                page.value = 0;
            }
            const offset = page.value * limit.value;

            filtros.limit = limit.value;
            filtros.offset = offset;
        } else if (filtroStatus.value.contatos) {
            limit.value = 100;
            const offset = page.value * limit.value;

            filtros.limit = limit.value;
            filtros.offset = offset;
        } else {
            if (zeraPage) {
                page.value = 0;
            }
            const offset = page.value * limit.value;

            filtros.limit = limit.value;
            filtros.offset = offset;
        }

        if (usuarioDepartamentos.value && usuarioDepartamentos.value.length > 0) {
            filtros.departamentos = usuarioDepartamentos.value.map((dep) => dep.cd_departamento).join(',');
        }

        try {
            const respChats = await MessagesServices.listarUltimaMensagemContato(filtros);
            console.log('🚀 ~ Chat.vue:2269 ~ listaChats ~ respChats:', respChats);

            if (respChats.statuscode === 200) {
                const dataChats = respChats.data[0];
                //

                // Atualiza o mapa de não lidos com os dados da API
                dataChats.chats.forEach((chat) => {
                    const key = `${chat.telefone}_${chat.instance}`;
                    contatosNaoLidos.value.set(key, {
                        qt: chat.qt_messages_notread,
                        tp_status: chat.tp_status,
                        tp_etapachat: chat.tp_etapachat,
                    });
                });

                // Atualiza contadores visuais
                atualizaContadoresNaoLidos();

                // Atualiza com dados do backend
                filtroStatus.value.qtEmAtendimento = modulos.in_crm
                    ? dataChats.atendimentoNaoLido.qt_fila_crm
                    : dataChats.atendimentoNaoLido.qt_ematendimento;
                filtroStatus.value.qtEmAndamento = modulos.in_crm
                    ? dataChats.atendimentoNaoLido.qt_emandamento_crm
                    : dataChats.atendimentoNaoLido.qt_emandamento;
                //

                const novosChats = dataChats.chats;

                if (novosChats?.length < limit.value) {
                    hasMore.value = false;
                }

                // Separar chats no cache baseado nos status do CRM
                if (modulos.in_crm) {
                    // Status para aba "Em Atendimento" (Fila)
                    const statusEmAtendimentoArray = statusEmAtendimentoCRM.value
                        .split(',')
                        .map((item) => item.trim().replace(/'/g, ''));

                    // Status para aba "Em Andamento" (Atendimento)
                    const statusEmAndamentoArray = statusEmAndamentoCRM.value
                        .split(',')
                        .map((item) => item.trim().replace(/'/g, ''));

                    // Separar chats no cache
                    cacheAtendimentos.value.emAtendimento = novosChats.filter((chat) =>
                        statusEmAtendimentoArray.includes(chat.tp_status)
                    );

                    cacheAtendimentos.value.emAndamento = novosChats.filter((chat) =>
                        statusEmAndamentoArray.includes(chat.tp_status)
                    );

                    // Filtrar lista visível baseado na aba selecionada
                    if (filtroStatus.value.emAtendimento) {
                        // Aba "Em Atendimento" mostra status: 'Fila','Transferido'
                        listaUltimosChats.value = zeraPage
                            ? cacheAtendimentos.value.emAtendimento
                            : [...listaUltimosChats.value, ...cacheAtendimentos.value.emAtendimento];
                    } else if (filtroStatus.value.emAndamento) {
                        // Aba "Em Andamento" mostra status: 'Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente','Transferido'
                        listaUltimosChats.value = zeraPage
                            ? cacheAtendimentos.value.emAndamento
                            : [...listaUltimosChats.value, ...cacheAtendimentos.value.emAndamento];
                    } else {
                        // Para outras abas (como Finalizados), mostrar todos
                        listaUltimosChats.value = zeraPage ? novosChats : [...listaUltimosChats.value, ...novosChats];
                    }
                } else {
                    // Lógica existente para módulo não-CRM
                    listaUltimosChats.value = zeraPage ? novosChats : [...listaUltimosChats.value, ...novosChats];
                }

                //

                // Find the maximum dt_ultimaimportacao date, or use current date if none exists
                maiorDtUltimaImportacao.value = listaUltimosChats.value.reduce((maxDate, chat) => {
                    // Skip chats without dt_ultimaimportacao or with invalid date
                    if (!chat.dt_ultimaimportacao) return maxDate;

                    const currentDate = new Date(chat.dt_ultimaimportacao);
                    // Verify it's a valid date before comparing
                    if (isNaN(currentDate.getTime())) return maxDate;

                    return currentDate > maxDate ? currentDate : maxDate;
                }, new Date(0)); // Start with "epoch" date

                // If no valid dt_ultimaimportacao was found (still at epoch date), use current date
                if (maiorDtUltimaImportacao.value.getTime() === 0) {
                    maiorDtUltimaImportacao.value = new Date();
                }

                page.value++;
            }
        } catch (error) {
            console.error('Erro ao carregar chats:', error);
        } finally {
            loadingChats.value = false;
        }
    }
    // Computed properties
    const chatsNaoPedidos = computed(() => {
        return listaUltimosChats.value.filter((chat) => chat.tp_status !== 'Pedido Realizado');
    });

    const chatsPedidosRealizados = computed(() => {
        //
        return listaUltimosChats.value.filter((chat) => chat.tp_status === 'Pedido Realizado');
    });

    const chatsNaoLidos = computed(() => {
        //
        // Verifica se listaUltimosChats é um array e filtra os chats não lidos
        if (Array.isArray(listaUltimosChats.value)) {
            return listaUltimosChats.value.filter((chat) => chat.qt_messages_notread > 0).length;
        }
        return 0; // Retorna 0 se listaUltimosChats não for um array
    });

    // Usando um watch para depurar (opcional)
    watch(chatsNaoLidos, (newValue) => {
        if (filtroStatus.value.emAtendimento) {
            filtroStatus.value.qtEmAtendimento = newValue;
        } else if (filtroStatus.value.emAndamento) {
            filtroStatus.value.qtEmAndamento = newValue;
        }
    });

    // const maiorDtUltimaImportacao = computed(() => {
    //     const pedidosRealizados = chatsPedidosRealizados.value;
    //     if (pedidosRealizados.length === 0) return null;

    //     return pedidosRealizados.reduce((maxDate, chat) => {
    //         const currentDate = new Date(chat.dt_ultimaimportacao);
    //         return currentDate > maxDate ? currentDate : maxDate;
    //     }, new Date(pedidosRealizados[0].dt_ultimaimportacao));
    // });

    const passouDoisMinutos = computed(() => {
        const maiorData = maiorDtUltimaImportacao.value;
        if (!maiorData) return false;

        const agora = new Date(); // Data/hora atual
        const diffMs = agora - maiorData; // Diferença em milissegundos
        const diffMin = diffMs / (1000 * 60); // Converte para minutos
        return diffMin > 2; // Retorna true se passou mais de 2 minutos
    });

    function onScroll(event) {
        const container = event.target;
        if (container.scrollTop + container.clientHeight >= container.scrollHeight - 20) {
            listaChats(false); // Carrega mais contatos
        }
    }

    async function filtrarChats() {
        if (inFiltraContatos.value && filtroNome.value.length > 3) {
            inSituacaoBot.value = true;

            const lstSituacao = listaSituacoes.value
                .filter((status) => status.in_habilitado)
                .map((element) => `'${element.tp_status}'`)
                .join(',');

            let lstInstancias;
            if (listaInstancias.value) {
                lstInstancias = listaInstancias.value
                    .filter((element) => element.in_selecionado)
                    .map((element) => `'${element.nameinstance}'`)
                    .join(',');
            }

            if (loadingChats.value || !hasMore.value) return; // Evita múltiplas requisições simultâneas
            loadingChats.value = true; // Ativa o card de carregamento
            await nextTick();
            if (!page.value) page.value = 0; // Inicializa a primeira página como 0
            // Calcular o offset baseado na página atual

            let filtros = {
                instance: lstInstancias,
                tp_status: lstSituacao ? lstSituacao : filtroStatus.value.status,
                in_mensagens_naolidas: inMsgNaoLida.value,
                nm_contato: filtroNome.value.toLowerCase(),
                //tp_chats: filtroStatus.value.emAtendimento ? 'emAtendimento' : 'finalizados',
                //limit: limit.value, // Limite de itens por página
                //offset: offset, // Quantidade de itens já carregados (offset)
            };

            try {
                //logger.debug('listaChats > filtros: ', filtros);
                const respChats = await MessagesServices.listarUltimaMensagemContato(filtros);
                logger.debug('listaChats > listarUltimaMensagemContato: ', respChats);
                if (respChats.statuscode === 200) {
                    const dataChats = respChats.data[0];
                    //

                    filtroStatus.value.qtEmAtendimento = modulos.in_crm
                        ? dataChats.naoLidas.qt_ematendimento_crm
                        : dataChats.naoLidas.qt_ematendimento;
                    //filtroStatus.value.qtfinalizados = dataChats.naoLidas.qt_finalizado;
                    filtroStatus.value.qtEmAndamento = modulos.in_crm
                        ? dataChats.naoLidas.qt_emandamento_crm
                        : dataChats.naoLidas.qt_emandamento;

                    let novosChats = dataChats.chats;

                    // if (novosChats.length < limit.value) {
                    //   hasMore.value = false; // Não há mais dados se o número retornado for menor que o limite
                    // }

                    listaUltimosChats.value = novosChats;

                    //page.value += 1; // Incrementa a página atual
                }
            } catch (error) {
                logger.error('listaChats > Error loading chats:', error);
            } finally {
                loadingChats.value = false; // Desativa o carregamento
            }

            return;
        }

        if (listaUltimosChatsOld.value.length == 0) {
            listaUltimosChatsOld.value = listaUltimosChats.value;
        }

        if (filtroNome.value == '') {
            listaUltimosChats.value = listaUltimosChatsOld.value;
            listaUltimosChatsOld.value = [];
        } else {
            listaUltimosChats.value = listaUltimosChats.value.filter((chat) => {
                if (chat.nome != null) {
                    try {
                        const nome = chat.nome.normalize('NFKD'); // Normaliza a string
                        return nome.toLowerCase().includes(filtroNome.value.toLowerCase());
                    } catch (error) {
                        console.error('Erro ao processar nome:', chat.nome, error);
                        return false; // Exclui itens que causam erro
                    }
                }
            });
        }
    }

    function formatFileSize(bytes) {
        if (bytes < 1024) {
            return bytes + ' bytes';
        } else if (bytes < 1024 * 1024) {
            return (bytes / 1024).toFixed(2) + ' KB';
        } else {
            return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
        }
    }

    // const handleAtendimentoSelect = async (atendimento) => {
    //     const filtros = {
    //         instance: chatAtivo.value.instance,
    //         telefone: chatAtivo.value.telefone,
    //         cd_atendimento: atendimento.cd_atendimento,
    //         selectedAtendimento: true,
    //     };
    //     await listMessages(filtros, true);
    // };

    // Modifique a função handleAtendimentoSelect para usar uma abordagem otimizada
    // const handleAtendimentoSelect = async (atendimento) => {
    //     // Inverte o estado atual
    //     atendimento.open = !atendimento.open;

    //     // Se estiver abrindo o atendimento e não tiver carregado as mensagens ainda
    //     if (atendimento.open && (!atendimento.messagesLoaded || atendimento.messages?.length === 0)) {
    //         isLoadingMessages.value = true;

    //         try {
    //             // Use um setTimeout para dar tempo à transição começar
    //             // antes de fazer a requisição das mensagens
    //             setTimeout(async () => {
    //                 // Carregue as mensagens do atendimento
    //                 const messages = await loadAtendimentoMessages(atendimento.cd_atendimento);

    //                 // Atualize as mensagens do grupo atual sem causar re-renderização completa
    //                 const groupIndex = groupedMessages.value.findIndex(
    //                     (g) => g.atendimento?.cd_atendimento === atendimento.cd_atendimento
    //                 );

    //                 if (groupIndex !== -1) {
    //                     // Use Vue.set ou atribuição direta para evitar problemas de reatividade
    //                     groupedMessages.value[groupIndex].messages = messages;
    //                     groupedMessages.value[groupIndex].atendimento.messagesLoaded = true;
    //                 }

    //                 isLoadingMessages.value = false;
    //             }, 50);
    //         } catch (error) {
    //             console.error('Erro ao carregar mensagens:', error);
    //             isLoadingMessages.value = false;
    //         }
    //     }
    // };

    const handleAtendimentoSelect = async (atendimento) => {
        // Inverte o estado atual
        atendimento.open = !atendimento.open;

        // Se estiver abrindo o atendimento
        if (atendimento.open) {
            // Verificar se já tem mensagens carregadas
            const group = groupedMessages.value.find(
                (g) => g.atendimento?.cd_atendimento === atendimento.cd_atendimento
            );

            // Se não tiver mensagens carregadas, carregar
            if (!group?.messages || group.messages.length === 0 || !atendimento.messagesLoaded) {
                isLoadingMessages.value = true;

                try {
                    await loadAtendimentoMessages(atendimento.cd_atendimento);
                    atendimento.messagesLoaded = true;
                } catch (error) {
                    console.error('Erro ao carregar mensagens:', error);
                } finally {
                    isLoadingMessages.value = false;
                }
            }
        }
    };

    // const loadAtendimentoMessages = async (atendimentoId) => {
    //     console.log('🚀 ~ Chat.vue:2578 ~ loadAtendimentoMessages ~ atendimentoId:', atendimentoId);
    //     const filtros = {
    //         instance: chatAtivo.value.instance,
    //         telefone: chatAtivo.value.telefone,
    //         cd_atendimento: atendimentoId,
    //         selectedAtendimento: true,
    //     };
    //     await listMessages(filtros, true);
    // };

    const loadAtendimentoMessages = async (atendimentoId) => {
        const filtros = {
            instance: chatAtivo.value.instance,
            telefone: chatAtivo.value.telefone,
            cd_atendimento: atendimentoId,
            selectedAtendimento: true,
        };

        // Chamar listMessages com os filtros corretos
        await listMessages(filtros, true);
    };

    async function carregaAtendimentos() {
        limitAtendimentos.value = 5;
        const resp = await listaAtendimentos({
            instance: chatAtivo.value.instance,
            telefone: chatAtivo.value.telefone,
        });

        if (resp.statuscode == 200) {
            novosAtendimentos.value.forEach((atendimento) => {
                groupedMessages.value.unshift({
                    atendimento,
                    messages: [],
                });
            });
        }
        // listMessages(chatAtivo.value, true);
    }

    async function listaAtendimentos(dados) {
        if (!pageAtendimentos.value) pageAtendimentos.value = 0;
        let offset = 0;
        pageAtendimentos.value == 1 ? (offset = 1) : (offset = pageAtendimentos.value * limitAtendimentos.value);
        dados.limit = limitAtendimentos.value;
        dados.offset = offset;

        const historicoResponse = await AtendimentoServices.listaAtendimentosHistorico(dados);
        if (historicoResponse.statuscode == 200) {
            novosAtendimentos.value = historicoResponse.data;
            const dataAtendimentos = [...atendimentos.value, ...novosAtendimentos.value];

            // CORREÇÃO: Ordenar por data de cadastro (mais antigo primeiro)
            atendimentos.value = [...dataAtendimentos].sort(
                (a, b) => new Date(a.dt_cadastro) - new Date(b.dt_cadastro)
            );

            pageAtendimentos.value += 1;
        }

        return historicoResponse;
    }

    // async function listMessages(dados, readMessages) {
    //     let filtros = {
    //         instance: dados.instance,
    //         telefone: dados.telefone,
    //         cd_atendimento: dados.cd_atendimento,
    //         readMessages,
    //     };
    //     const message = groupedMessages.value.find((at) => at.atendimento.cd_atendimento == dados.cd_atendimento);
    //     console.log('🚀 ~ Chat.vue:2641 ~ listMessages ~ message:', message);

    //     if (message?.messages.length == 0 || message == undefined) {
    //         mensagens.value = [];
    //         // logger.debug('listMessages > filtros: ', filtros);
    //         let respMessages = await MessagesServices.listMessages(filtros);
    //         console.log('🚀 ~ Chat.vue:2647 ~ listMessages ~ respMessages:', respMessages);
    //         //

    //         if (respMessages.statuscode == 200) {
    //             mensagens.value = respMessages.data.map((message) => {
    //                 //
    //                 if (message.mensagem != null) {
    //                     message.formattedFileLength =
    //                         message.file_length == null ? null : formatFileSize(message.file_length);
    //                     message.extension =
    //                         message.mimetype == null ? null : message.mimetype.split('/')[1].toUpperCase();
    //                     message.mensagem = formatMessage(message.mensagem);

    //                     return message;
    //                 }
    //             });

    //             if (dados?.selectedAtendimento == undefined) {
    //                 atendimentos.value.forEach((atendimento) => {
    //                     const isUltimoAtendimento = atendimento === atendimentos.value[atendimentos.length - 1];
    //                     const isAtendimentoSelecionado = atendimento.cd_atendimento === dados.cd_atendimento;
    //                     if (isAtendimentoSelecionado) {
    //                         atendimento.open = true;
    //                     } else {
    //                         atendimento.open = false;
    //                     }
    //                     const mostrarMensagens = isUltimoAtendimento || isAtendimentoSelecionado;
    //                     const mensagensAtendimento = mostrarMensagens
    //                         ? mensagens.value.filter((msg) => msg.cd_atendimento === atendimento.cd_atendimento)
    //                         : [];
    //                     groupedMessages.value.push({
    //                         atendimento,
    //                         messages: mensagensAtendimento, //.sort((a, b) => new Date(a.message_timestamp) - new Date(b.message_timestamp)),
    //                     });
    //                 });
    //                 console.log(
    //                     '🚀 ~ Chat.vue:2683 ~ atendimentos.value.forEach ~ groupedMessages.value:',
    //                     groupedMessages.value
    //                 );
    //             } else {
    //                 message.atendimento.open = true;
    //                 message.messages = mensagens.value;
    //             }

    //             if (readMessages) {
    //                 const key = `${dados.telefone}_${dados.instance}`;
    //                 const info = contatosNaoLidos.value.get(key);
    //                 if (info) {
    //                     info.qt = 0;
    //                     contatosNaoLidos.value.set(key, info);
    //                     atualizaContadoresNaoLidos();
    //                 }
    //             }

    //             //atualiza a quantidade de mensagens
    //             const result = listaUltimosChats.value.find((chat) => chat.telefone === dados.telefone);
    //             if (result) {
    //                 result.qt_messages_notread = 0;
    //             }

    //             //if (dados?.selectedAtendimento != true) {
    //             setTimeout(() => {
    //                 scrollToBottom();
    //             }, 100);
    //             // }
    //         }
    //     } else {
    //         if (chatAtivo.value.cd_atendimento != dados.cd_atendimento) {
    //             message.atendimento.open = false;
    //             message.messages = [];
    //         }
    //     }
    // }

    async function listMessages(dados, readMessages) {
        let filtros = {
            instance: dados.instance,
            telefone: dados.telefone,
            cd_atendimento: dados.cd_atendimento,
            readMessages,
        };

        // Encontrar o grupo do atendimento
        const groupIndex = groupedMessages.value.findIndex(
            (group) => group.atendimento?.cd_atendimento == dados.cd_atendimento
        );

        // Se é uma chamada para um atendimento específico
        if (dados.selectedAtendimento) {
            try {
                const respMessages = await MessagesServices.listMessages(filtros);

                if (respMessages.statuscode == 200) {
                    const messagesFormatted = respMessages.data.map((message) => {
                        if (message.mensagem != null) {
                            message.formattedFileLength =
                                message.file_length == null ? null : formatFileSize(message.file_length);
                            message.extension =
                                message.mimetype == null ? null : message.mimetype.split('/')[1].toUpperCase();
                            message.mensagem = formatMessage(message.mensagem);
                            return message;
                        }
                        return message;
                    });

                    // Atualizar o grupo específico com as mensagens
                    if (groupIndex !== -1) {
                        groupedMessages.value[groupIndex].messages = messagesFormatted;
                        groupedMessages.value[groupIndex].atendimento.messagesLoaded = true;
                        groupedMessages.value[groupIndex].atendimento.open = true;
                    }
                }
            } catch (error) {
                console.error('Erro ao carregar mensagens do atendimento:', error);
            }
            return;
        }

        // *** AQUI É A PARTE IMPORTANTE PARA CORRIGIR ***
        // Lógica para carregamento inicial do chat (quando clica no chat pela primeira vez)
        try {
            let respMessages = await MessagesServices.listMessages(filtros);

            if (respMessages.statuscode == 200) {
                mensagens.value = respMessages.data.map((message) => {
                    if (message.mensagem != null) {
                        message.formattedFileLength =
                            message.file_length == null ? null : formatFileSize(message.file_length);
                        message.extension =
                            message.mimetype == null ? null : message.mimetype.split('/')[1].toUpperCase();
                        message.mensagem = formatMessage(message.mensagem);
                        return message;
                    }
                    return message;
                });

                // *** PARTE CRUCIAL: POPULAR O groupedMessages INICIAL ***
                // Se groupedMessages está vazio (primeira vez abrindo o chat)
                if (groupedMessages.value.length === 0) {
                    // Separar mensagens por atendimento
                    const mensagensAgrupadasPorAtendimento = {};

                    mensagens.value.forEach((mensagem) => {
                        const cdAtendimento = mensagem.cd_atendimento;
                        if (!mensagensAgrupadasPorAtendimento[cdAtendimento]) {
                            mensagensAgrupadasPorAtendimento[cdAtendimento] = [];
                        }
                        mensagensAgrupadasPorAtendimento[cdAtendimento].push(mensagem);
                    });

                    // Para cada atendimento encontrado, buscar dados do atendimento e criar grupo
                    for (const [cdAtendimento, mensagensDoAtendimento] of Object.entries(
                        mensagensAgrupadasPorAtendimento
                    )) {
                        // Buscar dados do atendimento
                        const atendimentoEncontrado = atendimentos.value.find(
                            (at) => at.cd_atendimento == cdAtendimento
                        );

                        if (atendimentoEncontrado) {
                            // Determinar se deve estar aberto (último atendimento)
                            const isUltimoAtendimento =
                                atendimentoEncontrado === atendimentos.value[atendimentos.value.length - 1];

                            groupedMessages.value.push({
                                atendimento: {
                                    ...atendimentoEncontrado,
                                    open: isUltimoAtendimento, // Último atendimento fica aberto
                                    messagesLoaded: isUltimoAtendimento,
                                },
                                messages: isUltimoAtendimento ? mensagensDoAtendimento : [],
                            });
                        }
                    }

                    // Ordenar groupedMessages por data do atendimento (mais antigo primeiro)
                    groupedMessages.value.sort(
                        (a, b) => new Date(a.atendimento.dt_cadastro) - new Date(b.atendimento.dt_cadastro)
                    );
                }

                // Atualizar contadores de mensagens não lidas
                if (readMessages) {
                    const key = `${dados.telefone}_${dados.instance}`;
                    const info = contatosNaoLidos.value.get(key);
                    if (info) {
                        info.qt = 0;
                        contatosNaoLidos.value.set(key, info);
                        atualizaContadoresNaoLidos();
                    }
                }

                // Atualizar na lista de chats
                const result = listaUltimosChats.value.find((chat) => chat.telefone === dados.telefone);
                if (result) {
                    result.qt_messages_notread = 0;
                }

                setTimeout(() => {
                    scrollToBottom();
                }, 100);
            }
        } catch (error) {
            console.error('Erro ao carregar mensagens inicial:', error);
        }
    }

    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    async function startRecording() {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorder.value = new MediaRecorder(stream);

        isRecording.value = true;
        recordingTime.value = 0;
        timerAudio.value = setInterval(() => {
            recordingTime.value++;
        }, 1000);

        mediaRecorder.value.ondataavailable = (event) => {
            if (event.data.size > 0) {
                recordedChunks.value.push(event.data);
            }
        };

        mediaRecorder.value.start();
    }

    function cancelRecording() {
        clearInterval(timerAudio.value);
        isRecording.value = false;
        recordingTime.value = 0;
        if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
            mediaRecorder.value.stop();
        }

        recordedChunks.value = [];
    }
    async function stopRecording() {
        loading.value.show();
        clearInterval(timerAudio.value);
        isRecording.value = false;
        recordingTime.value = 0;
        if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
            mediaRecorder.value.stop();

            setTimeout(async () => {
                await saveAudio();
            }, 2000);
        }
        loading.value.hide();
    }
    async function saveAudio() {
        const blob = new Blob(recordedChunks.value, { type: 'audio/wav' });
        const url = URL.createObjectURL(blob);

        const fileName = 'audio-' + Date.now() + '.wav';
        const formData = new FormData();
        formData.append('files', blob, fileName);

        formData.append('ds_pasta', 'send');
        formData.append('instance', chatAtivo.value.instance);
        formData.append('is_converter', 'false');
        formData.append('is_category', 'true');

        let result = await UploadServices.uploadArquivos(formData);

        //

        if (result.statuscode == 200) {
            for (const data of result.data) {
                for (const file of data.files) {
                    if (file.category == 'audios') {
                        const respSendAudio = await sendAudio(file);
                        //
                    }
                }
            }
        }

        // Limpa os chunks de áudio para a próxima gravação
        recordedChunks.value = [];
    }
    function getMimeType(fileName) {
        const ext = fileName.split('.').pop()?.toLowerCase(); // Extrai a extensão

        if (ext && mimeTypes[ext]) {
            return mimeTypes[ext];
        }
        return undefined; // Caso não encontre a extensão
    }
    const formatMessage = (message) => {
        if (message == '') return message;
        if (message == null) return '';
        return message
            .replace(/\*(.*?)\*/g, '<strong>$1</strong>') // Negrito
            .replace(/_(.*?)_/g, '<em>$1</em>') // Itálico
            .replace(/~\*(.*?)\*~/g, '<del>$1</del>') // Riscado
            .replace(/~(.*?)~/g, '<u>$1</u>') // Sublinhado
            .replace(/```(.*?)```/g, '<code>$1</code>') // Bloco de código
            .replace(/`(.*?)`/g, '<code>$1</code>') // Código inline
            .replace(/\n/g, '<br>'); // Quebra de linha
    };
    async function loadMessages(message) {
        if (message?.mimetype == undefined && message.fileName) {
            message.mimetype = getMimeType(message.fileName);
            message.extension = 'PDF';
            message.file_name = message.fileName;
        }

        ////logger.debug('loadMessages:', message);
        message.mensagem = formatMessage(message.mensagem);

        mensagens.value.push(message);

        const messageFind = groupedMessages.value.find((at) => at.atendimento.cd_atendimento == message.cd_atendimento);

        if (messageFind) {
            messageFind.atendimento.open = true;
            messageFind.messages = mensagens.value;
        }

        setTimeout(() => {
            scrollToBottom();
        }, 300);
    }

    // Função para rolar até o final do chat
    // const scrollToBottom = () => {
    //     if (chatContainer.value) {
    //         chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    //     }
    // };

    const scrollToBottom = (selectedAtendimento) => {
        nextTick(() => {
            setTimeout(() => {
                if (selectedAtendimento) {
                    if (chatContainer.value) {
                        chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
                    }
                } else {
                    if (messagesContainer.value) {
                        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                    }
                }
            }); // Pequeno delay para garantir que as mensagens foram renderizadas
        });
    };

    // Função para classificar chats por message_timestamp em ordem decrescente
    async function sortChatsByTimestamp() {
        listaUltimosChats.value.sort((chatA, chatB) => {
            const timeA = chatA.message_timestamp;
            const timeB = chatB.message_timestamp;

            const timestampA = new Date(timeA).getTime();
            const timestampB = new Date(timeB).getTime();
            return timestampB - timestampA;
        });
    }
    async function situacaoBot(atendimento) {
        stopBot.value = stopBot.value == true ? false : true;

        let reqObj = {
            cd_atendimento: atendimento.cd_atendimento,
            in_stop_bot: stopBot.value,
        };
        let result = await AtendimentoServices.alterar(reqObj);
        //
        if (result.statuscode == 200) {
            const result = listaUltimosChats.value.find((chat) => chat.telefone === atendimento.telefone);
            if (result) {
                result.in_stop_bot = stopBot.value;
                const index = listaUltimosChats.value.indexOf(result);
                listaUltimosChats.value[index] = result;
            }
        }
    }
    async function situacaoMSGAut(atendimento) {
        stopMSGAutomatica.value = stopMSGAutomatica.value == true ? false : true;

        // let reqObj = {
        //     nr_telefone: atendimento.cd_atendimento,
        //     in_stop_msg_automatica: stopMSGAutomatica.value,
        // };
        // let result = await ClientesServices.alterar(reqObj);
        // //
        // if (result.statuscode == 200) {
        //     const result = listaUltimosChats.value.find((chat) => chat.telefone === atendimento.telefone);
        //     if (result) {
        //         result.in_stop_bot = stopBot.value;
        //         const index = listaUltimosChats.value.indexOf(result);
        //         listaUltimosChats.value[index] = result;
        //     }
        // }
    }

    async function carregaInstancias() {
        let filtros = {
            cd_estabelecimento: cdEstabelecimento.value,
            cd_usuario: parseInt(codUsuario.value),
            in_verificastatus: true,
            in_lista_instancia_usuario: true,
        };
        listaInstancias.value = [];
        situacaoInstancias.value = 'parado';

        let result = await InstanciaServices.listar(filtros);
        //console.log('🚀 ~ Chat.vue:2911 ~ carregaInstancias ~ result:', result);

        if (result.statuscode == 404) {
            let filtros = {
                cd_estabelecimento: cdEstabelecimento.value,
                in_verificastatus: true,
                in_lista_instancia_usuario: true,
            };
            listaInstancias.value = [];
            situacaoInstancias.value = 'parado';
            result = await InstanciaServices.listar(filtros);
        }

        if (result.statuscode == 200) {
            let instanciaOFF = false;
            let instanciaON = false;

            for (const inst of result.data) {
                //
                // if (inst.status != 'notFound') {
                listaInstancias.value.push({
                    cd_estabelecimento: inst.cd_estabelecimento,
                    id_instancia: inst.id_instancia,
                    nameinstance: inst.nameinstance,
                    nome: inst.nome,
                    image: inst.status_connection == 'Conectado' ? imgWhatsON : imgWhatsOFF,
                    in_stop_bot: inst.in_stop_bot,
                    telefone: inst.telefone,
                    status_connection: inst.status_connection,
                    in_selecionado: true,
                    nr_hash: inst.nr_hash,
                });

                if (inst.status_connection == 'Conectado') {
                    instanciaON = true;
                }

                joinSala(inst.nameinstance);
                // }
            }

            if (listaInstancias.value.length == 1) {
                if (listaInstancias.value[0].status_connection == 'Desconectado') {
                    instanciaOFF = true;
                    if (modalConexao) abrirModalConexao();
                }
                if (listaInstancias.value[0].status_connection == 'Conectando') {
                    instanciaOFF = true;
                    if (modalConexao) abrirModalConexao();
                }
            }

            if (instanciaOFF && instanciaON) {
                situacaoInstancias.value = 'alerta';
            } else if (!instanciaOFF && instanciaON) {
                situacaoInstancias.value = 'ok';
            } else if (!instanciaOFF && !instanciaON) {
                situacaoInstancias.value = 'parado';
            }

            instanciaSelecionada.value = listaInstancias.value[0].nameinstance;

            if (apiDesconectada.value && situacaoInstancias.value != 'parado') {
                toast.success('Api reconectada!');
            }
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }

        //
        //loading.value.hide();
    }

    function PedidoFechado(chat) {
        // Encontrar o chat correspondente
        const chatIndex = listaUltimosChats.value.findIndex(
            (cht) => cht.telefone === chat.telefone && cht.instance === chat.instance
        );
        if (chatIndex !== -1) {
            listaUltimosChats.value.splice(chatIndex, 1);
        }

        inModuloPedido.value = false;
        mensagensRecebidas.value = [];
        mensagens.value = [];
        mensagem.value = '';
        chatAtivo.value = '';
        chatBox.value = false;

        //focusTextAreaMensagem();
    }
    async function ReturnPedido(pedido) {
        if (chatAtivo.value.cd_pedido == undefined) {
            chatAtivo.value.tp_status = 'Em Pedido';
            chatAtivo.value.tp_situacao = 'Em Pedido';
        }

        if (chatAtivo.value.pedido_aberto == null || chatAtivo.value.pedido_aberto.length == 0) {
            chatAtivo.value.pedido_aberto = [{ cd_pedido: pedido.cd_pedido, tp_situacao: pedido.tp_situacao }];
        } else {
            chatAtivo.value.pedido_aberto[0].cd_pedido = pedido.cd_pedido;
            chatAtivo.value.pedido_aberto[0].tp_situacao = pedido.tp_situacao;
        }

        chatAtivo.value.cd_pedido = pedido.cd_pedido;
        if (pedido.tp_situacao != 'Em Pedido') {
            chatAtivo.value.pedido_aberto = null;
        }
        //
    }
    async function ReturnCliente(dados) {
        //
        if (dados == undefined) return;

        chatAtivo.value.cd_cliente = dados.cliente.cd_cliente;
        chatAtivo.value.ds_endereco = dados.cliente.ds_endereco;
        chatAtivo.value.nr_endereco = dados.cliente.nr_endereco;
        chatAtivo.value.ds_bairro = dados.cliente.ds_bairro;
        chatAtivo.value.ds_complemento = dados.cliente.ds_complemento;
        chatAtivo.value.ds_cidade = dados.cliente.ds_cidade;
        chatAtivo.value.ds_uf = dados.cliente.ds_uf;
        chatAtivo.value.ds_pais = dados.cliente.ds_pais;
        chatAtivo.value.ds_estado = dados.cliente.ds_estado;
        //
    }
    async function atendido() {
        //
        let reqObj = {
            cd_atendimento: chatAtivo.value.cd_atendimento,
        };
        await AtendimentoServices.atendido(reqObj);
        //

        if (!filtroStatus.value.status.includes('Atendido')) {
            //Retira da lista de Atendimento
            const result = listaUltimosChats.value.filter(
                (contato) => contato.cd_atendimento != chatAtivo.value.cd_atendimento
            );

            listaUltimosChats.value = result;
            mensagensRecebidas.value = [];
            mensagens.value = [];
            mensagem.value = '';
            chatAtivo.value = '';
            inModuloPedido.value = false;
            chatBox.value = false;
        }
    }
    const abrirModalEncerrar = () => {
        if (modulos.in_crm) {
            modalEncerrarAtendimentoRef.value.abreModalEncerrar(chatAtivo.value);
        } else {
            encerrarAtendimento();
        }
    };
    async function encerrarAtendimento(motivoEncerramento) {
        let reqObj = {
            tp_status: 'Atendimento Finalizado',
            tp_situacao: 'Atendimento Finalizado',
            tp_etapachat: 'Atendimento Finalizado',
            in_stop_bot: false,
            cd_atendimento: chatAtivo.value.cd_atendimento,
            cd_motivo: motivoEncerramento ? motivoEncerramento.cd_assunto : undefined,
            ds_resumo: motivoEncerramento ? motivoEncerramento.ds_resumo : undefined,
            cd_usuario_encerramento: parseInt(codUsuario.value),
            cd_estabelecimento: cdEstabelecimento.value,
            instance: chatAtivo.value.instance,
            telefone: chatAtivo.value.telefone,
        };

        const resp = await AtendimentoServices.encerrar(reqObj);

        toast.success('Atendimento encerrado com sucesso!');
        //

        removerChatDoCache(chatAtivo.value.telefone, chatAtivo.value.instance);

        if (!filtroStatus.value.status.includes('Atendimento Finalizado')) {
            //Retira da lista de Atendimento
            const result = listaUltimosChats.value.filter(
                (contato) => contato.cd_atendimento != chatAtivo.value.cd_atendimento
            );

            listaUltimosChats.value = result;
            mensagensRecebidas.value = [];
            mensagens.value = [];
            mensagem.value = '';
            chatAtivo.value = '';
            inModuloPedido.value = false;
            chatBox.value = false;
        }
    }
    async function carregaMensagensAtalhos() {
        //
        let filtros = {
            cd_estabelecimento: cdEstabelecimento.value,
            in_ativa: true,
            in_adicionaatalho: true,
        };
        listaMensagensAtalhos.value = [];
        listaMensagensAtalhosOriginal.value = [];
        //
        const respMensagens = await FluxoAtendimentoServices.listarMensagensAtalhos(filtros);
        ////logger.debug('carregaMensagensAtalhos:', respMensagens);
        if (respMensagens.statuscode == 200) {
            listaMensagensAtalhos.value = respMensagens.data;
            listaMensagensAtalhosOriginal.value = respMensagens.data;
        }
    }
    async function filtraMensagensAtalho() {
        const searchTerm = inputAtalhoMensagem.value.trim().toLowerCase();
        // Se o campo de pesquisa estiver vazio, restaura a lista original
        if (searchTerm === '') {
            listaMensagensAtalhos.value = listaMensagensAtalhosOriginal.value;
        } else {
            // Verifica se o primeiro caractere é um número
            if (!isNaN(searchTerm[0])) {
                // Aplica a regra de busca exata se começar com número
                listaMensagensAtalhos.value = listaMensagensAtalhosOriginal.value.filter((mensagem) =>
                    mensagem.ds_titulo.toLowerCase().startsWith(searchTerm + ' ')
                );
            } else {
                // Aplica a regra com includes para busca parcial
                listaMensagensAtalhos.value = listaMensagensAtalhosOriginal.value.filter((mensagem) =>
                    mensagem.ds_titulo.toLowerCase().includes(searchTerm)
                );
            }
        }
    }
    const filteredMensagensAtalhos = computed(() => {
        if (messageInput.value === '') {
            return listaMensagensAtalhos.value;
        }
        return listaMensagensAtalhos.value.filter((mensagem) =>
            mensagem.ds_titulo.toLowerCase().includes(messageInput.value.toLowerCase())
        );
    });
    const handleKeydown = (event) => {
        if (event.ctrlKey && event.key === '\\') {
            dropdownVisible.value = !dropdownVisible.value;
            if (dropdownVisible.value) {
                nextTick(() => {
                    adjustDropdownPosition();
                });
            }
            event.preventDefault();
        } else if (dropdownVisible.value) {
            if (event.key === 'ArrowDown') {
                highlightedIndex.value = (highlightedIndex.value + 1) % filteredMensagensAtalhos.value.length;
                event.preventDefault();
            } else if (event.key === 'ArrowUp') {
                highlightedIndex.value =
                    (highlightedIndex.value - 1 + filteredMensagensAtalhos.value.length) %
                    filteredMensagensAtalhos.value.length;
                event.preventDefault();
            } else if (event.key === 'Enter') {
                if (highlightedIndex.value >= 0) {
                    handleItemClick(filteredMensagensAtalhos.value[highlightedIndex.value]);
                }
                event.preventDefault();
            } else if (event.key === 'Escape' || event.key === 'Esc') {
                dropdownVisible.value = false;
                highlightedIndex.value = -1; // Reset the highlighted index
                event.preventDefault();
            }
        }
    };
    const handleItemClick = (mensagem) => {
        //

        //if (modalAtalho.value == false) return;
        if (mensagem.tp_funcionalidade == 'Comprovante de Pedido') {
            if (chatAtivo.value?.pedido_aberto == null || chatAtivo.value?.pedido_aberto?.length == 0) {
                toast.warning('Pedido não está em aberto !');
                dropdownVisible.value = false; // Fechar a lista após clicar no item
                dropdownVisibleButton.value = false;
                highlightedIndex.value = -1; // Reset the highlighted index
                modalAtalho.value = false;
                return;
            }
        }
        listaMensagensAtalhos.value = listaMensagensAtalhosOriginal.value;
        enviarMensagem(mensagem);
        dropdownVisible.value = false; // Fechar a lista após clicar no item
        dropdownVisibleButton.value = false;
        highlightedIndex.value = -1; // Reset the highlighted index
        modalAtalho.value = false;
    };
    const adjustDropdownPosition = () => {
        if (textAreaMensagem.value && suggestionsList.value) {
            const textareaRect = textAreaMensagem.value.getBoundingClientRect();
            suggestionsList.value.style.bottom = `${textareaRect.height + 10}px`; // Ajuste a posição conforme necessário
            suggestionsList.value.style.left = '40px'; // Ajuste a posição para a direita conforme necessário
        }
    };

    watch(messageInput, () => {
        if (dropdownVisible.value) {
            highlightedIndex.value = -1; // Reset the highlighted index when filtering
        }
    });

    // Mover a seleção na lista
    const moveSelection = (direction) => {
        if (listaMensagensAtalhos.value.length === 0) return;
        selectedIndex.value += direction;

        // Limitar os índices ao tamanho da lista
        if (selectedIndex.value >= listaMensagensAtalhos.value.length) {
            selectedIndex.value = 0; // Voltar para o topo
        } else if (selectedIndex.value < 0) {
            selectedIndex.value = listaMensagensAtalhos.value.length - 1; // Voltar para o final
        }
    };
    // Função chamada ao pressionar Enter
    const selectMessage = () => {
        if (selectedIndex.value >= 0 && selectedIndex.value < listaMensagensAtalhos.value.length) {
            handleItemClick(listaMensagensAtalhos.value[selectedIndex.value]);
        } else if (listaMensagensAtalhos.value.length == 1) {
            handleItemClick(listaMensagensAtalhos.value[0]);
        }
    };
    // Monitorar a mudança de filtro e resetar seleção
    watch(inputAtalhoMensagem, () => {
        selectedIndex.value = -1; // Resetar a seleção ao filtrar
    });

    async function enviarMensagem(mensagem) {
        // enviandoMensagem.value = true;
        //
        messageInput.value = '';

        await consultaCliente();

        //
        let reqObj = mensagem;
        //reqObj.from_me = true;
        reqObj.instance = chatAtivo.value.instance;
        reqObj.cd_atendimento = chatAtivo.value.cd_atendimento;
        reqObj.telefone = chatAtivo.value.telefone;
        reqObj.nome = chatAtivo.value.nome;
        reqObj.nr_hash = chatAtivo.value.nr_hash;
        reqObj.ds_endereco = chatAtivo.value.ds_endereco;
        reqObj.nr_endereco = chatAtivo.value.nr_endereco;
        reqObj.ds_bairro = chatAtivo.value.ds_bairro;
        reqObj.ds_complemento = chatAtivo.value.ds_complemento;
        reqObj.ds_cidade = chatAtivo.value.ds_cidade;
        reqObj.ds_uf = chatAtivo.value.ds_uf;
        reqObj.ds_pais = chatAtivo.value.ds_pais;
        reqObj.ds_estado = chatAtivo.value.ds_estado;
        reqObj.pedido_aberto = chatAtivo.value?.pedido_aberto != null ? chatAtivo.value?.pedido_aberto[0] : null;
        reqObj.cd_pedido = chatAtivo.value.cd_pedido;

        WhatsAppServices.enviarMensagem(reqObj);
        //
    }
    // Modificar a função consultaContatos
    async function consultaContatos(novaConversa) {
        if (novaConversa) {
            listaContatos.value = [];
            pageContatos.value = 0;
            hasMoreContatos.value = true;
            searchContatos.value = '';
            filtroStatus.value.emAtendimento = false;
            filtroStatus.value.emAndamento = false;
            filtroStatus.value.contatos = true;
        }

        if (loadingContatos.value || !hasMoreContatos.value) return;

        loadingContatos.value = true;

        try {
            const filtros = {
                cd_estabelecimento: cdEstabelecimento.value,
                limit: limitContatos.value,
                offset: pageContatos.value * limitContatos.value,
                search: searchContatos.value.trim() || undefined,
                instance: instanciaSelecionada.value,
            };

            const response = await ContatosServices.listar(filtros);

            if (response.statuscode === 200) {
                const novosContatos = response.data;

                if (novosContatos.length < limitContatos.value) {
                    hasMoreContatos.value = false;
                }

                // Se for busca, substitui a lista, senão adiciona
                if (searchContatos.value.trim()) {
                    listaContatos.value = novosContatos;
                } else {
                    listaContatos.value =
                        pageContatos.value === 0 ? novosContatos : [...listaContatos.value, ...novosContatos];
                }

                pageContatos.value++;
            }
        } catch (error) {
            toast.error('Erro ao carregar contatos');
            console.error('Erro ao carregar contatos:', error);
        } finally {
            loadingContatos.value = false;
            loadingMoreContatos.value = false;
        }
    }

    // Função para buscar contatos
    const searchContactos = async (query) => {
        searchContatos.value = query;
        pageContatos.value = 0;
        hasMoreContatos.value = true;
        listaContatos.value = [];
        await consultaContatos();
    };

    // Função para carregar mais contatos
    const loadMoreContatos = async () => {
        if (loadingMoreContatos.value || !hasMoreContatos.value) return;
        loadingMoreContatos.value = true;
        await consultaContatos();
    };

    // Função para selecionar contato

    const onContactSelected = async (contact, index) => {
        if (contact) {
            contact.nome = contact.push_name || contact.nome || contact.telefone;
            contact.instance = instanciaSelecionada.value;
            contact.cd_estabelecimento = cdEstabelecimento.value;

            //consulta se tem atendimento antes de iniciar
            const atendimento = await AtendimentoServices.listaAtendimentoPorTelefone({
                telefone: contact.telefone,
                cd_estabelecimento: cdEstabelecimento.value,
            });
            //

            if (atendimento.statuscode == 200) {
                toast.warning('Já existe atendimento para este contato!');
                const dataAtendimento = atendimento.data[0];

                // Determinar qual aba o atendimento deve estar
                let abaDestino = null;
                if (
                    statusEmAtendimentoCRM.value
                        .split(',')
                        .map((s) => s.replace(/'/g, '').trim())
                        .includes(dataAtendimento.tp_status)
                ) {
                    abaDestino = 'emAtendimento';
                    filtroStatus.value.emAtendimento = true;
                    filtroStatus.value.emAndamento = false;
                    filtroStatus.value.contatos = false;
                    filtroStatus.value.finalizados = false;
                } else if (
                    statusEmAndamentoCRM.value
                        .split(',')
                        .map((s) => s.replace(/'/g, '').trim())
                        .includes(dataAtendimento.tp_status)
                ) {
                    abaDestino = 'emAndamento';
                    filtroStatus.value.emAtendimento = false;
                    filtroStatus.value.emAndamento = true;
                    filtroStatus.value.contatos = false;
                    filtroStatus.value.finalizados = false;
                }

                if (abaDestino) {
                    // Verificar se o atendimento está no cache da aba correspondente
                    const chatNoCache = cacheAtendimentos.value[abaDestino]?.find(
                        (chat) => chat.telefone === contact.telefone && chat.instance === contact.instance
                    );

                    if (chatNoCache) {
                        //

                        // Atualizar a lista atual com os dados do cache
                        listaUltimosChats.value = [...cacheAtendimentos.value[abaDestino]];

                        // Aguardar o próximo tick para garantir que a lista foi atualizada
                        await nextTick();

                        // Focar no chat específico
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone === contact.telefone && chat.instance === contact.instance
                        );

                        if (chatIndex !== -1) {
                            // Abrir o chat automaticamente
                            await showChatBox(listaUltimosChats.value[chatIndex], chatIndex);

                            // Opcional: Scroll até o chat na lista (se necessário)
                            scrollToChat(chatIndex);
                        }
                    } else {
                        //

                        // Se não estiver no cache, carregar a aba normalmente
                        const situacaoParaCarregar = abaDestino === 'emAtendimento' ? 'Em Atendimento' : 'Em Andamento';
                        await consultaAtendimentos(situacaoParaCarregar);

                        // Após carregar, tentar focar no chat
                        await nextTick();
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone === contact.telefone && chat.instance === contact.instance
                        );

                        if (chatIndex !== -1) {
                            await showChatBox(listaUltimosChats.value[chatIndex], chatIndex);
                            scrollToChat(chatIndex);
                        }
                    }
                }
            } else {
                // Se não tem atendimento, criar novo chat
                showChatBox(contact, 0);
            }
        }
    };

    // Função auxiliar para fazer scroll até o chat na lista (opcional)
    const scrollToChat = (chatIndex) => {
        nextTick(() => {
            // Encontrar o elemento do chat na lista pelo índice
            const chatElement = document.querySelector(`[data-chat-index="${chatIndex}"]`);
            if (chatElement) {
                chatElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });

                // Opcional: Adicionar um destaque visual temporário
                chatElement.classList.add('highlight-chat');
                setTimeout(() => {
                    chatElement.classList.remove('highlight-chat');
                }, 2000);
            }
        });
    };

    // Função para iniciar chat com contato
    const onStartChat = async (contact) => {
        //
        try {
            if (contact && contact.numero) {
                await searchContactos(contact.telefone);
                if (listaContatos.value.length > 0) {
                    toast.warning('Contato já existe na lista de contatos');

                    return;
                }
            }
            const now = new Date();
            const time = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

            const idunico = await generateSHA1Hash(`${now}${contact.telefone}`);
            //Criar ou buscar atendimento para este contato
            const atendimentoData = {
                origem: 'chat',
                message: 'Sucesso',
                event: 'messages.upsert',
                telefone: contact.telefone,
                nome: contact.push_name,
                from_me: true,
                message_timestamp: now,
                horario: time,
                remote_jid: contact.telefone + '@s.whatsapp.net',
                message_id: undefined,
                url_profile_picture: contact.url_profile_picture || '',
                type_message: 'textMessage',
                ds_hash: contact.ds_hash || '',
                nr_hash: contact.nr_hash || '',
                idunico: idunico,
                tp_status_atendimento: 'Iniciado',
                cd_atendente: codUsuario.value,
                nm_atendente: nmUsuario.value,
                instance: instanciaSelecionada.value, // ou alguma lógica para selecionar instância
                cd_estabelecimento: cdEstabelecimento.value,
            };

            // Aqui você implementaria a lógica para criar/buscar o atendimento
            // e então mostrar o chat

            showChatBox(atendimentoData, 0);
            // Exemplo: redirecionar para a aba de atendimentos e selecionar o chat
            //await consultaAtendimentos('Em Atendimento');
        } catch (error) {
            toast.error('Erro ao iniciar conversa');
            console.error('Erro ao iniciar chat:', error);
        }
    };

    async function consultaAtendimentos(situacao) {
        if (!filtroStatus.value.contatos) {
            ultimaAbaSelecionada.value = situacao;
        }

        filtroStatus.value.contatos = false;
        listaUltimosChats.value = [];
        listaSituacoes.value = [];
        page.value = 0;
        inFiltraContatos.value = false;

        if (modulos.in_crm) {
            filtroStatus.value.emAtendimento = situacao == 'Em Atendimento' ? true : false;
            filtroStatus.value.emAndamento = situacao == 'Em Andamento' ? true : false;
            filtroStatus.value.finalizados = situacao == 'Finalizados' ? true : false;
            filtroStatus.value.contatos = false;
            filtroStatus.value.status = situacao == 'Finalizados' ? statusFinalizadosCRM.value : statusCRM.value;
            const statusArray =
                situacao == 'Finalizados'
                    ? statusFinalizadosCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }))
                    : statusCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }));
            listaSituacoes.value.push(...statusArray);
        } else {
            if (situacao == 'Em Atendimento') {
                filtroStatus.value.emAtendimento = true;
                filtroStatus.value.emAndamento = false;
                filtroStatus.value.contatos = false;
                filtroStatus.value.status = modulos.in_crm ? statusEmAtendimentoCRM.value : statusEmAtendimento.value;
                const statusArray = modulos.in_crm
                    ? statusEmAtendimentoCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }))
                    : statusEmAtendimento.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }));

                listaSituacoes.value.push(...statusArray);
            } else if (situacao == 'Em Andamento') {
                filtroStatus.value.emAtendimento = false;
                filtroStatus.value.emAndamento = true;
                filtroStatus.value.contatos = false;

                filtroStatus.value.status = modulos.in_crm ? statusEmAndamentoCRM.value : statusEmAndamento.value;
                const statusArray = modulos.in_crm
                    ? statusEmAndamentoCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }))
                    : statusEmAndamento.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }));

                listaSituacoes.value.push(...statusArray);
            } else {
                inFiltraContatos.value = true;
                filtroStatus.value.emAtendimento = false;
                filtroStatus.value.emAndamento = false;
                filtroStatus.value.contatos = false;
                filtroStatus.value.status = undefined;

                filtroStatus.value.status = modulos.in_crm ? statusFinalizadosCRM.value : undefined;
                const statusArray = modulos.in_crm
                    ? statusFinalizadosCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }))
                    : statusEmAtendimento.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }));

                const statusArray2 = modulos.in_crm
                    ? statusFinalizadosCRM.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }))
                    : statusEmAndamento.value
                          .split(',')
                          .map((item) => ({ tp_status: item.trim().replace(/'/g, ''), in_habilitado: false }));

                listaSituacoes.value.push(...statusArray, ...statusArray2);
            }
        }

        hasMore.value = true;
        await listaChats(true);
    }

    // Função para pedir permissão de notificação
    const requestNotificationPermission = async () => {
        if (Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                // showNotificationWindows();
            } else {
                toast.warning('Notificações bloqueadas pelo usuário.');
            }
        } else if (Notification.permission === 'granted') {
            //showNotificationWindows();
        } else {
            toast.warning('Notificações bloqueadas pelo usuário.');
        }
    };

    // Função para exibir a notificação
    const showNotificationWindows = (titulo, mensagem) => {
        const notification = new Notification(titulo, {
            body: mensagem,
            // icon: 'https://sandbox.oizap.com.br/assets/logobranco-wapi-a3x2MGoU.png', // Adicione o URL do seu ícone personalizado aqui
            // icon: imageOiZap, TIREI PORQUE A CADA REC. DE MSG CARREGA A IMAGEM DO ICONE (GET)
        });

        // Evento ao clicar na notificação
        notification.onclick = () => {
            window.focus(); // Foca na aba do navegador ao clicar na notificação
        };
    };

    // Função para verificar se o chat deve ser removido ou não
    // async function verificaRemocao(message) {
    //     // //logger.debug('verificaRemocao', message);
    //     // //logger.debug('chatAtivo.value at line 2690 in chat/Chat.vue:', chatAtivo.value);
    //     let inRemovido = false;
    //     let status;
    //     status = message?.status_pedido;
    //     if (!status) status = message?.tp_status_atendimento;
    //     if (!status) status = message?.tp_status;

    //     if (message.tp_etapachat == 'Falar com Atendente') {
    //         status = message?.tp_etapachat;
    //     }

    //     if (status == undefined) {
    //         inRemovido = false;
    //         return;
    //     }

    //  if (!filtroStatus.value.status.includes(status)) {
    //         if (chatAtivo.value?.telefone == message.telefone && status == 'Pedido Realizado') {
    //                chatAtivo.value = null;
    //             mensagensRecebidas.value = [];
    //             mensagem.value = '';
    //         }
    //         inRemovido = true;
    //         // Encontrar o chat correspondente
    //         const chatIndex = listaUltimosChats.value.findIndex(
    //             (chat) => chat.telefone === message.telefone && chat.instance === message.instance
    //         );
    //         if (chatIndex !== -1) {
    //             listaUltimosChats.value.splice(chatIndex, 1);
    //         }
    //     }
    //     return inRemovido;
    // }

    async function verificaRemocao(message) {
        let inRemovido = false;
        let status;

        status = message?.status_pedido;
        if (!status) status = message?.tp_status_atendimento;
        if (!status) status = message?.tp_status;

        if (message.tp_etapachat == 'Falar com Atendente') {
            status = message?.tp_etapachat;
        }

        if (status == undefined) {
            inRemovido = false;
            return;
        }

        if (status == 'Falar com Atendente') {
            return;
        }

        //
        // Verificar se status está na lista de filtros atual
        //
        if (filtroStatus.value.status == undefined) {
            return false;
        }
        if (!filtroStatus.value.status?.includes(status)) {
            // Remover da lista de visualização, mas não afetar contagem
            if (chatAtivo.value?.telefone == message.telefone && status == 'Pedido Realizado') {
                chatAtivo.value = null;
                mensagensRecebidas.value = [];
                mensagens.value = [];
                mensagem.value = '';
            }

            inRemovido = true;

            // Encontrar o chat correspondente
            const chatIndex = listaUltimosChats.value.findIndex(
                (chat) => chat.telefone === message.telefone && chat.instance === message.instance
            );

            if (chatIndex !== -1) {
                listaUltimosChats.value.splice(chatIndex, 1);
            }
        }

        return inRemovido;
    }

    const atalhoOpcoes = async (event) => {
        //
        if (chatAtivo.value != '' && chatAtivo.value != undefined) {
            if (event.key === 'F9') {
                inputAtalhoMensagem.value = '';
                modalAtalho.value = true;
                await nextTick(); // Aguarda o DOM ser atualizado
                setTimeout(() => {
                    focusPesquisaMensagem(); // Foca no input com um pequeno atraso
                }, 200); // Pequeno atraso de 200ms
            } else if (event.ctrlKey && event.key) {
                const respAtalho = listaMensagensAtalhos.value.find((at) => at.ds_teclaatalho == 'CRTL+' + event.key);

                if (respAtalho) enviarMensagem(respAtalho);
            }
        }
    };

    // Função para focar no input após abrir o modal
    const focusPesquisaMensagem = () => {
        let input = textPesquisaMensagem.value; // Acessa o input via ref
        if (input) {
            input.focus(); // Foca no input
            input.select(); // Seleciona o texto dentro do input
        }
    };

    function fechaAtalhoOpcoes() {
        //
        modalAtalho.value = false;
        focusTextAreaMensagem();
    }

    const backgroundStyle = computed(() => ({
        backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url(${
            darkMode.value ? backgroundImagedark : backgroundImage
        })`,
        backgroundSize: '500px', // Tamanho fixo menor para criar padrão
        backgroundRepeat: 'repeat', // Repetir a imagem
        backgroundPosition: 'top left', // Posição inicial
        backgroundAttachment: 'local', // Fixa com o scroll
    }));

    const focusTextAreaMensagem = async () => {
        let input = textAreaMensagem.value;

        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };

    // Função para auto-resize do textarea
    const autoResizeTextarea = () => {
        const textarea = textAreaMensagem.value;
        if (!textarea) return;

        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';

        // Calculate new height based on content
        const newHeight = Math.min(textarea.scrollHeight, 200); // Máximo de 200px (aproximadamente 10 linhas)

        // Apply new height
        textarea.style.height = `${newHeight}px`;

        // Add scroll if content exceeds max height
        if (textarea.scrollHeight > 200) {
            textarea.style.overflowY = 'auto';
        } else {
            textarea.style.overflowY = 'hidden';
        }
    };

    const getKeyForStatus = (chat) => {
        return `status-${chat.tp_status}-${chat.id}`; // Utilize um identificador único
    };

    const getIconForStatus = (status) => {
        switch (status) {
            case 'Em Atendimento':
                return 'robot';
            case 'Em Pedido':
                return 'cart-shopping';
            case 'Em Pagamento':
                return 'dollar-sign';
            case 'Pedido Realizado':
                return 'clipboard-check';
            case 'Pedido Confirmado':
                return 'list-check';
            case 'Pedido Cancelado':
                return 'ban';
            case 'Atendimento Cancelado':
                return 'ban';
            case 'Pedido Pronto':
                return 'thumbs-up';
            case 'Saiu para Entrega':
                return 'truck-fast';
            case 'Pedido Entregue':
                return 'heart-pulse';
            case 'Atendimento Finalizado':
                return 'check-to-slot';
            default:
                return 'robot'; // Ícone padrão caso não haja correspondência
        }
    };

    const getColorForStatus = (status) => {
        switch (status) {
            case 'Fila':
                return 'text-orange-700 bg-orange-200';
            case 'Em Atendimento':
                return 'text-green-700 bg-green-200';
            case 'Em Pedido':
                return 'text-sky-700 bg-sky-200';
            case 'Em Pagamento':
                return 'text-teal-700 bg-teal-200';
            case 'Pedido Realizado':
                return 'text-indigo-700 bg-indigo-200';
            case 'Pedido Confirmado':
                return 'text-orange-700 bg-orange-200';
            case 'Pedido Cancelado':
                return 'text-red-700 bg-red-200';
            case 'Atendimento Cancelado':
                return 'text-red-700 bg-red-200';
            case 'Pedido Pronto':
                return 'text-yellow-700 bg-yellow-200';
            case 'Saiu para Entrega':
                return 'text-purple-700 bg-purple-200';
            case 'Pedido Entregue':
                return 'text-slate-700 bg-slate-200';
            case 'Atendimento Finalizado':
                return 'text-neutral-700 bg-neutral-200';
            default:
                return 'text-green-700 bg-green-200';
        }
    };

    async function handleImageError(event) {
        event.target.src = notFoundImage; // Caminho para a imagem padrão
    }

    const transferirAtendimento = async (chat, index) => {
        //
        modalTransAtendimentoRef.value.abreModalTransferir(chat);
    };

    const returnAtendimento = (atendimento) => {
        const findChat = listaUltimosChats.value.find((chat) => chat.cd_atendimento === atendimento.cd_atendimento);
        if (findChat) {
            findChat.cd_departamento = atendimento.cd_departamento;
            findChat.ds_departamento = atendimento.ds_departamento;
            findChat.cd_atendente = atendimento.cd_atendente;
            findChat.nm_atendente = atendimento.nm_atendente;
            findChat.ds_comentario = atendimento.ds_comentario;
        }
    };

    const returnEncerramento = (motivoEncerramento) => {
        //
        if (!motivoEncerramento) {
            toast.error('Erro ao encerrar atendimento!');
            return;
        }
        encerrarAtendimento(motivoEncerramento);
    };

    // Função auxiliar para atualizar chat no cache (reutilizando da implementação anterior)
    const atualizarChatNoCache = (chatAtualizado) => {
        const telefoneInstance = `${chatAtualizado.telefone}_${chatAtualizado.instance}`;

        // Remove o chat de todas as abas do cache
        Object.keys(cacheAtendimentos.value).forEach((key) => {
            const index = cacheAtendimentos.value[key].findIndex(
                (chat) => `${chat.telefone}_${chat.instance}` === telefoneInstance
            );
            if (index !== -1) {
                cacheAtendimentos.value[key].splice(index, 1);
            }
        });

        // Determina em qual(is) aba(s) o chat deve estar agora
        const statusEmAtendimentoArray = statusEmAtendimentoCRM.value
            .split(',')
            .map((item) => item.trim().replace(/'/g, ''));

        const statusEmAndamentoArray = statusEmAndamentoCRM.value
            .split(',')
            .map((item) => item.trim().replace(/'/g, ''));

        // Adiciona nas abas corretas
        if (statusEmAtendimentoArray.includes(chatAtualizado.tp_status)) {
            const exists = cacheAtendimentos.value.emAtendimento.find(
                (chat) => `${chat.telefone}_${chat.instance}` === telefoneInstance
            );

            if (!exists) {
                cacheAtendimentos.value.emAtendimento.unshift(chatAtualizado);
                // Ordena por timestamp
                cacheAtendimentos.value.emAtendimento.sort(
                    (a, b) => new Date(b.message_timestamp) - new Date(a.message_timestamp)
                );
            }
        }

        if (statusEmAndamentoArray.includes(chatAtualizado.tp_status)) {
            const exists = cacheAtendimentos.value.emAndamento.find(
                (chat) => `${chat.telefone}_${chat.instance}` === telefoneInstance
            );

            if (!exists) {
                cacheAtendimentos.value.emAndamento.unshift(chatAtualizado);
                // Ordena por timestamp
                cacheAtendimentos.value.emAndamento.sort(
                    (a, b) => new Date(b.message_timestamp) - new Date(a.message_timestamp)
                );
            }
        }
    };

    // Função para remover chat do cache (reutilizando da implementação anterior)
    const removerChatDoCache = (telefone, instance) => {
        const telefoneInstance = `${telefone}_${instance}`;
        Object.keys(cacheAtendimentos.value).forEach((key) => {
            const index = cacheAtendimentos.value[key].findIndex(
                (chat) => `${chat.telefone}_${chat.instance}` === telefoneInstance
            );
            if (index !== -1) {
                cacheAtendimentos.value[key].splice(index, 1);
            }
        });
    };

    async function eventosSocket() {
        socket.on('qrcode.updated', (message) => {
            //

            if (message.data.base64) {
                qrCode.value = message.data.base64;
                // modalConexaoRef.value?.atualizaQrCode(message.data);
                // toast.success('QR Code atualizado!');
            }
        });

        socket.on('connection.update', async (message) => {
            logger.debug('connection.update', message);

            // let resp = {
            //     statuscode: 200,
            //     message: 'Sucesso',
            //     data: message,
            // };

            // //const resultInstancia = listaInstancia.value.find((chat) => chat.telefone === message.instance);
            // //
            // //if (resultInstancia) {
            // //se fechar a conexão verifica o status para atualizar o status
            // if (message.status_connection == 'Desconectado') {
            //     await connectionState();
            // } else {
            //     await connection(resp);
            // }
            //}
        });

        socket.on('messages.update', (message) => {
            let result = mensagens.value.find((chat) => chat.message_id == message.data.message_id);

            if (result) {
                result.status_message = message.data.status_message;
                //const index = mensagens.value.indexOf(result);
                // mensagens.value[index] = result;
            }
        });

        socket.on('messages.edited', (message) => {
            ////logger.debug('messages.edited at line 2492 in chat/Chat.vue:', message);
            const result = mensagens.value.find((chat) => chat.message_id == message.data.message_id);
            if (result) {
                result.mensagem_edited = result.mensagem;
                result.mensagem = formatMessage(message.data.mensagem);
                result.in_mensagem_edited = true;
                // const index = mensagensRecebidas.value.indexOf(result);
                // mensagensRecebidas.value[index] = result;
            }

            if (listaUltimosChats.value.length > 0) {
                const resultChat = listaUltimosChats.value.find(
                    (chat) =>
                        chat.telefone === message.data.telefone &&
                        chat.instance == message.data.instance &&
                        chat.message_id == message.data.message_id
                );
                if (resultChat) {
                    resultChat.mensagem = functions.limitWords(formatMessage(message.data.mensagem));
                }
            }
        });

        socket.on('messages.delete', (message) => {
            // logger.debug('messages.delete at line 2492 in chat/Chat.vue:', message);
            const result = mensagens.value.find((chat) => chat.message_id == message.data.message_id);
            if (result) {
                result.in_mensagem_delete = true;
                // const index = mensagensRecebidas.value.indexOf(result);
                // mensagensRecebidas.value[index] = result;
            }

            if (listaUltimosChats.value.length > 0) {
                const resultChat = listaUltimosChats.value.find(
                    (chat) =>
                        chat.telefone === message.data.telefone &&
                        chat.instance == message.data.instance &&
                        chat.message_id == message.data.message_id
                );
                if (resultChat) {
                    resultChat.in_delete = true;
                }
            }
        });

        socket.on('nova_mensagem', (message) => {
            socket.emit('mensagem_recebida', message.data.key.message_id);
        });

        socket.on('status.atendimento', async (message) => {
            console.log('🚀 ~ Chat.vue:4197 ~ socket.on ~ message:', message);
            if (modulos.in_crm) {
                if (usuarioDepartamentos.value && usuarioDepartamentos.value.length > 0) {
                    //caso tenha atendente na msg, ja verifica se é o mesmo logado
                    if (message.cd_atendente && message.cd_atendente != codUsuario.value) {
                        // Encontrar o chat correspondente
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone == message.nr_telefone && chat.instance == message.instance
                        );

                        if (chatIndex !== -1) {
                            listaUltimosChats.value.splice(chatIndex, 1);
                        }

                        return;
                    }

                    //caso não tenha atendente, verifica se o departamento é do usuario logado
                    const departamento = usuarioDepartamentos.value.find(
                        (dep) => dep.cd_departamento == message.cd_departamento
                    );
                    if (!departamento) {
                        // Encontrar o chat correspondente
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone === message.nr_telefone && chat.instance === message.instance
                        );

                        if (chatIndex !== -1) {
                            listaUltimosChats.value.splice(chatIndex, 1);
                        }
                        return;
                    }
                }
            }
            if (message?.dt_ultima_atualizacao) {
                maiorDtUltimaImportacao.value = converters.date('DD/MM HH:MM:SS', message.dt_ultima_atualizacao);
            } else {
                maiorDtUltimaImportacao.value = converters.date('DD/MM HH:MM:SS', new Date().toISOString());
            }
            //logger.debug('status.atendimento', message);
            message.telefone = message.nr_telefone;

            if (modulos.in_crm && chatAtivo.value != undefined) {
                if (chatAtivo.value.nr_telefone != message.nr_telefone) {
                    const inRemovido = await verificaRemocao(message);
                    //
                    if (inRemovido) return;
                }
            } else {
                const inRemovido = await verificaRemocao(message);
                //
                if (inRemovido) return;
            }

            //socket.emit('mensagem_recebida', message.data.key.message_id);
            const result = listaUltimosChats.value.find((chat) => chat.telefone === message.nr_telefone);
            //
            if (result) {
                if (message?.tp_status_atendimento != 'Transferido') {
                    result.tp_status = message?.tp_status_atendimento
                        ? message?.tp_status_atendimento
                        : message?.tp_status;
                }
                result.cd_atendimento = message?.cd_atendimento;
                result.cd_cliente = message?.cd_cliente;
                result.ds_hash = message?.ds_hash;
                result.in_stop_bot = message?.in_stop_bot;
                result.cd_departamento = message?.cd_departamento;
                result.ds_departamento = message?.ds_departamento;

                //
                const index = listaUltimosChats.value.indexOf(result);
                listaUltimosChats.value[index] = result;
                //
            }
        });

        socket.on('send.message.oizap.update', async (message) => {
            //
            ////logger.debug('send.message.oizap.update', message);

            const result = listaUltimosChats.value.find(
                (chat) => chat.telefone === message.nr_telefone && chat.instance === message.instance
            );
            //
            if (result) {
                result.tp_status = message?.status_pedido;

                const inRemovido = await verificaRemocao(message);
                if (inRemovido) return;
            }

            //

            let resultMsg = mensagens.value.find((chat) => chat.idunico == message.idunico);

            if (resultMsg) {
                resultMsg.status_message = message.status;

                resultMsg.message_id = message.message_id;

                //
                const index = mensagens.value.indexOf(resultMsg);
                mensagens.value[index] = resultMsg;
                //
            }
        });

        socket.on('connect', async () => {
            //
            if (!reconectando.value) {
                reconectando.value = true;
                await carregaInstancias(false);
                reconectando.value = false;
            }
        });

        socket.on('disconnect', async (reason) => {
            //
            situacaoInstancias.value = 'parado';
            apiDesconectada.value = true;
            // toast.error('Api desconectada!');

            if (reason === 'io server disconnect') {
                if (!reconectando.value) {
                    reconectando.value = true;
                    await carregaInstancias(false);
                    reconectando.value = false;
                }
            }
        });

        socket.on('reconnect_attempt', () => {
            logger.error('Tentando reconectar');
        });

        socket.on('reconnect', (attemptNumber) => {
            logger.error(`Reconectado após ${attemptNumber} tentativas`);
        });

        socket.on('reconnect_failed', () => {
            logger.error('Falha ao reconectar');
        });

        socket.on('messages.upsert', async (dataMessage) => {
            console.log('🚀 ~ Chat.vue:4527 ~ socket.on ~ dataMessage:', dataMessage);

            let message = dataMessage.data[0];
            if (message.telefone == undefined) return;

            // Verifica se o usuário tem departamentos e se o departamento da mensagem é válido
            if (modulos.in_crm) {
                //console.log('🚀 ~ Chat.vue:4534 ~ socket.on ~ modulos.in_crm:', modulos.in_crm);
                //console.log('🚀 ~ Chat.vue:4536 ~ socket.on ~ usuarioDepartamentos.value:', usuarioDepartamentos.value);
                if (usuarioDepartamentos.value && usuarioDepartamentos.value.length > 0) {
                    //caso tenha atendente na msg, ja verifica se é o mesmo logado
                    if (message.cd_atendente && message.cd_atendente != codUsuario.value) {
                        // Encontrar o chat correspondente e remover tanto da lista quanto do cache
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone == message.nr_telefone && chat.instance == message.instance
                        );

                        if (chatIndex !== -1) {
                            listaUltimosChats.value.splice(chatIndex, 1);
                        }

                        // Remover do cache também
                        removerChatDoCache(message.nr_telefone, message.instance);
                        return;
                    }

                    //caso não tenha atendente, verifica se o departamento é do usuario logado
                    const departamento = usuarioDepartamentos.value.find(
                        (dep) => dep.cd_departamento == message.cd_departamento
                    );
                    if (!departamento) {
                        // Encontrar o chat correspondente e remover tanto da lista quanto do cache
                        const chatIndex = listaUltimosChats.value.findIndex(
                            (chat) => chat.telefone === message.nr_telefone && chat.instance === message.instance
                        );

                        if (chatIndex !== -1) {
                            listaUltimosChats.value.splice(chatIndex, 1);
                        }

                        // Remover do cache também
                        removerChatDoCache(message.nr_telefone, message.instance);
                        return;
                    }
                }
            }

            const key = `${message.telefone}_${message.instance}`;

            //
            if (chatAtivo.value?.telefone == message?.telefone) {
                if ((message.from_me && message.origem != 'chat') || !message.from_me) {
                    loadMessages(message);
                }
            } else {
                //
                if (!message.from_me) {
                    const info = contatosNaoLidos.value.get(key) || {
                        qt: 0,
                        tp_status: message.tp_status_atendimento,
                        tp_etapachat: message.tp_etapachat,
                    };

                    // Só incrementa se não estiver visualizando o chat
                    if (chatAtivo.value?.telefone !== message.telefone) {
                        info.qt++;

                        if (modulos.in_crm) {
                            // Verifica se é um atendimento "Falar com Atendente"
                            if (message.tp_etapachat === 'Falar com Atendente') {
                                // Incrementa em ambas as abas se o status pertencer à aba "Em Andamento"
                                if (
                                    ['Pedido Confirmado', 'Pedido Pronto', 'Saiu para Entrega'].includes(
                                        message.tp_status_atendimento
                                    )
                                ) {
                                    filtroStatus.value.qtEmAtendimento++;
                                    filtroStatus.value.qtEmAndamento++;
                                }
                            } else {
                                //const statusEmAtendimentoCRM = ref(`'Fila','Transferido'`);
                                // const statusEmAndamentoCRM = ref(`'Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente'`);

                                // Se for CRM, verifica o status para contar em uma ou ambas as abas
                                if (
                                    statusEmAtendimentoCRM.value
                                        .split(',')
                                        .map((s) => s.replace(/'/g, '').trim())
                                        .includes(info.tp_status)
                                ) {
                                    filtroStatus.value.qtEmAtendimento++;
                                } else if (
                                    statusEmAndamentoCRM.value
                                        .split(',')
                                        .map((s) => s.replace(/'/g, '').trim())
                                        .includes(info.tp_status)
                                ) {
                                    filtroStatus.value.qtEmAndamento++;
                                }
                            }
                        } else {
                            // Se não for CRM, conta normalmente

                            // Verifica se é um atendimento "Falar com Atendente"
                            if (message.tp_etapachat === 'Falar com Atendente') {
                                // Incrementa em ambas as abas se o status pertencer à aba "Em Andamento"
                                if (
                                    ['Pedido Confirmado', 'Pedido Pronto', 'Saiu para Entrega'].includes(
                                        message.tp_status_atendimento
                                    )
                                ) {
                                    filtroStatus.value.qtEmAtendimento++;
                                    filtroStatus.value.qtEmAndamento++;
                                }
                            } else {
                                // Lógica normal para outros casos
                                if (
                                    [
                                        'Fila',
                                        'Em Atendimento',
                                        'Em Pedido',
                                        'Em Pagamento',
                                        'Falar com Atendente',
                                        'Pedido Realizado',
                                    ].includes(message.tp_status)
                                ) {
                                    filtroStatus.value.qtEmAtendimento++;
                                } else if (
                                    ['Pedido Confirmado', 'Pedido Pronto', 'Saiu para Entrega'].includes(
                                        message.tp_status_atendimento
                                    )
                                ) {
                                    filtroStatus.value.qtEmAndamento++;
                                }
                            }
                        }
                    }

                    info.tp_status = message.tp_status_atendimento;
                    info.tp_etapachat = message.tp_etapachat;

                    contatosNaoLidos.value.set(key, info);
                    atualizaContadoresNaoLidos();

                    playAudioWhats();
                    showNotificationWindows(
                        'Oi Zap - ' + message.nome,
                        functions.limitWords(formatMessage(message.mensagem))
                    );
                }
            }

            const inRemovido = await verificaRemocao(message);
            //
            if (inRemovido) return;

            //
            const findInstancia = listaInstancias.value.find(
                (instancia) => instancia.nameinstance === message.instance
            );

            let result;
            if (listaUltimosChats.value.length > 0) {
                result = listaUltimosChats.value.find(
                    (chat) => chat.telefone == message.telefone && chat.instance == message.instance
                );
            }

            let nome = null;
            if (message.origem == 'bot' && message.nm_cliente) {
                nome = message.nm_cliente;
            } else if (message.origem == 'bot' && !message?.from_me) {
                nome = message.nome;
            } else {
                nome = message.nome;
                if (!nome) {
                    nome = message?.pedidos?.cliente?.ds_nome;
                }
            }

            // Variável para armazenar o chat atualizado (para sincronizar com o cache)
            let chatAtualizado = null;

            //
            if (result) {
                let qt_messages_notread = 0;

                if (chatAtivo.value?.telefone != message?.telefone) {
                    qt_messages_notread = !message.from_me
                        ? parseInt(result.qt_messages_notread) + 1
                        : parseInt(result.qt_messages_notread);
                }

                let ds_foto;

                if (message.url_profile_picture != undefined) {
                    ds_foto = message.url_profile_picture;
                }

                if (ds_foto == undefined) {
                    if (message.atendimento) {
                        if (message.atendimento.ds_foto != '') {
                            ds_foto = chatAtivo.value?.url_profile_picture;
                        } else {
                            ds_foto = message.atendimento.ds_foto;
                        }
                    }
                }

                if (ds_foto == undefined) {
                    ds_foto = message?.ds_foto;
                }

                if (ds_foto == undefined) {
                    ds_foto = chatAtivo.value?.url_profile_picture;
                }

                result.mensagem = functions.limitWords(formatMessage(message.mensagem));
                result.message_id = message.message_id;
                result.message_timestamp = message.message_timestamp;
                result.horario = message.horario;
                result.in_novamsg = chatAtivo.value?.telefone == message?.telefone ? false : true;
                if (message?.cd_atendimento != undefined) {
                    result.cd_atendimento = message?.cd_atendimento;
                }
                if (message?.cd_cliente != undefined) {
                    result.cd_cliente = message?.cd_cliente;
                }
                if (nome) {
                    result.nome = nome;
                }

                result.ds_hash = message?.ds_hash;
                if (message.tp_status_atendimento != undefined && message.tp_status_atendimento != '') {
                    result.tp_status = message.tp_status_atendimento;
                } else if (message.tp_situacao_atendimento != undefined && message.tp_situacao_atendimento != '') {
                    result.tp_status = message.tp_situacao_atendimento;
                } else if (message.status_pedido != undefined && message.status_pedido != '') {
                    result.tp_status = message.status_pedido;
                }
                result.presence = undefined;
                result.qt_messages_notread = qt_messages_notread;
                result.quotedmessage = message.quotedmessage;
                result.status_message = message.from_me ? 'Enviado' : 'Lido';
                if (message.mensagem == 'Falar com Atendente') {
                    result.in_stop_bot = true;
                } else {
                    result.in_stop_bot = message.in_stop_bot;
                }
                result.nr_hash = findInstancia.nr_hash;
                result.status_pedido = message.status_pedido;
                result.pedidos = message.pedidos;
                result.online = true;
                result.in_delete = false;
                result.tp_etapachat = message.tp_etapachat;
                result.ds_obscliente = message?.ds_obscliente;

                const messageTimestamp = new Date(message.message_timestamp);
                const now = new Date();
                const diffInMinutes = (now - messageTimestamp) / 1000 / 60;

                if (diffInMinutes > 10) {
                    result.minutos_atendimento = 10;
                } else if (diffInMinutes > 5) {
                    result.minutos_atendimento = 5;
                } else {
                    result.minutos_atendimento = 0;
                }

                // Copiar para sincronizar com cache
                chatAtualizado = { ...result };
            } else {
                // Chat novo - criar e adicionar
                let dsFoto = message.url_profile_picture;
                if (dsFoto == '' || dsFoto == undefined) {
                    dsFoto = message.ds_foto;
                }

                let tpStatus;

                if (message.tp_status_atendimento != undefined && message.tp_status_atendimento != '') {
                    tpStatus = message.tp_status_atendimento;
                } else if (message.tp_situacao_atendimento != undefined && message.tp_situacao_atendimento != '') {
                    tpStatus = message.tp_situacao_atendimento;
                } else if (message.status_pedido != undefined && message.status_pedido != '') {
                    tpStatus = message.status_pedido;
                }

                if (tpStatus === undefined) return;

                const messageTimestamp = new Date(message.message_timestamp);
                const now = new Date();
                const diffInMinutes = (now - messageTimestamp) / 1000 / 60;
                let minutos_atendimento = 0;
                if (diffInMinutes > 10) {
                    minutos_atendimento = 10;
                } else if (diffInMinutes > 5) {
                    minutos_atendimento = 5;
                } else {
                    minutos_atendimento = 0;
                }

                let chat = {
                    id: undefined,
                    event: message.event,
                    instance: message.instance,
                    nameinstance: findInstancia?.nome,
                    cd_estabelecimento: findInstancia?.cd_estabelecimento,
                    horario: converters.date('time2', message.message_timestamp),
                    from_me: message?.from_me,
                    mensagem: functions.limitWords(formatMessage(message.mensagem)),
                    telefone: message.telefone,
                    message_id: message.message_id,
                    remote_jid: message.remote_jid,
                    message_timestamp: message.message_timestamp,
                    id_ultimo_fluxo: undefined,
                    hash: message?.ds_hash,
                    cd_atendimento: message?.cd_atendimento,
                    cd_cliente: message?.cd_cliente ? message?.cd_cliente : message?.pedidos?.cd_cliente,
                    ds_obscliente: message?.ds_obscliente ? message?.ds_obscliente : message?.pedidos?.ds_obscliente,
                    ds_hash: message?.ds_hash,
                    tp_status: tpStatus,
                    in_stop_bot: message.in_stop_bot,
                    online: true,
                    presence: undefined,
                    dateTimePresence: undefined,
                    qt_messages_notread: 1,
                    nr_hash: findInstancia?.nr_hash,
                    status_message: message.from_me ? 'Enviado' : 'Lido',
                    in_novamsg: chatAtivo.value?.telefone == message?.telefone ? false : true,
                    status_pedido: message.status_pedido,
                    tp_etapachat: message.tp_etapachat,
                    minutos_atendimento: minutos_atendimento,
                    pedidos: message.pedidos,
                    in_delete: false,
                };

                if (nome) {
                    chat.nome = nome;
                }

                if (dsFoto != '') {
                    chat.url_profile_picture = dsFoto;
                }

                listaUltimosChats.value.push(chat);

                // Copiar para sincronizar com cache
                chatAtualizado = { ...chat };
            }

            // **SINCRONIZAR COM O CACHE** - Esta é a parte nova e importante!
            if (modulos.in_crm && chatAtualizado) {
                // Determinar em qual cache o chat deve estar baseado no status atual
                const statusEmAtendimentoArray = statusEmAtendimentoCRM.value
                    .split(',')
                    .map((item) => item.trim().replace(/'/g, ''));

                const statusEmAndamentoArray = statusEmAndamentoCRM.value
                    .split(',')
                    .map((item) => item.trim().replace(/'/g, ''));

                // Atualizar cache
                atualizarChatNoCache(chatAtualizado);

                // Se o chat foi movido para uma aba diferente da atual, atualizar a lista visível
                const statusAtual = chatAtualizado.tp_status;
                const pertenceAbaAtual =
                    (filtroStatus.value.emAtendimento && statusEmAtendimentoArray.includes(statusAtual)) ||
                    (filtroStatus.value.emAndamento && statusEmAndamentoArray.includes(statusAtual));

                // Se o chat NÃO pertence mais à aba atual, remove da lista visível
                if (!pertenceAbaAtual) {
                    const chatIndex = listaUltimosChats.value.findIndex(
                        (chat) => chat.telefone === chatAtualizado.telefone && chat.instance === chatAtualizado.instance
                    );
                    if (chatIndex !== -1) {
                        listaUltimosChats.value.splice(chatIndex, 1);
                    }
                }
            }

            socket.emit('resp.messages', { message_id: message.message_id, idunico: message.idunico });
            // Depois de atualizar a lista de chats, classifique-os com base em message_timestamp
            await sortChatsByTimestamp();
        });
        socket.on('presence.update', (message) => {
            //
            if (message.data.presence == 'disponível' || message.data.presence == 'indisponível') return;

            let result;
            if (listaUltimosChats.value.length > 0) {
                result = listaUltimosChats.value.find(
                    (chat) => chat.telefone === message.data.id.replace('@s.whatsapp.net', '')
                );
            }

            if (result) {
                const presence = message.data.presence + ' ...';
                if (presence == 'digitando' || presence == 'gravando áudio' || presence == 'disponível') {
                    result.online = true;
                } else {
                    result.online = false;
                }
                result.presence = presence;
                result.dateTimePresence = message.data.date_time;
                const index = listaUltimosChats.value.indexOf(result);
                listaUltimosChats.value[index] = result;

                if (chatAtivo.value?.telefone == message.data.id.replace('@s.whatsapp.net', '')) {
                    chatAtivo.value.online = true;
                    chatAtivo.value.presence = presence;
                    chatAtivo.value.dateTimePresence = message.data.date_time;
                }
            }
        });
    }

    const focusProduto = async () => {
        await compPedidoRef.value.setFocusProduto();
    };

    async function ReturnFocusMsg() {
        await focusTextAreaMensagem();
    }

    // Função para determinar a cor do ícone com base no status e no timestamp da mensagem
    const updateColors = () => {
        if (listaUltimosChats.value.length == 0 || !listaUltimosChats.value) return;

        listaUltimosChats.value.forEach((chat, index) => {
            if (statusEmAtendimento.value.includes(chat.tp_status)) {
                chat.online = false;
                chat.presence = undefined;
                const messageTimestamp = new Date(chat.message_timestamp);
                const now = new Date();
                const diffInMinutes = (now - messageTimestamp) / 1000 / 60;

                if (diffInMinutes > 10) {
                    chat.minutos_atendimento = 10;
                } else if (diffInMinutes > 5) {
                    chat.minutos_atendimento = 5;
                } else {
                    chat.minutos_atendimento = 0;
                }

                if (previousColors[index] !== chat.minutos_atendimento) {
                    previousColors[index] = chat.minutos_atendimento; // Atualiza o estado anterior
                } else {
                    chat.minutos_atendimento = previousColors[index]; // Manter o mesmo valor se não mudou
                }
            }
        });
    };

    async function salvaObsCliente() {
        const reqObj = {
            cd_cliente: chatAtivo.value.cd_cliente,
            ds_obscliente: chatAtivo.value.ds_obscliente,
            cd_estabelecimento: cdEstabelecimento.value,
        };

        await ClientesServices.alterar(reqObj);
    }

    async function consultaCliente() {
        //

        if (chatAtivo.value.nr_telefone == undefined) return;

        let filtros = {
            cd_cliente: chatAtivo.value.cd_cliente == 0 ? undefined : chatAtivo.value.cd_cliente,
            nr_telefonezap: chatAtivo.value.nr_telefone,
            cd_estabelecimento: chatAtivo.value.cd_estabelecimento,
        };

        //
        const respClientes = await ClientesServices.listar(filtros);
        //
        if (respClientes.statuscode == 200) {
            chatAtivo.value.ds_endereco = respClientes.data[0].ds_endereco;
            chatAtivo.value.nr_endereco = respClientes.data[0].nr_endereco;
            chatAtivo.value.ds_bairro = respClientes.data[0].ds_bairro;
            chatAtivo.value.ds_complemento = respClientes.data[0].ds_complemento;
            chatAtivo.value.ds_cidade = respClientes.data[0].ds_cidade;
            // formDataPedido.ds_pais =dataCliente.value.ds_pais;
            chatAtivo.value.ds_uf = respClientes.data[0].ds_uf;
        }
    }

    const abreModalObsCliente = (obs) => {
        dsObsCliente.value = obs;
        modalObsCliente.value = true;
    };

    const fechaModalObsCliente = () => {
        chatAtivo.value.ds_obscliente = dsObsCliente.value;
        dsObsCliente.value = '';
        salvaObsCliente();
        modalObsCliente.value = false;
    };

    async function mudancaConexao(dados_instancia) {
        //
        if (dados_instancia.status_connection == 'Conectado') {
            await carregaInstancias(false);
            toast.success('Conexão estabelecida com sucesso!');
        } else if (dados_instancia.status_connection == 'Desconectado') {
            toast.error('Conexão encerrada!');
        } else if (dados_instancia.status_connection == 'Erro') {
            toast.error('Erro ao conectar!');
        }
    }

    function abrirModalConexao() {
        //
        if (listaInstancias.value.length == 1) {
            const dados = {
                id_instancia: listaInstancias.value[0].id_instancia,
                in_stop_bot: listaInstancias.value[0].in_stop_bot,
                nameinstance: listaInstancias.value[0].nameinstance,
                nome: listaInstancias.value[0].nome,
                nr_hash: listaInstancias.value[0].nr_hash,
                nr_telefone: listaInstancias.value[0].telefone,
                status_connection: listaInstancias.value[0].status_connection,
            };
            //
            modalConexaoRef.value?.abrir(dados);
        }
    }

    const consultaPedidoAntedimento = (atendimento) => {
        if (chatAtivo.value) {
            compPedidoRef.value.consultarPedido(atendimento.cd_atendimento, chatAtivo.value.cd_cliente, false);
        }
    };

    // Método para decidir quando mostrar avatar
    const shouldShowAvatar = (mensagem, index) => {
        if (!mensagem) return false;
        if (mensagem.from_me) return false;
        if (index === 0) return true;

        const mensagemAnterior = mensagens.value[index - 1];
        if (!mensagemAnterior) return true;
        if (mensagemAnterior.from_me) return true;

        // Mostrar avatar se for de remetente diferente
        return mensagemAnterior.pushName !== mensagem.pushName;
    };

    // Métodos
    const toggleAttachmentMenu = () => {
        showAttachmentMenu.value = !showAttachmentMenu.value;
        if (showAttachmentMenu.value) {
            showEmojiPicker.value = false;
        }
    };

    const toggleEmojiPicker = () => {
        showEmojiPicker.value = !showEmojiPicker.value;
        if (showEmojiPicker.value) {
            showAttachmentMenu.value = false;
        }
    };

    const carregaUsuarioDepartamentos = async () => {
        const respDepartamentos = await UsuariosServices.listaUsuariosDepartamentos({
            cd_estabelecimento: cdEstabelecimento.value,
            cd_usuario: codUsuario.value,
        });
        if (respDepartamentos.statuscode == 200) {
            usuarioDepartamentos.value = respDepartamentos.data;
        }
    };

    onMounted(async () => {
        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;

        tpAmbiente.value = import.meta.env.MODE;
        //
        window.addEventListener('keydown', atalhoOpcoes);
        window.addEventListener('resize', adjustDropdownPosition);

        requestNotificationPermission();
        pagina.pagename = 'Chat';
        pagina.description = 'Chat';
        pagina.module = 'Chat';
        codUsuario.value = localStorage.getItem('codusuario');
        nmUsuario.value = localStorage.getItem('usuario');
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);

            cdEstabelecimento.value = estabelecimentosLiberado.value[0].cd_estabelecimento;
            nrHash.value = estabelecimentosLiberado.value[0].nr_hash;

            if (modulos.in_crm) {
                filtroStatus.value.emAndamento = true;
                filtroStatus.value.status = statusEmAtendimentoCRM.value;
                filtroStatus.value.arStatus = [statusEmAtendimentoCRM.value];
                await carregaUsuarioDepartamentos();
            }

            await carregaInstancias(true);
            await carregaMensagensAtalhos();
            await consultaAtendimentos('Em Atendimento');
            scrollToBottom();
            eventosSocket();
            setInterval(() => {
                updateColors();
            }, 60000);
        } else {
            toast.warning('Estabelecimento não liberado!');
        }

        watch(modalAtalho, (newVal) => {
            //
            if (newVal) {
                setTimeout(() => {
                    focusPesquisaMensagem(); // Força o foco no input após o modal estar visível
                }, 200); // Tempo adicional para o foco após a abertura do modal
            }
        });
    });

    onUnmounted(async () => {
        contatosNaoLidos.value.clear();
        clearTimeout(timerAtendimento.value);
        window.removeEventListener('keydown', atalhoOpcoes);
        window.removeEventListener('resize', adjustDropdownPosition);
        if (listaInstancias.value) {
            for (const instancia of listaInstancias.value) {
                socket.emit('disconnectRoom', instancia.nameinstance);
            }
            socket.close();
        }
    });
</script>

<style scoped>
    .chat__chat-list {
        height: 400px; /* Defina uma altura fixa */
        overflow-y: auto;
    }
    .dropdown-menu {
        position: absolute;
        top: 100%; /* Ajuste conforme necessário para posicionar o dropdown abaixo do botão */
        left: 0;
        z-index: 1000; /* Garante que o dropdown fique acima de outros elementos */
        display: none; /* Esconde o menu inicialmente */
    }

    .dropdown-menu.show {
        display: block; /* Exibe o menu quando necessário */
    }
    .flex-grow {
        flex-grow: 1;
    }
    .overflow-y-auto {
        overflow-y: auto;
    }
    .suggestions-list {
        position: absolute;
        bottom: 60px; /* Ajuste inicial */
        left: 40px; /* Ajuste a posição conforme necessário */
        right: 0;
        background: white;
        border: 1px solid #ccc;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        border-radius: 5px;
    }

    .suggestions-list ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .suggestions-list li {
        padding: 3px;
        padding-left: 5px;
        cursor: pointer;
    }

    .suggestions-list li.highlighted {
        background-color: #00af6c;
        color: white;
    }
    .form-switch .form-check-input {
        width: 30px !important;
        height: 18px !important;
    }
    .form-switch .form-check-input:before {
        content: '';
        width: 13px !important;
        height: 13px !important;
    }
    .form-switch .form-check-input:checked::before {
        margin-left: 13px !important;
    }

    .table th {
        padding-bottom: 0 !important;
        padding-top: 0 !important;
    }
    .table-report:not(.table-report--bordered):not(.table-report--tabulator) {
        border-spacing: 0 4px;
    }
    .table td {
        padding-bottom: 0 !important;
        padding-top: 0 !important;
        padding-left: 3 !important;
        padding-right: 3 !important;
    }
    .contatoSelecionado {
        background-color: #2ddd6b26;
    }
    .chat .chat__box {
        height: 90vh !important;
    }

    .image-zoom {
        transition: transform 0.5s;
    }

    .image-zoom-zoomed {
        transform: scale(2); /* Ajuste o valor conforme necessário para o zoom desejado */
    }

    .upload-button {
        display: none;
    }
    .remove-icon {
        opacity: 0;
    }

    .remove-icon.show {
        opacity: 1;
    }

    .chat-window {
        flex: 1;
        display: flex;
        flex-direction: column;

        height: 100%;
        border-left: 1px solid #eee;
    }

    .messages {
        flex: 1;
        overflow-y: auto;
        /* padding: 12px;*/
        display: flex;
        flex-direction: column;
        scroll-behavior: smooth;
    }

    .messages-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .chat-header {
        padding: 12px;
        background: #fff;
        border-bottom: 1px solid #eee;
        z-index: 1;
        display: flex;
        justify-content: left;
        align-items: left;
    }

    .contact-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    .contact-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .avatar-placeholder {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2em;
        color: #6c757d;
        text-transform: uppercase;
    }

    .contact-name {
        font-size: 1.1em;
        font-weight: 600;
    }

    .error-message {
        color: #dc3545;
    }

    .messages-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .chat__chat-list-container {
        display: flex;
        flex-direction: column;
        height: 80vh; /* Ajuste conforme necessário */
    }

    .chat__chat-list {
        flex-grow: 1; /* Cresce para ocupar espaço disponível */
        overflow-y: auto;
    }

    .chat__chat-list-pedidos {
        border-top: 1px solid #ccc; /* Linha separadora opcional */
        overflow-y: auto;
    }

    .messages-container {
        /* scroll-behavior: smooth; */
    }

    .messages-container::-webkit-scrollbar {
        width: 6px;
    }

    .messages-container::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.5);
    }

    /* Input com bordas arredondadas igual WhatsApp */
    textarea {
        font-family: inherit;
        line-height: 1.4;
    }

    textarea::-webkit-scrollbar {
        width: 4px;
    }

    textarea::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;
    }

    /* Animações suaves */
    .transition-all {
        transition: all 0.2s ease-in-out;
    }

    /* Hover effects */
    button:hover {
        transform: translateY(-1px);
    }

    button:active {
        transform: translateY(0);
    }

    /* Sombras sutis */
    .shadow-sm {
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    /* Estilo do placeholder */
    ::placeholder {
        color: #9ca3af;
        opacity: 0.8;
    }

    .dark ::placeholder {
        color: #64748b;
        opacity: 0.8;
    }

    .dark .messages-container::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    .dark .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
    }

    .dark .messages-container::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.2s ease, max-height 0.3s ease;
        max-height: 1000px; /* Valor alto o suficiente para conter todas as mensagens */
        overflow: hidden;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
        max-height: 0;
    }
    .highlight-chat {
        background-color: #e3f2fd !important;
        border-left: 4px solid #2196f3 !important;
        transition: all 0.3s ease;
    }

    .dark .highlight-chat {
        background-color: #1565c0 !important;
        border-left: 4px solid #64b5f6 !important;
    }
</style>
