<template>
  <div>
    <ShowLoading ref="loading" />
    <ShowNotifications ref="showNotifications" />
    
    <div class="intro-y flex items-center mt-2">
      <h2 class="text-lg font-medium mr-auto">Status do Sistema de Autorização</h2>
      <button @click="router.push({ name: 'Home' })" class="btn btn-primary-soft col-span-4 mt-2">Voltar</button>
    </div>
    
    <div class="grid grid-cols-12 gap-6 mt-3">
      <div class="intro-y col-span-12">
        <AdminStatus />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import ShowNotifications from '@/components/show-notifications/Main.vue'
import ShowLoading from '@/components/show-loading/Main.vue'
import AdminStatus from '@/components/admin-status/Main.vue'

const router = useRouter()
</script> 