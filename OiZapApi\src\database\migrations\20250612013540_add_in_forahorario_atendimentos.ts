import { Knex } from 'knex';
import path from 'path';

export async function up(knex: Knex): Promise<void> {
  try {
    // Auto-limpeza antes de executar
    const migrationsPath = path.join(__dirname, '..');
    // await cleanOrphanMigrations(knex, migrationsPath);

    // Verifica se a coluna existe
    const hasColumn = await knex.schema.hasColumn('atendimentos', 'in_forahorario');

    if (!hasColumn) {
      // Cria a coluna
      await knex.schema.alterTable('atendimentos', (table) => {
        table
          .boolean('in_forahorario')
          .defaultTo(false)
          .notNullable()
          .comment('Indica se o atendimento está fora do horário');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('atendimentos', (table) => {
      table.dropColumn('in_forahorario');
    });
  } catch (error) {
    throw error;
  }
}
