-- Migration: add_in_forahorario_atendimentos
-- Created: 2025-06-12T01:35:40.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna in_forahorario na tabela atendimentos
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'atendimentos' 
        AND column_name = 'in_forahorario'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE atendimentos ADD COLUMN in_forahorario BOOLEAN DEFAULT FALSE NOT NULL;
        COMMENT ON COLUMN atendimentos.in_forahorario IS 'Indica se o atendimento está fora do horário';
        RAISE NOTICE 'Coluna in_forahorario adicionada à tabela atendimentos';
    ELSE
        RAISE NOTICE 'Coluna in_forahorario já existe na tabela atendimentos';
    END IF;
END $$; 