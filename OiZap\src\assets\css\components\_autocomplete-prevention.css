/* ========================================
   ADVANCED AUTOCOMPLETE PREVENTION
   ======================================== */

/* Método 1: Inputs fake invisíveis via CSS */
.form-fake-inputs::before {
    content: '';
    position: absolute;
    top: -1000px;
    left: -1000px;
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
    background-image: url("data:text/html,<input type='text' name='prevent_autofill_username' autocomplete='username' tabindex='-1'><input type='password' name='prevent_autofill_password' autocomplete='current-password' tabindex='-1'>");
    background-repeat: no-repeat;
    z-index: -9999;
}

/* Método 2: Desabilitar completamente */
.no-browser-features,
.no-browser-features input,
.no-browser-features textarea,
.no-browser-features select {
    autocomplete: off !important;
    autocorrect: off !important;
    autocapitalize: off !important;
    spellcheck: false !important;
    data-form-type: other !important;
    data-lpignore: true !important;
    data-1p-ignore: true !important;
}

/* Método 3: Para campos específicos como busca/filtro */
.search-input,
.filter-input,
input[class*='filtro'],
input[class*='busca'],
input[class*='search'],
input[class*='filter'] {
    autocomplete: new-password !important;
    autocorrect: off !important;
    autocapitalize: off !important;
    spellcheck: false !important;
    data-form-type: other !important;
    data-lpignore: true !important;
    data-1p-ignore: true !important;
}

/* Método 4: Randomizar nomes para confundir o navegador */
.randomize-names input {
    name: attr(data-original-name, randomized-input) !important;
}

/* Método 5: Delay para limpar campos */
.clear-on-focus input:focus,
.clear-on-focus textarea:focus {
    animation: clearAutofill 0.1s ease-in-out;
}

@keyframes clearAutofill {
    0% {
        background: transparent;
    }
    100% {
        background: transparent;
    }
}

/* Método 6: Solução para LastPass e outros gerenciadores */
.lastpass-disable,
.lastpass-disable input,
.lastpass-disable textarea {
    data-lpignore: true !important;
    data-1p-ignore: true !important;
    data-bwignore: true !important;
    data-dashlane-ignore: true !important;
}
