import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('messages migration started');
    let hasColumn = await knex.schema.hasColumn('messages', 'cd_motivo');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.integer('cd_motivo').comment('Indica o motivo do atendimento');
      });
    }
    hasColumn = await knex.schema.hasColumn('messages', 'ds_resumo');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.text('ds_resumo').comment('Indica o resumo do atendimento');
      });
    }
    hasColumn = await knex.schema.hasColumn('messages', 'cd_usuario_encerramento');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.integer('cd_usuario_encerramento').comment('Indica o usuário que encerrou o atendimento');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('messages', (table) => {
      table.dropColumn('cd_motivo');
      table.dropColumn('ds_resumo');
      table.dropColumn('cd_usuario_encerramento');
    });
  } catch (error) {
    throw error;
  }
}
