<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <div class="max-h-screen scrollbar-hidden">
        <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Dashboard</h2>
        </div>

        <!-- BEGIN: Filtros -->
        <div class="grid grid-cols-4 md:grid-cols-12 gap-4 mt-5 mx-2 md:m-8">
            <!-- <div class="col-span-12">
                <div class="flex items-center gap-1 pb-1"><DiamondIcon class="w-4 h-4" /> Nome da Instância</div>
                <input type="text" ref="inputID" class="form-control bg-transparent border-gray-400" />
            </div> -->
            <div class="intro-y col-span-4 md:col-span-12 flex flex-row justify-between items-center w-full">
                <h2 class="font-medium opacity-80 flex items-center ml-2">
                    <UserIcon class="w-4 h-4 mr-1" />
                    {{ nomeUsuario || 'OiZap!' }}
                </h2>
                <div class="flex items-center gap-2">
                    <div class="items-center gap-2 hidden w-0 md:flex md:w-auto">
                        <button
                            v-for="(tempo, index) in listaPeriodosSelect"
                            v-show="index != 4"
                            :key="index"
                            type="button"
                            :class="`btn btn-sm  ${
                                periodoSelecionado === index ? 'btn-primary-soft' : 'btn-secondary-soft'
                            } rounded-full w-20 mr-2`"
                            @click="periodoSelecionado = index"
                        >
                            {{ tempo }}
                        </button>
                    </div>
                    <div class="md:hidden">
                        <Dropdown placement="bottom-end">
                            <DropdownToggle
                                tag="a"
                                :class="`btn btn-sm  btn-primary-soft  rounded-full w-20 mr-2 `"
                                href="javascript:;"
                            >
                                {{ listaPeriodosSelect[periodoSelecionado] }}
                            </DropdownToggle>
                            <DropdownMenu class="w-32">
                                <DropdownContent>
                                    <DropdownItem
                                        v-for="(tempo, index) in listaPeriodosSelect"
                                        v-show="index != 4"
                                        :key="index"
                                        @click="periodoSelecionado = index"
                                    >
                                        {{ tempo }}
                                    </DropdownItem>
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                    <Tippy
                        as="div"
                        class="cursor-pointer"
                        content="Selecionar período personalizado"
                        key="periodo_custom"
                    >
                        <Dropdown placement="bottom-end">
                            <DropdownToggle
                                tag="a"
                                :class="`btn btn-sm  ${
                                    periodoSelecionado == 4 ? 'btn-primary-soft' : 'btn-secondary-soft'
                                } rounded-full `"
                                href="javascript:;"
                            >
                                <CalendarSearchIcon class="w-4 h-4 mr-1" />
                            </DropdownToggle>
                            <DropdownMenu class="w-max">
                                <DropdownContent class="flex items-end gap-2">
                                    <div class="w-64 p-2">
                                        <label for="filtro3" class="form-label mb-0">Período</label>
                                        <div class="relative w-full">
                                            <Litepicker
                                                class="form-control"
                                                v-model="periodoPersonalizado"
                                                @update:modelValue="selecionaPeriodoPersonalizado"
                                                :options="{
                                                    lang: 'pt-BR',
                                                    footer: false,
                                                    autoApply: true,
                                                    singleMode: false,
                                                    numberOfColumns: 2,
                                                    numberOfMonths: 2,
                                                    showWeekNumbers: false,
                                                    dropdowns: {
                                                        minYear: 2022,
                                                        maxYear: new Date().getFullYear(),
                                                        months: true,
                                                        years: true,
                                                    },
                                                    tooltipText: {
                                                        one: 'dia',
                                                        other: 'dias',
                                                    },
                                                    format: 'DD/MM/YYYY',
                                                }"
                                            />
                                            <!-- <XIcon
                                                v-if="filtros.dt_enviado_inicial || filtros.dt_enviado_final"
                                                @click="
                                                    filtros.dt_enviado_inicial = null;
                                                    filtros.dt_enviado_final = null;
                                                    dt_envio = null;
                                                "
                                                class="w-4 h-8 absolute flex justify-center items-center rounded-full right-0 top-0 bottom-0 my-auto ml-auto mr-4 cursor-pointer"
                                            /> -->
                                        </div>
                                    </div>
                                    <!-- <button
                                        class="btn btn-primary-soft btn-sm px-3 h-[37px]"
                                        @click="(value) => selecionaPeriodoPersonalizado(null, true)"
                                    >
                                        Ok
                                    </button> -->
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown>
                    </Tippy>
                </div>
            </div>
        </div>
        <!-- END: Filtros -->

        <div class="border-b border-slate-300/60 mt-2 md:-mt-5 mx-2 md:mx-8"></div>

        <!-- BEGIN: Cards -->
        <div class="grid grid-cols-4 md:grid-cols-12 mx-2 my-6 pb-2 md:mx-8 gap-y-2 gap-x-8">
            <div class="intro-y col-span-4 md:col-span-12 flex flex-row justify-between items-center w-full">
                <h2 class="font-medium opacity-80 flex items-center ml-2">Instâncias</h2>
            </div>
            <div class="col-span-4 intro-y" @click="router.push({ name: 'listaInstancias' })">
                <div class="flex items-center px-5 py-3 mb-3 box zoom-in">
                    <div class="flex-none w-16 h-16 overflow-hidden rounded-full image-fit">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-full text-gray-500 bg-gradient-to-bl from-gray-100 to-gray-300 dark:from-darkmode-300 dark:to-darkmode-400"
                        >
                            <MessageCircleDashedIcon class="w-6 h-6" style="stroke-width: 2.5" />
                        </div>
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="font-medium">Número de Instâncias</div>
                        <!-- <div class="text-slate-500 text-xs mt-0.5">10/10/2023</div> -->
                    </div>
                    <div class="text-gray-600 text-3xl pr-2 font-bold">
                        {{ contagemInstancias.total || 0 }}
                    </div>
                </div>
            </div>
            <div
                class="col-span-4 intro-y"
                @click="router.push({ name: 'listaInstancias', state: { status: 'Conectadas' } })"
            >
                <div class="flex items-center px-5 py-3 mb-3 box zoom-in">
                    <div class="flex-none w-16 h-16 overflow-hidden rounded-full image-fit">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-full text-green-500 bg-gradient-to-bl from-green-100 to-green-300 dark:from-darkmode-300 dark:to-darkmode-400"
                        >
                            <MessageCircleIcon class="w-6 h-6" style="stroke-width: 2.5" />
                        </div>
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="font-medium">Instâncias Conectadas</div>
                        <!-- <div class="text-slate-500 text-xs mt-0.5">10/10/2023</div> -->
                    </div>
                    <div class="text-green-600 text-3xl pr-2 font-bold">
                        {{ contagemInstancias.conectadas || 0 }}
                    </div>
                </div>
            </div>
            <div
                class="col-span-4 intro-y"
                @click="router.push({ name: 'listaInstancias', state: { status: 'Desconectadas' } })"
            >
                <div class="flex items-center px-5 py-3 mb-3 box zoom-in">
                    <div class="flex-none w-16 h-16 overflow-hidden rounded-full image-fit">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-full text-red-500 bg-gradient-to-bl from-red-100 to-red-300 dark:from-darkmode-300 dark:to-darkmode-400"
                        >
                            <MessageCircleOffIcon class="w-6 h-6" style="stroke-width: 2.5" />
                        </div>
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="font-medium">Instâncias Desconectadas</div>
                        <!-- <div class="text-slate-500 text-xs mt-0.5">10/10/2023</div> -->
                    </div>
                    <div class="text-red-600 text-3xl pr-2 font-bold">
                        {{ contagemInstancias.desconectadas || 0 }}
                    </div>
                </div>
            </div>
            <!-- <div class="col-span-4 intro-y !z-0">
                <div
                    :class="[
                        'relative zoom-in',
                        'before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-[\'\']',
                    ]"
                >
                    <div class="p-5 box">
                        <div class="flex">
                            <CheckCheckIcon
                                class="w-[28px] h-[28px] text-success opacity-70"
                                style="stroke-width: 2.5"
                            />
                            <div class="ml-auto">
                                <Tippy
                                    :key="'erro_' + contagem.total_enviado"
                                    as="div"
                                    class="cursor-pointer bg-success py-[3px] flex rounded-full text-white text-xs px-2 items-center font-medium"
                                    :content="`${
                                        contagem.total > 0
                                            ? ((contagem.total_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }% do total de mensagens!`"
                                >
                                    {{
                                        contagem.total > 0
                                            ? ((contagem.total_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }}%
                                </Tippy>
                            </div>
                        </div>

                        <div
                            class="mt-4 md:mt-6 flex flex-row md:flex-col justify-start items-baseline gap-y-1 gap-x-2"
                        >
                            <div class="text-3xl font-medium leading-8">{{ contagem.total_enviado }}</div>
                            <div class="text-base text-slate-500">Enviado</div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
        <!-- END: Cards -->

        <div class="border-b border-slate-300/60 -mt-5 mx-2 md:mx-8"></div>

        <!-- BEGIN: Dados -->
        <div class="grid grid-cols-4 md:grid-cols-12 mx-2 my-6 pb-2 md:mx-8 gap-y-2 gap-x-8">
            <div class="intro-y col-span-4 md:col-span-12 flex flex-row justify-between items-center w-full">
                <h2 class="font-medium opacity-80 flex items-center ml-2">Mensagens</h2>
            </div>

            <div class="col-span-4 md:col-span-8 intro-y">
                <div class="col-span-12 sm:col-span-6 lg:col-span-4 xl:col-span-3">
                    <div class="cursos-pointer w-full p-4 box mb-4">
                        <label class="form-label mb-0">Instâncias: </label>
                        <TomSelect
                            size="small"
                            class="w-full min-w-[180px] !cursor-pointer"
                            @update:modelValue="selecionaInstancia"
                            v-model="instanciaSelecionada"
                            :options="{ allowEmptyOption: true }"
                        >
                            <option value="">Todas</option>
                            <option
                                v-for="item in listaInstancias"
                                :key="item.nameinstance"
                                v-bind:value="item.nameinstance"
                            >
                                {{ item.nome }}
                                {{ item.status_connection == 'Conectado' ? '(conectado)' : '(desconectado)' }}
                            </option>
                        </TomSelect>
                    </div>
                    <div class="p-5 box">
                        <div class="flex items-center">Relação enviado/recebido no período selecionado</div>
                        <div class="flex pb-2 mt-4 border-b border-slate-200">
                            <div class="text-xs text-slate-500">Diferença percentual</div>
                            <Tippy
                                as="div"
                                class="flex ml-auto text-xs font-medium cursor-pointer"
                                :class="{
                                    'text-success':
                                        Number(contagemMensagens.enviadas) <= Number(contagemMensagens.recebidas),
                                    'text-danger':
                                        Number(contagemMensagens.enviadas) > Number(contagemMensagens.recebidas),
                                }"
                                content="Diferença percentual entre mensagens enviadas e recebidas."
                                :key="'diferenca_' + contagemMensagens.enviadas"
                            >
                                {{
                                    (() => {
                                        const media =
                                            (Number(contagemMensagens.enviadas) + Number(contagemMensagens.recebidas)) /
                                            2;
                                        const diferencaAbsoluta = Math.abs(
                                            Number(contagemMensagens.enviadas) - Number(contagemMensagens.recebidas)
                                        );

                                        const diferencaPercentual = (diferencaAbsoluta / media) * 100;
                                        if (isNaN(diferencaPercentual)) return 0;
                                        return diferencaPercentual.toFixed(1) + '%';
                                    })()
                                }}%
                            </Tippy>
                        </div>
                        <div class="mt-2 pb-2 border-b broder-slate-200">
                            <div class="-mb-1.5 -ml-2.5">
                                <!-- <v-chart class="chart" :option="chartOption" autoresize /> -->
                                <LineChart :data="lineValores" :dataAxis="lineLabels" :Legend="lineLegendas" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-4 intro-y">
                <div class="col-span-12 sm:col-span-6 lg:col-span-4 xl:col-span-3">
                    <div class="p-5 box">
                        <div class="flex justify-between items-start">
                            <div>
                                <div class="flex items-center">Mensagens por tipo</div>
                                <div class="mt-1 text-2xl font-medium">
                                    {{ contagemMensagens.total || 0 }}
                                    <p class="text-xs text-slate-500 -my-1 mt-0.5">
                                        <span class="!text-lg">{{ contagemMensagens.enviadas || 0 }}</span> enviadas
                                    </p>
                                    <p class="text-xs text-slate-500 -my-1">
                                        <span class="!text-lg">{{ contagemMensagens.recebidas || 0 }}</span> recebidas
                                    </p>
                                </div>
                            </div>
                            <button
                                type="button"
                                class="btn btn-primary-soft btn-sm py-1 px-2 text-xs font-medium"
                                @click="router.push({ name: 'relMensagens' })"
                            >
                                Relatorio
                            </button>
                        </div>
                        <div class="flex pb-2 mt-1 border-b border-slate-200"></div>
                        <div class="mt-1 pb-2 border-b broder-slate-200">
                            <div class="-mb-1.5 -ml-2.5">
                                <!-- <v-chart class="chart" :option="chartOption" autoresize /> -->
                                <BarChart :data="barValores" :dataAxis="barLabels" />
                            </div>
                        </div>
                        <!-- <div class="flex pb-2 mt-4 mb-2 text-xs border-b text-slate-500 border-slate-200">
                                <div>Top Active Pages</div>
                                <div class="ml-auto">Active Users</div>
                            </div> -->
                        <div
                            class="form-label flex items-center intro-x mt-4"
                            v-for="(chave, index) in barLabels"
                            :key="index"
                        >
                            <span
                                class="w-3 h-3 rounded-full mr-2"
                                :style="{ backgroundColor: barValores[index].itemStyle.color }"
                            ></span>
                            <div class="font-medium opacity-80">
                                {{ chave.split('')[0].toUpperCase() + chave.slice(1) }}
                            </div>
                            <div class="ml-auto">{{ barValores[index].value }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- END: Dados -->
    </div>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';
    import { useRouter } from 'vue-router';
    import { usePagina } from '@/stores/pagina';
    import notifications from '@/components/show-notifications/Main.vue';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import dayjs from 'dayjs';
    import BarChart from '../../../components/charts/BarChart.vue';
    import { is } from 'date-fns/locale';
    import LineChart from '../../../components/charts/LineChart.vue';
    import { Legend } from 'chart.js';
    import { CalendarSearch } from 'lucide-vue-next';
    import converters from '../../../utils/converters';

    const pagina = usePagina();
    const router = useRouter();
    const showNotifications = ref();
    const loading = ref();

    const listaInstancias = ref([]);
    const barValores = ref([]);
    const barLabels = ref([]);
    const lineValores = ref([]);
    const lineLabels = ref([]);
    const lineLegendas = ref(['Total', 'Enviadas', 'Recebidas']);
    const cores = ref([
        '#FF6384', // rosa avermelhado
        '#36A2EB', // azul claro
        '#FFCE56', // amarelo
        '#4BC0C0', // ciano
        '#9966FF', // roxo
        '#FF9F40', // laranja
        '#C9CBCF', // cinza claro
        '#8BC34A', // verde claro
        '#E91E63', // rosa escuro
        '#00BCD4', // azul piscina
        '#CDDC39', // amarelo esverdeado
        '#FF5722', // laranja escuro
        '#3F51B5', // azul índigo
        '#009688', // verde água
    ]);
    const periodoPersonalizado = ref('');

    const nomeUsuario = ref('OiZap!');
    const periodoSelecionado = ref(0);
    const instanciaSelecionada = ref('');
    const listaPeriodosSelect = ref(['Hoje', 'Ontem', 'Semana', 'Mês', 'Busca']);
    const listaPeriodos = ref([
        [new Date(), new Date(), 0],
        [dayjs(new Date()).subtract(1, 'day'), dayjs(new Date()).subtract(1, 'day'), 1],
        [dayjs(new Date()).subtract(7, 'day'), dayjs(new Date()), 6],
        [dayjs(new Date()).subtract(1, 'month'), dayjs(new Date()), 30],
        ['', '', 0],
    ]);

    const contagemInstancias = ref({
        conectadas: 0,
        desconectadas: 0,
        total: 0,
    });
    const contagemMensagens = ref({
        enviadas: 0,
        recebidas: 0,
        total: 0,
    });

    function montarBarChart(data) {
        barLabels.value = [];
        barValores.value = [];

        Object.keys(data).forEach((chave, index) => {
            const chaveIgnorada = chave != 'mensagens_periodo' && chave != 'enviadas' && chave != 'recebidas';
            if (chaveIgnorada) {
                barLabels.value.push(chave);
                barValores.value.push({
                    value: data[chave],
                    itemStyle: {
                        color: cores.value[chaveIgnorada ? index - 2 : index - 1] || cores.value[index - 3],
                    },
                });
            }
        });
    }

    function montarLineChart(data) {
        lineLabels.value = [];
        lineValores.value = [];

        const series = [[], [], []];
        const cores = ['#848484', '#36db52', '#2844d3'];
        data.forEach((item, index) => {
            lineLabels.value.push(dayjs(item.dia).format('DD/MM/YYYY'));
            series[0].push(item.total_mensagens);
            series[1].push(item.total_enviadas);
            series[2].push(item.total_recebidas);
        });
        lineLegendas.value.forEach((item, index) => {
            lineValores.value.push({
                name: item,
                type: 'line',
                stack: 'Total',
                data: series[index],
                itemStyle: {
                    color: cores[index],
                },
                smooth: true,
            });
        });
    }

    function selecionaInstancia(value) {
        if (!value) {
            instanciaSelecionada.value = '';
            carregaDadosDashboard();
            return;
        }
        const instancia = listaInstancias.value.find((instancia) => instancia.nameinstance === value);
        // if (instancia?.status_connection !== 'Conectado') {
        //     setTimeout(() => {
        //         instanciaSelecionada.value = '';
        //     }, 100);
        //     showNotifications.value.showWarningNotification(
        //         'Instância não conectada. Não é possível testar os endpoints.'
        //     );
        //     return;
        // }
        instanciaSelecionada.value = instancia.nameinstance;
        carregaDadosDashboard();
    }

    function selecionaPeriodoPersonalizado(value) {
        if (dayjs(listaPeriodos.value[0][0]).format('DD/MM/YYYY') == value.split(' - ')[0]) {
            return console.log('igual');
        }

        const [inicio, fim] = value.split(' - ');

        listaPeriodos.value[4][0] = converters.date('YYYY-MM-DD', inicio);
        listaPeriodos.value[4][1] = converters.date('YYYY-MM-DD', fim);
        listaPeriodos.value[4][2] = contarDiasPeriodo(inicio, fim);
        periodoSelecionado.value = 4;
        console.log('periodo4', listaPeriodos.value[4]);

        carregaDadosDashboard();
    }

    function contarDiasPeriodo(inicio, fim) {
        const dataInicio = dayjs(converters.date('YYYY-MM-DD', inicio));
        const dataFim = dayjs(converters.date('YYYY-MM-DD', fim));
        const dias = dataFim.diff(dataInicio, 'day') + 1;

        return dias;
    }

    async function carregaDadosDashboard() {
        loading.value.show();
        contagemMensagens.value = {
            enviadas: 0,
            recebidas: 0,
            total: 0,
        };
        const periodo = listaPeriodos.value[periodoSelecionado.value].map((item, index) => {
            if (index == 2) {
                const periodosPersonalizados = {
                    0: 1,
                    1: 2,
                    6: 6,
                    30: 30,
                };
                return periodosPersonalizados[item] || item;
            }
            return dayjs(item).format('YYYY-MM-DD');
        });
        // console.log('periodo', periodo);

        const filtros = {
            cd_estabelecimento: localStorage.getItem('estabelecimentos')[0].cd_estabelecimento,
            dt_enviado_inicial: periodo[0],
            dt_enviado_final: periodo[1],
            instance: instanciaSelecionada.value || null,
            periodo: periodo[2],
        };
        const result = await InstanciaServices.listarDadosDashboard(filtros);
        if (result.statuscode == 200) {
            montarBarChart(result.data[0]);
            montarLineChart(result.data[0]?.mensagens_periodo);
            contagemMensagens.value = {
                total: Number(result.data[0].enviadas) + Number(result.data[0].recebidas),
                recebidas: result.data[0].recebidas,
                enviadas: result.data[0].enviadas,
            };
        } else {
            showNotifications.value.showWarningNotification(result.message);
            contagemMensagens.value = {
                total: 0,
                recebidas: 0,
                enviadas: 0,
            };
            barValores.value = [];
            barLabels.value = [];
            lineValores.value = [];
            lineLabels.value = [];
            montarBarChart({});
            montarLineChart([]);
        }

        loading.value.hide(500);
    }

    async function carregaInstancias() {
        loading.value.show();
        const filtros = {
            cd_estabelecimento: localStorage.getItem('estabelecimentos')[0].cd_estabelecimento,
            cd_usuario: parseInt(localStorage.getItem('codusuario')),
            in_verificastatus: true,
        };

        const result = await InstanciaServices.listar(filtros);

        if (result.statuscode == 200) {
            contagemInstancias.value = {
                conectadas: result.data.filter((item) => item.status_connection == 'Conectado').length,
                desconectadas: result.data.filter((item) => item.status_connection != 'Conectado').length,
                total: result.data.length,
            };
            listaInstancias.value = result.data;
            if (listaInstancias.value.length == 0) {
                showNotifications.value.showWarningNotification(
                    'Nenhuma instância encontrada, adicione uma instância para testar os endpoints!'
                );
                listaInstancias.value = [];
            }
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
            contagemInstancias.value = {
                conectadas: 0,
                desconectadas: 0,
                total: 0,
            };
        } else {
            showNotifications.value.showErrorNotification(result.message);
            contagemInstancias.value = {
                conectadas: 0,
                desconectadas: 0,
                total: 0,
            };
        }
        loading.value.hide();
    }

    onMounted(async () => {
        pagina.pagename = 'Dashboard';
        pagina.description = 'Dahsboard';
        pagina.module = 'Dashboard';

        periodoPersonalizado.value = '';
        nomeUsuario.value = localStorage.getItem('usuario') || 'OiZap!';
        await carregaInstancias();
        await carregaDadosDashboard();
    });

    watch(periodoSelecionado, async (newValue) => {
        await carregaDadosDashboard();
    });
</script>

<style scoped>
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-dropdown) {
        border-radius: 1.5rem !important;
        margin-top: 2px !important;
        overflow: hidden !important;
        padding: 0.4rem 0 !important;
    }
    ::v-deep(.option) {
        margin: 0.4rem 0 !important;
    }
    ::v-deep(.tom-select .ts-input) {
        cursor: pointer !important;
    }
    ::v-deep(.dropdown-item-disabled) {
        opacity: 0.4 !important;
        pointer-events: none !important;
    }
</style>
