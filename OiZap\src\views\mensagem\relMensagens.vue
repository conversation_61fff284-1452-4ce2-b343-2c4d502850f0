<template>
    <ShowLoading ref="loading" />

    <div class=" max-h-screen scrollbar-hidden">
        <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Relatório</h2>
            <button class="btn btn-secondary btn-sm" @click="router.go(-1)">Voltar</button>
        </div>

        <!-- BEGIN: Filtros -->
        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6 intro-y pb-2">
            <div class="flex flex-row justify-between items-center gap-y-4 gap-x-4">
                <div>
                    <h2 class="text-lg font-semibold">Filtros</h2>
                    <p class="text-md text-slate-600 dark:text-slate-400">
                        Utilize os filtros abaixo para refinar sua busca.
                    </p>
                </div>
                <div class="flex items-center gap-2">
                    <button
                        type="button"
                        class="btn btn-primary-soft btn-sm sx:btn-md flex items-center gap-1"
                        :class="{ '!btn-secondary-soft': !showFiltros }"
                        @click="showFiltros = !showFiltros"
                    >
                        <ListFilterIcon class="w-4 h-4 pb-0.5" />
                        Filtrar
                    </button>
                </div>
            </div>
            <div
                class="flex items-end gap-4 gap-y-3 mt-5 transition-transform flex-wrap md:flex-nowrap"
                v-show="showFiltros"
            >
                <div class="w-full">
                    <label for="instancia" class="form-label mb-0">Instância</label>
                    <div class="form-control">
                        <TomSelect
                            id="instancia"
                            v-model="filtros.instance"
                            size="small"
                            class="w-full min-w-[180px] !cursor-pointer"
                        >
                            <option v-for="item in listaInstancias" :key="item.nameinstance" :value="item.nameinstance">
                                {{ item.nome }}
                                {{ item.status_connection == 'Conectado' ? '(conectado)' : '(desconectado)' }}
                            </option>
                        </TomSelect>
                    </div>
                </div>
                <div class="w-full">
                    <label for="status" class="form-label mb-0">Status</label>
                    <div class="form-control">
                        <TomSelect
                            id="status"
                            v-model="filtros.tp_status"
                            :options="{
                                placeholder: 'Selecione o módulo',
                            }"
                            size="small"
                            class="w-full min-w-[180px] !cursor-pointer"
                            @change="selecionaStatus"
                        >
                            <option v-for="item in listaStatus" :key="item.label" v-bind:value="item.label">
                                {{ item.label }}
                            </option>
                        </TomSelect>
                    </div>
                </div>
                <div class="w-full">
                    <label for="telefone" class="form-label mb-0">Telefone</label>
                    <input
                        id="telefone"
                        type="text"
                        class="form-control"
                        v-model="filtros.telefone"
                        @input="formatarTelefone"
                        placeholder="(00) 00000-0000"
                    />
                </div>
            </div>
            <div
                class="flex items-end gap-4 gap-y-3 mt-5 transition-transform flex-wrap md:flex-nowrap"
                v-show="showFiltros"
            >
                <div class="w-full">
                    <label for="filtro3" class="form-label mb-0">Data de Envio</label>
                    <!-- <input id="filtro1" type="text" class="form-control" placeholder="Filtro 1" /> -->
                    <div class="relative w-full mx-auto">
                        <Litepicker
                            class="form-control"
                            v-model="dt_envio"
                            @update:modelValue="(value) => selecionarData(value, 'dt_enviado')"
                            :options="{
                                lang: 'pt-BR',
                                footer: false,
                                autoApply: true,
                                singleMode: false,
                                numberOfColumns: 2,
                                numberOfMonths: 2,
                                showWeekNumbers: false,
                                dropdowns: {
                                    minYear: 2022,
                                    maxYear: new Date().getFullYear(),
                                    months: true,
                                    years: true,
                                },
                                tooltipText: {
                                    one: 'dia',
                                    other: 'dias',
                                },
                                format: 'DD/MM/YYYY',
                            }"
                        />
                        <XIcon
                            v-if="filtros.dt_enviado_inicial || filtros.dt_enviado_final"
                            @click="
                                filtros.dt_enviado_inicial = null;
                                filtros.dt_enviado_final = null;
                                dt_envio = null;
                            "
                            class="w-4 h-8 absolute flex justify-center items-center rounded-full right-0 top-0 bottom-0 my-auto ml-auto mr-4 cursor-pointer"
                        />
                    </div>
                </div>
                <div class="w-full">
                    <label for="filtro3" class="form-label mb-0">Data de Recebimento</label>
                    <!-- <input id="filtro1" type="text" class="form-control" placeholder="Filtro 1" /> -->
                    <div class="relative w-full mx-auto">
                        <Litepicker
                            class="form-control"
                            v-model="dt_recebido"
                            defaultDate=""
                            @update:modelValue="(value) => selecionarData(value, 'dt_recebido')"
                            :options="{
                                lang: 'pt-BR',
                                footer: false,
                                autoApply: true,
                                singleMode: false,
                                numberOfColumns: 2,
                                numberOfMonths: 2,
                                showWeekNumbers: true,
                                buttonText: {
                                    apply: 'Confirmar',
                                    cancel: 'Cancelar',
                                },
                                dropdowns: {
                                    minYear: 2022,
                                    maxYear: null,
                                    months: true,
                                    years: true,
                                },
                                tooltipText: {
                                    one: 'dia',
                                    other: 'dias',
                                },
                                format: 'DD/MM/YYYY',
                            }"
                        />
                        <XIcon
                            v-if="filtros.dt_recebido_inicial || filtros.dt_recebido_final"
                            @click="
                                filtros.dt_recebido_inicial = null;
                                filtros.dt_recebido_final = null;
                                dt_recebido = null;
                            "
                            class="w-4 h-8 absolute flex justify-center items-center rounded-full right-0 top-0 bottom-0 my-auto ml-auto mr-4 cursor-pointer"
                        />
                    </div>
                </div>
                <div class="w-full">
                    <label for="filtro3" class="form-label mb-0">Data de Lido</label>
                    <!-- <input id="filtro1" type="text" class="form-control" placeholder="Filtro 1" /> -->
                    <div class="relative w-full mx-auto">
                        <Litepicker
                            class="form-control"
                            v-model="dt_lido"
                            @update:modelValue="(value) => selecionarData(value, 'dt_lido')"
                            :options="{
                                lang: 'pt-BR',
                                footer: false,
                                autoApply: true,
                                singleMode: false,
                                numberOfColumns: 2,
                                numberOfMonths: 2,
                                showWeekNumbers: true,
                                // lockFuture: [['2001-01-01', '2025-04-11']],
                                buttonText: {
                                    apply: 'Confirmar',
                                    cancel: 'Cancelar',
                                },
                                dropdowns: {
                                    minYear: 2022,
                                    maxYear: null,
                                    months: true,
                                    years: true,
                                },
                                tooltipText: {
                                    one: 'dia',
                                    other: 'dias',
                                },
                                format: 'DD/MM/YYYY',
                            }"
                        />
                        <XIcon
                            v-if="filtros.dt_lido_inicial || filtros.dt_lido_final"
                            @click="
                                filtros.dt_lido_inicial = null;
                                filtros.dt_lido_final = null;
                                dt_lido = null;
                            "
                            class="w-4 h-8 absolute flex justify-center items-center rounded-full right-0 top-0 bottom-0 my-auto ml-auto mr-4 cursor-pointer"
                        />
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button type="button" class="btn btn-primary w-full" @click="() => carregaRelatorio()">
                        Pesquisar
                    </button>
                    <button type="button" class="btn btn-secondary min-w-max" @click="limparFiltros">Limpar</button>
                </div>
            </div>
        </div>
        <!-- END: Filtros -->

        <!-- BEGIN: Cards -->
        <div class="grid grid-cols-4 md:grid-cols-12 mx-2 my-6 pb-2 md:m-8 gap-8">
            <div class="col-span-4 intro-y !z-0">
                <div
                    :class="[
                        'relative zoom-in',
                        'before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-[\'\']',
                    ]"
                >
                    <div
                        class="p-5 box"
                        @click="
                            if (filtros.tp_status == 'Enviado') {
                                filtros.tp_status = null;
                            } else {
                                filtros.tp_status = 'Enviado';
                            }
                            carregaRelatorio(true);
                        "
                    >
                        <div class="flex">
                            <CheckCheckIcon
                                class="w-[28px] h-[28px] text-success opacity-70"
                                style="stroke-width: 2.5"
                            />
                            <div class="ml-auto">
                                <Tippy
                                    :key="'erro_' + contagem.total_enviado"
                                    as="div"
                                    class="cursor-pointer bg-success py-[3px] flex rounded-full text-white text-xs px-2 items-center font-medium"
                                    :content="`${
                                        contagem.total > 0
                                            ? ((contagem.total_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }% do total de mensagens!`"
                                >
                                    {{
                                        contagem.total > 0
                                            ? ((contagem.total_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }}%
                                </Tippy>
                            </div>
                        </div>

                        <div
                            class="mt-4 md:mt-6 flex flex-row md:flex-col justify-start items-baseline gap-y-1 gap-x-2"
                        >
                            <div class="text-3xl font-medium leading-8">{{ contagem.total_enviado }}</div>
                            <div class="text-base text-slate-500">Enviado</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-4 intro-y !z-0">
                <div
                    :class="[
                        'relative zoom-in',
                        'before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-[\'\']',
                    ]"
                >
                    <div
                        class="p-5 box"
                        @click="
                            if (filtros.tp_status == 'Não Enviado') {
                                filtros.tp_status = null;
                            } else {
                                filtros.tp_status = 'Não Enviado';
                            }
                            carregaRelatorio(true);
                        "
                    >
                        <div class="flex">
                            <ClockIcon class="w-[28px] h-[28px] text-pending opacity-70" style="stroke-width: 2.5" />
                            <div class="ml-auto">
                                <Tippy
                                    :key="'erro_' + contagem.total_nao_enviado"
                                    as="div"
                                    class="cursor-pointer bg-pending py-[3px] flex rounded-full text-white text-xs px-2 items-center font-medium"
                                    :content="`${
                                        contagem.total > 0
                                            ? ((contagem.total_nao_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }% do total de mensagens!`"
                                >
                                    {{
                                        contagem.total > 0
                                            ? ((contagem.total_nao_enviado / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }}%
                                </Tippy>
                            </div>
                        </div>
                        <div
                            class="mt-4 md:mt-6 flex flex-row md:flex-col justify-start items-baseline gap-y-1 gap-x-2"
                        >
                            <div class="text-3xl font-medium leading-8">{{ contagem.total_nao_enviado }}</div>
                            <div class="text-base text-slate-500">Não Enviado</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-4 intro-y !z-0">
                <div
                    :class="[
                        'relative zoom-in',
                        'before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-[\'\']',
                    ]"
                >
                    <div
                        class="p-5 box"
                        @click="
                            filtros.tp_status = 'Erro';

                            carregaRelatorio(true);
                        "
                    >
                        <div class="flex">
                            <AlertCircleIcon
                                class="w-[28px] h-[28px] text-danger opacity-70"
                                style="stroke-width: 2.5"
                            />
                            <div class="ml-auto">
                                <Tippy
                                    :key="'erro_' + contagem.total_erro"
                                    as="div"
                                    class="cursor-pointer bg-danger py-[3px] flex rounded-full text-white text-xs px-2 items-center font-medium"
                                    :content="`${
                                        contagem.total > 0
                                            ? ((contagem.total_erro / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }% do total de mensagens!`"
                                >
                                    {{
                                        contagem.total > 0
                                            ? ((contagem.total_erro / contagem.total) * 100).toFixed(1)
                                            : 0
                                    }}%
                                </Tippy>
                            </div>
                        </div>
                        <div
                            class="mt-4 md:mt-6 flex flex-row md:flex-col justify-start items-baseline gap-y-1 gap-x-2"
                        >
                            <div class="text-3xl font-medium leading-8">{{ contagem.total_erro }}</div>
                            <div class="text-base text-slate-500">Indefinido</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: Cards -->

        <!-- BEGIN: Lista -->
        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6 intro-y pb-6">
            <div class="flex flex-col md:flex-row justify-between items-center gap-y-4 gap-x-4">
                <div>
                    <h2 class="text-lg font-semibold">Mensagens</h2>
                    <p class="text-md text-slate-600 dark:text-slate-400">
                        Aqui estão as mensagens enviadas e recebidas.
                    </p>
                </div>
                <!-- Botões de ação -->
                <div class="flex items-center gap-2">
                    <button
                        type="button"
                        class="btn btn-primary btn-sm"
                        :disabled="mensagensSelecionadas.length === 0"
                        @click="abreModalConfirmaTodos('selecionadas')"
                    >
                        Reenviar Selecionadas ({{ mensagensSelecionadas.length }})
                    </button>
                    <button
                        type="button"
                        class="btn btn-secondary btn-sm"
                        :disabled="listaRelatorio.lista?.length === 0"
                        @click="abreModalConfirmaTodos('todos')"
                    >
                        Reenviar Todas
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto scrollbar-hidden">
                <div id="tabulator" ref="tableRef" class="mt-5"></div>
            </div>
        </div>
        <!-- END: Lista -->
    </div>

    <!-- Modal  confirma remover-->
    <Modal :show="modalConfirmaTodos" @hidden="fechaModalConfirma">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-warning/30 rounded-full flex justify-center items-center">
                <AlertTriangleIcon class="text-warning w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Confirmação</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label v-if="tpMensagens == 'todos'">
                        Deseja reenviar todas as mensagens? Esta ação não pode ser desfeita.
                    </label>
                    <label v-else> Deseja reenviar as mensagens selecionadas? Esta ação não pode ser desfeita. </label>
                </div>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="fechaModalConfirma" class="btn btn-danger-soft w-24 mr-2">Não</button>
            <button type="button" class="btn btn-success-soft w-24" @click="confirmaReenvio">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma remover -->
</template>

<script setup>
    import { ref, reactive, onMounted, toRaw } from 'vue';
    import { useRouter, useRoute } from 'vue-router';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import MessagesServices from '@/services/chat/MessagesServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import { usePagina } from '@/stores/pagina';
    import converters from '../../utils/converters';
    import dayjs from 'dayjs';
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();
    const router = useRouter();
    let pagina = usePagina();
    const route = useRoute();

    const loading = ref();
    const tableRef = ref(null);
    const tabulator = ref(null);
    const showFiltros = ref(false);
    const pularChecagem = ref(true);
    const modalConfirmaTodos = ref();
    const listaInstancias = ref([]);
    const listaRelatorio = ref([]);
    const mensagensSelecionadas = ref([]); // Array para armazenar IDs das mensagens selecionadas
    const tpMensagens = ref();
    const contagem = ref({
        total_enviado: 0,
        total_nao_enviado: 0,
        total_erro: 0,
        total: 0,
    });
    const dt_envio = ref(null);
    const dt_recebido = ref(null);
    const dt_lido = ref(null);
    const filtros = reactive({
        id: null,
        instance: null,
        tp_status: null,
        telefone: '',
        dt_enviado_inicial: null,
        dt_enviado_final: null,
        dt_recebido_inicial: null,
        dt_recebido_final: null,
        dt_lido_inicial: null,
        dt_lido_final: null,
    });

    const listaStatus = ref([{ label: '' }, { label: 'Fila' }, { label: 'Recebido' }, { label: 'Lido' }]);

    function sorterDateColumns(a, b) {
        const dateA = new Date(a).getTime();
        const dateB = new Date(b).getTime();
        return dateA - dateB;
    }

    const initTabulator = () => {
        if (tableRef.value) {
            tabulator.value = new Tabulator(tableRef.value, {
                data: listaRelatorio.value,
                locale: 'pt-BR',
                pagination: true,
                paginationButtonCount: 3,
                paginationMode: 'local',
                paginationSize: 25,
                paginationSizeSelector: [25, 50, 100, 200, 500],
                layout: 'fitColumns',
                responsiveLayout: false,
                placeholder: 'Nenhuma mensagem encontrada!',
                selectableRows: true,
                columns: [
                    {
                        title: 'Selecionar',
                        field: 'select',
                        width: 80,
                        hozAlign: 'center',
                        headerSort: false,
                        resizable: false,
                        formatter: function (cell, formatterParams, onRendered) {
                            const row = cell.getRow();
                            const data = row.getData();
                            const isSelected = mensagensSelecionadas.value.includes(data.id);

                            return `<input type="checkbox" ${isSelected ? 'checked' : ''}
                                    onchange="window.toggleMensagem(${data.id}, this.checked)"
                                    class="form-check-input">`;
                        },
                        cellClick: function (e, cell) {
                            e.stopPropagation();
                            const checkbox = e.target;
                            if (checkbox.type === 'checkbox') {
                                const data = cell.getRow().getData();
                                toggleMensagemSelecionada(data.id, checkbox.checked);
                            }
                        },
                    },
                    {
                        title: 'Instância',
                        field: 'instance',
                        width: 120, // Mudança: usar width fixo ao invés de minWidth
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                    },
                    {
                        title: 'Envio',
                        field: 'message_timestamp',
                        width: 150, // Mudança: usar width fixo
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                        formatter(cell) {
                            const response = cell.getData();
                            return `<p><strong>${converters.date(
                                'DD/MM HH:MM:SS',
                                response.message_timestamp
                            )}</strong> </p>`;
                        },
                        sorter: sorterDateColumns,
                    },
                    {
                        title: 'Recebido',
                        field: 'date_received',
                        width: 150, // Mudança: usar width fixo
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                        formatter(cell) {
                            const response = cell.getData();
                            return `<p><strong>${converters.date(
                                'DD/MM HH:MM:SS',
                                response.date_received
                            )}</strong></p>`;
                        },
                        sorter: sorterDateColumns,
                    },
                    {
                        title: 'Lido',
                        field: 'date_read',
                        width: 150, // Mudança: usar width fixo
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                        formatter(cell) {
                            const response = cell.getData();
                            return `<p><strong>${converters.date('DD/MM HH:MM:SS', response.date_read)}</strong> </p>`;
                        },
                        sorter: sorterDateColumns,
                    },
                    {
                        title: 'Contato',
                        field: 'telefone',
                        width: 180, // Mudança: usar width fixo
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                        formatter(cell) {
                            const response = cell.getData();
                            return formatarTelefone({ target: { value: response.telefone } }, false, true);
                        },
                    },
                    {
                        title: 'Mensagem',
                        field: 'mensagem',
                        width: 200,
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                    },
                    {
                        title: 'Status',
                        field: 'tp_status',
                        width: 100,
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                    },
                    {
                        title: 'Log',
                        field: 'ds_log',
                        width: 450, // Mudança: usar width fixo
                        vertAlign: 'middle',
                        print: false,
                        download: false,
                    },
                ],
            });

            // Adicionar checkbox "Selecionar Todos" no header
            const headerCheckbox = document.createElement('input');
            headerCheckbox.type = 'checkbox';
            headerCheckbox.className = 'form-check-input';
            headerCheckbox.addEventListener('change', function () {
                selecionarTodasMensagens(this.checked);
            });

            // Adicionar o checkbox ao header da coluna "Selecionar"
            setTimeout(() => {
                const headerCell = document.querySelector('[tabulator-field="select"] .tabulator-col-title');
                if (headerCell) {
                    headerCell.innerHTML = '';
                    headerCell.appendChild(headerCheckbox);
                }
            }, 100);
        }
    };

    // Função para alternar seleção de mensagem individual
    function toggleMensagemSelecionada(id, isSelected) {
        if (isSelected) {
            if (!mensagensSelecionadas.value.includes(id)) {
                mensagensSelecionadas.value.push(id);
            }
        } else {
            const index = mensagensSelecionadas.value.indexOf(id);
            if (index > -1) {
                mensagensSelecionadas.value.splice(index, 1);
            }
        }

        // Atualizar o checkbox do header
        updateHeaderCheckbox();
    }

    // Função para selecionar/deselecionar todas as mensagens
    function selecionarTodasMensagens(selectAll) {
        if (selectAll) {
            mensagensSelecionadas.value = listaRelatorio.value.lista?.map((msg) => msg.id) || [];
        } else {
            mensagensSelecionadas.value = [];
        }

        // Recriar a tabela para atualizar os checkboxes
        if (tabulator.value) {
            tabulator.value.setData(listaRelatorio.value.lista);
        }
    }

    // Função para atualizar o checkbox do header
    function updateHeaderCheckbox() {
        setTimeout(() => {
            const headerCheckbox = document.querySelector('[tabulator-field="select"] input[type="checkbox"]');
            if (headerCheckbox) {
                const totalMensagens = listaRelatorio.value.lista?.length || 0;
                const selecionadas = mensagensSelecionadas.value.length;

                if (selecionadas === 0) {
                    headerCheckbox.checked = false;
                    headerCheckbox.indeterminate = false;
                } else if (selecionadas === totalMensagens) {
                    headerCheckbox.checked = true;
                    headerCheckbox.indeterminate = false;
                } else {
                    headerCheckbox.checked = false;
                    headerCheckbox.indeterminate = true;
                }
            }
        }, 50);
    }

    function confirmaReenvio() {
        if (tpMensagens.value === 'todos') {
            reenviarTodasMensagens();
        } else {
            reenviarMensagensSelecionadas();
        }
        modalConfirmaTodos.value = false;
    }

    // Função para reenviar mensagens selecionadas
    async function reenviarMensagensSelecionadas() {
        if (mensagensSelecionadas.value.length === 0) {
            toast.warning('Selecione pelo menos uma mensagem para reenviar.');
            return;
        }

        loading.value.show();
        try {
            const payload = {
                ids: mensagensSelecionadas.value,
            };

            const result = await MessagesServices.reenviarMensagens(payload);

            if (result && result.statuscode === 200) {
                toast.success(`${mensagensSelecionadas.value.length} mensagem(ns) reenviada(s) com sucesso!`);

                // Limpar seleções
                mensagensSelecionadas.value = [];

                // Recarregar o relatório
                await carregaRelatorio();
            } else {
                toast.error(result?.message || 'Erro ao reenviar mensagens selecionadas.');
            }
        } catch (error) {
            console.error('Erro ao reenviar mensagens:', error);
            toast.error('Erro ao reenviar mensagens selecionadas.');
        } finally {
            loading.value.hide();
        }
    }

    function abreModalConfirmaTodos(tpMsg) {
        if (filtros.instance === null || filtros.instance === '') {
            toast.warning('Selecione uma instância para reenviar as mensagens.');
            return;
        }
        const findInstancia = listaInstancias.value.find((inst) => inst.nameinstance === filtros.instance);
        if (!findInstancia || findInstancia.status_connection !== 'Conectado') {
            toast.warning(
                'Não é possivel reenviar as mensagens, pois a instância está desconectada, conecte a instância primeiro.'
            );
            return;
        }

        if (tpMsg === 'todos' && listaRelatorio.value.lista.length === 0) {
            toast.warning('Não há mensagens para reenviar.');
            return;
        } else if (tpMsg === 'selecionadas' && mensagensSelecionadas.value.length === 0) {
            toast.warning('Selecione pelo menos uma mensagem para reenviar.');
            return;
        }
        tpMensagens.value = tpMsg;
        modalConfirmaTodos.value = true;
    }

    // Função para reenviar todas as mensagens
    async function reenviarTodasMensagens() {
        if (!listaRelatorio.value.lista || listaRelatorio.value.lista.length === 0) {
            toast.warning('Não há mensagens para reenviar.');
            return;
        }

        loading.value.show();
        try {
            const allIds = listaRelatorio.value.lista.map((msg) => msg.id);
            const payload = {
                ids: allIds,
            };

            const result = await MessagesServices.reenviarMensagens(payload);

            if (result && result.statuscode === 200) {
                toast.success(`Todas as ${allIds.length} mensagens foram reenviadas com sucesso!`);

                // Limpar seleções
                mensagensSelecionadas.value = [];

                // Recarregar o relatório
                await carregaRelatorio();
            } else {
                toast.error(result?.message || 'Erro ao reenviar todas as mensagens.');
            }
        } catch (error) {
            console.error('Erro ao reenviar todas as mensagens:', error);
            toast.error('Erro ao reenviar todas as mensagens.');
        } finally {
            loading.value.hide();
        }
    }

    // Tornar a função disponível globalmente para o onclick do checkbox
    window.toggleMensagem = toggleMensagemSelecionada;

    function selecionarData(valor, chave) {
        if (!valor) return;
        const inicial = valor.split('-')[0].trim();
        const final = valor.split('-')[1].trim();
        filtros[chave + '_inicial'] = converters.date('YYYY-MM-DD', inicial);
        filtros[chave + '_final'] = converters.date('YYYY-MM-DD', final);
    }

    function formatarTelefone(event, removerEspeciais, retornarValor) {
        if (removerEspeciais) {
            return event.target.value.replace(/\D/g, '');
        }
        let valor = event.target.value;
        valor = valor?.replace(/\D/g, '');
        if (valor?.length <= 10) {
            valor = valor?.replace(/^(\d{2})(\d{2})(\d{4})(\d{0,4})/, '+$1 ($1) $2-$3');
        } else {
            valor = valor?.replace(/^(\d{2})(\d{2})(\d{5})(\d{4})/, '+$1 ($2) $3-$4');
        }

        if (retornarValor) {
            return valor;
        } else {
            telefone.value = valor;
        }
    }

    function limparFiltros() {
        filtros.instance = null;
        filtros.tp_status = null;
        filtros.telefone = '';
        filtros.dt_enviado_inicial = dayjs(new Date()).format('YYYY-MM-DD');
        filtros.dt_enviado_final = dayjs(new Date()).format('YYYY-MM-DD');
        filtros.dt_recebido_inicial = '';
        filtros.dt_recebido_final = '';
        filtros.dt_lido_inicial = '';
        filtros.dt_lido_final = '';
        dt_envio.value = dayjs(new Date()).format('DD/MM/YYYY') + ' - ' + dayjs(new Date()).format('DD/MM/YYYY');
        dt_recebido.value = null;
        dt_lido.value = null;
    }

    async function carregaInstancias() {
        loading.value.show();
        try {
            const codUsuario = localStorage.getItem('codusuario');
            const estatabelecimentos = localStorage.getItem('estabelecimentos');
            const estabelecimentosLiberado = JSON.parse(estatabelecimentos);

            let filtrosI = {
                cd_estabelecimento: estabelecimentosLiberado[0].cd_estabelecimento,
                cd_usuario: parseInt(codUsuario),
                instance: filtros.instance,
                in_verificastatus: true,
            };

            listaInstancias.value = [];

            let result = await InstanciaServices.listar(filtrosI);
            //console.log('result at line 830 in mensagem/relMensagens.vue:', result);

            if (result.statuscode == 200) {
                listaInstancias.value = result.data;

                if (listaInstancias.value.length == 0) {
                    toast.warning('Nenhuma instância encontrada, adicione uma instância para testar os endpoints!');
                    listaInstancias.value = [];
                    // formData.nr_hash = '';
                } else {
                    filtros.instance = result.data[0].nameinstance;

                    const findInstancia = listaInstancias.value.find((inst) => inst.id_instancia == filtros.id);

                    if (findInstancia) {
                        filtros.instance = findInstancia.nameinstance;
                    } else {
                        filtros.instance = result.data[0].nameinstance;
                    }
                }
            }
            if (result.statuscode == 404) {
                toast.warning('Nenhuma instância encontrada, adicione uma instância para testar os endpoints!');
                listaInstancias.value = [];
                // formData.nr_hash = '';
            }
        } catch (error) {
            // console.log('error at line 634 in mensagem/relMensagens.vue:', error);
            //console.log('error at line 122:', error);
            toast.error('Erro ao carregar instâncias!');
        } finally {
            loading.value.hide();
        }
    }

    async function carregaRelatorio(clickCard) {
        loading.value.show();
        if (!clickCard && (filtros.tp_status == 'Enviado' || filtros.tp_status == 'Não Enviado')) {
            filtros.tp_status = undefined;
        }

        try {
            const estatabelecimentos = localStorage.getItem('estabelecimentos');
            const estabelecimentosLiberado = JSON.parse(estatabelecimentos);
            initTabulator();

            const payload = {
                ...toRaw(filtros),
                telefone: formatarTelefone({ target: { value: filtros.telefone } }, true, true),
                cd_estabelecimento: estabelecimentosLiberado[0].cd_estabelecimento,
                from_me: 'SIM',
            };

            // console.log('payload at line 899 in mensagem/relMensagens.vue:', payload);
            const result = await MessagesServices.listarRelatorioMensagens(payload);

            if (result.statuscode == 200) {
                listaRelatorio.value = result.data;
                contagem.value.total_enviado = parseInt(result.data?.total_enviado) || 0;
                contagem.value.total_nao_enviado = parseInt(result.data?.total_nao_enviado) || 0;
                contagem.value.total_erro = parseInt(result.data?.total_erro) || 0;
                contagem.value.total =
                    parseInt(result.data?.total_enviado) +
                        parseInt(result.data?.total_nao_enviado) +
                        parseInt(result.data?.total_erro) || 0;

                if (tabulator.value) {
                    tabulator.value.setData(listaRelatorio.value.lista);
                }

                // Limpar seleções quando carregar novos dados
                mensagensSelecionadas.value = [];
            } else {
                toast.warning(result.message);
                listaRelatorio.value = [];
            }
        } catch (error) {
            toast.error('Erro ao carregar Relatório!');
        } finally {
            loading.value.hide();
        }
    }

    function fechaModalConfirma() {
        modalConfirmaTodos.value = false;
    }

    onMounted(async () => {
        //initTabulator();
        pagina.pagename = 'Relatorio de Mensagens';
        pagina.description = 'Relatorio';
        pagina.module = 'Instâncias';

        filtros.id = route.query.id;
        filtros.tp_status = route.query.tipo;

        let clickCard = false;
        if (route.query.idInstancia || route.query.tipo) {
            clickCard = true;
        }

        dt_envio.value = dayjs(new Date()).format('DD/MM/YYYY') + ' - ' + dayjs(new Date()).format('DD/MM/YYYY');
        dt_recebido.value = null;
        dt_lido.value = null;
        filtros.dt_enviado_inicial = dayjs(new Date()).format('YYYY-MM-DD');
        filtros.dt_enviado_final = dayjs(new Date()).format('YYYY-MM-DD');
        filtros.dt_recebido_inicial = '';
        filtros.dt_recebido_final = '';
        filtros.dt_lido_inicial = '';
        filtros.dt_lido_final = '';
        pularChecagem.value = false;
        await carregaInstancias();
        await carregaRelatorio(clickCard);
    });
</script>

<style scoped>
    .table th {
        padding-bottom: 0 !important;
    }
    ::v-deep(.tabulator-paginator > label) {
        /* opacity: 0 !important;
        max-width: 0 !important;
        content: '' !important; */
        display: none !important;
    }
    ::v-deep(.multiselect-clear) {
        display: none !important;
        width: 0 !important;
    }
    ::v-deep(.multiselect-single-label) {
        padding-right: 28px !important;
    }
    ::v-deep(.dropdown-item-disbled) {
        opacity: 0.4 !important;
        pointer-events: none !important;
    }
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-input) {
        cursor: pointer !important;
    }
</style>
