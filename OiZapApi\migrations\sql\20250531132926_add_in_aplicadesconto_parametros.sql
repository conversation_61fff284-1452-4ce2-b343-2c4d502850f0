-- Migration: add_in_aplicadesconto_parametros
-- Created: 2025-05-31T13:29:26.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna in_aplicadesconto na tabela parametros
-- ========================================

DO $$
BEGIN
    -- Remover coluna se já existir para recriar
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'parametros' 
        AND column_name = 'in_aplicadesconto'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE parametros DROP COLUMN in_aplicadesconto;
        RAISE NOTICE 'Coluna in_aplicadesconto removida da tabela parametros';
    END IF;
    
    -- <PERSON><PERSON><PERSON> coluna
    ALTER TABLE parametros ADD COLUMN in_aplicadesconto BOOLEAN DEFAULT FALSE NOT NULL;
    COMMENT ON COLUMN parametros.in_aplicadesconto IS 'Indica se o desconto deve ser aplicado';
    
    RAISE NOTICE 'Coluna in_aplicadesconto adicionada à tabela parametros';
END $$; 