import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { EstabelecimentoModulosModel } from '../../models/estabelecimento/EstabelecimentoModulosModel';

export class EstabelecimentoModulosController {
  static async incluirEstabelecimentoModulos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.cd_modulo === undefined) {
        errors.push('O campo "cd_modulo" é obrigatório.');
      }
      // if (req.body.tp_situacao === undefined || req.body.tp_situacao.trim() === '') {
      //   errors.push('O campo "tp_situacao" é obrigatório.');
      // }
      // if (req.body.dt_vencimento === undefined) {
      //   errors.push('O campo "dt_vencimento" é obrigatório.');
      // }
      // if (req.body.vl_modulo === undefined) {
      //   errors.push('O campo "vl_modulo" é obrigatório.');
      // }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModulosModel().incluirEstabelecimentoModulos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterarEstabelecimentoModulos(req: Request, res: Response): Promise<Response> {
    try {
      // console.log('🚀 ~ EstabelecimentoModulosController.ts:38 ~ alterarEstabelecimentoModulos ~ req.body:', req.body);
      const errors: string[] = [];
      if (req.body.nr_controle === undefined) {
        errors.push('O campo "nr_controle" é obrigatório.');
      }
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.cd_modulo === undefined) {
        errors.push('O campo "cd_modulo" é obrigatório.');
      }
      if (req.body.tp_situacao === undefined || req.body.tp_situacao.trim() === '') {
        errors.push('O campo "tp_situacao" é obrigatório.');
      }
      // if (req.body.dt_vencimento === undefined) {
      //   errors.push('O campo "dt_vencimento" é obrigatório.');
      // }
      // if (req.body.vl_modulo === undefined) {
      //   errors.push('O campo "vl_modulo" é obrigatório.');
      // }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModulosModel().alterarEstabelecimentoModulos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarEstabelecimentoModulos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModulosModel().listarEstabelecimentoModulos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async removerEstabelecimentoModulos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.nr_controle === undefined) {
        errors.push('O campo "nr_controle" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModulosModel().removerEstabelecimentoModulos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
