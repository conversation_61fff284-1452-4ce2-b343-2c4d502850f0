<template>
    <!-- BEGIN: Mobile Menu -->
    <div
        class="mobile-menu md:hidden !bg-gradient-to-br from-emerald-400 to-teal-600"
        :class="{
            'mobile-menu--active': activeMobileMenu,
        }"
    >
        <div class="mobile-menu-bar">
            <!-- <a href="" class="flex mr-auto">
        <img alt="Midone Tailwind HTML Admin Template" class="w-6" />
      </a> -->
            <Dropdown class="ml-2 mt-1 w-8 h-8">
                <DropdownToggle
                    tag="div"
                    role="button"
                    class="w-8 h-8 rounded-full overflow-hidden shadow-lg image-fit zoom-in bg-white"
                >
                    <div class="font-medium text-green-800 flex h-full items-center justify-center">
                        {{ props.aliasName }}
                    </div>
                </DropdownToggle>
                <DropdownMenu class="w-56">
                    <DropdownContent class="bg-primary text-white">
                        <DropdownHeader tag="div" class="!font-normal">
                            <div class="font-medium">{{ props.formData.name }}</div>
                        </DropdownHeader>
                        <DropdownDivider class="border-white/[0.08]" />

                        <DropdownItem
                            v-show="props.privilegio != 'Usuário'"
                            class="dropdown-item hover:bg-white/5"
                            @click="emit('click:abreListaEstabelecimento')"
                        >
                            <ContactIcon class="w-4 h-4 mr-2" />Meus Dados
                        </DropdownItem>
                        <DropdownItem class="dropdown-item hover:bg-white/5" @click="emit('click:abreListaUsuarios')">
                            <UserIcon class="w-4 h-4 mr-2" />Usuários
                        </DropdownItem>
                        <DropdownItem class="dropdown-item hover:bg-white/5" @click="emit('click:exibirModaSenha')">
                            <KeyIcon class="w-4 h-4 mr-2" />Trocar senha
                        </DropdownItem>
                        <DropdownDivider class="border-white/[0.08]" />
                        <DropdownItem class="dropdown-item hover:bg-white/5" @click="emit('click:atualizarDados')">
                            <DatabaseBackupIcon class="w-4 h-4 mr-2" /> Atualizar Dados
                        </DropdownItem>
                        <DropdownDivider class="border-white/[0.08]" />
                        <DropdownItem class="dropdown-item hover:bg-white/5" @click="emit('click:irParalogin')">
                            <ToggleRightIcon class="w-4 h-4 mr-2" /> Sair
                        </DropdownItem>
                    </DropdownContent>
                </DropdownMenu>
            </Dropdown>
            <a href="javascript:;" class="mobile-menu-toggler">
                <BarChart2Icon class="w-8 h-8 text-white transform -rotate-90" @click="toggleMobileMenu" />
            </a>
        </div>
        <div class="scrollable !bg-gradient-to-br from-emerald-400 to-teal-600">
            <a href="javascript:;" class="mobile-menu-toggler">
                <XCircleIcon class="w-8 h-8 text-white transform -rotate-90" @click="toggleMobileMenu" />
            </a>
            <ul class="scrollable__content py-2">
                <!-- BEGIN: First Child -->
                <template v-for="(menu, menuKey) in formattedMenu">
                    <li v-if="menu == 'devider'" :key="menu + menuKey" class="menu__devider my-6"></li>
                    <li v-else :key="() => menu + menuKey">
                        <a
                            href="javascript:;"
                            class="menu"
                            :class="{
                                'menu--active': menu.active,
                                'menu--open': menu.activeDropdown,
                            }"
                            @click="linkTo(menu, router)"
                        >
                            <div class="menu__icon">
                                <component :is="menu.icon" />
                            </div>
                            <div class="menu__title">
                                {{ menu.title }}
                                <div
                                    v-if="menu.subMenu"
                                    class="menu__sub-icon"
                                    :class="{ 'transform rotate-180': menu.activeDropdown }"
                                >
                                    <ChevronDownIcon />
                                </div>
                            </div>
                        </a>
                        <!-- BEGIN: Second Child -->
                        <transition @enter="enter" @leave="leave">
                            <ul v-if="menu.subMenu && menu.activeDropdown">
                                <li v-for="(subMenu, subMenuKey) in menu.subMenu" :key="subMenuKey">
                                    <a
                                        href="javascript:;"
                                        class="menu"
                                        :class="{ 'menu--active': subMenu.active }"
                                        @click="linkTo(subMenu, router)"
                                    >
                                        <div class="menu__icon">
                                            <ActivityIcon />
                                        </div>
                                        <div class="menu__title">
                                            {{ subMenu.title }}
                                            <div
                                                v-if="subMenu.subMenu"
                                                class="menu__sub-icon"
                                                :class="{
                                                    'transform rotate-180': subMenu.activeDropdown,
                                                }"
                                            >
                                                <ChevronDownIcon />
                                            </div>
                                        </div>
                                    </a>
                                    <!-- BEGIN: Third Child -->
                                    <transition @enter="enter" @leave="leave">
                                        <ul v-if="subMenu.subMenu && subMenu.activeDropdown">
                                            <li
                                                v-for="(lastSubMenu, lastSubMenuKey) in subMenu.subMenu"
                                                :key="lastSubMenuKey"
                                            >
                                                <a
                                                    href="javascript:;"
                                                    class="menu"
                                                    :class="{ 'menu--active': lastSubMenu.active }"
                                                    @click="linkTo(lastSubMenu, router)"
                                                >
                                                    <div class="menu__icon">
                                                        <ZapIcon />
                                                    </div>
                                                    <div class="menu__title">
                                                        {{ lastSubMenu.title }}
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </transition>
                                    <!-- END: Third Child -->
                                </li>
                            </ul>
                        </transition>
                        <!-- END: Second Child -->
                    </li>
                </template>
                <!-- END: First Child -->
            </ul>
        </div>
    </div>
    <!-- END: Mobile Menu -->
</template>

<script setup>
    import { computed, onMounted, ref, watch } from 'vue';
    import { useRoute, useRouter } from 'vue-router';
    import { helper as $h } from '@/utils/helper';
    //import { useSideMenuStore } from '@/stores/side-menu';
    import { useSimpleMenuStore } from '@/stores/simple-menu';
    import { activeMobileMenu, toggleMobileMenu, linkTo, enter, leave } from './index';
    import { nestedMenu } from '@/layouts/side-menu';
    import dom from '@left4code/tw-starter/dist/js/dom';
    import SimpleBar from 'simplebar';

    const route = useRoute();
    const router = useRouter();
    const formattedMenu = ref([]);
    const simpleMenuStore = useSimpleMenuStore();
    const mobileMenu = computed(() => nestedMenu(simpleMenuStore.menu, route));
    let privilegio = ref('');
    const props = defineProps({
        formData: Object,
        privilegio: String,
        aliasName: String,
    });

    const emit = defineEmits([
        'click:abreListaEstabelecimento',
        'click:abreListaUsuarios',
        'click:exibirModaSenha',
        'click:irParalogin',
        'click:atualizarDados',
    ]);

    watch(
        computed(() => route.path),
        () => {
            formattedMenu.value = $h.toRaw(mobileMenu.value);
        }
    );

    onMounted(() => {
        new SimpleBar(dom('.mobile-menu .scrollable')[0]);
        formattedMenu.value = $h.toRaw(mobileMenu.value);
    });
</script>

<style scoped>
    @media screen and (max-width: 768px) {
        .mobile-menu {
            top: 0;
            left: 0;
            margin: 0;
        }
    }

    ::v-deep(.mobile-menu) {
        display: flex;
        align-items: center;
    }
    ::v-deep(.mobile-menu-bar) {
        top: 0 !important;
        left: 0 !important;
        justify-content: space-between;
        align-items: center;
    }
</style>
