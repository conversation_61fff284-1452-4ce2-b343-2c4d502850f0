import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('cliente_contatos migration started');
    let hasColumn = await knex.schema.hasColumn('cliente_contatos', 'cd_estabelecimento');

    if (!hasColumn) {
      await knex.schema.alterTable('cliente_contatos', (table) => {
        table.integer('cd_estabelecimento');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('cliente_contatos', (table) => {
      table.dropColumn('cd_estabelecimento');
    });
  } catch (error) {
    throw error;
  }
}
