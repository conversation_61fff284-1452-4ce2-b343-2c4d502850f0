import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    let hasColumn = await knex.schema.hasTable('disparos_pixel');

    if (!hasColumn) {
      console.log('Tabela não existe, criando...');
      await knex.schema.createTable('disparos_pixel', (table) => {
        table.increments('cd_disparo').primary();
        table.string('ds_disparo', 200).notNullable();
        table.string('ds_url', 300).notNullable();
        table.string('nr_hash', 200).notNullable();
        table.boolean('in_ativo').defaultTo(true);
        table.integer('cd_usucad').nullable();
        table.timestamp('dt_cadastro').nullable();
        table.timestamps(true, true);
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.dropTable('disparos_pixel');
  } catch (error) {
    throw error;
  }
}
