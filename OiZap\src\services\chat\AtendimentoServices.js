import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';
const hostApi = hosts.apiOiZap + '/atendimentos';

const AtendimentoServices = {
    async incluir(req) {
        try {
            const response = await callApi('post', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async novo(req) {
        try {
            const response = await callApi('post', `${hostApi}/novo/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async alterar(req) {
        try {
            const response = await callApi('put', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async iniciar(req) {
        try {
            const response = await callApi('post', `${hostApi}/iniciar/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async transferir(req) {
        try {
            const response = await callApi('post', `${hostApi}/transferir/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async encerrar(req) {
        try {
            const response = await callApi('post', `${hostApi}/encerrar/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async atendido(req) {
        try {
            const response = await callApi('post', `${hostApi}/atendido/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async remover(req) {
        try {
            const response = await callApi('delete', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listar(req) {
        try {
            const response = await callApi('get', `${hostApi}/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaAtendimentosHistorico(req) {
        try {
            const response = await callApi('get', `${hostApi}/listaAtendimentosHistorico/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaAtendimentoPorTelefone(req) {
        try {
            const response = await callApi('get', `${hostApi}/lista-atendimentos-por-telefone/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listarAtendimentosFluxo(req) {
        try {
            const response = await callApi('get', `${hostApi}/listarAtendimentosFluxo/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async stopAtendimentos(req) {
        try {
            const response = await callApi('post', `${hostApi}/stop/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async executa(req) {
        //console.log('AtendimentoServices > executa', body);
        try {
            const response = await callApi('post', hosts.api + '/atendimento/executar/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async stopBotAtendimento(req) {
        //console.log('AtendimentoServices > executa', body);
        try {
            const response = await callApi('post', hosts.api + '/atendimento/stopBotAtendimento/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaAtendimentos(req) {
        //console.log('AtendimentoServices > executa', body);
        try {
            const response = await callApi('get', hosts.api + '/atendimento/listaAtendimentos/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async uploadArquivos(body) {
        //console.log('uploadArquivos > executa', body);
        try {
            const response = await callApi('post', hosts.api + '/atendimento/uploadArquivos/v1', undefined, body);
            //console.log('uploadArquivos > response', response);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaAtendimentosHistorico(req) {
        try {
            const response = await callApi('get', `${hostApi}/listaAtendimentosHistorico/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaAtendimentoData(filtros) {
        try {
            return await callApi('get', `${hostApi}/lista-atendimentos-data/v1`, filtros, undefined);
        } catch (error) {
            // console.log('callApi > error', error);
            return null;
        }
    },
    async listaAtendimentoHora(filtros) {
        try {
            return await callApi('get', `${hostApi}/lista-atendimentos-hora/v1`, filtros, undefined);
        } catch (error) {
            // console.log('callApi > error', error);
            return null;
        }
    },
};
export default AtendimentoServices;
