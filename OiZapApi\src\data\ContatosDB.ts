require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';

const logger = Logger.getLogger();
export class ContatosDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['contacts'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      const resp = await new PostgreSQLServices().executar(opDb);
      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['contacts'],
        chaves: { cd_contato: req.body.cd_contato, cd_estabelecimento: req.body.cd_estabelecimento },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listar(req: Request): Promise<IRetorno> {
    try {
      //console.log('🚀 ~ ContatosDB.ts:45 ~ listar ~ req.query:', req.query);
      let sql = `select c.*, i.nome nameinstance,i.telefone nr_telefoneinstancia from contacts c 
      left join instances i on i.nameinstance = c.instance
      where 1=1`;

      if (req.query.cd_estabelecimento) {
        sql += ` and c.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }

      if (req.query.instance) {
        sql += ` and c.instance = '${req.query.instance}'`;
      }

      if (req.query.search) {
        const search = req.query.search as string;
        const isName = isNaN(Number(search));
        if (isName && search.trim() !== '') {
          sql += ` and upper(c.push_name) like upper('%${req.query.search}%')`;
        }
        if (!isName) {
          let telefone = req.query.search as string;
          let oitoUltimosDigitos = telefone.slice(-8);
          sql += ` and (trim(replace(c.telefone,'-',''))  = '%${telefone}%' or 
                     trim(replace(c.telefone,'-',''))  like '%${oitoUltimosDigitos}%') `;
        }
      }
      if (req.query.limit && req.query.page) {
        const offsetPage = (parseInt(req.query.page as string) - 1) * parseInt(req.query.limit as string);
        sql += `  order by c.push_name asc limit ${req.query.limit} offset ${offsetPage}`;
      }

      if (req.query.offset) {
        sql += `  order by c.push_name asc limit ${req.query.limit} offset ${req.query.offset}`;
      }

      // console.log('🚀 ~ ContatosDB.ts:69 ~ listar ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['contacts'],
        chaves: {
          cd_contato: req.body.cd_contato,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
