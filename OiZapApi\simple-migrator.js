require('dotenv').config();
const { Client } = require('pg');

const environment = process.env.NODE_ENV || 'DEV';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
    SANDBOX: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
    PROD: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

// Migrations SQL direto - SEM COMPLICAÇÃO!
const migrations = [
  {
    name: 'create_perfis_table',
    sql: `
      CREATE TABLE IF NOT EXISTS perfis (
        id SERIAL PRIMARY KEY,
        cd_perfil INTEGER NOT NULL,
        nm_perfil VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_perfis_cd_perfil ON perfis(cd_perfil);
    `,
  },
  {
    name: 'create_perfis_telas_table',
    sql: `
      CREATE TABLE IF NOT EXISTS perfis_telas (
        id SERIAL PRIMARY KEY,
        cd_perfil INTEGER NOT NULL,
        cd_tela INTEGER NOT NULL,
        in_visualizar BOOLEAN DEFAULT TRUE,
        in_inserir BOOLEAN DEFAULT TRUE,
        in_alterar BOOLEAN DEFAULT TRUE,
        in_excluir BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_perfis_telas_cd_perfil ON perfis_telas(cd_perfil);
      CREATE INDEX IF NOT EXISTS idx_perfis_telas_cd_tela ON perfis_telas(cd_tela);
    `,
  },
];

async function runMigrations() {
  const config = getConnectionConfig(environment);
  const client = new Client(config);

  try {
    await client.connect();
    console.log(`🚀 Executando migrations SIMPLES - Ambiente: ${environment}`);

    // Criar tabela de controle
    await client.query(`
      CREATE TABLE IF NOT EXISTS simple_migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Executar cada migration
    for (const migration of migrations) {
      // Verificar se já foi executada
      const result = await client.query('SELECT name FROM simple_migrations WHERE name = $1', [migration.name]);

      if (result.rows.length > 0) {
        console.log(`⏭️  Migration ${migration.name} já executada`);
        continue;
      }

      console.log(`🔄 Executando migration: ${migration.name}`);

      // Executar a migration
      await client.query(migration.sql);

      // Registrar como executada
      await client.query('INSERT INTO simple_migrations (name) VALUES ($1)', [migration.name]);

      console.log(`✅ Migration ${migration.name} executada com sucesso!`);
    }

    console.log(`🎉 Todas as migrations foram executadas com sucesso!`);
  } catch (error) {
    console.error('❌ Erro:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigrations();
