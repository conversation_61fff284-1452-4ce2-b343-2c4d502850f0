require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';
export class EstabelecimentoInstanciasDB {
  static async incluirEstabelecimentoInstancias(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['estabelecimento_instancias'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarEstabelecimentoInstancias(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['estabelecimento_instancias'],
        chaves: {
          id_instancia: req.body.id_instancia,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarEstabelecimentoInstancias(req: Request): Promise<IRetorno> {
    try {
      /*const opDb: OperationObject = {
        operacao: 'select',
        tabelas: ['estabelecimento_instancias'],
        chaves: req.query,
        retorno: '*',
      };*/
      let sql = `select ei.*,i.nameinstance from estabelecimento_instancias ei, instances i where i.id=ei.id_instancia and ei.nr_hash = '${req.query.nr_hash}'`;
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async removerEstabelecimentoInstancias(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['estabelecimento_instancias'],
        chaves: {
          nr_controle: req.body.nr_controle,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaInstanciasPorEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      let sql = `select e.cd_estabelecimento,e.nm_estabelecimento,e.tp_situacao,ei.nr_hash
                ,i.nameinstance,i.status,i.telefone,i.status_connection,i.nome,i.in_stop_bot,i.dt_start_stop_bot 
                ,e.host_proxy,e.in_habilitaproxy,e.in_apidedicada
                ,(case when (trim(coalesce(replace(replace(e.host_proxy,'{',''),'}',''),'')) = '') then false else true end) in_proxy
                from estabelecimento e
                left join estabelecimento_instancias ei on ei.cd_estabelecimento = e.cd_estabelecimento
                left join instances i on i.id = ei.id_instancia where 1=1 `;

      if (req.query.nm_estabelecimento) {
        sql += ` and lower(e.nm_estabelecimento) like lower('%${req.query.nm_estabelecimento}%')`;
      }
      if (req.query.telefone) {
        sql += ` and i.telefone like '${req.query.telefone}'`;
      }
      if (req.query.status_connection) {
        sql += ` and i.status_connection like '${req.query.status_connection}'`;
      }
      if (req.query.nameinstance) {
        sql += ` and i.nameinstance like '%${req.query.nameinstance}%'`;
      }
      if (req.query.nr_hash) {
        sql += ` and ei.nr_hash like '%${req.query.nr_hash}%'`;
      }
      if (req.query.in_proxy) {
        sql += ` and (case when (trim(coalesce(replace(replace(e.host_proxy,'{',''),'}',''),'')) = '') then false else true end) = ${req.query.in_proxy}`;
      }

      sql += ` order by e.nm_estabelecimento `;

      if (req.query.limit && req.query.page) {
        const offsetPage = (parseInt(req.query.page as string) - 1) * parseInt(req.query.limit as string);
        sql += ` limit ${req.query.limit} offset ${offsetPage}`;
      }
      //console.log('sql at line 106 in estabelecimento/EstabelecimentoInstanciasDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaInstanciasDoEstabelecimento(cdEstabelecimento: number): Promise<IRetorno> {
    try {
      let sql = ` select i.nameinstance,ei.nr_hash from estabelecimento_instancias ei,instances i where i.id = ei.id_instancia  and ei.cd_estabelecimento =${cdEstabelecimento}`;
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
