import { config } from 'dotenv';
import { Client } from 'pg';
import { dadosNaoEncontrados, erroInterno, IRetorno, sucesso } from '../interfaces/IRetorno';
config();
// import Logger from './Logger';
//const logger = Logger.getLogger();

class PostgreSQLServicesDirect {
  private client: Client;

  constructor(server: string) {
    const pass = process.env.PASSWORD_DATABASE || '';
    const user = process.env.USER_DATABASE || '';
    const port = process.env.PORT_DATABASE || '';
    const host = process.env.HOST_DATABASE || '';

    let base: string = '';
    if (server == 'SANDBOX') {
      base = 'oizap-sandbox'; //process.env.DATABASE_SANDBOX || '';
    } else if (server == 'DEV') {
      base = 'oizap-dev';
    } else if (server == 'CRM') {
      base = 'oizap-crm';
    } else if (server == 'CLIENTE') {
      base = 'oizap-cliente';
    } else if (server == 'PRODUCAO') {
      base = 'oizap'; //process.env.DATABASE || '';
    }

    const connectionString = `postgres://${user}:${pass}@${host}:${port}/${base}`;

    this.client = new Client({
      connectionString: connectionString,
    });
  }

  async query(sql: any): Promise<IRetorno> {
    try {
      this.client.connect();
      const result = await this.client.query(sql);

      if (result.rowCount && result.rowCount > 0) {
        return sucesso(result.rows);
      } else {
        return dadosNaoEncontrados();
      }
    } catch (error) {
      console.error('Error executing PostgreSQL query:', error);
      this.client.end(); // Libera a conexão de volta para o pool
      return erroInterno(error);
      //throw error;
    } finally {
      await this.client.end();
    }
  }
}

export default PostgreSQLServicesDirect;
