import { execSync } from 'child_process';
import dotenv from 'dotenv';
import * as http from 'http';
import * as path from 'path';
import { app } from './app';
import Logger from './services/Logger';
dotenv.config();

const logger = Logger.getLogger();
const port: number = parseInt(process.env.PORT || '3100', 10);
const server = http.createServer(app);

// Função para executar migrations automaticamente
async function runAutoMigrations() {
  const environment = process.env.NODE_ENV || process.env.AMBIENTE;

  // Não executar migrations automaticamente no DEV
  if (environment === 'DEV' || environment === 'development') {
    logger.info('🔧 Ambiente DEV detectado - migrations automáticas desabilitadas');
    return;
  }

  try {
    logger.info(`🚀 Executando migrations automáticas - Ambiente: ${environment}`);

    const migrationScript = path.join(__dirname, '..', 'migrate-auto.js');
    execSync(`node ${migrationScript}`, {
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: environment },
    });

    logger.info('✅ Migrations executadas com sucesso!');
  } catch (error: any) {
    logger.error('❌ Erro ao executar migrations:', error.message);

    // Em produção, não parar a API por causa de migration
    if (environment === 'PROD' || environment === 'CRM') {
      logger.warn('⚠️ API continuará inicializando apesar do erro de migration');
    } else {
      process.exit(1);
    }
  }
}

// Executar migrations antes de iniciar o servidor
runAutoMigrations()
  .then(() => {
    server.listen(port, () => {
      logger.info(`**** Ambiente: ${process.env.AMBIENTE} **** Oi Zap API iniciado na porta:  ${port}`);
    });
  })
  .catch((error) => {
    logger.error('Erro ao iniciar servidor:', error);
    process.exit(1);
  });
