import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';

export class CliquesDB {
  static async criarClique(data: any) {
    const opDb: OperationObject = {
      operacao: 'insert',
      tabelas: ['cliques'],
      dados: data,
      retorno: '*',
    };
    console.log('🚀 ~ CliquesDB.ts:11 ~ criarClique ~ opDb: OperationObject.data:', data);
    return await new PostgreSQLServices().executar(opDb);
  }

  static async listarCliques(data: any) {
    const opDb: OperationObject = {
      operacao: 'select',
      tabelas: ['cliques'],
      chaves: data,
      retorno: '*',
    };

    return await new PostgreSQLServices().executar(opDb);
  }
}
