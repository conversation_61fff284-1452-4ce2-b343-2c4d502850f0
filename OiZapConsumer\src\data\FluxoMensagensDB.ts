import { IRetorno, erroInterno, parametrosInvalidos, sucesso } from '../interfaces/IRetorno';
import { PostgreSQLServices } from '../services/PostgreSQLServices';
//import { REDIS_TIMER, RedisServices } from '../services/RedisServices';
import Logger from '../services/Logger';
import { REDIS_TIMER, RedisServices } from '../services/RedisServices';
const logger = Logger.getLogger();

export class FluxoMensagensDB {
  static async listarFluxo(req: any): Promise<IRetorno> {
    try {
      let sql = `select f.*
,e.nm_estabelecimento,e.ds_hostentregadochef,e.ds_endereco,e.nr_latitude,e.nr_longitude
from fluxo_atendimento f
inner join instances i on i.nameinstance = f."instance" 
inner join estabelecimento_instancias ei on ei.id_instancia = i.id  
inner join estabelecimento e on e.cd_estabelecimento = ei.cd_estabelecimento 
where i.nameinstance = '${req.instance}' and f.ds_evento = 'FLUXO'      
and upper(retira_acentuacao(f.tp_funcionalidade)) = retira_acentuacao('${req.tp_funcionalidade.toUpperCase()}')
order by f.nr_ordem,f.nr_controle `;

      const result = await new PostgreSQLServices().query(sql);
      //console.log('result:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async consultaFluxo(req: any): Promise<IRetorno> {
    try {
      // const filtroRedis = {
      //   nr_controle: req.nr_controle,
      //   tp_mensagem: req.tp_mensagem,
      //   ds_titulo: req.ds_titulo,
      //   instance: req.instance,
      //   ds_evento: req.ds_evento,
      //   tp_funcionalidade: req.tp_funcionalidade,
      // };
      // const keyRedis = `fluxo-consulta-${JSON.stringify(filtroRedis)}`;
      // const resultRedis = await new RedisServices().get(keyRedis);
      // //console.log('resultRedis :', resultRedis);
      // if (resultRedis) return sucesso(resultRedis);

      const respFluxoMensagem = await this.listarFluxo(req);
      // console.log('respFluxoMensagem at line 33 in clientes/FluxoMensagensDB.ts:', respFluxoMensagem);

      // if (respFluxoMensagem.statuscode == 200) {
      //   await new RedisServices().set(keyRedis, respFluxoMensagem.data, REDIS_TIMER);
      // }

      return respFluxoMensagem;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarFluxoAtendimentos(req: any): Promise<IRetorno> {
    const keyRedis = `${req.cd_estabelecimento}_${process.env.AMBIENTE}:fluxo_atendimento:${JSON.stringify(req)}`;
    const resultRedis = await new RedisServices().get(keyRedis);
    if (resultRedis) return sucesso(resultRedis);

    try {
      let sql = `select a.*  from fluxo_atendimento a `;

      let inFiltroDsToken = false;
      if (req.ds_token != undefined && req.ds_token != '') {
        inFiltroDsToken = true;
        sql += ` inner join fluxo_tokens t on t.cd_estabelecimento = a.cd_estabelecimento and t.nr_contfluxoatendimento = a.nr_controle and t.ds_token = '${req.ds_token}'`;
      }

      sql += ` WHERE a.in_ativa = true and a.cd_estabelecimento = ${req.cd_estabelecimento}`;

      let inFiltroTpFun = false;

      if (req.tp_funcionalidade != undefined && req.tp_funcionalidade != '') {
        inFiltroTpFun = true;
        sql += ` and a.tp_funcionalidade = '${req.tp_funcionalidade}'`;
      }

      let inFiltroControle = false;
      if (req.nr_controle != undefined) {
        inFiltroControle = true;
        sql += ` and a.nr_controle =${req.nr_controle}`;
      }

      if (inFiltroTpFun == false && inFiltroControle == false && inFiltroDsToken == false) {
        return parametrosInvalidos('Filtro de Funcionalidade ou Controle ou Token é Obrigatório!', '');
      }
      // console.log('sql at line 107 in data/FluxoMensagensDB.ts:', sql);

      //logger.debug('sql at line 120 in data/FluxoMensagensDB.ts: ' + JSON.stringify(sql));
      console.log('🚀 ~ FluxoMensagensDB.ts:87 ~ listarFluxoAtendimentos ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);
      console.log('🚀 ~ FluxoMensagensDB.ts:87 ~ listarFluxoAtendimentos ~ result:', result);
      // logger.debug('result at line 123 in data/FluxoMensagensDB.ts: ' + JSON.stringify(result));

      if (result.statuscode == 200) {
        await new RedisServices().set(keyRedis, result.data, REDIS_TIMER);
      }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarFluxoAtendimentosEst(req: any): Promise<IRetorno> {
    //  const keyRedis = `${req.cd_estabelecimento}:fluxo_atendimento-estabelecimento_instancias-estabelecimento-instances:${JSON.stringify(req)}`;
    //  const resultRedis = await new RedisServices().get(keyRedis);
    //  if (resultRedis) return sucesso(resultRedis);

    try {
      let sql = `select e.nr_hash,a.nr_controle,a.tp_mensagem,a.ds_mensagem,a.hr_tempo
    ,a.ds_titulo,a."instance",a.ds_evento,a.in_opcoes,a.in_finalizaatendimento
    ,a.tp_funcionalidade,a.in_resposta
    ,a.in_fluxopadrao,a.cd_estabelecimento,a.in_tipolista,a.ds_titulobotao
    ,a.ds_descritivo,a.in_ativa,a.in_adicionatexto_imagem,a.in_adicionaatalho
    ,e1.nm_estabelecimento,e1.ds_hostentregadochef,e1.ds_endereco
    ,e1.nr_latitude,e1.nr_longitude
    ,(select json_agg(imag)
      from (select fi.nr_controle,fi.ds_host,fi.nm_arquivo,fi.ds_categoria,fi.ds_extensao
            from fluxo_imagens fi 
            where fi.nr_contfluxoatendimento = a.nr_controle
            and fi.cd_estabelecimento=a.cd_estabelecimento
      ) as imag) as imagens
    ,(select json_agg(msgrel)
      from (select fa.nr_controle,fa.tp_mensagem,fa.ds_mensagem,fa.hr_tempo
				    ,fa.ds_titulo,fa.ds_evento
				    ,fa.tp_funcionalidade,fa.in_resposta,mr.in_opcoes
				    ,fa.in_fluxopadrao,fa.cd_estabelecimento,fa.in_tipolista,fa.ds_titulobotao
				    ,fa.ds_descritivo,fa.in_ativa,fa.in_adicionatexto_imagem,fa.in_adicionaatalho 
            ,(select json_agg(imag) 
              from (select fia.nr_controle,fia.ds_host,fia.nm_arquivo,fia.ds_categoria,fia.ds_extensao
			              from fluxo_imagens fia 
			              where fia.nr_contfluxoatendimento = fa.nr_controle
                     and fia.cd_estabelecimento=fa.cd_estabelecimento
			              ) as imag
			        ) as imagens
				from mensagem_relacionadas mr
				, fluxo_atendimento fa 
				where fa.nr_controle = mr.nr_contmensagem_relacionada 
				and mr.nr_contmensagem = a.nr_controle 
        and mr.cd_estabelecimento=fa.cd_estabelecimento
        and mr.cd_estabelecimento=a.cd_estabelecimento
        and fa.in_ativa = true
        order by mr.nr_ordem
      ) as msgrel ) as msg_relacionadas            
 from fluxo_atendimento a
 JOIN estabelecimento_instancias e ON e.cd_estabelecimento = a.cd_estabelecimento
 JOIN estabelecimento e1 ON e1.cd_estabelecimento = a.cd_estabelecimento
 JOIN instances i ON i.id = e.id_instancia 
 WHERE a.in_ativa = true
 and i.nameinstance = '${req.instance}'
 and a.cd_estabelecimento = ${req.cd_estabelecimento}`;

      let inFiltroTpFun = false;

      if (req.tp_funcionalidade != undefined && req.tp_funcionalidade != '') {
        inFiltroTpFun = true;
        sql += ` and a.tp_funcionalidade = '${req.tp_funcionalidade}'`;
      }
      let inFiltroControle = false;
      if (req.nr_controle != undefined) {
        inFiltroControle = true;
        sql += ` and a.nr_controle =${req.nr_controle}`;
      }

      if (inFiltroTpFun == false && inFiltroControle == false) {
        return parametrosInvalidos('Filtro de Funcionalidade ou Controle é Obrigatório!', '');
      }
      // console.log('sql at line 107 in data/FluxoMensagensDB.ts:', sql);

      logger.debug('sql at line 120 in data/FluxoMensagensDB.ts: ' + JSON.stringify(sql));
      const result = await new PostgreSQLServices().query(sql);
      logger.debug('result at line 123 in data/FluxoMensagensDB.ts: ' + JSON.stringify(result));

      // if (result.statuscode == 200) {
      //   await new RedisServices().set(keyRedis, result.data, REDIS_TIMER);
      // }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarPedidoCompletos(req: any): Promise<IRetorno> {
    try {
      let sql = `select p.*
            ,(select to_jsonb(dados) from ( 
              select pg.tp_forma_pgto
              ,(case when (pg.tp_forma_pgto=7) then cg.nr_diasprazo else null end) nr_diasprazooizap,pg.vl_total vl_valor
						  from pedidos pg,clientes cg
					  	where cg.cd_cliente = pg.cd_cliente  and pg.cd_estabelecimento = cg.cd_estabelecimento 
						  and pg.cd_pedido = p.cd_pedido and pg.cd_estabelecimento = p.cd_estabelecimento) dados ) pagamentos	 
      ,a.ds_hash ds_hashatendimento,a.instance,a.ds_foto,a.profile_picture_base64
      ,p.vl_produtos+coalesce(p.vl_adicional,0)+coalesce(p.vl_borda,0) vl_total_produtomaisadicional
            ,(select COALESCE(to_jsonb  (clientes), '{}'::jsonb) from (select * from clientes c1 where c1.cd_cliente = p.cd_cliente and c1.cd_estabelecimento = p.cd_estabelecimento) clientes) cliente
      ,(select COALESCE(to_jsonb  (estabelecimento), '{}'::jsonb) from (select * from estabelecimento e1 where e1.cd_estabelecimento = p.cd_estabelecimento) estabelecimento) estabelecimento
,(select COALESCE(jsonb_agg(itens), '[]'::jsonb) from (
	    select k.*
	    ,(case when (coalesce(k.ds_tamanho,k.ds_tamanhopizza) is null) then k.ds_produto else concat(k.ds_produto,' - ',coalesce(k.ds_tamanho,k.ds_tamanhopizza)) end) ds_produto
	    from (
        select pi1.cd_item,pi1.nr_item,pi1.cd_pedido,pi1.cd_produto,pi1.ds_produto,false in_aplicadesconto      
                                ,pi1.cd_tamanho,sum(pi1.qt_produto) qt_produto,sum(pi1.vl_produto) vl_produto
                                ,sum(coalesce(pi1.vl_adicional,0)) vl_adicional
                                ,sum(coalesce(pi1.vl_totaladicional,0)) vl_totaladicional
                                ,sum(coalesce(pi1.vl_borda,0)) vl_borda
                                ,sum(coalesce(pi1.vl_totalborda,0)) vl_totalborda
                                ,sum(coalesce(pi1.vl_subtotal,0)) vl_subtotal
                                ,sum(coalesce(pi1.vl_desconto,0)) vl_desconto
                                ,sum(coalesce(pi1.vl_acrescimo,0)) vl_acrescimo
                                ,sum(pi1.vl_subtotal*pi1.qt_produto) vl_unitariosubtotal
                                ,sum(coalesce(pi1.vl_total,0)) vl_total
                                ,pi1.ds_observacao
                                ,(case when ((select count(*) from pedido_item_pizza px1 where px1.cd_estabelecimento=pi1.cd_estabelecimento
                                             and px1.cd_pedido=pi1.cd_pedido and px1.cd_item_pedido = pi1.cd_item) > 0) then 'P' else  pi1.tp_produto end) tp_produto
                                ,pi1.cd_estabelecimento
                                ,t.ds_tamanho,t.qt_sabor,t.vl_tamanho,
                                (select COALESCE(jsonb_agg(adicionais), '[]'::jsonb) from (select pia.*,pi2.cd_produto,a.ds_adicional,a.vl_adicional
                                                                       from pedido_item_adicional pia
                                                                       inner join pedido_item pi2 on pi2.cd_item = pia.cd_item and pi2.cd_pedido = pia.cd_pedido
                                                                       left join adicional_agrupado a on a.cd_adicional = pia.cd_adicional and a.cd_estabelecimento = pia.cd_estabelecimento and a.cd_agrupador = pi2.cd_produto
                                                                       where pia.cd_pedido = pi1.cd_pedido
                                                                       and pia.cd_item = pi1.cd_item
                                                                       and pia.cd_estabelecimento = pi1.cd_estabelecimento) adicionais ) adicionais,
        (select COALESCE(jsonb_agg(bordas), '[]'::jsonb) from (select  row_number() OVER (PARTITION by 0) as seq,concat(qt_produto,'/',(select count(*) from pedido_item_pizza pip where pip.cd_pedido = pib.cd_pedido and pip.cd_estabelecimento = pib.cd_estabelecimento
                                                              and pip.cd_item_pedido = pib.cd_item and pip.tp_produto = 'Borda')) qt_itemborda,pib.*,pi2.cd_produto,a.ds_produto ds_borda
                                                                       from pedido_item_bordas pib
                                                                       inner join pedido_item pi2 on pi2.cd_item = pib.cd_item and pi2.cd_pedido = pib.cd_pedido and pi2.cd_estabelecimento = pib.cd_estabelecimento
                                                                       left join produtos a on a.cd_produto = pib.cd_borda and a.cd_estabelecimento = pib.cd_estabelecimento and coalesce(a.in_borda,'N') = 'S'
                                                                       where pib.cd_pedido = pi1.cd_pedido
                                                                       and pib.cd_item = pi1.cd_item
                                                                       and pib.cd_estabelecimento = pi1.cd_estabelecimento) bordas ) bordas,
        (select COALESCE(jsonb_agg(sabores), '[]'::jsonb) from (select  row_number() OVER (PARTITION by 0) as seq
                                                               ,concat(qt_produto,'/',(select count(*) from pedido_item_pizza pip where pip.cd_pedido = piz.cd_pedido and pip.cd_estabelecimento = piz.cd_estabelecimento
                                                               and pip.cd_item_pedido = piz.cd_item_pedido and pip.tp_produto = 'Pizza')) qt_itempizza,trim(split_part(piz.ds_produto,'-',1)) ds_sabor,piz.*
                                                               from pedido_item_pizza piz
                                                               where piz.cd_pedido = pi1.cd_pedido
                                                               and piz.cd_item_pedido = pi1.cd_item
                                                               and piz.cd_estabelecimento = pi1.cd_estabelecimento
                                                               and piz.tp_produto = 'Pizza') sabores ) sabores,
                   (select distinct piz.ds_tamanho
                    from pedido_item_pizza piz
                    where piz.cd_pedido = pi1.cd_pedido
                    and piz.cd_estabelecimento = pi1.cd_estabelecimento
                    and piz.cd_item_pedido = pi1.cd_item
                    and piz.tp_produto = 'Pizza') ds_tamanhopizza
                    from pedido_item pi1
                    left join tamanhos t on t.cd_produto = pi1.cd_produto and t.cd_tamanho = pi1.cd_tamanho and t.cd_estabelecimento = pi1.cd_estabelecimento                                         
                    where pi1.cd_estabelecimento = p.cd_estabelecimento and pi1.cd_pedido = p.cd_pedido
                    group by pi1.cd_item,pi1.nr_item,pi1.cd_pedido,pi1.cd_produto,t.vl_tamanho
                    ,t.ds_tamanho,pi1.ds_produto,pi1.cd_tamanho,pi1.ds_observacao,pi1.tp_produto,pi1.cd_estabelecimento,t.ds_tamanho,t.qt_sabor) k ) itens  ) itens
       from pedidos p, atendimentos a where  a.cd_atendimento = p.cd_atendimento and a.cd_estabelecimento = p.cd_estabelecimento and p.tp_situacao not in ('Pedido Cancelado')  `;

      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and p.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.cd_pedido != undefined) {
        sql += ` and p.cd_pedido = ${req.query.cd_pedido} `;
      }
      if (req.query.cd_atendimento != undefined) {
        sql += ` and p.cd_atendimento = ${req.query.cd_atendimento} `;
      }
      if (req.query.nr_hash != undefined) {
        sql += ` and a.nr_hash ='${req.query.nr_hash}'`;
      }
      if (req.query.ds_hashatendimento != undefined) {
        sql += ` and a.ds_hashatendimento ='${req.query.ds_hashatendimento}'`;
      }

      sql += ` order by p.cd_pedido `;

      //  console.log('sql at line 66 in pedidos/PedidosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
