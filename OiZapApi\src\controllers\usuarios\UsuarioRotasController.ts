import { Request, Response } from 'express';
import { PostgreSQLServices } from '../../services/PostgreSQLServices';

export class UsuarioRotasController {
  static async rotasPermitidas(req: Request, res: Response) {
    try {
      const { cd_usuario } = req.params;
      const sql = `
        SELECT DISTINCT t.ds_rota
        FROM usuarios_perfis up
        JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
        JOIN telas t ON t.cd_tela = pt.cd_tela
        WHERE up.cd_usuario = ${cd_usuario}
          AND pt.in_visualizar = true
      `;
      const result = await new PostgreSQLServices().query(sql);
      const rotas = (result.data || []).map((r: any) => r.ds_rota);
      return res.json({ rotas });
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao buscar rotas permitidas' });
    }
  }
} 