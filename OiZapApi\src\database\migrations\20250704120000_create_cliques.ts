import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('cliques', (table) => {
    table.increments('cd_clique').primary();
    table.integer('cd_disparo').unsigned().references('cd_disparo').inTable('disparos_pixel').onDelete('CASCADE');
    table.string('utm_source', 100).notNullable();
    table.timestamp('data_clique').notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('cliques');
} 