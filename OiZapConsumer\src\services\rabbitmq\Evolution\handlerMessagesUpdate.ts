import { config } from 'dotenv';
import { EstabelecimentosDB } from '../../../data/EstabelecimentosDB';
import { IMessageStatus } from '../../../interfaces/IMessageStatus';
import { erroInterno, IRetorno, sucesso } from '../../../interfaces/IRetorno';
import { MessagesModel } from '../../../models/MessagesModel';
import { PushWebHook } from '../../../services/PushWebHook';
import { Funcoes } from '../../Funcoes';
import Logger from '../../Logger';
config();
const logger = Logger.getLogger();

export class handlerMessagesUpdate {
  static async messagesUpdate(messageContent: any): Promise<IRetorno> {
    try {
      console.log('ESTA OCORRENDO ALGUM ERRO AQUI QUE FAZ DESCONECTA!');
      //let messageContent = JSON.parse(message.content.toString());
      logger.debug('handlerMessagesUpdate > messageContent: ' + JSON.stringify(messageContent));

      //       2025-06-29 18:57:15  -  debug : 🐛  handlerMessagesUpdate > messageContent: {"event":"messages.update","instance":"2378f6183a13e63d1592f12ef","data":{"messageId":"cmci7nofx087hpe500qd2rjpm","keyId":"3EB01E3DFA52F3D7A686CA021643E82312036E8A","remoteJid":"<EMAIL>","fromMe":true,"participant":"<EMAIL>","status":"DELIVERY_ACK","instanceId":"4ecfe2d3-20ee-479a-8a91-5348f0a9e59d"},"server_url":"https://evov2-sandbox.oizap.com.br","date_time":"2025-06-29T18:57:31.864Z","sender":"<EMAIL>","apikey":null}
      // err at line 152 in src/server.ts: Error: read ECONNRESET
      //     at TCP.onStreamRead (node:internal/stream_base_commons:217:20)
      //     at TCP.callbackTrampoline (node:internal/async_hooks:130:17) {
      //   errno: -4077,
      //   code: 'ECONNRESET',
      //   syscall: 'read'
      // }
      // 2025-06-29 18:57:15  -  error : ❌  Erro não capturado:{"errno":-4077,"code":"ECONNRESET","syscall":"read"}

      messageContent.data.status_message = Funcoes.trataStatus(messageContent?.data?.status);
      const messageUpdate: IMessageStatus = {
        event: messageContent.event,
        instance: messageContent.instance,
        message_id: messageContent.data.keyId == undefined ? messageContent.data.id : messageContent.data.keyId,
        messageId: messageContent.data.messageId == undefined ? messageContent.data.id : messageContent.data.messageId,
        keyId: messageContent.data.keyId == undefined ? messageContent.data.id : messageContent.data.keyId,
        remoteJid: messageContent.data.remoteJid,
        fromMe: messageContent.data.fromMe,
        participant: messageContent.data.participant,
        status: messageContent.data.status,
        instanceId:
          messageContent.data.instanceId == undefined ? messageContent.instance : messageContent.data.instanceId,
        tp_status: Funcoes.trataStatus(messageContent?.data?.status),
        server_url: messageContent.server_url,
        date_time: messageContent.date_time,
        sender: messageContent.sender,
        apikey: messageContent.apikey,
      };

      const respModulosAtivos = await EstabelecimentosDB.modulosAtivos({
        nameinstance: messageContent.instance,
      });

      const inAPI = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_api : false;
      const inChat = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_chat_pedidos : false;
      //Quando foi API, nao vai salvar a mensagem no banco, apenas envia o webhook
      if (inAPI == true && inChat == false) {
        const retorno = {
          webhookType: 'messageStatus',
          event: 'update',
          instance: messageContent.instance,
          message_id: messageUpdate.message_id,
          messageId: messageUpdate.messageId,
          keyId: messageUpdate.keyId,
          remoteJid: messageUpdate.remoteJid,
          fromMe: messageUpdate.fromMe,
          participant: messageUpdate.participant,
          status: messageUpdate.status,
          tp_status: messageUpdate.tp_status,
          date_time: messageUpdate.date_time,
          sender: messageUpdate.sender,
        };

        await PushWebHook.enviarWebhookComRetry(messageUpdate.instance, retorno, 3);
      }

      //console.log('messageContent at line 55 in rabbitmq/MessagesUpdateConsumer.ts:', messageContent);
      logger.debug('MessagesUpdateConsumer > messageUpdate: ' + JSON.stringify(messageUpdate));
      let respMessage = await new MessagesModel().updateMessage(messageUpdate);
      logger.debug('MessagesUpdateConsumer > messageUpdate > respMessage: ' + JSON.stringify(respMessage));
      //console.log('respMessage at line 56 in rabbitmq/MessagesUpdateConsumer.ts:', respMessage);

      if (respMessage.statuscode === 200) {
        return sucesso(messageUpdate, 'ack');
      } else {
        logger.error(`MessagesUpdateConsumer > Error updateMessage message PostgreSQL ${JSON.stringify(respMessage)}`);
        return erroInterno(respMessage.errors, 'reject');
      }
    } catch (error: any) {
      logger.error('error at line 59 in Evolution/handlerMessagesUpdate.ts:' + JSON.stringify(error));
      return sucesso(error, 'reject');
    }
  }
}
