-- Migration: remove_instance_contacts
-- Created: 2025-06-01T16:46:15.000Z
-- Environment: ALL

-- ========================================
-- UP: Remover coluna instance da tabela contacts
-- ========================================

DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'contacts' 
        AND column_name = 'instance'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE contacts DROP COLUMN instance;
        RAISE NOTICE 'Coluna instance removida da tabela contacts';
    ELSE
        RAISE NOTICE 'Coluna instance não existe na tabela contacts';
    END IF;
END $$; 