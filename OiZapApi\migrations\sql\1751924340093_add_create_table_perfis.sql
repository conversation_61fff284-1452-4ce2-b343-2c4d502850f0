-- Migration: add_create_table_perfis
-- Created: 2025-07-07T21:39:00.093Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

-- Exemplo: Criar tabela
CREATE TABLE IF NOT EXISTS perfis (
  cd_perfil SERIAL PRIMARY KEY,
  nm_perfil VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Exemplo: Adicionar coluna
-- ALTER TABLE tabela_existente 
-- ADD COLUMN IF NOT EXISTS nova_coluna VARCHAR(50);

-- Exemplo: Criar índice  
-- CREATE INDEX IF NOT EXISTS idx_exemplo_nome 
-- ON exemplo_tabela(nome);

-- ========================================
-- Adicione seu SQL aqui:
-- ========================================


