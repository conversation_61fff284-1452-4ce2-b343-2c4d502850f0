import { IToken } from '@/interfaces/IToken';
import { Request } from 'express';
import { MotivosAtendimentoDB } from '../../data/motivos-atendimento/MotivosAtendimentoDB';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';

export class MotivosAtendimentoModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      const token = req.query.token as unknown as IToken;
      req.body.cd_usucad = token?.cd_usuario || 0;
      return await MotivosAtendimentoDB.incluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      return await MotivosAtendimentoDB.listar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async alterar(req: Request): Promise<IRetorno> {
    try {
      return await MotivosAtendimentoDB.alterar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async excluir(req: Request): Promise<IRetorno> {
    try {
      return await MotivosAtendimentoDB.excluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
