//import { EstabelecimentoInstanciasDB } from '../data/EstabelecimentoInstanciasDB';
import {
  IRetorno,
  dadosNaoEncontrados,
  erroInterno,
  generico,
  parametrosInvalidos,
  sucesso,
} from '../interfaces/IRetorno';
import {
  ISendAudio,
  ISendButton,
  ISendDocument,
  ISendImage,
  ISendList,
  ISendLocation,
  ISendMessage,
  MessageType,
  SectionList,
} from '../interfaces/ISendMessageWhats';
// import MessagesDB from '../data/MessagesDB';
// import Messages from '../models/Messages';
import { MessagesModel } from '../models/MessagesModel';
import { Funcoes } from '../services/Funcoes';
//import SendMessageOiZap from '../services/rabbitmq/OiZap/SendMessageOiZap';
import { AtendimentosDB } from '../data/AtendimentosDB';
import { FluxoMensagensDB } from '../data/FluxoMensagensDB';
import { IMessage } from '../interfaces/IMessage';
import { WhatsAppService } from '../services/WhatsAppService';
// import { ClientesDB } from '../data/ClientesDB';
// import { json } from 'body-parser';
import dotenv, { config } from 'dotenv';
import path from 'path';
import Logger from '../services/Logger';
import { publishToQueue } from '../services/rabbitmq/RabbitMQ';
const logger = Logger.getLogger();

config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

export class WhatsAppModel {
  private URL_ARQUIVOS: string;

  constructor() {
    if (process.env.AMBIENTE == 'PROD') {
      this.URL_ARQUIVOS = process.env.URL_ARQUIVOS || '';
    } else if (process.env.AMBIENTE == 'CRM') {
      this.URL_ARQUIVOS = process.env.URL_ARQUIVOS || '';
    } else {
      this.URL_ARQUIVOS = process.env.URL_ARQUIVOS_SANDBOX || '';
    }
  }

  async montaComprovanteVenda(pedido: any): Promise<string> {
    let mensagem = '```';
    mensagem += `${pedido.estabelecimento.nm_estabelecimento}
--------------------------- 
Segue o seu pedido, qualquer divergência, favor informar 
--------------------------- 
Cliente..: ${pedido.cliente.ds_nome}  
--------------------------- 
Local de Retirada : 
Endereço.: ${pedido.cliente.ds_endereco},${pedido.cliente.nr_endereco} 
Compl....: ${pedido.cliente.ds_complemento} 
Bairro...: ${pedido.cliente.ds_bairro}
--------------------------- 
Produto 
 Qtde  Vl. Unit   Vl. Total 
---------------------------`;
    for (const item of pedido.itens) {
      let adicionais = '';
      mensagem += `
${item.ds_produto}`;
      for (const adicional of item.adicionais) {
        if (adicionais == '') {
          adicionais += `${adicional.ds_adicional}`;
        } else {
          adicionais += `, ${adicional.ds_adicional}`;
        }
      }
      if (adicionais != '') {
        mensagem += `
${adicionais}`;
      }
      mensagem += `
    ${item.qt_produto}      ${item.vl_produto}       ${item.vl_total}`;
    }

    mensagem += `
--------------------------- 
Total Produtos.:  ${pedido.vl_produtos} 
  Taxa Serviço.:  ${pedido.vl_entrega}
   VALOR TOTAL.:  ${pedido.vl_total}

    Troco Para.:  ${pedido.vl_trocopara}    
 
Envio automático por Resulti Sistemas 
(44) 99950-5202`;

    mensagem += '```';

    return mensagem;
  }
  async comprovantePedido(dados: any): Promise<IRetorno> {
    //console.log('dados at line 321 in models/WhatsAppModel.ts:', JSON.stringify(dados));

    try {
      let pedido = dados.pedido ? dados.pedido : dados.pedidos;
      if (pedido.itens.length == 0) {
        const msg =
          'Estabelecimento ' +
          dados?.cd_estabelecimento +
          ', Instancia ' +
          dados.instance +
          ',  telefone ' +
          dados.telefone +
          ', Atendimento ' +
          dados?.cd_atendimento +
          ', Pedido ' +
          pedido?.cd_pedido +
          ' sem itens';
        logger.info(msg);
        return dadosNaoEncontrados(msg);
      }
      let mensagem = dados.mensagem;
      //console.log('mensagem :', mensagem);

      mensagem = mensagem.replace(/\[Pagamento\]/g, '[_MeioPgto_]');

      const substituirBloco = (mensagem: string, bloco: string, conteudo: string): string => {
        const regex = new RegExp(`\\[${bloco}\\][\\s\\S]*\\[${bloco}\\]`, 'g');
        if (conteudo.trim()) {
          return mensagem.replace(regex, conteudo);
        } else {
          return mensagem.replace(regex, '').replace(/^\s*$(?:\r\n?|\n)/gm, '');
        }
      };

      const obterBloco = (mensagem: string, bloco: string): string => {
        // Cria um regex para capturar o conteúdo entre as tags [bloco]
        const regex = new RegExp(`\\[${bloco}\\]([\\s\\S]*?)\\[${bloco}\\]`, 'g');

        // Executa o regex na mensagem e obtém o conteúdo do bloco
        const match = regex.exec(mensagem);

        // Se encontrar o bloco, retorna apenas o conteúdo; caso contrário, retorna uma string vazia
        return match ? match[1].trim() : '';
      };

      mensagem = mensagem
        //.replace('{Contato}', dados.nome)
        .replace('{Fantasia}', dados.estabelecimento)
        .replace('{Telefone}', dados.telefone)
        .replace('{Hash}', dados.hash || '')
        .replace('{VlTotal}', Funcoes.formataValor(pedido.vl_total))
        .replace('{VlProdutos}', Funcoes.formataValor(pedido.vl_subtotal))
        .replace('{VlDescontoTotal}', Funcoes.formataValor(pedido.vl_descontototal))
        .replace('{VlDescontoPedido}', Funcoes.formataValor(pedido.vl_descontopedido))
        .replace('{VlSubTotal}', Funcoes.formataValor(pedido.vl_subtotal))
        .replace('{VlEntrega}', Funcoes.formataValor(pedido.vl_entrega) || '');

      let itens = '';

      let blocoItens = obterBloco(mensagem, 'Lista');
      let blocoAdicional = obterBloco(blocoItens, 'Adicional');
      let blocoBorda = obterBloco(blocoItens, 'Borda');
      let blocoSabor = obterBloco(blocoItens, 'Sabor');
      for (const item of pedido.itens) {
        let adicionais = '';
        let bordas = '';
        let sabores = '';
        let adicionaisItens = '';
        let bordasItens = '';
        let saboresItens = '';
        for (const sabor of item.sabores) {
          let nmSabor = sabor.ds_produto.replace(' - ' + sabor.ds_tamanho, '') + ' ' + sabor.qt_itempizza;

          if (sabor.ds_observacao != undefined && sabor.ds_observacao != null && sabor.ds_observacao != '') {
            nmSabor += '\n' + sabor.ds_observacao;
          }
          //let ad = blocoSabor.replace('{Sabores}', nmSabor);
          let ad = blocoSabor;
          ad = ad.replace('{Sabores}', nmSabor);
          sabores += sabores ? '\n' + ad : ad;
        }
        for (const borda of item.bordas) {
          //let ad = blocoBorda.replace('{Bordas}', borda.ds_borda);
          let ad = blocoBorda;
          ad = ad.replace('{Bordas}', borda.ds_borda);
          bordas += bordas ? '\n' + ad : ad;
        }
        for (const adicional of item.adicionais) {
          //let ad = blocoAdicional.replace('{Adicionais}', adicional.ds_adicional);
          let ad = blocoAdicional;
          ad = ad.replace('{Adicionais}', adicional.ds_adicional);
          adicionais += adicionais ? '\n' + ad : ad;
        }

        if (itens != '') itens += `\n`;
        itens += blocoItens
          .replace('{Produto}', item.ds_produto)
          .replace('{Quantidade}', Funcoes.formataQuantidade(item.qt_produto))
          .replace('{VlUnitario}', Funcoes.formataValor(item.vl_produto))
          .replace('{VlUnitarioSubTotal}', Funcoes.formataValor(item.vl_subtotal))
          .replace('{VlDescontoItem}', Funcoes.formataValor(item.vl_desconto))
          .replace('{VlUnitarioTotalBruto}', Funcoes.formataValor(item.vl_unitariosubtotal))
          .replace('{VlTotalProduto}', Funcoes.formataValor(item.vl_total));

        if (item.ds_observacao == '' || item.ds_observacao == null) {
          //itens = itens.replace(/```{ObservacaoItem}```/g, '');
          itens = substituirBloco(itens, 'ObservacaoItem', '');
        } else {
          let blocoObsItem = obterBloco(blocoItens, 'ObservacaoItem');
          let obsItem = blocoObsItem.replace('{ObservacaoItem}', item.ds_observacao);
          itens = substituirBloco(itens, 'ObservacaoItem', obsItem);
        }

        if (sabores == '') {
          itens = substituirBloco(itens, 'Sabor', '');
        } else {
          // blocoSabor = blocoSabor.replace(/```/g, '');
          // blocoSabor = blocoSabor.trim();
          // saboresItens = blocoSabor.replace('{Sabores}', sabores);
          // itens = substituirBloco(itens, 'Sabor', saboresItens);
          let blSabor = blocoSabor;
          blSabor = blSabor.replace(/```/g, '');
          blSabor = blSabor.trim();
          saboresItens = blSabor.replace('{Sabores}', sabores);
          itens = substituirBloco(itens, 'Sabor', saboresItens);
        }

        if (bordas == '') {
          itens = substituirBloco(itens, 'Borda', '');
        } else {
          // blocoBorda = blocoBorda.replace(/```/g, '');
          // blocoBorda = blocoBorda.trim();
          // bordasItens = blocoBorda.replace('{Bordas}', bordas);
          // itens = substituirBloco(itens, 'Borda', bordasItens);
          let blBorda = blocoBorda;
          blBorda = blBorda.replace(/```/g, '');
          blBorda = blBorda.trim();
          bordasItens = blBorda.replace('{Bordas}', bordas);
          itens = substituirBloco(itens, 'Borda', bordasItens);
        }
        if (adicionais == '') {
          itens = substituirBloco(itens, 'Adicional', '');
        } else {
          // blocoAdicional = blocoAdicional.replace(/```/g, '');
          // blocoAdicional = blocoAdicional.trim();
          // adicionaisItens = blocoAdicional.replace('{Adicionais}', adicionais);
          // itens = substituirBloco(itens, 'Adicional', adicionaisItens);
          let blAdicional = blocoAdicional;
          blAdicional = blAdicional.replace(/```/g, '');
          blAdicional = blAdicional.trim();
          adicionaisItens = blAdicional.replace('{Adicionais}', adicionais);
          itens = substituirBloco(itens, 'Adicional', adicionaisItens);
        }
      }

      mensagem = substituirBloco(mensagem, 'Lista', itens);

      let pagamentoBloco = obterBloco(mensagem, '_MeioPgto_');
      if (pedido.tp_forma_pgto == 1) {
        if (pedido.vl_trocopara > 0) {
          let blocoTroco = obterBloco(mensagem, 'Troco');
          let meioPgto = blocoTroco
            .replace('{VlTrocoPara}', Funcoes.formataValor(pedido.vl_trocopara))
            .replace('{VlTroco}', Funcoes.formataValor(pedido.vl_troco));
          pagamentoBloco = substituirBloco(pagamentoBloco, 'Troco', meioPgto);
        } else {
          pagamentoBloco = substituirBloco(pagamentoBloco, 'Troco', '');
        }

        pagamentoBloco = pagamentoBloco.replace('{FormaPagamento}', 'Dinheiro');
      } else if (pedido.tp_forma_pgto == 3) {
        pagamentoBloco = pagamentoBloco.replace('{FormaPagamento}', 'Cartão de Crédito');
      } else if (pedido.tp_forma_pgto == 4) {
        pagamentoBloco = pagamentoBloco.replace('{FormaPagamento}', 'Cartão de Débito');
      } else if (pedido.tp_forma_pgto == 11) {
        pagamentoBloco = pagamentoBloco.replace('{FormaPagamento}', 'Pix');
      } else if (pedido.tp_forma_pgto == 7) {
        pagamentoBloco = pagamentoBloco.replace('{FormaPagamento}', 'Prazo');
      } else {
        pagamentoBloco = '';
      }

      pagamentoBloco = substituirBloco(pagamentoBloco, 'Troco', '');

      mensagem = mensagem.replace('{VlTotal}', Funcoes.formataValor(pedido.vl_total));
      mensagem = substituirBloco(mensagem, '_MeioPgto_', pagamentoBloco);
      //console.log('mensagem at line 411 in models/WhatsAppModel.ts:', mensagem);

      let endereco = '';
      //3 = Entrega, 2 = Retirada (Balcão)
      if (pedido.tp_atendimento == 3) {
        let entregaBloco = obterBloco(mensagem, 'Delivery');

        let tEndereco = '';
        let tComplemento = pedido?.cliente?.ds_complemento;
        let tBairro = pedido?.cliente?.ds_bairro;
        if (pedido.ds_endereco != undefined) {
          tEndereco = pedido.ds_endereco + ', ' + pedido.nr_endereco;
        } else if (pedido?.cliente?.ds_endereco != undefined) {
          tEndereco = pedido.cliente.ds_endereco + ', ' + pedido.cliente.nr_endereco;
        }
        let tContato = pedido?.cliente?.ds_nome;

        endereco = entregaBloco
          .replace('{Contato}', tContato ? tContato : dados.nome)
          .replace('{Endereco}', tEndereco == '' ? '' : tEndereco)
          .replace('{Complemento}', tComplemento ? tComplemento : '')
          .replace('{Bairro}', tBairro ? tBairro : '');

        mensagem = substituirBloco(mensagem, 'Delivery', endereco);

        mensagem = substituirBloco(mensagem, 'Retirada', '');
      } else {
        let retiradaBloco = obterBloco(mensagem, 'Retirada');

        endereco = retiradaBloco.replace('{Endereco}', pedido.estabelecimento.ds_endereco);

        mensagem = substituirBloco(mensagem, 'Retirada', endereco);
        mensagem = substituirBloco(mensagem, 'Delivery', '');
      }

      mensagem = mensagem.replace('{ObservacaoPedido}', pedido.ds_observacao != null ? pedido.ds_observacao : '');

      //mensagem = mensagem.replace(/^\s*$(?:\r\n?|\n)/gm, '');

      mensagem += `
      
Envio automático por Resulti Sistemas 
(44) 99950-5202 `;

      let pulaLinha = `\n`;
      mensagem = mensagem.replace(/\{pularLinha\}/g, pulaLinha);

      // console.log('mensagem at line 293 in models/WhatsAppModel.ts:', mensagem);

      let reqObj = {
        telefone: dados.telefone,
        instance: dados.instance,
        cd_estabelecimento: dados?.cd_estabelecimento,
        cd_atendimento: dados?.cd_atendimento,
        mensagem: mensagem,
        timer: dados.hr_tempo,
        linkpreview: true,
      };

      //console.log('reqObj at line 266 in models/WhatsAppModel.ts:', reqObj);
      await this.mensagemTexto(reqObj);

      return sucesso([mensagem]);
    } catch (error: any) {
      //console.log('error at line 444 in models/WhatsAppModel.ts:', error);
      return erroInterno(error);
    }
  }
  //Função para ver se é cliente novo ou existente
  // >>> REGRAS <<<
  // - Mensagem de verificação de cliente não tem imagem e não é do tipo lista
  async verificaCliente(dados: any): Promise<IRetorno> {
    //console.log('Testar se está passando o estabelecimento e atendimento:', dados);
    //mensagem: any, msgRelacionada: any, query:any
    try {
      if (dados.msgRelacionada == null) return dadosNaoEncontrados();

      if (dados.query.tp_funcionalidade === 'Inicio') {
        //let enviado = false;

        // Determina o tipo de cliente e define a funcionalidade correspondente
        const funcionalidade = dados.query.cd_cliente === undefined ? 'Novo Cliente' : 'Cliente Existente';

        // Filtra a mensagem que corresponde à funcionalidade e não é uma opção
        const mensagem = dados.msgRelacionada.find(
          (msg: any) => !msg.in_opcoes && msg.tp_funcionalidade === funcionalidade,
        );

        if (mensagem) {
          // Prepara o objeto de requisição
          const reqObj = {
            telefone: dados.query.telefone,
            instance: dados.query.instance,
            cd_estabelecimento: dados.query.cd_estabelecimento,
            cd_atendimento: dados.query.cd_atendimento,
            mensagem: Funcoes.replaceData(mensagem.ds_mensagem, dados.dadosReplace) || '',
            timer: mensagem.hr_tempo,
            imagens: mensagem.imagens,
            in_adicionatexto_imagem: mensagem.in_adicionatexto_imagem,
          };

          // Verifica se há mensagem para enviar
          if (reqObj.mensagem || reqObj.imagens) {
            if (reqObj.imagens) {
              await this.mensagemImagem(reqObj); // Envia mensagem com imagem
            } else {
              await this.mensagemTexto(reqObj); // Envia mensagem de texto
            }
          }
        }
      }

      /*
      if (dados.query.tp_funcionalidade == 'Inicio') {
        let enviado = false;
        for (const msg of dados.msgRelacionada) {
          if (enviado) break;
          if (msg.in_opcoes == false) {
            let reqObj = {
              telefone: dados.query.telefone,
              instance: dados.query.instance,
              cd_estabelecimento: dados.query.cd_estabelecimento,
              cd_atendimento: dados.query.cd_atendimento,
              mensagem: '',
              timer: msg.hr_tempo,
              imagens: undefined,
              in_adicionatexto_imagem: msg.in_adicionatexto_imagem,
            };

            if (dados.query.cd_cliente == undefined && msg.tp_funcionalidade == 'Novo Cliente') {
              reqObj.mensagem = Funcoes.replaceData(msg.ds_mensagem, dados.dadosReplace);
              if (msg.imagens == null) {
                //se não contem imagem envia a msg de texto
                if (reqObj.mensagem != '' || reqObj.mensagem != undefined || reqObj.mensagem != null) {
                  enviado = true;
                  await this.mensagemTexto(reqObj);
                }
              } else {
                reqObj.imagens = msg.imagens;
                await this.mensagemImagem(reqObj);
              }
            }

            if (dados.query.cd_cliente != undefined && msg.tp_funcionalidade == 'Cliente Existente') {
              reqObj.mensagem = Funcoes.replaceData(msg.ds_mensagem, dados.dadosReplace);

              if (msg.imagens == null) {
                //se não contem imagem envia a msg de texto
                if (reqObj.mensagem != '' || reqObj.mensagem != undefined || reqObj.mensagem != null) {
                  enviado = true;
                  await this.mensagemTexto(reqObj);
                }
              } else {
                reqObj.imagens = msg.imagens;
                await this.mensagemImagem(reqObj);
              }
            }
          }
        }
      }
*/
      return sucesso([]);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  //Função para consultar a situação do Pedido
  async consultarPedido(dados: any): Promise<IRetorno> {
    // console.log('dados at line 247 in models/WhatsAppModel.ts:', dados);
    //mensagem: any, msgRelacionada: any, query:any
    try {
      if (dados == null) return dadosNaoEncontrados();

      let enviado = false;
      for (const msg of dados.mensagem) {
        if (enviado) break;
        if (msg.in_opcoes == false) {
          let reqObj = {
            telefone: dados.query.telefone,
            instance: dados.query.instance,
            mensagem: '',
            timer: msg.hr_tempo,
            imagens: undefined,
            in_adicionatexto_imagem: msg.in_adicionatexto_imagem,
            cd_estabelecimento: dados.query.cd_estabelecimento,
            cd_atendimento: dados.query.cd_atendimento,
          };

          if (dados.query.tp_situacao_pedido == msg.tp_funcionalidade) {
            reqObj.mensagem = Funcoes.replaceData(msg.ds_mensagem, dados.dadosReplace);
            if (msg.imagens == null) {
              //se não contem imagem envia a msg de texto
              if (reqObj.mensagem != '' || reqObj.mensagem != undefined || reqObj.mensagem != null) {
                enviado = true;
                await this.mensagemTexto(reqObj);
              }
            } else {
              reqObj.imagens = msg.imagens;
              await this.mensagemImagem(reqObj);
            }
          }
        }
      }
      return sucesso([]);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async mensagemTexto(dados: any): Promise<IRetorno> {
    // console.log('dados at line 434 in models/WhatsAppModel.ts:', dados);
    try {
      if (dados.mensagem == '' || dados.mensagem == undefined || dados.mensagem == null) {
        console.log('mensagem vazia', dados);
        return generico('mensagem vazia');
      }

      const idunico = Funcoes.gerarHashUnica(dados.telefone, 20);
      const dataMessage = {
        cd_estabelecimento: dados.cd_estabelecimento,
        cd_atendimento: dados.cd_atendimento,
        telefone: dados.telefone,
        nome: dados?.nome,
        instance: dados.instance,
        mensagem: dados.mensagem,
        timer: dados.timer,
        in_rabbitmq: true,
        //nr_hash: mensagem.nr_hash,
        type: MessageType.Message,
        idunico: idunico,
        linkpreview: dados.linkpreview == undefined ? false : dados.linkpreview,
        tp_funcionalidade: dados.tp_funcionalidade,
        ds_evento: dados.ds_evento,
        status_pedido: dados?.status_pedido,
        pedidos: dados?.pedidos,
        ds_foto: dados?.ds_foto,
        profile_picture_base64: dados?.profile_picture_base64,
        in_acessoulink: dados?.in_acessoulink,
        tp_etapachat: dados?.tp_etapachat,
        in_enviointerno: true,
      };
      //console.log('dataMessage at line 158 in models/WhatsAppModel.ts:', dataMessage);
      const respSendMSG = await this.sendMessage(dataMessage);
      // console.log('respSendMSG at line 118 in fluxos/FluxoAtendimentoModel.ts:', respSendMSG);
      return respSendMSG;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  // Função sleep para pausar a execução por um determinado período
  sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  async mensagemImagem(dados: any): Promise<IRetorno> {
    // console.log('dados at line 555 in models/WhatsAppModel.ts:', dados);
    try {
      let respSendMSG: any;
      let mensagem = '';
      if (dados.in_adicionatexto_imagem == true) {
        mensagem = dados.mensagem;
      } else {
        let reqObj = {
          cd_estabelecimento: dados.cd_estabelecimento,
          cd_atendimento: dados.cd_atendimento,
          telefone: dados.telefone,
          nome: dados?.nome,
          instance: dados.instance,
          mensagem: dados.mensagem,
          timer: dados.timer,
          ds_evento: dados.ds_evento,
          status_pedido: dados?.status_pedido,
          pedidos: dados?.pedidos,
          ds_foto: dados?.ds_foto,
          profile_picture_base64: dados?.profile_picture_base64,
          in_acessoulink: dados?.in_acessoulink,
          tp_etapachat: dados?.tp_etapachat,
          in_enviointerno: true,
        };
        await this.mensagemTexto(reqObj);
      }
      for (const imagem of dados.imagens) {
        await this.sleep(2000);
        const idunico = Funcoes.gerarHashUnica(dados.telefone, 30);
        //${this.URL_ARQUIVOS}

        let url_midia = `${imagem.ds_host}/${imagem.nm_arquivo}`;
        //console.log('url_midia at line 434 in models/WhatsAppModel.ts:', url_midia);
        let dataImageMessage = {
          cd_estabelecimento: dados.cd_estabelecimento,
          cd_atendimento: dados.cd_atendimento,
          telefone: dados.telefone,
          nome: dados?.nome,
          instance: dados.instance,
          mensagem: mensagem,
          timer: dados.timer,
          in_rabbitmq: true,
          //nr_hash: mensagem.nr_hash,
          type: MessageType.Image,
          idunico: idunico,
          url_midia: url_midia,
          url_midiacompleta: `${this.URL_ARQUIVOS}${url_midia}`,
          fileName: imagem.nm_arquivo,
          caption: imagem.nm_arquivo,
          ds_evento: dados.ds_evento,
          status_pedido: dados?.status_pedido,
          pedidos: dados?.pedidos,
          ds_foto: dados?.ds_foto,
          profile_picture_base64: dados?.profile_picture_base64,
          in_acessoulink: dados?.in_acessoulink,
          tp_etapachat: dados?.tp_etapachat,
          in_enviointerno: true,
        };
        //console.log('dataImageMessage at line 158 in models/WhatsAppModel.ts:', dataImageMessage);
        if (imagem.ds_categoria == 'documents') {
          respSendMSG = await this.sendDocument(dataImageMessage);
        } else {
          respSendMSG = await this.sendImage(dataImageMessage);
        }
        //console.log('respSendMSG at line 118 in fluxos/FluxoAtendimentoModel.ts:', respSendMSG);
      }

      if (respSendMSG.statuscode == 200) {
        return sucesso(respSendMSG.data);
      } else {
        return erroInterno(respSendMSG.errors);
      }
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async mensagemLista(dados: any): Promise<IRetorno> {
    try {
      //console.log('dados at line 531 in models/WhatsAppModel.ts:', dados);
      let dataList: ISendList;
      let rows = [];
      let lista: string = dados.msg_relacionadas
        .filter((msg: any) => msg.in_opcoes) // Filtra apenas os itens com in_opcoes marcado
        .map((msg: any) => '• ' + msg.ds_titulo) // Mapeia para o formato desejado
        .join('\n'); // Faz o join sem \n extras

      for (const msg of dados.msg_relacionadas) {
        if (msg.in_opcoes == true) {
          rows.push({
            title: msg.ds_titulo,
            //description: '',
            rowId: String(msg.nr_controle),
          });
        }
      }
      const checkList: SectionList = {
        title: '',
        rows: rows,
      };

      const idunico = Funcoes.gerarHashUnica(dados.telefone, 20);
      dataList = {
        cd_estabelecimento: dados.cd_estabelecimento,
        cd_atendimento: dados.cd_atendimento,
        telefone: dados.telefone,
        timer: dados.hr_tempo,
        instance: dados.instance,
        mensagem: dados.mensagem,
        in_rabbitmq: true,
        //nr_hash: mensagem.nr_hash,
        type: MessageType.List,
        idunico: idunico,
        title: dados.title,
        description: 'Selecione uma das opções',
        buttonText: dados.ds_titulobotao,
        sections: [checkList],
        footerText: '',
        opcoes: lista,
        ds_evento: dados.ds_evento,
        status_pedido: dados?.status_pedido,
        pedidos: dados?.pedidos,
        ds_foto: dados?.ds_foto,
        profile_picture_base64: dados?.profile_picture_base64,
        in_acessoulink: dados?.in_acessoulink,
        tp_etapachat: dados?.tp_etapachat,
        in_enviointerno: true,
      };

      // console.log('dataList at line 133 in fluxos/FluxoAtendimentoModel.ts:', dataList);
      logger.debug('dataList at line 579 in models/WhatsAppModel.ts: ' + JSON.stringify(dataList));
      const respSendMSG = await this.sendList(dataList); //WhatsServices.sendList(dataList);
      logger.debug('respSendMSG at line 581 in models/WhatsAppModel.ts:' + JSON.stringify(respSendMSG));
      //console.log('respSendMSG at line 149 in fluxos/FluxoAtendimentoModel.ts:', respSendMSG);

      return respSendMSG;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async mensagemOpcoes(dados: any): Promise<IRetorno> {
    try {
      // console.log('dados at line 326 in models/WhatsAppModel.ts:', dados);
      let lista: string = dados.msg_relacionadas
        .filter((msg: any) => msg.in_opcoes == true) // Filtra apenas onde in_opcoes == true
        .map((msg: any) => msg.ds_titulo) // Mapeia para o formato desejado
        .join('\n'); // Junta os itens com nova linha

      // console.log('lista at line 536 in models/WhatsAppModel.ts:', lista);
      const idunico = Funcoes.gerarHashUnica(dados.telefone, 20);
      const dataMessage = {
        cd_estabelecimento: dados.cd_estabelecimento,
        cd_atendimento: dados.cd_atendimento,
        telefone: dados.telefone,
        instance: dados.instance,
        mensagem: lista,
        timer: dados.timer,
        in_rabbitmq: true,
        //nr_hash: mensagem.nr_hash,
        type: MessageType.Message,
        idunico: idunico,
        linkpreview: dados.linkpreview == undefined ? false : dados.linkpreview,
        tp_funcionalidade: dados.tp_funcionalidade,
        ds_evento: dados.ds_evento,
        status_pedido: dados?.status_pedido,
        pedidos: dados?.pedidos,
        ds_foto: dados?.ds_foto,
        profile_picture_base64: dados?.profile_picture_base64,
        in_acessoulink: dados?.in_acessoulink,
        tp_etapachat: dados?.tp_etapachat,
        in_enviointerno: true,
      };
      //console.log('dataMessage at line 158 in models/WhatsAppModel.ts:', dataMessage);
      const respSendMSG = await this.sendMessage(dataMessage);

      return respSendMSG;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async mensagemLocation(dados: any): Promise<IRetorno> {
    try {
      if (dados.message != '' || dados.message != undefined) {
        let reqObj = {
          cd_estabelecimento: dados.cd_estabelecimento,
          cd_atendimento: dados.cd_atendimento,

          telefone: dados.telefone,
          instance: dados.instance,
          mensagem: dados.mensagem,
          timer: dados.timer,
        };
        await this.mensagemTexto(reqObj);
      }
      const idunico = Funcoes.gerarHashUnica(dados.telefone, 20);
      const dataMessage = {
        cd_estabelecimento: dados.cd_estabelecimento,
        cd_atendimento: dados.cd_atendimento,
        telefone: dados.telefone,
        nome: dados?.nome,
        instance: dados.instance,
        estabelecimento: dados.estabelecimento,
        address: dados.address,
        latitude: dados.latitude,
        longitude: dados.longitude,
        timer: dados.timer,
        in_rabbitmq: true,
        type: MessageType.Location,
        idunico: idunico,
        ds_evento: dados.ds_evento,
        status_pedido: dados?.status_pedido,
        pedidos: dados?.pedidos,
        ds_foto: dados?.ds_foto,
        profile_picture_base64: dados?.profile_picture_base64,
        in_acessoulink: dados?.in_acessoulink,
        tp_etapachat: dados?.tp_etapachat,
        in_enviointerno: true,
      };
      //console.log('dataMessage at line 158 in models/WhatsAppModel.ts:', dataMessage);
      const respSendMSG = await this.sendLocation(dataMessage);
      //console.log('respSendMSG at line 118 in fluxos/FluxoAtendimentoModel.ts:', respSendMSG);
      return respSendMSG;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async enviarMensagem(req: any): Promise<IRetorno> {
    try {
      //logger.debug('req at line 632 in models/WhatsAppModel.ts:' + JSON.stringify(req.body));
      if (req.body.nr_controle == undefined && req.body.tp_funcionalidade == undefined) {
        return parametrosInvalidos('Tipo de Funcionalidade não informada!');
      }

      let query = req.body;
      let in_acessoulink = false;
      //console.log('query at line 14 in models/WhatsAppModel.ts:', query);

      //caso tenha pedido, consulta o pedido
      if (query.cd_pedido != undefined) {
        req.query = {
          cd_estabelecimento: query.cd_estabelecimento,
          cd_pedido: query.cd_pedido,
        };
        const respPedido = await FluxoMensagensDB.listarPedidoCompletos(req);
        //console.log('respPedido at line 572 in models/WhatsAppModel.ts:', respPedido);
        if (respPedido.statuscode == 200) {
          query.pedidos = respPedido.data[0];
        }
      } else if (query.cd_atendimento != undefined) {
      }

      let respMensagens = dadosNaoEncontrados('');
      let respFalarAtendente = undefined;

      if (query?.nr_controle != undefined || query?.tp_funcionalidade) {
        req.query = {
          cd_estabelecimento: query.cd_estabelecimento,
          nr_controle: query.nr_controle,
          instance: query.instance,
          tp_funcionalidade: query.tp_funcionalidade,
        };

        //logger.debug('req.query at line 666 in models/WhatsAppModel.ts:' + JSON.stringify(req.query));
        respMensagens = await FluxoMensagensDB.listarFluxoAtendimentosEst(req.query);
        // logger.debug('respMensagens at line 667 in models/WhatsAppModel.ts:' + JSON.stringify(respMensagens));
      }

      //Caso não ache, verifica se tem msg de Opções
      /*Robinho - 01/11/2024 11:38
        Desativado a verificação para opções para ver se é o problema de duplicação de msg*/
      // if (respMensagens.statuscode != 200) {
      //   req.query = {
      //     cd_estabelecimento: query.cd_estabelecimento,
      //     //nr_controle: query.nr_controle,
      //     instance: query.instance,
      //     tp_funcionalidade: 'Opções',
      //   };

      //   logger.debug('req.query at line 681 in models/WhatsAppModel.ts:' + JSON.stringify(req.query));
      //   respMensagens = await FluxoMensagensDB.listarFluxoAtendimentosEst(req.query);
      //   logger.debug('respMensagens at line 683 in models/WhatsAppModel.ts:' + JSON.stringify(respMensagens));
      // }
      // console.log('respMensagens at line 489 in models/WhatsAppModel.ts:', respMensagens);

      //Verifica se tem atendimento e pedido que não foi entregue
      if (
        query.tp_situacao_atendimento == 'Em Atendimento' &&
        respMensagens?.data[0]?.tp_funcionalidade != 'Consultar Pedido' &&
        respMensagens?.data[0]?.tp_funcionalidade != 'Falar com Atendente' &&
        query.tp_status_pedido != '' &&
        query.tp_status_pedido != undefined &&
        (query.tp_status_pedido != 'Em Pedido' ||
          query.tp_status_pedido == 'Em Pagamento' ||
          query.tp_status_pedido == 'Pedido Entregue')
      ) {
        req.query = {
          cd_estabelecimento: query.cd_estabelecimento,
          instance: query.instance,
          tp_funcionalidade: 'Verifica Pedido',
        };

        // logger.debug('req.query at line 704 in models/WhatsAppModel.ts:' + JSON.stringify(req.query));
        respMensagens = await FluxoMensagensDB.listarFluxoAtendimentosEst(req.query);
        // logger.debug('respMensagens at line 706 in models/WhatsAppModel.ts:' + JSON.stringify(respMensagens));
      }

      if (
        (query.tp_situacao_atendimento == 'Em Atendimento' &&
          query.tp_situacao_pedido != 'Em Pedido' &&
          query.tp_situacao_pedido != 'Em Pagamento' &&
          query.tp_situacao_pedido != null &&
          query.tp_situacao_pedido != undefined) ||
        query.tp_situacao_atendimento == 'Atendimento Finalizado' ||
        query.tp_situacao_atendimento == 'Atendimento Cancelado'
      ) {
        // console.log('Entrou no if de falar com atendente');
        const reqObj = {
          cd_atendimento: query.cd_atendimento,
          tp_status_atendimento: 'Falar com Atendente',
        };

        await AtendimentosDB.atualizaStatusAtendimento(reqObj);

        query.tp_etapachat = 'Falar com Atendente';
        query.in_stop_bot = true;
        respFalarAtendente = sucesso([query]);
      }

      //console.log('respMensagens at line 712 in models/WhatsAppModel.ts:', respMensagens);

      if (respMensagens.statuscode == 200) {
        // console.log(
        //   'respMensagens at line 714 in models/WhatsAppModel.ts:',
        //   'VERIFICAR NECESSIDADE DESSE CODIGO ABAIXO COMENTADO',
        // );

        // req.query = {
        //   cd_estabelecimento: query.cd_estabelecimento,
        //   nr_controle: query.nr_controle,
        //   instance: query.instance,
        //   tp_funcionalidade: query.tp_funcionalidade,
        // };

        // logger.debug('req.query at line 719 in models/WhatsAppModel.ts:' + JSON.stringify(req.query));
        // respMensagens = await FluxoMensagensDB.listarFluxoAtendimentosEst(req.query);
        // logger.debug('respMensagens at line 721 in models/WhatsAppModel.ts:' + JSON.stringify(respMensagens));

        //percorre a mensagem localizada, pois pode er mais de uma
        for (const mensagem of respMensagens.data) {
          //console.log('mensagem at line 732 in models/WhatsAppModel.ts:', mensagem);
          //console.log('mensagem at line 86 in fluxos/FluxoAtendimentoModel.ts:', mensagem);

          if (
            query.cd_atendimento != undefined &&
            mensagem.tp_funcionalidade &&
            mensagem.tp_funcionalidade != '{}' &&
            mensagem.tp_funcionalidade != ''
          ) {
            const reqObj = {
              cd_pedido: query.cd_pedido,
              cd_atendimento: query.cd_atendimento,
              tp_status_atendimento: mensagem.tp_funcionalidade,
              cd_estabelecimento: query.cd_estabelecimento,
              tp_status_pedido: query.tp_status_pedido,
              ds_evento: mensagem.ds_evento,
            };

            const resp = await AtendimentosDB.atualizaStatusAtendimento(reqObj);
            if (resp.statuscode == 200) {
              in_acessoulink = resp.data[0].acessouLink;
            }
            // console.log('resp at line 329 in models/WhatsAppModel.ts:', resp);
          }

          if (query.cd_atendimento != undefined && mensagem.in_finalizaatendimento) {
            const reqObj = {
              cd_atendimento: query.cd_atendimento,
              cd_estabelecimento: query.cd_estabelecimento,
              tp_status_atendimento: 'Atendimento Finalizado',
              in_finalizaatendimento: mensagem.in_finalizaatendimento,
              ds_comentario: 'Atendimento finalizado pela configuração do fluxo de mensagens in_finalizaatendimento',
            };

            const resp = await AtendimentosDB.atualizaStatusAtendimento(reqObj);
            if (resp.statuscode == 200) {
              in_acessoulink = resp.data[0].acessouLink;
            }
            // console.log('resp at line 329 in models/WhatsAppModel.ts:', resp);
          }

          const dadosReplace = {
            nome: query.nome,
            contato: query.ds_contato,
            nm_estabelecimento: mensagem.nm_estabelecimento,
            ds_hostentregadochef: mensagem.ds_hostentregadochef,
            telefone: query.telefone,
            hash: query.hash,
            endereco_empresa: mensagem?.ds_endereco,
            endereco: query?.ds_endereco,
            numero: query?.nr_endereco,
            complemento: query?.ds_complemento,
            bairro: query?.ds_bairro,
            pedido_aberto: query?.pedido_aberto,
            pedidos: query.pedidos,
          };
          //console.log('dadosReplace at line 93 in models/WhatsAppModel.ts:', dadosReplace);

          let message = Funcoes.replaceData(mensagem.ds_mensagem, dadosReplace);
          // console.log('message at line 106 in models/WhatsAppModel.ts:', message);

          if (mensagem.tp_funcionalidade == 'Inicio') {
            //Mensagens Relacionadas que não são Lista de Opções
            await this.verificaCliente({
              mensagem: respMensagens.data,
              msgRelacionada: mensagem.msg_relacionadas,
              query,
              dadosReplace,
            });
          }

          if (mensagem.in_tipolista) {
            let reqObj = {
              telefone: query.telefone,
              nome: query?.nome,
              instance: query.instance,
              mensagem: message,
              title: Funcoes.replaceData(mensagem.ds_mensagem, dadosReplace),
              timer: mensagem.hr_tempo,
              msg_relacionadas: mensagem.msg_relacionadas,
              ds_titulobotao: mensagem.ds_titulobotao,
              cd_estabelecimento: query.cd_estabelecimento,
              cd_atendimento: query.cd_atendimento,
              ds_evento: mensagem.ds_evento,
              status_pedido: query?.status_pedido,
              pedidos: query?.pedidos,
              ds_foto: query?.ds_foto,
              profile_picture_base64: query?.profile_picture_base64,
              in_acessoulink: in_acessoulink,
              tp_etapachat: query?.tp_etapachat,
            };

            //if (process.env.VERSAO_EVOLUTION == 'V1') {
            await this.mensagemLista(reqObj);
            // } else {
            //   await this.mensagemOpcoes(reqObj);
            // }
          }

          if (mensagem.imagens != null) {
            let reqObj = {
              telefone: query.telefone,
              nome: query?.nome,
              instance: query.instance,
              mensagem: message,
              timer: mensagem.hr_tempo,
              imagens: mensagem.imagens,
              in_adicionatexto_imagem: mensagem.in_adicionatexto_imagem,
              cd_estabelecimento: query.cd_estabelecimento,
              cd_atendimento: query.cd_atendimento,
              ds_evento: mensagem.ds_evento,
              status_pedido: query?.status_pedido,
              pedidos: query?.pedidos,
              ds_foto: query?.ds_foto,
              profile_picture_base64: query?.profile_picture_base64,
              in_acessoulink: in_acessoulink,
              tp_etapachat: query?.tp_etapachat,
            };

            await this.mensagemImagem(reqObj);
          } else if (mensagem.in_tipolista == false) {
            if (mensagem.tp_funcionalidade == 'Endereço') {
              let reqObj = {
                telefone: query.telefone,
                nome: query?.nome,
                instance: query.instance,
                mensagem: message,
                estabelecimento: mensagem.nm_estabelecimento,
                address: mensagem.ds_endereco,
                latitude: mensagem.nr_latitude,
                longitude: mensagem.nr_longitude,
                timer: mensagem.hr_tempo,
                cd_estabelecimento: query.cd_estabelecimento,
                cd_atendimento: query.cd_atendimento,
                ds_evento: mensagem.ds_evento,
                status_pedido: query?.status_pedido,
                pedidos: query?.pedidos,
                ds_foto: query?.ds_foto,
                profile_picture_base64: query?.profile_picture_base64,
                in_acessoulink: in_acessoulink,
                tp_etapachat: query?.tp_etapachat,
              };
              await this.mensagemLocation(reqObj);
            } else if (mensagem.tp_funcionalidade == 'Comprovante de Pedido') {
              let reqObj = {
                telefone: query.telefone,
                nome: query?.nome,
                instance: query.instance,
                mensagem: mensagem.ds_mensagem,
                estabelecimento: mensagem.nm_estabelecimento,
                address: mensagem.ds_endereco,
                latitude: mensagem.nr_latitude,
                longitude: mensagem.nr_longitude,
                timer: mensagem.hr_tempo,
                pedido: query.pedido,
                cd_estabelecimento: query.cd_estabelecimento,
                cd_atendimento: query.cd_atendimento,
                ds_evento: mensagem.ds_evento,
                status_pedido: query?.status_pedido,
                pedidos: query?.pedidos,
                ds_foto: query?.ds_foto,
                profile_picture_base64: query?.profile_picture_base64,
                in_acessoulink: in_acessoulink,
                tp_etapachat: query?.tp_etapachat,
              };
              await this.comprovantePedido(reqObj);
            } else {
              if (message != '') {
                let reqObj = {
                  telefone: query.telefone,
                  nome: query?.nome,
                  instance: query.instance,
                  mensagem: message,
                  timer: mensagem.hr_tempo,
                  cd_estabelecimento: query.cd_estabelecimento,
                  cd_atendimento: query.cd_atendimento,
                  tp_funcionalidade: mensagem.tp_funcionalidade,
                  ds_evento: mensagem.ds_evento,
                  status_pedido: query?.status_pedido,
                  pedidos: query?.pedidos,
                  ds_foto: query?.ds_foto,
                  profile_picture_base64: query?.profile_picture_base64,
                  in_acessoulink: in_acessoulink,
                  tp_etapachat: query?.tp_etapachat,
                };
                await this.mensagemTexto(reqObj);
              }
            }
          }

          if (mensagem.msg_relacionadas != null) {
            if (mensagem.tp_funcionalidade == 'Consultar Pedido') {
              await this.consultarPedido({
                mensagem: mensagem.msg_relacionadas,
                query,
                dadosReplace,
              });
              return respMensagens;
            }

            for (const msgrel of mensagem.msg_relacionadas) {
              //console.log('msgrel at line 899 in models/WhatsAppModel.ts:', msgrel);
              if (
                mensagem.in_tipolista == false ||
                (mensagem.in_tipolista == true && msgrel.in_opcoes == false && msgrel.tp_funcionalidade == null)
              ) {
                message = Funcoes.replaceData(msgrel.ds_mensagem, dadosReplace);
                if (msgrel.imagens != null) {
                  let reqObj = {
                    telefone: query.telefone,
                    nome: query?.nome,
                    instance: query.instance,
                    mensagem: message,
                    timer: msgrel.hr_tempo,
                    imagens: msgrel.imagens,
                    in_adicionatexto_imagem: msgrel.in_adicionatexto_imagem,
                    cd_estabelecimento: query.cd_estabelecimento,
                    cd_atendimento: query.cd_atendimento,
                    ds_evento: mensagem.ds_evento,
                    status_pedido: query?.status_pedido,
                    pedidos: query?.pedidos,
                    ds_foto: query?.ds_foto,
                    profile_picture_base64: query?.profile_picture_base64,
                    in_acessoulink: in_acessoulink,
                    tp_etapachat: query?.tp_etapachat,
                  };

                  await this.mensagemImagem(reqObj);
                } else if (msgrel.in_tipolista == false) {
                  if (msgrel.tp_funcionalidade == 'Endereço') {
                    let reqObj = {
                      telefone: query.telefone,
                      nome: query?.nome,
                      instance: query.instance,
                      mensagem: message,
                      estabelecimento: msgrel.nm_estabelecimento,
                      address: msgrel.ds_endereco,
                      latitude: msgrel.nr_latitude,
                      longitude: msgrel.nr_longitude,
                      timer: msgrel.hr_tempo,
                      cd_estabelecimento: query.cd_estabelecimento,
                      cd_atendimento: query.cd_atendimento,
                      ds_evento: mensagem.ds_evento,
                      status_pedido: query?.status_pedido,
                      pedidos: query?.pedidos,
                      ds_foto: query?.ds_foto,
                      profile_picture_base64: query?.profile_picture_base64,
                      in_acessoulink: in_acessoulink,
                      tp_etapachat: query?.tp_etapachat,
                    };
                    await this.mensagemLocation(reqObj);
                  } else if (msgrel.tp_funcionalidade == 'Comprovante de Pedido') {
                    let reqObj = {
                      telefone: query.telefone,
                      nome: query?.nome,
                      instance: query.instance,
                      mensagem: msgrel.ds_mensagem,
                      estabelecimento: mensagem.nm_estabelecimento,
                      address: mensagem.ds_endereco,
                      latitude: mensagem.nr_latitude,
                      longitude: mensagem.nr_longitude,
                      timer: mensagem.hr_tempo,
                      pedido: query.pedido,
                      cd_estabelecimento: query.cd_estabelecimento,
                      cd_atendimento: query.cd_atendimento,
                      ds_evento: mensagem.ds_evento,
                      status_pedido: query?.status_pedido,
                      pedidos: query?.pedidos,
                      ds_foto: query?.ds_foto,
                      profile_picture_base64: query?.profile_picture_base64,
                      in_acessoulink: in_acessoulink,
                      tp_etapachat: query?.tp_etapachat,
                    };
                    await this.comprovantePedido(reqObj);
                  } else {
                    let reqObj = {
                      telefone: query.telefone,
                      nome: query?.nome,
                      instance: query.instance,
                      mensagem: message,
                      timer: msgrel.hr_tempo,
                      cd_estabelecimento: query.cd_estabelecimento,
                      cd_atendimento: query.cd_atendimento,
                      ds_evento: mensagem.ds_evento,
                      status_pedido: query?.status_pedido,
                      pedidos: query?.pedidos,
                      ds_foto: query?.ds_foto,
                      profile_picture_base64: query?.profile_picture_base64,
                      in_acessoulink: in_acessoulink,
                      tp_etapachat: query?.tp_etapachat,
                    };
                    await this.mensagemTexto(reqObj);
                  }
                }
              }
            }
          }

          /* ESTOU COMENTANDO AQUI PARA TENTAR AJUSTAR O FLUXO

            if (mensagem.tp_funcionalidade == 'Pedido Finalizado') {
              message = await this.montaComprovanteVenda(query.pedido);
              // console.log('message at line 72 in models/WhatsAppModel.ts:', message);
              idunico = Funcoes.gerarHashUnica(query.telefone, 20);
              dataMessage = {
                telefone: query.telefone,
                instance: query.instance,
                mensagem: message,
                timer: mensagem.hr_tempo,
                in_rabbitmq: true,
                // nr_hash: mensagem.nr_hash,
                type: MessageType.Message,
                idunico: idunico,
                linkpreview: true,
              };
              //console.log('dataMessage at line 158 in models/WhatsAppModel.ts:', dataMessage);
              respSendMSG = await this.sendMessage(dataMessage);
              // console.log('respSendMSG at line 118 in fluxos/FluxoAtendimentoModel.ts:', respSendMSG);
            }
          }*/
        }
      }
      respMensagens = respFalarAtendente != undefined ? respFalarAtendente : respMensagens;
      return respMensagens;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async sendMessage(objeto: any): Promise<IRetorno> {
    // console.log('🚀 ~ WhatsAppModel.ts:1225 ~ sendMessage ~ objeto:', objeto);
    // logger.debug('objeto at line 1141 in models/WhatsAppModel.ts:' + JSON.stringify(objeto));
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      //console.log('sendMessage', objeto);
      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }
      const message: ISendMessage = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        type: MessageType.Message,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        linkpreview: objeto.linkpreview,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        tp_funcionalidade: objeto.tp_funcionalidade,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
        cd_atendente: objeto?.dataCRM?.cd_atendente,
        tp_status_crm: objeto?.dataCRM?.tp_status,
      };

      //console.log('message at line 1270 in models/WhatsAppModel.ts:', message);
      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //sendMessageRabbitMQ.connect();

        try {
          // logger.debug(
          //   'publishToQueue Message > message at line 1100 in models/WhatsAppModel.ts:' + JSON.stringify(message),
          // );
          // Chama o método publishToQueue para enviar a mensagem à fila
          // console.log('MENSAGEM ENVIADA PARA FILA:', message);
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          //logger.debug(`Message sent to queue oizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`Error Message in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          respMessage = {
            statuscode: 200,
            message: 'Mensagem enviada com sucesso!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: objeto.idunico,
                timestamp: objeto.message_timestamp,
                status: 'Fila',
              },
            ],
          };
        }
        return respMessage;
      } else {
        let result = await WhatsAppService.sendMessage(message);
        // console.log('result at line 1311 in models/WhatsAppModel.ts:', result);

        const dataMessage: any = message;
        dataMessage.dadosSend = result;
        // console.log('dataMessage at line 1324 in models/WhatsAppModel.ts:', dataMessage)
        const respMessage = await new MessagesModel().incluiMessages(messageInput);
        // console.log('respMessage at line 1309 in models/WhatsAppModel.ts:', respMessage);

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: respMessage.statuscode,
            message: respMessage.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: respMessage.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: respMessage.statuscode != 200 ? respMessage.message : undefined,
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendMessage:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendImage(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }

      const message: ISendImage = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        type: MessageType.Image,
        url_midia: objeto.url_midia,
        url_midiacompleta: objeto.url_midiacompleta,
        ds_base64: objeto.base64,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //console.log('respMessage at line 102 in models/WhatsAppModel.ts:', respMessage);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //await sendMessageRabbitMQ.connect();

        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`Image sent to queue oizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`Error Image in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        //console.log('respMessage at line 1280 in models/WhatsAppModel.ts:', respMessage);
        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          respMessage = {
            statuscode: 200,
            message: 'Mensagem enviada com sucesso!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: objeto.idunico,
                timestamp: objeto.message_timestamp,
                status: 'Fila',
              },
            ],
          };
        }

        return respMessage;
      } else {
        result = await WhatsAppService.sendImage(message);
        // console.log('result at line 100 in models/WhatsAppModel.ts:', result);

        const dataMessage: IMessage = message;
        dataMessage.dadosSend = result;
        if (result.statuscode == 200) {
          dataMessage.url_midia = result.data[0].url_midia;
          dataMessage.ds_base64 = objeto.base64;
          dataMessage.file_length = result.data[0].file_length;
          dataMessage.file_sha256 = result.data[0].file_sha256;
          dataMessage.media_key = result.data[0].media_key;
          dataMessage.nr_height = result.data[0].nr_height;
          dataMessage.nr_width = result.data[0].nr_width;
          dataMessage.mimetype = result.data[0].mimetype;
          dataMessage.message_timestamp = result.data[0].timestamp;
          dataMessage.horario = result.data[0].time;
        }

        const respMessage = await new MessagesModel().incluiMessages(messageInput);

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: respMessage.statuscode,
            message: respMessage.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: respMessage.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: respMessage.statuscode != 200 ? respMessage.message : undefined,
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendMessage:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendDocument(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }

      const message: ISendDocument = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        type: MessageType.Document,
        mediatype: objeto.mediatype,
        fileName: objeto.fileName,
        caption: objeto.caption,
        url_midia: objeto.url_midia,
        url_midiacompleta: objeto.url_midiacompleta,
        ds_base64: objeto.base64,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //console.log('respMessage at line 102 in models/WhatsAppModel.ts:', respMessage);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //await sendMessageRabbitMQ.connect();

        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`Document sent to queueoizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`Error Document in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        //console.log('respMessage at line 1280 in models/WhatsAppModel.ts:', respMessage);
        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          respMessage = {
            statuscode: 200,
            message: 'Mensagem enviada com sucesso!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: objeto.idunico,
                timestamp: objeto.message_timestamp,
                status: 'Fila',
              },
            ],
          };
        }
        return respMessage;
      } else {
        result = await WhatsAppService.sendDocument(message);
        // console.log('result at line 100 in models/WhatsAppModel.ts:', result);

        const dataMessage: IMessage = message;
        dataMessage.dadosSend = result;
        if (result.statuscode == 200) {
          dataMessage.url_midia = result.data[0].url_midia;
          dataMessage.file_length = result.data[0].file_length;
          dataMessage.file_sha256 = result.data[0].file_sha256;
          dataMessage.media_key = result.data[0].media_key;
          dataMessage.nr_height = result.data[0].nr_height;
          dataMessage.nr_width = result.data[0].nr_width;
          dataMessage.mimetype = result.data[0].mimetype;
          dataMessage.message_timestamp = result.data[0].timestamp;
          dataMessage.horario = result.data[0].time;
        }

        const respMessage = await new MessagesModel().incluiMessages(messageInput);

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: respMessage.statuscode,
            message: respMessage.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: respMessage.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: respMessage.statuscode != 200 ? respMessage.message : undefined,
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendMessage:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendAudio(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }

      const message: ISendAudio = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        type: MessageType.Audio,
        url_midia: objeto.url_midia,
        url_midiacompleta: objeto.url_midiacompleta,
        ds_base64: objeto.base64,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //console.log('respMessage at line 102 in models/WhatsAppModel.ts:', respMessage);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        // await sendMessageRabbitMQ.connect();
        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`Audio sent to queueoizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`Error Audio in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: respMessage.statuscode,
            message: respMessage.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: respMessage.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: respMessage.statuscode != 200 ? respMessage.message : undefined,
              },
            ],
          };
        }

        return respMessage;
      } else {
        result = await WhatsAppService.sendAudio(message);
        // console.log('result at line 100 in models/WhatsAppModel.ts:', result);

        const dataMessage: IMessage = message;
        dataMessage.dadosSend = result;
        if (result.statuscode == 200) {
          dataMessage.url_midia = result.data[0].url_midia;
          dataMessage.file_length = result.data[0].file_length;
          dataMessage.file_sha256 = result.data[0].file_sha256;
          dataMessage.media_key = result.data[0].media_key;
          dataMessage.nr_height = result.data[0].nr_height;
          dataMessage.nr_width = result.data[0].nr_width;
          dataMessage.mimetype = result.data[0].mimetype;
          dataMessage.message_timestamp = result.data[0].timestamp;
          dataMessage.horario = result.data[0].time;
        }

        const respMessage = await new MessagesModel().incluiMessages(messageInput);
        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: result.statuscode,
            message: result.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: result.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: result.statuscode != 200 ? result.message : undefined,
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendMessage:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendList(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      //console.log('sendMessage', objeto);
      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }
      for (const section of objeto.sections) {
        if (section.title == undefined) {
          section.title = '';
        }
      }

      const message: ISendList = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        title: objeto.title,
        description: objeto.description,
        buttonText: objeto.buttonText,
        footerText: objeto.footerText,
        sections: objeto.sections,
        type: MessageType.List,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        opcoes: objeto.opcoes,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        result = await new MessagesModel().incluiMessages(messageInput);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //await sendMessageRabbitMQ.connect();
        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          logger.debug(
            'publishToQueue List > message at line 1442 in models/WhatsAppModel.ts:' + JSON.stringify(message),
          );
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`List sent to queueoizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`List Document in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = result.data[0];
          result = {
            statuscode: result.statuscode,
            message: result.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: result.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: result.statuscode != 200 ? result.message : undefined,
              },
            ],
          };
        }
        return result;
      } else {
        result = await WhatsAppService.sendList(message);
        //console.log('result at line 200 in models/WhatsAppModel.ts:', result);

        const dataMessage: IMessage = message;
        dataMessage.dadosSend = result;
        if (result.statuscode == 200) {
          dataMessage.title = result.data[0].title;
          dataMessage.description = result.data[0].description;
          dataMessage.buttonText = result.data[0].buttonText;
          dataMessage.listType = result.data[0].listType;
          dataMessage.message_timestamp = result.data[0].timestamp;
          dataMessage.horario = result.data[0].time;
        }

        const respMessage = await new MessagesModel().incluiMessages(messageInput);

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: 200,
            message: 'Mensagem enviada com sucesso!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: 'Enviada',
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendList:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendButton(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      //console.log('sendMessage', objeto);
      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }
      for (const section of objeto.sections) {
        if (section.title == undefined) {
          section.title = '';
        }
      }

      const message: ISendButton = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        title: objeto.title,
        description: objeto.description,
        footer: objeto.footer,
        buttons: objeto.buttons,
        type: MessageType.Button,
        idunico: objeto.idunico,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //await sendMessageRabbitMQ.connect();
        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`List sent to queueoizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`List Document in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }
        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: result.statuscode,
            message: result.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: result.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: result.statuscode != 200 ? result.message : undefined,
              },
            ],
          };
        }
        return respMessage;
      } else {
        result = await WhatsAppService.sendButton(message);
        console.log('result at line 200 in models/WhatsAppModel > sendButton:', result);
        console.log('TRATAR O RETORNO PRARA GRAVAR sendButton');
        /* const dataMessage: IMessage = message;
        dataMessage.dadosSend = result;
        if (result.statuscode == 200) {
          dataMessage.title = result.data[0].title;
          dataMessage.description = result.data[0].description;
          dataMessage.footer = result.data[0].footer;
          dataMessage.listType = result.data[0].listType;
          dataMessage.message_timestamp = result.data[0].timestamp;
          dataMessage.horario = result.data[0].time;
        }

        const respMessage = await new MessagesModel().incluiMessages(messageInput);*/

        return result;
      }
    } catch (error) {
      console.error('Error in sendButton:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }

  async sendLocation(objeto: any): Promise<IRetorno> {
    try {
      if (objeto.tempo != undefined) {
        const tempoEmMilissegundos = Funcoes.converterTempoParaMilissegundos(objeto.tempo);
        objeto.timer = tempoEmMilissegundos;
      } else {
        objeto.timer = 1200;
      }

      //console.log('sendMessage', objeto);
      if (objeto.idunico == undefined) {
        objeto.idunico = Funcoes.gerarHashUnica(objeto.telefone, 20);
      }
      const message: ISendLocation = {
        telefone: objeto.telefone,
        nome: objeto?.nome,
        timer: objeto.timer,
        mensagem: objeto.mensagem,
        instance: objeto.instance,
        idunico: objeto.idunico,
        type: MessageType.Location,
        event: 'messages.upsert',
        message_timestamp: Funcoes.dateTimestamp('datetime'),
        horario: Funcoes.dateTimestamp('time'),
        in_rabbitmq: objeto.in_rabbitmq,
        estabelecimento: objeto.estabelecimento,
        address: objeto.address ? objeto.address : objeto.endereco,
        latitude: objeto.latitude,
        longitude: objeto.longitude,
        origem: objeto.origem,
        cd_estabelecimento: objeto.cd_estabelecimento,
        cd_atendimento: objeto.cd_atendimento,
        ds_evento: objeto.ds_evento,
        status_pedido: objeto.status_pedido,
        pedidos: objeto.pedidos,
        ds_foto: objeto.ds_foto,
        profile_picture_base64: objeto?.profile_picture_base64,
        in_acessoulink: objeto?.in_acessoulink,
        tp_etapachat: objeto?.tp_etapachat,
        in_enviointerno: objeto?.in_enviointerno == undefined ? false : objeto.in_enviointerno,
      };

      //quando utiliza o metodo incluiMessages para salvar a msg que
      //estamos recebendo do cliente, vem dentro do data:[]
      const messageInput = {
        data: [message],
      };
      let result: any;
      if (objeto.in_rabbitmq == undefined || objeto.in_rabbitmq === true) {
        let respMessage = await new MessagesModel().incluiMessages(messageInput);
        //const sendMessageRabbitMQ = new SendMessageOiZap(objeto.instance, message);
        //  await sendMessageRabbitMQ.connect();
        try {
          // Chama o método publishToQueue para enviar a mensagem à fila
          await publishToQueue('oizap', objeto.instance, 'oizap.sendmessage', message);
          logger.debug(`Location sent to queueoizap.sendmessage on server oizap, instance ${objeto.instance}`);
        } catch (error: any) {
          logger.error(`Error Location in models/WhatsAppModel.ts: ${JSON.stringify(error.message)}`);
        }

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: respMessage.statuscode,
            message: respMessage.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: respMessage.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: respMessage.statuscode != 200 ? respMessage.message : undefined,
              },
            ],
          };
        }
        return result;
      } else {
        result = await WhatsAppService.sendLocation(message);

        const dataMessage: any = message;
        dataMessage.dadosSend = result;
        const respMessage = await new MessagesModel().incluiMessages(messageInput);

        if (!objeto?.in_enviointerno || objeto?.in_enviointerno == false) {
          const data = respMessage.data[0];
          result = {
            statuscode: result.statuscode,
            message: result.statuscode == 200 ? 'Mensagem enviada com sucesso!' : 'Erro ao enviar mensagem!',
            data: [
              {
                telefone: objeto.telefone,
                instance: objeto.instance,
                idunico: data.idunico,
                timestamp: objeto.message_timestamp,
                status: result.statuscode == 200 ? 'Enviada' : 'Erro ao enviar mensagem',
                erro: result.statuscode != 200 ? result.message : undefined,
              },
            ],
          };
        }
        return result;
      }
    } catch (error) {
      console.error('Error in sendLocation:', error);
      //throw error; // Propagate the error to the caller if needed
      return erroInterno(error);
    }
  }
}
