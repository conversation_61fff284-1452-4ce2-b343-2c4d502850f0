<template>
    <ShowLoading ref="loading" />

    <div class="p-4">
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-4 text-white">
            <div class="flex flex-col md:flex-row md:items-center">
                <!-- Grupo: Ícone + Texto + Estatísticas -->
                <div class="flex items-center space-x-6 mb-4 md:mb-0">
                    <!-- Í<PERSON><PERSON> e Texto -->
                    <div class="flex items-center space-x-3">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                            <Building2Icon
                                class="w-6 h-6"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                            />
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Lista de Instancias</h3>
                            <p class="text-blue-100 text-sm">Instâncias contratadas para o estabelecimento.</p>
                        </div>
                    </div>

                    <!-- Cards de estatística -->
                    <div class="flex space-x-4">
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-2 text-center cursor-pointer"
                            @click="selecionaStatus(0)"
                        >
                            <div class="text-2xl font-bold">{{ estatisticasConexao.totalinstancia }}</div>
                            <div class="text-xs text-blue-100">Total Instâncias</div>
                        </div>
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-2 text-center cursor-pointer"
                            @click="
                                () => {
                                    selecionaStatus(1);
                                }
                            "
                        >
                            <div class="text-2xl font-bold">{{ estatisticasConexao.conectado }}</div>
                            <div class="text-xs text-blue-100">Conectadas</div>
                        </div>
                        <div
                            class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-2 text-center cursor-pointer"
                            @click="
                                () => {
                                    selecionaStatus(2);
                                }
                            "
                        >
                            <div class="text-2xl font-bold text-red-500">{{ estatisticasConexao.desconectado }}</div>
                            <div class="text-xs text-blue-100">Desconectadas</div>
                        </div>
                    </div>
                </div>

                <!-- Botão à direita -->
                <div class="ml-auto">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="viewInstancia(null, 'nova')"
                    >
                        <PlusIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Nova Instância</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <div class="box p-4">
                <!-- <div class="flex items-center gap-2">
                    <button
                        :class="`btn btn-sm ${filtroStatus == 1 ? 'btn-success text-white' : 'btn-success-soft'}`"
                        @click="
                            () => {
                                selecionaStatus(1);
                            }
                        "
                    >
                        Conectadas
                    </button>
                    <button
                        :class="`btn btn-sm ${filtroStatus == 2 ? 'btn-danger' : 'btn-danger-soft'}`"
                        @click="
                            () => {
                                selecionaStatus(2);
                            }
                        "
                    >
                        Desconectadas
                    </button>
                </div> -->

                <div
                    v-if="listaInstanciasFiltradas.length"
                    class="flex justify-start items-center flex-wrap gap-4 pb-4"
                >
                    <div v-for="instancia in listaInstanciasFiltradas" :key="instancia" class="intro-x flex flex-col">
                        <div
                            class="border border-slate-200 dark:border-darkmode-300 rounded-lg shadow-sm p-4 w-full max-w-[380px] grid grid-cols-12 gap-2"
                        >
                            <div
                                class="flex justify-between items-center col-span-12 pb-4 border-b border-slate-200 dark:border-darkmode-300"
                            >
                                <div class="flex flex-col whitespace-nowrap -mt-0.5">
                                    <h2 class="text-lg font-medium whitespace-nowrap">
                                        {{ instancia.nome }}
                                    </h2>

                                    <div class="flex items-center gap-2">
                                        <span class="text-xs text-slate-400">
                                            {{ converters.formatarTelefone(instancia.telefone) }}
                                        </span>
                                        <Tippy tag="div" content="Copiar telefone" v-show="instancia.telefone != null">
                                            <CopyIcon
                                                class="w-4 h-4 cursor-pointer text-slate-400 hover:text-slate-600"
                                                @click="copyTelefone(instancia.telefone)"
                                            />
                                        </Tippy>
                                        <span class="text-xs text-slate-400" v-show="instancia.telefone == null">
                                            .
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div
                                        class="text-center text-white px-2 py-1 rounded-md"
                                        :class="{
                                            'bg-success/70': instancia.status_connection == 'Conectado',
                                            'bg-warning/70': instancia.status_connection == 'Conectando',
                                            'bg-danger/70': instancia.status_connection != 'Conectado',
                                        }"
                                    >
                                        {{ instancia.status_connection }}
                                    </div>
                                    <Dropdown>
                                        <DropdownToggle tag="a" class="w-5 h-5 block -mr-2" href="javascript:;">
                                            <MoreVerticalIcon class="w-5 h-5 text-slate-500" />
                                        </DropdownToggle>
                                        <DropdownMenu class="w-max">
                                            <DropdownContent>
                                                <DropdownItem @click="viewRelMensagens(instancia.id_instancia)">
                                                    <ClipboardListIcon class="w-4 h-4 mr-2" />
                                                    Relatório de Mensagens
                                                </DropdownItem>
                                                <DropdownDivider />
                                                <DropdownItem @click="viewInstancia(instancia.id_instancia, 'editar')">
                                                    <SquarePenIcon class="w-4 h-4 mr-2" />
                                                    Editar
                                                </DropdownItem>
                                                <!-- <DropdownItem
                                            tag="buttom"
                                            :class="{
                                                'dropdown-item-disbled': instancia.status_connection == 'Conectando',
                                            }"
                                            @click="abrirModalConexao(instancia)"
                                        >
                                            <PlugIcon class="w-4 h-4 mr-2" />
                                            {{
                                                instancia.status_connection == 'Conectado'
                                                    ? 'Desconectar'
                                                    : instancia.status_connection == 'Conectando'
                                                    ? 'Aguarde'
                                                    : 'Conectar'
                                            }}
                                        </DropdownItem> -->
                                                <DropdownItem @click="abreModalExcluir(instancia)">
                                                    <Trash2Icon class="w-4 h-4 mr-2" />
                                                    Excluir
                                                </DropdownItem>
                                                <DropdownDivider />
                                                <!-- <DropdownItem>
                                                <DollarSignIcon class="w-4 h-4 mr-2" />
                                                Pagamento
                                            </DropdownItem> -->
                                            </DropdownContent>
                                        </DropdownMenu>
                                    </Dropdown>
                                </div>
                            </div>

                            <div class="col-span-12" v-show="modulos.in_api">
                                <div class="flex items-center gap-1 pb-1">
                                    <ChartNoAxesColumnIcon class="w-4 h-4" /> Estatísticas de mensagens
                                </div>
                                <div class="grid grid-cols-12 gap-2">
                                    <Tippy
                                        tag="div"
                                        class="col-span-4 relative cursor-pointer"
                                        content="Clique aqui para ver as mensagens enviadas"
                                    >
                                        <div
                                            class="col-span-4 border rounded bg-slate-50 dark:bg-darkmode-400 flex flex-col items-center"
                                            @click="viewRelMensagens(instancia.id_instancia, 'Enviado')"
                                        >
                                            <div class="text-xs text-slate-600 dark:text-slate-300">Enviadas hoje</div>
                                            <div class="text-md font-semibold text-success">
                                                {{
                                                    converters.formataQuantidade(
                                                        instancia?.estatisticas.total_enviado
                                                            ? instancia?.estatisticas.total_enviado
                                                            : 0
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </Tippy>
                                    <Tippy
                                        tag="div"
                                        class="col-span-4 relative cursor-pointer"
                                        content="Clique aqui para ver as mensagens na fila de envio"
                                    >
                                        <div
                                            class="col-span-4 border rounded bg-slate-50 dark:bg-darkmode-400 flex flex-col items-center"
                                            @click="viewRelMensagens(instancia.id_instancia, 'Fila')"
                                        >
                                            <div class="text-xs text-slate-600 dark:text-slate-300">Na fila</div>
                                            <div class="text-md font-semibold text-warning">
                                                {{
                                                    converters.formataQuantidade(
                                                        instancia?.estatisticas.total_nao_enviado
                                                            ? instancia?.estatisticas.total_nao_enviado
                                                            : 0
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </Tippy>
                                    <Tippy
                                        tag="div"
                                        class="col-span-4 relative cursor-pointer"
                                        content="Clique aqui para ver as mensagens não enviadas"
                                    >
                                        <div
                                            class="col-span-4 border rounded bg-slate-50 dark:bg-darkmode-400 flex flex-col items-center"
                                            @click="viewRelMensagens(instancia.id_instancia, 'Erro')"
                                        >
                                            <div class="text-xs text-slate-600 dark:text-slate-300">Não enviadas</div>
                                            <div class="text-md font-semibold text-danger">
                                                {{
                                                    converters.formataQuantidade(
                                                        instancia?.estatisticas.total_erro
                                                            ? instancia?.estatisticas.total_erro
                                                            : 0
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </Tippy>
                                </div>
                            </div>

                            <div class="col-span-12 pb-2">
                                <div class="flex items-center gap-1 pb-1">
                                    <KeyIcon class="w-4 h-4" />
                                    ID da Instância
                                </div>
                                <Tippy tag="div" class="w-full relative cursor-pointer" content="ID">
                                    <input
                                        ref="inputID"
                                        :type="!instancia.showID ? 'password' : 'text'"
                                        class="form-control"
                                        :value="instancia.nameinstance"
                                        disabled
                                    />
                                    <CopyIcon
                                        class="absolute right-2 top-0 bottom-0 my-auto w-4 h-4"
                                        @click="() => copyID(instancia.nameinstance)"
                                    />
                                    <EyeIcon
                                        class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                        v-if="instancia.showID"
                                        @click="instancia.showID = !instancia.showID"
                                    />
                                    <EyeOffIcon
                                        class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                        v-else
                                        @click="instancia.showID = !instancia.showID"
                                    />
                                </Tippy>
                            </div>

                            <div class="col-span-12">
                                <div class="flex justify-between items-end pb-1">
                                    <div class="flex items-center gap-1">
                                        <ShieldEllipsisIcon class="w-4 h-4" />
                                        Token de Integração
                                    </div>
                                    <button
                                        v-show="modulos.in_api"
                                        type="button"
                                        class="text-sm hover:underline text-slate-600 dark:text-slate-400 hidden md:flex items-center gap-1"
                                        @click="modalNovoToken = true"
                                    >
                                        <RotateCcwIcon class="w-3 h-3" /> Gerar novo Token
                                    </button>
                                </div>

                                <Tippy tag="div" class="w-full relative cursor-pointer" content="Token">
                                    <input
                                        ref="inputToken"
                                        :type="!instancia.showToken ? 'password' : 'text'"
                                        class="form-control"
                                        :value="instancia.nr_hash"
                                        disabled
                                    />
                                    <CopyIcon
                                        class="absolute right-2 top-0 bottom-0 my-auto w-4 h-4"
                                        @click="() => copyHash(instancia.nr_hash)"
                                    />
                                    <EyeIcon
                                        class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                        v-if="instancia.showToken"
                                        @click="instancia.showToken = !instancia.showToken"
                                    />
                                    <EyeOffIcon
                                        class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                        v-else
                                        @click="instancia.showToken = !instancia.showToken"
                                    />
                                </Tippy>

                                <div class="col-span-12 w-full flex justify-end">
                                    <button
                                        v-show="modulos.in_api"
                                        type="button"
                                        class="text-sm hover:underline text-slate-600 dark:text-slate-400 flex md:hidden items-center gap-1 mt-2"
                                        @click="modalNovoToken = true"
                                    >
                                        <RotateCcwIcon class="w-3 h-3" /> Gerar novo Token
                                    </button>
                                </div>
                                <!-- <div
                                class="col-span-12 w-full bg-slate-100 rounded-md p-4 mt-4 flex flex-col gap-2 dark:bg-darkmode-800/60"
                            >
                                <div class="w-full flex justify-between items-center">
                                    <p class="opacity-60">Situação:</p>
                                    <p class="font-medium">---</p>
                                </div>
                                <div class="w-full flex justify-between items-center">
                                    <p class="opacity-60">Pagamento:</p>
                                    <p class="font-medium">---</p>
                                </div>
                                <div class="w-full flex justify-between items-center">
                                    <p class="opacity-60">Tipo de Pagamento:</p>
                                    <p class="font-medium">---</p>
                                </div>
                                <div class="w-full flex justify-between items-center">
                                    <p class="opacity-60">Vence em:</p>
                                    <p class="font-medium">---</p>
                                </div>
                            </div> -->
                                <div class="col-span-12 flex justify-between items-center mt-4 gap-2 w-full">
                                    <Tippy
                                        :key="instancia.nameinstance + '-' + instancia.in_stop_bot"
                                        :content="`${
                                            !instancia.in_stop_bot
                                                ? 'O seu bot está Ativado, clique aqui para Desativar!'
                                                : 'O seu bot está Desativado, clique aqui para Ativar!'
                                        }`"
                                        class="tooltip"
                                    >
                                        <button
                                            :class="`btn w-24 btn-sm  ${
                                                instancia.in_stop_bot
                                                    ? 'btn-danger-soft text-red'
                                                    : 'btn-success-soft text-green'
                                            }`"
                                            @click="onChangeBot(instancia, !instancia.in_stop_bot)"
                                        >
                                            <BotIcon
                                                v-if="!instancia.in_stop_bot"
                                                class="w-4 md:w-5 h-4 md:h-5 mr-1 md:mr-1"
                                            />
                                            <BotOffIcon v-else class="w-4 md:w-5 h-4 md:h-5 mr-1 md:mr-1" />
                                            <span class="text-ellipsis whitespace-nowrap overflow-hidden">
                                                ChatBot
                                            </span>
                                        </button>
                                    </Tippy>
                                    <Tippy
                                        :key="instancia.nameinstance"
                                        content="Clique aqui para reiniciar a Instância."
                                        class="tooltip"
                                    >
                                        <button
                                            class="btn btn-pending-soft btn-sm w-24 text-orange"
                                            @click="restartInstance(instancia)"
                                        >
                                            <RotateCcwIcon class="w-4 md:w-5 h-4 md:h-5 mr-1 md:mr-1" />
                                            <span class="text-ellipsis whitespace-nowrap overflow-hidden">
                                                Reiniciar
                                            </span>
                                        </button>
                                    </Tippy>
                                    <Tippy
                                        :key="instancia.nameinstance + '-' + instancia.status_connection"
                                        :content="`O seu whatsapp está ${
                                            instancia.status_connection
                                        } clique aqui para ${
                                            instancia.status_connection == 'Conectado'
                                                ? 'Desconectar do OiZap'
                                                : 'Conectar com o OiZap'
                                        }!`"
                                        class="tooltip"
                                    >
                                        <button
                                            type="button"
                                            :class="`btn  w-28  btn-sm  ${
                                                instancia.status_connection == 'Conectado'
                                                    ? 'btn-danger-soft text-red'
                                                    : 'btn-success-soft text-green'
                                            } `"
                                            @click="() => abrirModalConexao(instancia)"
                                        >
                                            <PlugIcon class="w-4 md:w-5 min-w-5 h-4 md:h-5 mr-1 md:mr-1" />
                                            <span class="text-ellipsis whitespace-nowrap overflow-hidden">
                                                {{
                                                    instancia.status_connection == 'Conectado'
                                                        ? 'Desconectar'
                                                        : instancia.status_connection == 'Conectando'
                                                        ? 'Aguarde'
                                                        : 'Conectar'
                                                }}
                                            </span>
                                        </button>
                                    </Tippy>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else class="w-full intro-x mx-auto flex justify-start items-center gap-2 my-2">
                    <CircleAlertIcon />
                    <h3>Nenhuma instância encontrada</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN: Modal Conexao -->
    <modalConexao ref="modalConexaoRef" @conexao="mudancaConexao" />
    <!-- END: Modal Conexao -->

    <!-- Modal  confirma novo token-->
    <Modal :show="modalNovoToken" @hidden="modalNovoToken = false">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Gerar um novo token?</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label>
                        Ao gerar um novo token, o token atual será invalidado e você precisará atualizar o token em
                        todos os lugares que o utilizam.
                    </label>
                </div>
            </div>
            <div class="text-slate-500 mt-2">Deseja mesmo gerar um novo token?</div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="modalNovoToken = false" class="btn btn-outline-secondary w-24 mr-2">
                Não
            </button>
            <button type="button" class="btn btn-danger w-24" @click="gerarNovoToken">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma novo token -->

    <!-- Modal  confirma remover-->
    <Modal :show="modalRemoveInstancia" @hidden="fechaModalRemover">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Confirmação</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label>
                        Ao remover a instância <strong> {{ instanciaSelecionada?.nome }}</strong> , você irá perder
                        todos os dados relacionados com essa instância!
                    </label>
                </div>
            </div>
            <div class="text-slate-500 mt-2">
                Deseja remover a instância
                <strong class="text-danger">{{ instanciaSelecionada?.nome }} </strong> ?
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="fechaModalRemover" class="btn btn-outline-secondary w-24 mr-2">Não</button>
            <button type="button" class="btn btn-danger w-24" @click="remover()">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma remover -->
</template>

<script setup>
    import { ref, reactive, onMounted, computed } from 'vue';
    import { useRouter } from 'vue-router';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import WhatsAppServices from '@/services/whatsapp/WhatsAppServices';
    import AtendimentoServices from '@/services/chat/AtendimentoServices';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';

    import modalConexao from './modalConexao.vue';
    import converters from '@/utils/converters';
    import { usePagina } from '@/stores/pagina';
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();
    const router = useRouter();
    let pagina = usePagina();

    const loading = ref();
    const modalConexaoRef = ref();
    let modalRemoveInstancia = ref();
    let modalNovoToken = ref(false);
    let listaInstancias = ref([]);
    let listaInstanciasFiltradas = ref([]);
    let instanciaSelecionada = ref(null);
    let listaStatus = ref([
        { label: 'Todas', value: 0 },
        { label: 'Conectadas', value: 1 },
        { label: 'Desconectadas', value: 2 },
    ]);
    let filtroStatus = ref(0);
    let codUsuario = ref();
    //let codEstabelecimento = ref();
    let estabelecimentosLiberado = ref();

    const modulos = reactive({
        in_chat_pedidos: false,
        in_api: false,
        in_crm: false,
        qt_instancias: 0,
        qt_usuarios: 0,
    });

    function chamaCadInstancia(id) {
        console.log('teste');
        if (listaInstancias.value.length >= 1) {
            toast.warning('É permitido apenas uma instância por estabelecimento!');
            return;
        }
        router.push({ name: 'cadInstancia' });
    }

    function abreModalExcluir(instancia) {
        modalRemoveInstancia.value = true;
        instanciaSelecionada.value = instancia;
    }

    function viewInstancia(id_instancia, acao) {
        // if (!modulos.in_api) {
        //     if (listaInstancias.value.length >= 1 && acao != 'editar') {
        //         toast.warning('É permitido apenas uma instância por estabelecimento!');
        //         return;
        //     }
        // }
        if (acao == 'nova') {
            if (listaInstancias.value.length >= modulos.qt_instancias) {
                toast.warning(`É permitido apenas ${modulos.qt_instancias} instância(s) por estabelecimento!`);
                return;
            }
        }
        router.push({ name: 'viewInstancia', params: { idInstancia: id_instancia } });
        //router.push({ name: 'cadInstancia', params: { idInstancia: id_instancia } });
    }

    function viewRelMensagens(id_instancia, tipo) {
        //console.log('nameinstance at line 482 in instancia/listaInstancias.vue:', nameinstance);
        router.push({ name: 'relMensagens', query: { id: id_instancia, tipo } });
    }

    function fechaModalRemover() {
        modalRemoveInstancia.value = false;
    }

    function abrirModalConexao(instancia) {
        if (instancia.status_connection == 'Conectado') {
            logoutInstance(instancia);
            return;
        }

        modalConexaoRef.value?.abrir(instancia);
    }

    async function mudancaConexao(dados_instancia) {
        // console.log('dados_instancia:', dados_instancia);
        // console.log('listaInstancias.value:', listaInstancias.value);

        // if (dados_instancia.status_connection === 'Conectado') {
        //     inDesconectado.value = false;
        // } else {
        //     inDesconectado.value = true;
        // }

        if (dados_instancia.status_connection == 'Conectado' && dados_instancia.in_stop_bot) {
            toast.confirm(
                {
                    title: 'Confirmar a ativação do ChatBot',
                    description: 'O ChatBot está desativado, deseja ativá-lo ?',
                    confirmText: 'Sim',
                    cancelText: 'Não',
                    confirmColor: 'success',
                    cancelColor: 'warning',
                },
                () => {
                    dados_instancia.in_stop_bot = !dados_instancia.in_stop_bot;
                    const findInstancia = listaInstancias.value.find(
                        (instancia) => instancia.nameinstance === dados_instancia.nameinstance
                    );
                    findInstancia.in_stop_bot = dados_instancia.in_stop_bot;
                    onChangeBot(dados_instancia, dados_instancia.in_stop_bot);
                },
                () => {
                    toast.warning('Opção cancelada!');
                }
            );
        }

        listaInstancias.value = listaInstancias.value.map((instancia) => {
            if (instancia.nameinstance === dados_instancia.nameinstance) {
                instancia.status_connection = dados_instancia.status_connection;
            }
            return instancia;
        });
        selecionaStatus(0);
    }

    async function copyTelefone(telefone) {
        if (telefone) {
            try {
                await navigator.clipboard.writeText(telefone);
                toast.success('Telefone copiado com sucesso!');
            } catch (err) {
                console.error('Falha ao copiar: ', err);
                toast.error('Erro ao copiar telefone');
            }
        }
    }

    function selecionaStatus(status) {
        if (!status) filtroStatus.value = 0;
        else {
            if (status == filtroStatus.value) {
                filtroStatus.value = 0;
            } else {
                filtroStatus.value = status;
            }
        }
        const filtros = [
            () => listaInstancias.value,
            () => listaInstancias.value.filter((it) => it.status_connection === 'Conectado'),
            () => listaInstancias.value.filter((it) => it.status_connection === 'Desconectado'),
        ];
        listaInstanciasFiltradas.value = filtros[filtroStatus.value]();
    }

    const estatisticasConexao = computed(() => {
        const stats = listaInstancias.value.reduce(
            (acc, instancia) => {
                if (instancia.status_connection === 'Conectado') {
                    acc.conectado++;
                } else {
                    acc.desconectado++;
                }
                acc.totalinstancia++;
                return acc;
            },
            { conectado: 0, desconectado: 0, total: 0, totalinstancia: 0 }
        );

        return stats;
    });

    async function carregaInstancias() {
        loading.value.show();
        const filtros = {
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            cd_usuario: parseInt(codUsuario.value),
            in_verificastatus: true,
            in_lista_instancia_usuario: true,
        };

        listaInstancias.value = [];

        const result = await InstanciaServices.listar(filtros);

        if (result.statuscode == 200) {
            listaInstancias.value = result.data.map((instancia) => {
                return {
                    ...instancia,
                    showToken: false,
                    showID: false,
                };
            });

            // console.log('listaEstabelecimentoModulos.value:', listaEstabelecimentoModulos.value);
            //toast.success('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
            listaInstancias.value = [];
        } else {
            toast.error(result.message);
            listaInstancias.value = [];
        }
        loading.value.hide();
        selecionaStatus();
    }

    async function alterar(instancia) {
        loading.value.show();
        let filtros = {
            id: instancia.id,
            in_stop_bot: instancia.in_stop_bot,
        };

        let result = await InstanciaServices.alterar(filtros);

        if (result.statuscode == 200) {
            if (!instancia.in_stop_bot) {
                toast.success('ChatBot Ativado com sucesso!');
            } else {
                toast.warning('ChatBot Parado com sucesso!');
            }

            filtros = {
                instance: instancia.instance,
                in_stop_bot: instancia.in_stop_bot,
            };

            result = await AtendimentoServices.stopBotAtendimento(filtros);
        } else if (result.statuscode == 404) {
            toast.warning(result.message);
        } else {
            toast.error(result.message);
        }
        loading.value.hide();
    }

    async function remover() {
        loading.value.show();
        modalRemoveInstancia.value = false;

        try {
            const filtro = {
                id_instancia: instanciaSelecionada.value.id_instancia,
                instance: instanciaSelecionada.value.nameinstance,
                nr_hash: instanciaSelecionada.value.nr_hash,
                cd_estabelecimento: instanciaSelecionada.value.cd_estabelecimento,
            };

            const result = await InstanciaServices.remover(filtro);

            if (result.statuscode == 200) {
                toast.success('Instância removida com sucesso!');

                carregaInstancias();
            } else if (result.statuscode == 404) {
                toast.warning(result.message);
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            toast.error('Erro ao remover instância!');
        } finally {
            loading.value.hide();
        }
    }

    const onChangeBot = async (instancia, estado) => {
        instancia.in_stop_bot = estado;
        const req = {
            id: instancia.id_instancia,
            instance: instancia.nameinstance,
            in_stop_bot: instancia.in_stop_bot,
        };
        await alterar(req);
    };

    async function copyID(inputField) {
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField);
                toast.success('ID copiado com sucesso!');
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function copyHash(inputField) {
        if (inputField) {
            try {
                // Usar a API Clipboard para copiar o valor do input
                await navigator.clipboard.writeText(inputField);
                toast.success('Token copiado com sucesso!');
            } catch (err) {
                console.error('Falha ao copiar: ', err);
            }
        } else {
            console.error('Elemento de entrada não encontrado.');
        }
    }

    async function gerarNovoToken(index) {
        loading.value.show();
        modalNovoToken.value = false;
        try {
            const { id_instancia } = formData;
            const dadosEstabelecimento = JSON.parse(localStorage.getItem('estabelecimentos'));
            if (!dadosEstabelecimento) {
                return toast.error('Não foi possível gerar um novo token!');
            }
            const cd_estabelecimento = dadosEstabelecimento[0].cd_estabelecimento;
            const nr_conrole = dadosEstabelecimento[0].nr_conrole;

            const result = await EstabelecimentoInstancias.alterar({
                id_instancia: Number(id_instancia),
                cd_estabelecimento,
                nr_conrole,
            });
            if (result.statuscode == 200) {
                toast.success('Novo token gerado com sucesso!');
                formData.nr_hash = result.data[0].nr_hash;
            } else {
                toast.error(result.message);
            }
        } catch (err) {
            console.error('Erro ao gerar novo token: ', err.message);
            toast.error(err.message);
        } finally {
            loading.value.hide();
        }
    }

    async function restartInstance(instance) {
        loading.value.show();
        //console.log('instance at line 759 in instancia/listaInstancias.vue:', instance);
        let filtros = {
            instance: instance.nameinstance,
            nr_hash: instance.nr_hash,
        };

        const respInstance = await WhatsAppServices.restartInstance(filtros);
        // console.log('respInstance at line 769 in instancia/listaInstancias.vue:', respInstance);

        if (respInstance.statuscode == 200) {
            toast.success('Instância reiniciada com sucesso!');
        } else {
            toast.warning('Não foi possivel reiniciar a instância!');
        }
        loading.value.hide();
    }

    async function logoutInstance(instance) {
        loading.value.show();
        //console.log('instance at line 759 in instancia/listaInstancias.vue:', instance);
        let filtros = {
            instance: instance.nameinstance,
            nr_hash: instance.nr_hash,
        };

        const respInstance = await WhatsAppServices.logoutInstance(filtros);
        // console.log('respInstance at line 769 in instancia/listaInstancias.vue:', respInstance);

        if (respInstance.statuscode == 200) {
            toast.success('Instância desconectada com sucesso!');
            await carregaInstancias();
        } else {
            toast.warning('Não foi possivel desconectar a instância!');
        }
        loading.value.hide();
    }

    onMounted(async () => {
        //initTabulator();
        pagina.pagename = 'Lista de Instâncias';
        pagina.description = 'Instâncias';
        pagina.module = 'Instâncias';

        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        //console.log('🚀 ~ listaInstancias.vue:882 ~ onMounted ~ respModulos:', respModulos);
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;
        modulos.qt_instancias = respModulos.qt_instancias;
        modulos.qt_usuarios = respModulos.qt_usuarios;
        //console.log('modulos at line 791 in instancia/listaInstancias.vue:', modulos.value);

        codUsuario.value = localStorage.getItem('codusuario');
        //estabelecimentosLiberado.value = JSON.parse(await localStorage.getItem('estabelecimentos'));
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);

            await carregaInstancias();
            selecionaStatus(0);
        } else {
            toast.warning('Estabelecimento não liberado!');
        }

        const state = router.options.history.state;
        if (!!state?.status) {
            const status = listaStatus.value.find((item) => item.label === state.status);
            selecionaStatus(!!status ? status.value.toString() : '0');
        }
    });
</script>

<style scoped>
    .table th {
        padding-bottom: 0 !important;
    }
    ::v-deep(.multiselect-clear) {
        display: none !important;
        width: 0 !important;
    }
    ::v-deep(.multiselect-single-label) {
        padding-right: 28px !important;
    }
    ::v-deep(.dropdown-item-disbled) {
        opacity: 0.4 !important;
        pointer-events: none !important;
    }
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-input) {
        cursor: pointer !important;
    }
</style>
