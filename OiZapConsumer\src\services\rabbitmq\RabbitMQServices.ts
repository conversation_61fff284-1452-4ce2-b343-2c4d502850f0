import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { Channel } from 'amqplib';
import axios from 'axios';
import { erroInterno, IRetorno, sucesso } from '../../interfaces/IRetorno';
import Logger from '../Logger';
//import RabbitMQConnectionEvolution from './Evolution/RabbitMQConnectionEvolution';
const logger = Logger.getLogger();

export default class RabbitMQServices {
  private queuePrefix: string = '';
  //private connection: Connection | null = null;
  private channel: Channel | null = null;

  constructor() {
    //queuePrefix?: string
    // this.queuePrefix = queuePrefix;
  }

  async removeQueue(queuePrefix: string): Promise<IRetorno> {
    try {
      // let HOST_RABBITMQ: string = '';

      // if (process.env.AMBIENTE == 'PROD') {
      //   HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      // } else {
      //   HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      // }

      // logger.debug('RemoveQueues > HOST_RABBITMQ=' + HOST_RABBITMQ);
      // logger.debug('RemoveQueues > this.queuePrefix=' + this.queuePrefix);

      // this.connection = await amqp.connect(HOST_RABBITMQ);
      // this.channel = await this.connection.createChannel();

      //this.channel = await RabbitMQConnectionEvolution.getInstance().connect();

      const queuesToDelete = await this.getQueuesWithPrefix('oizap', queuePrefix);

      logger.debug('RemoveQueues > queuesToDelete=' + queuesToDelete);

      for (const queueName of queuesToDelete) {
        await this.deleteQueue(queueName);
      }

      const queuesToDeleteEvo = await this.getQueuesWithPrefix('evolution', queuePrefix);

      logger.debug('RemoveQueues > queuesToDelete=' + queuesToDelete);

      for (const queueName of queuesToDeleteEvo) {
        await this.deleteQueue(queueName);
      }

      // await this.channel.close();
      // await this.connection.close();
      return sucesso(queuesToDelete);
    } catch (error) {
      console.error('Erro ao processar as filas:', error);
      return erroInterno(error);
    }
  }

  async getQueueArguments(serverName: string, queueName: string, vhost: string = '/'): Promise<IRetorno> {
    let HOST_RABBITMQ: string = '';

    if (serverName == 'oizap') {
      HOST_RABBITMQ = process.env.API_RABBITMQOIZAP as string;
    } else {
      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }
    }

    //${encodeURIComponent(vhost)}/
    const apiUrl = `${HOST_RABBITMQ}/${encodeURIComponent(vhost)}/${queueName}`;
    try {
      const response = await axios.get(apiUrl, {
        auth: {
          username: process.env.USER_RABBITMQ as string,
          password: process.env.PASS_RABBITMQ as string,
        },
      });

      return sucesso([response.data]);
      // return response.data.arguments; // Retorna os argumentos da fila
    } catch (error: any) {
      console.error(`Failed to get arguments for queue ${queueName}:`, error.message);
      return erroInterno(error); // Retorna null em caso de falha
    }
  }

  //   async getQueuesWithPrefix(serverName: string, prefix: string, vhost: string = '/'): Promise<string[]> {
  //     try {
  //       /*    let HOST_RABBITMQ: string = '';

  //       if (serverName == 'oizap') {
  //         HOST_RABBITMQ = process.env.API_RABBITMQOIZAP as string;
  //       } else {
  //         if (process.env.AMBIENTE == 'PROD') {
  //           HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
  //         } else {
  //           HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
  //         }
  //       }
  //       //${encodeURIComponent(vhost)}/
  //       const apiUrl = `${HOST_RABBITMQ}/${encodeURIComponent(vhost)}/${queueName}`;
  // */
  //       let HOST_RABBITMQ: string = '';

  //       if (serverName == 'oizap') {
  //         HOST_RABBITMQ = process.env.API_RABBITMQOIZAP as string;
  //       } else {
  //         if (process.env.AMBIENTE == 'PROD') {
  //           HOST_RABBITMQ = process.env.API_RABBITMQ as string;
  //         } else {
  //           HOST_RABBITMQ = process.env.API_RABBITMQSANDBOX as string;
  //         }
  //       }
  //       const apiUrl = `${HOST_RABBITMQ}/default`;

  //       const response = await axios.get(apiUrl, {
  //         auth: {
  //           username: process.env.USER_RABBITMQ as string,
  //           password: process.env.PASS_RABBITMQ as string,
  //         },
  //       });
  //       const queues = response.data;

  //       // logger.debug('getQueuesWithPrefix > queues: ' + JSON.stringify(queues));

  //       return queues
  //         .map((queue: { name: string }) => queue.name)
  //         .filter((queueName: string) => queueName.startsWith(prefix));
  //     } catch (error) {
  //       console.error('error at line 150 in rabbitmq/RabbitMQServices.ts:', error);

  //       return [];
  //     }
  //   }

  async getQueuesWithPrefix(serverName: string, prefix: string, vhost: string = '/'): Promise<string[]> {
    try {
      let HOST_RABBITMQ: string = '';

      if (serverName == 'oizap') {
        HOST_RABBITMQ = process.env.API_RABBITMQOIZAP as string;
      } else {
        if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
          HOST_RABBITMQ = process.env.API_RABBITMQ as string;
        } else {
          HOST_RABBITMQ = process.env.API_RABBITMQSANDBOX as string;
        }
      }

      // Certifica-se que a URL base está correta e inclui o protocolo https
      if (!HOST_RABBITMQ.startsWith('http')) {
        HOST_RABBITMQ = `https://${HOST_RABBITMQ}`;
      }

      // Adiciona a porta 15672 se não estiver especificada
      if (!HOST_RABBITMQ.includes(':15672')) {
        HOST_RABBITMQ = `${HOST_RABBITMQ}:15672`;
      }

      const apiUrl = `${HOST_RABBITMQ}/api/queues/${encodeURIComponent(vhost)}`;

      const response = await axios.get(apiUrl, {
        auth: {
          username: process.env.USER_RABBITMQ as string,
          password: process.env.PASS_RABBITMQ as string,
        },
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.data) {
        logger.warn('Resposta da API sem dados');
        return [];
      }

      const queues = response.data;
      const filteredQueues = queues
        .map((queue: { name: string }) => queue.name)
        .filter((queueName: string) => queueName.startsWith(prefix));

      logger.debug(`Filas encontradas: ${filteredQueues.length}`);
      return filteredQueues;
    } catch (error: any) {
      logger.error(`Erro ao buscar filas: ${error.message}`);
      if (error.response) {
        logger.error(`Status: ${error.response.status}`);
        logger.error(`Headers: ${JSON.stringify(error.response.headers)}`);
      }
      return [];
    }
  }

  async deleteQueue(queueName: string) {
    try {
      await this.channel!.deleteQueue(queueName);
    } catch (error) {
      console.error(`Erro ao remover a fila ${queueName}:`, error);
    }
  }
}
