<template>
  <div>
    <ShowLoading ref="loading" />
    <ShowNotifications ref="showNotifications" />
    <div class="intro-y flex items-center mt-2">
      <h2 class="text-lg font-medium mr-auto">Associação Perfis x Telas</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-3">
      <div class="intro-y col-span-12">
        <PreviewComponent class="intro-y box">
          <div class="p-5">
            <label>Perfil:</label>
            <select v-model="perfilSelecionado" @change="carregarTelasPerfil" class="form-control">
              <option v-for="p in perfis" :key="p.cd_perfil" :value="p.cd_perfil">{{ p.nm_perfil }}</option>
            </select>
            <div v-if="telas.length">
              <label class="mt-4">Telas permitidas:</label>
              <div class="grid grid-cols-2 gap-2">
                <div v-for="t in telas" :key="t.cd_tela">
                  <input type="checkbox" :value="t.cd_tela" v-model="telasSelecionadas" />
                  {{ t.nm_tela }} ({{ t.ds_rota }})
                </div>
              </div>
              <button class="btn btn-primary mt-4" @click="salvarAssociacoes">Salvar</button>
            </div>
          </div>
        </PreviewComponent>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import api from '@/utils/api'
import ShowNotifications from '@/components/show-notifications/Main.vue'
import ShowLoading from '@/components/show-loading/Main.vue'
const showNotifications = ref()
const loading = ref()
const perfis = ref([])
const telas = ref([])
const perfilSelecionado = ref(null)
const telasSelecionadas = ref([])

onMounted(async () => {
  perfis.value = (await api.get('/perfis')).data
  telas.value = (await api.get('/telas')).data
})

async function carregarTelasPerfil() {
  if (!perfilSelecionado.value) return
  const resp = await api.get(`/perfis/${perfilSelecionado.value}/telas`)
  telasSelecionadas.value = resp.data.map(t => t.cd_tela)
}

async function salvarAssociacoes() {
  loading.value.show()
  try {
    await api.post('/perfis/associar', {
      cd_perfil: perfilSelecionado.value,
      telas: telasSelecionadas.value
    })
    showNotifications.value.showSuccessNotification('Associações salvas!')
  } catch (e) {
    showNotifications.value.showErrorNotification('Erro ao salvar associações')
  }
  loading.value.hide()
}
</script> 