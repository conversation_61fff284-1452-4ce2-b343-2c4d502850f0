import { Knex } from 'knex';
import { safeCreateIndex } from '../../scripts/indexUtils';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('perfis', (table) => {
    table.increments('cd_perfil').primary();
    table.string('nm_perfil', 100).notNullable();
    table.timestamps(true, true);
  });

  console.log('Iniciando criação dos índices na tabela perfis');

  await safeCreateIndex(knex, 'perfis', 'cd_perfil', 'perfis_cd_perfil_idx');
  console.log('Índice cd_perfil criado com sucesso na tabela perfis');
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('perfis');
}
