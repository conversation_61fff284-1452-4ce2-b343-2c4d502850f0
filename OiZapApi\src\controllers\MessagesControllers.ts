import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../interfaces/IRetorno';
import { MessagesModel } from '../models/MessagesModel';

export class MessagesController {
  static async incluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      if (req.body.telefone === undefined) {
        errors.push('O campo "telefone" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MessagesModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarUltimaMensagemContato(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      // if (req.query.tp_situacao === undefined) {
      //   errors.push('O campo "tp_situacao" é obrigatório.');
      // }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MessagesModel().listarUltimaMensagemContato(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listMessages(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      if (req.query.telefone === undefined) {
        errors.push('O campo "telefone" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MessagesModel().listMessages(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async readMessages(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      if (req.body.telefone === undefined) {
        errors.push('O campo "telefone" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MessagesModel().readMessages(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarRelatorioMensagens(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      // m.date_sent,m.date_received,m.date_read
      if (
        !(!!req.query.dt_enviado_inicial && !!req.query.dt_enviado_final) &&
        !(!!req.query.dt_recebido_inicial && !!req.query.dt_recebido_final) &&
        !(!!req.query.dt_lido_inicial && !!req.query.dt_lido_final)
      ) {
        errors.push('Adicione ao menos um filtro de data.');
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors, 'Adicione ao menos um filtro de data.'));
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MessagesModel().listarRelatorioMensagens(req);

      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaMensagensData(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new MessagesModel().listaMensagensData(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaMensagensHora(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new MessagesModel().listaMensagensHora(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async reenviarMensagens(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      // if (req.body.instance === undefined) {
      //   errors.push('O campo "instance" é obrigatório.');
      // }
      // if (req.body.telefone === undefined) {
      //   errors.push('O campo "telefone" é obrigatório.');
      // }
      // if (errors.length > 0) {
      //   return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      // }
      const result = await new MessagesModel().reenviarMensagens(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
