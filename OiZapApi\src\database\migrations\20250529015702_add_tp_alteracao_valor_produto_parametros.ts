import { Knex } from 'knex';
import path from 'path';

export async function up(knex: Knex): Promise<void> {
  try {
    // Auto-limpeza antes de executar
    const migrationsPath = path.join(__dirname, '..');
    //await cleanOrphanMigrations(knex, migrationsPath);

    // Verifica se a coluna existe
    const hasColumn = await knex.schema.hasColumn('parametros', 'tp_alteracao_valor_produto');

    if (!hasColumn) {
      console.log('Coluna não existe, criando...');
      await knex.schema.alterTable('parametros', (table) => {
        table.string('tp_alteracao_valor_produto', 100);
      });
    }
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('parametros', (table) => {
      table.dropColumn('tp_alteracao_valor_produto');
    });
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
