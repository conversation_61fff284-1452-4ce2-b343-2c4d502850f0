<template>
    <!-- Header <PERSON> <PERSON><PERSON> de módulos -->
    <div class="p-4">
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <MessageSquareIcon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Assuntos de Atendimento</h3>
                        <p class="text-blue-100 text-sm">Cadastro de assuntos para encerramento do atendimento.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Formulário -->
        <div class="bg-white dark:bg-slate-800 rounded-b-xl border-t-0">
            <div class="p-6">
                <form @submit.prevent="save">
                    <div class="grid grid-cols-12 gap-4 mb-6">
                        <!-- Motivo -->
                        <div class="col-span-12">
                            <label
                                for="ds_motivo"
                                class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
                            >
                                Descrição do Assunto
                                <span class="text-red-500">*</span>
                            </label>
                            <BaseInput
                                :disabled="loading"
                                autofocus
                                id="ds_motivo"
                                v-model.trim="formData.ds_motivo"
                                type="text"
                                name="ds_motivo"
                                placeholder="Digite a descrição do assunto"
                                class="w-full"
                                maxlength="60"
                            />
                        </div>

                        <!-- Status Ativo/Inativo -->
                        <div class="col-span-12">
                            <div class="flex items-center space-x-3">
                                <div class="form-check form-switch">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        :value="formData.in_ativo"
                                        :checked="formData.in_ativo"
                                        @change="onChangeAtivo($event)"
                                        :disabled="loading"
                                    />
                                </div>
                                <label class="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    {{ formData.in_ativo ? 'Ativo' : 'Inativo' }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Campos obrigatórios -->
                    <div class="mb-2">
                        <cite class="text-xs text-slate-500 dark:text-slate-400">
                            <span class="text-red-500">*</span> Campos obrigatórios
                        </cite>
                    </div>

                    <!-- Botões de ação -->
                    <div
                        class="flex justify-start items-center gap-3 pt-4 border-t border-slate-200 dark:border-slate-600"
                    >
                        <button
                            type="submit"
                            class="btn btn-success-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                            :disabled="loading"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        >
                            <SaveIcon class="w-4 h-4 mr-1.5" v-if="!loading" />
                            <div
                                v-if="loading"
                                class="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1.5"
                            ></div>
                            <span>{{ loading ? 'Salvando...' : 'Salvar' }}</span>
                        </button>

                        <button
                            type="button"
                            class="btn btn-warning-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                            @click="limparFormulario"
                            :disabled="loading"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        >
                            <RotateCcwIcon class="w-4 h-4 mr-1.5" />
                            <span>Limpar</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Filtros e Listagem -->
        <div class="mt-2">
            <!-- Filtros -->
            <div class="bg-white dark:bg-slate-800 rounded-lg p-4 mb-4">
                <div class="flex flex-wrap gap-3 items-end">
                    <div class="flex-1 min-w-48">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Pesquisar
                        </label>
                        <BaseInput
                            v-model="filter.value"
                            type="text"
                            placeholder="Digite para filtrar..."
                            @input="onFilter"
                        />
                    </div>
                    <button
                        type="button"
                        class="btn btn-warning-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                        @click="onResetFilter"
                    >
                        <RotateCcwIcon class="w-4 h-4 mr-1" />
                        Limpar
                    </button>
                </div>

                <div class="p-2">
                    <div class="overflow-x-auto">
                        <div id="tabulator" class="table-report table-report--tabulator"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, reactive, onMounted } from 'vue';
    import { useRouter } from 'vue-router';
    import { createIcons, icons } from 'lucide';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import dom from '@left4code/tw-starter/dist/js/dom';
    import MotivosAtendimentoServices from '@/services/chat/MotivosAtendimentoServices';
    import { useToast } from '@/global-components/toastify/useToast';

    // Icons
    import { MessageSquareIcon, SaveIcon, RotateCcwIcon } from 'lucide-vue-next';

    const router = useRouter();
    const toast = useToast();

    // Refs e Reactive
    const motivos = ref([]);
    const tabulator = ref();
    const loading = ref(false);

    const filter = reactive({
        field: 'ds_motivo',
        type: 'like',
        value: '',
    });

    const formData = reactive({
        cd_motivo: undefined,
        ds_motivo: undefined,
        in_ativo: true,
        cd_estabelecimento: undefined,
    });

    // Colunas da tabela
    const columns = [
        {
            title: 'Descrição do Assunto',
            minWidth: 250,
            responsive: 0,
            field: 'ds_motivo',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Status',
            minWidth: 100,
            field: 'in_ativo',
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                const isActive = cell.getData().in_ativo;
                return `<div class="flex items-center justify-center">
                    <span class="px-2 py-1 text-xs rounded-full ${
                        isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }">
                        ${isActive ? 'Ativo' : 'Inativo'}
                    </span>
                </div>`;
            },
        },
        {
            title: 'Ações',
            minWidth: 120,
            responsive: 1,
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                const a = dom(`
                    <div class="flex justify-center items-center gap-2">
                        <button class="btn btn-sm btn-outline-primary flex items-center edit-btn">
                            <i data-lucide="edit-2" class="w-3 h-3 mr-1"></i>
                            Editar
                        </button>
                        <button class="btn btn-sm btn-outline-danger flex items-center delete-btn">
                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                            Excluir
                        </button>
                    </div>
                `);

                // Evento de editar
                dom(a)
                    .find('.edit-btn')
                    .on('click', function () {
                        editar(cell.getData());
                    });

                // Evento de excluir
                dom(a)
                    .find('.delete-btn')
                    .on('click', function () {
                        excluirMotivo(cell.getData());
                    });

                return a[0];
            },
        },
    ];

    // Métodos
    function onChangeAtivo(event) {
        formData.in_ativo = event.target.checked;
    }

    function limparFormulario() {
        formData.cd_motivo = undefined;
        formData.ds_motivo = undefined;
        formData.in_ativo = true;
    }

    async function save() {
        if (!validarFormulario()) return;

        loading.value = true;

        try {
            let response;

            if (formData.cd_motivo) {
                // Edição
                response = await MotivosAtendimentoServices.alterar(formData);
                if (response) {
                    toast.success('Motivo alterado com sucesso!');
                }
            } else {
                // Inclusão
                response = await MotivosAtendimentoServices.incluir(formData);
                if (response) {
                    toast.success('Motivo incluído com sucesso!');
                }
            }

            console.log('🚀 ~ cadMotivosAtendimento.vue:292 ~ save ~ response:', response);
            if (response) {
                limparFormulario();
                await listaDados();
            } else {
                toast.error('Erro ao salvar motivo');
            }
        } catch (error) {
            toast.error('Erro ao salvar: ' + error.message);
        } finally {
            loading.value = false;
        }
    }

    function validarFormulario() {
        if (!formData.ds_motivo.trim()) {
            toast.error('Descrição do motivo é obrigatória');
            return false;
        }
        if (formData.ds_motivo.length > 60) {
            toast.error('Descrição do motivo deve ter no máximo 60 caracteres');
            return false;
        }
        return true;
    }

    function editar(motivo) {
        formData.cd_motivo = motivo.cd_motivo;
        formData.ds_motivo = motivo.ds_motivo;
        formData.in_ativo = motivo.in_ativo;

        // Scroll para o topo do formulário
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    async function excluirMotivo(motivo) {
        try {
            toast.confirm(
                {
                    title: 'Confirmar a exclusão',
                    description: `Tem certeza que deseja excluir o motivo "${motivo.ds_motivo}"?`,
                    confirmText: 'Sim',
                    cancelText: 'Não',
                    confirmColor: 'success',
                    cancelColor: 'warning',
                },
                () => {
                    excluir(motivo);
                },
                () => {
                    return;
                }
            );
        } catch (error) {
            toast.error('Erro ao excluir motivo: ' + error.message);
        }
    }

    async function excluir(motivo) {
        try {
            const response = await MotivosAtendimentoServices.remover({ cd_motivo: motivo.cd_motivo });

            if (response.statuscode == 200) {
                toast.success('Motivo excluído com sucesso!');
                motivos.value = motivos.value.filter((m) => m.cd_motivo !== motivo.cd_motivo);
                initTabulator();
            } else {
                toast.error('Erro ao excluir motivo');
            }
        } catch (error) {
            toast.error('Erro ao excluir: ' + error.message);
        }
    }

    function onFilter() {
        if (tabulator.value) {
            tabulator.value.setFilter(filter.field, filter.type, filter.value);
        }
    }

    function onResetFilter() {
        filter.field = 'ds_motivo';
        filter.type = 'like';
        filter.value = '';
        onFilter();
    }

    async function listaDados() {
        try {
            motivos.value = [];
            if (tabulator.value) {
                tabulator.value.destroy();
            }
            const data = await MotivosAtendimentoServices.listar({
                field: filter.field,
                type: filter.type,
                value: filter.value,
                cd_estabelecimento: formData.cd_estabelecimento,
            });

            if (data.statuscode == 200) {
                motivos.value = data.data;
                initTabulator();
            }
        } catch (error) {
            toast.error('Erro ao carregar dados: ' + error.message);
        }
    }

    function initTabulator() {
        if (tabulator.value) {
            tabulator.value.destroy();
        }

        tabulator.value = new Tabulator('#tabulator', {
            data: motivos.value,
            layout: 'fitColumns',
            responsiveLayout: 'collapse',
            placeholder: 'Nenhum motivo encontrado',
            columns: columns,
            pagination: 'local',
            paginationSize: 10,
            paginationSizeSelector: [5, 10, 20, 50],
        });

        tabulator.value.on('renderComplete', function () {
            createIcons({
                icons,
                'stroke-width': 1.5,
                nameAttr: 'data-lucide',
            });
        });
    }

    const reInitOnResizeWindow = () => {
        window.addEventListener('resize', () => {
            if (tabulator.value) {
                tabulator.value.redraw();
                createIcons({
                    icons,
                    'stroke-width': 1.5,
                    nameAttr: 'data-lucide',
                });
            }
        });
    };

    onMounted(async () => {
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            const estabelecimentosLiberado = JSON.parse(estatabelecimentos);
            formData.cd_estabelecimento = estabelecimentosLiberado[0].cd_estabelecimento;
        } else {
            toast.warning('Estabelecimento não liberado!');
        }
        reInitOnResizeWindow();
        await listaDados();
    });
</script>
