{"name": "OiZapApi", "description": "", "version": "1.1.0", "main": "server.js", "scripts": {"start": "nodemon", "start:dev": "nodemon --watch src/**/*.ts --exec ts-node src/server.ts", "watch": "tsc -w", "copy-files": "npx copyfiles -u 1 \"src/database/migrations/**/*\" dist/database/migrations && npx copyfiles package.json dist && npx copyfiles \"downloads/**/*\" dist && npx copyfiles \"src/config/**/*\" dist && npx copyfiles \"migrations/**/*\" dist/migrations && npx copyfiles migrate-config.json dist && npx copyfiles migrate-auto.js dist", "test": "echo \"Error: no test specified\" && exit 1", "localhost": "node utils/enviarServidor.js localhost", "deploy": "node utils/enviarServidor.js", "oizap": "node utils/enviarServidor.js oizap", "sandbox": "node utils/enviarServidor.js sandbox", "devzap": "node utils/enviarServidor.js devzap", "crm": "node utils/enviarServidor.js crm", "commit": "node utils/commit.js", "version": "node utils/version.js", "tsc": "tsc", "migration:legacy": "cross-env NODE_ENV=CRM ts-node -r dotenv/config ./node_modules/knex/bin/cli.js migrate:latest --knexfile src/config/knexfile.ts", "migrate": "node-pg-migrate", "migrate:up": "node-pg-migrate up", "migrate:down": "node-pg-migrate down", "migrate:create": "node-pg-migrate create", "migrate:dev": "cross-env NODE_ENV=DEV node migrate-auto.js", "migrate:crm": "cross-env NODE_ENV=CRM node migrate-auto.js", "migrate:sandbox": "cross-env NODE_ENV=SANDBOX node migrate-auto.js", "migrate:prod": "cross-env NODE_ENV=PROD node migrate-auto.js", "migrate:simple": "cross-env NODE_ENV=DEV node simple-migrator.js", "migrate:simple:crm": "cross-env NODE_ENV=CRM node simple-migrator.js", "migrate:simple:sandbox": "cross-env NODE_ENV=SANDBOX node simple-migrator.js", "migrate:simple:prod": "cross-env NODE_ENV=PROD node simple-migrator.js", "migration:run": "cross-env NODE_ENV=DEV node migration-runner.js", "migration:run:crm": "cross-env NODE_ENV=CRM node migration-runner.js", "migration:run:sandbox": "cross-env NODE_ENV=SANDBOX node migration-runner.js", "migration:run:prod": "cross-env NODE_ENV=PROD node migration-runner.js", "migration:create": "node migration-runner.js create"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.5", "@types/knex": "^0.16.1", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.17.50", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.0", "@types/ssh2": "^1.15.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.1", "inquirer": "^8.2.4", "nodemon": "^3.1.0", "prettier": "^3.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.0", "typescript": "^5.8.3"}, "author": "", "license": "ISC", "dependencies": {"@babel/eslint-parser": "^7.23.10", "@types/fs-extra": "^11.0.4", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "child_process": "^1.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.6.1", "express": "^4.18.2", "express-interceptor": "^1.2.0", "form-data": "^4.0.1", "fs-extra": "^11.2.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-pg-migrate": "^8.0.3", "node-ssh": "^13.2.0", "nodemailer": "^6.9.8", "pg": "^8.16.0", "redis": "^4.6.15", "ssh2": "^1.16.0", "ssh2-sftp-client": "^10.0.3", "winston": "^3.14.2"}}