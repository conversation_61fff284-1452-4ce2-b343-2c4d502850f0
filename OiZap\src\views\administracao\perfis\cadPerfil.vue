<template>
  <div>
    <ShowLoading ref="loading" />
    <ShowNotifications ref="showNotifications" />
    <div class="intro-y flex items-center mt-2">
      <h2 class="text-lg font-medium mr-auto">Cadastro de Perfil</h2>
      <button @click="router.push({ name: 'listaPerfis' })" class="btn btn-primary-soft col-span-4 mt-2">Voltar</button>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-3">
      <div class="intro-y col-span-12">
        <PreviewComponent class="intro-y box">
          <div class="p-5">
            <form class="validate-form" @submit.prevent="save">
              <div class="grid grid-cols-3 gap-2 mt-2">
                <div class="form-inline col-span-3">
                  <label class="forceLeft form-label sm:w-20 self-center">Nome do Perfil:</label>
                  <input v-model.trim="formData.nm_perfil" type="text" class="form-control col-span-2" required />
                </div>
              </div>
              <button type="submit" class="btn btn-primary mt-5">Salvar</button>
            </form>
          </div>
        </PreviewComponent>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import ShowNotifications from '@/components/show-notifications/Main.vue'
import ShowLoading from '@/components/show-loading/Main.vue'
import api from '@/utils/api'
const router = useRouter()
const showNotifications = ref()
const loading = ref()
const formData = ref({ nm_perfil: '' })

async function save() {
  loading.value.show()
  try {
    await api.post('/perfis', formData.value)
    showNotifications.value.showSuccessNotification('Perfil cadastrado!')
    router.push({ name: 'listaPerfis' })
  } catch (e) {
    showNotifications.value.showErrorNotification('Erro ao salvar perfil')
  }
  loading.value.hide()
}
</script> 