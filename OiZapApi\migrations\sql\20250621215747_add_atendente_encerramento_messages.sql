-- Migration: add_atendente_encerramento_messages
-- Created: 2025-06-21T21:57:47.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar colunas de encerramento na tabela messages
-- ========================================

-- Adicionar cd_motivo
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'cd_motivo'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN cd_motivo INTEGER;
        COMMENT ON COLUMN messages.cd_motivo IS 'Indica o motivo do atendimento';
        RAISE NOTICE 'Coluna cd_motivo adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna cd_motivo já existe na tabela messages';
    END IF;
END $$;

-- Adicionar ds_resumo
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'ds_resumo'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN ds_resumo TEXT;
        COMMENT ON COLUMN messages.ds_resumo IS 'Indica o resumo do atendimento';
        RAISE NOTICE 'Coluna ds_resumo adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna ds_resumo já existe na tabela messages';
    END IF;
END $$;

-- Adicionar cd_usuario_encerramento
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'cd_usuario_encerramento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN cd_usuario_encerramento INTEGER;
        COMMENT ON COLUMN messages.cd_usuario_encerramento IS 'Indica o usuário que encerrou o atendimento';
        RAISE NOTICE 'Coluna cd_usuario_encerramento adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna cd_usuario_encerramento já existe na tabela messages';
    END IF;
END $$; 