<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <Modal size="modal-lg" :show="props.estado" @hidden="fecharModal" key="props" slideOver>
        <ModalHeader class="flex justify-between items-center font-medium text-xl">
            Cliente
            <XIcon class="w-6 h-6 cursor-pointer" @click="fecharModal" />
        </ModalHeader>
        <ModalBody
            class="h-[calc(100vh-55px)] max-h-[calc(100vh-55px)] overflow-y-auto flex flex-col items-center justify-between p-0"
        >
            <div class="w-full flex flex-col items-center p-6">
                <h3 class="text-md font-medium text-slate-500 self-start flex items-center mb-1">
                    <UserIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Dad<PERSON> pessoais
                </h3>
                <div class="grid grid-cols-12 w-full gap-2">
                    <div class="form-control col-span-12 md:col-span-3">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Cod.
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Código"
                                v-model="formData.cd_cliente"
                                disabled
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-12 md:col-span-9 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Telefone <span class="text-red-500">*</span>
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                v-model="formData.nr_telefonezap"
                                @input="formatarTelefone"
                                placeholder="(00) 00000-0000"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-12 md:col-span-12">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Nome <span class="text-red-500">*</span>
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder=" Nome do Cliente"
                                v-model="formData.ds_nome"
                            />
                        </div>
                    </div>
                </div>
                <h3 class="text-md font-medium text-slate-500 self-start border-t border-slate-200 pt-4 mt-4 w-full">
                    Endereço
                </h3>
                <div class="grid grid-cols-12 w-full gap-2">
                    <div class="form-control col-span-3 md:col-span-2 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Estado
                        </label>
                        <TomSelect
                            v-model="formData.ds_uf"
                            :options="{
                                allowEmptyOption: true,
                                create: true,
                            }"
                            size="small"
                            class="w-full !cursor-pointer"
                            @change=""
                        >
                            <option value="" disabled>UF</option>
                            <option v-for="item in listaUF" :key="item" :value="item">
                                {{ item }}
                            </option>
                        </TomSelect>
                    </div>

                    <div class="form-control col-span-9 md:col-span-10 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Cidade
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Informe sua cidade"
                                v-model="formData.ds_cidade"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-9 md:col-span-10 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Endereço
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Informe seu endereço"
                                v-model="formData.ds_endereco"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-3 md:col-span-2 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Número
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="SN"
                                v-model="formData.nr_endereco"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-12 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Bairro
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Informe o Bairro"
                                v-model="formData.ds_bairro"
                            />
                        </div>
                    </div>

                    <div class="form-control col-span-12 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Complemento
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Informe o Complemento"
                                v-model="formData.ds_complemento"
                            />
                        </div>
                    </div>
                </div>
                <h3
                    class="text-md font-medium text-slate-500 self-start border-t border-slate-200 pt-4 mt-4 mb-0 w-full"
                >
                    Extras
                </h3>
                <div class="grid grid-cols-12 w-full">
                    <div class="form-control col-span-12 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                            >Observações</label
                        >
                        <div class="relative w-full">
                            <textarea
                                class="form-control col-span-1 block"
                                placeholder="Observações do Cliente"
                                v-model="formData.ds_obscliente"
                                rows="5"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <p class="text-xs text-slate-500 w-full py-2">
                    <span class="text-red-500">*</span> Campos obrigatórios
                </p>
            </div>
            <ModalFooter class="w-full flex justify-end items-center">
                <button class="btn btn-secondary-soft border-none shadow-none mr-2" @click="fecharModal">
                    <XIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Cancelar
                </button>
                <button class="btn btn-primary-soft border-none shadow-none" @click="salvarCliente">
                    <SaveIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Salvar
                </button>
            </ModalFooter>
        </ModalBody>
    </Modal>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import ClientesServices from '@/services/pedidos/ClientesServices';
    import notifications from '@/components/show-notifications/Main.vue';

    const showNotifications = ref();
    const loading = ref();

    const props = defineProps({
        estado: {
            type: Boolean,
            default: false,
        },
        dados: {
            type: Object,
            default: () => ({}),
        },
    });
    const emit = defineEmits(['update:estado', 'response:success', 'response:error']);

    const formData = ref({
        cd_estabelecimento: '',
        cd_cliente: '',
        ds_uf: '',
        ds_nome: '',
        nr_telefonezap: '',
        ds_obscliente: '',
        ds_endereco: '',
        nr_endereco: '',
        ds_cidade: '',
        ds_bairro: '',
        ds_estado: '',
        ds_pais: '',
        ds_complemento: '',
    });

    const listaUF = ref([
        '',
        'AC',
        'AL',
        'AM',
        'AP',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MG',
        'MS',
        'MT',
        'PA',
        'PB',
        'PE',
        'PI',
        'PR',
        'RJ',
        'RN',
        'RO',
        'RR',
        'RS',
        'SC',
        'SE',
        'SP',
        'TO',
    ]);

    function fecharModal() {
        emit('update:estado', false);
    }

    function formatarTelefone(event, removerEspeciais, retornarValor) {
        let valor = event.target?.value?.replace(/\D/g, ''); // Remove todos os caracteres não numéricos

        if (removerEspeciais) {
            return valor;
        }

        // Aplica a formatação: +CC (AA) NNNNN-NNNN
        if (valor?.length < 11) {
            valor = valor?.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
        } else if (valor?.length >= 11) {
            valor = valor?.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
        }

        if (retornarValor) {
            return valor;
        } else {
            formData.value.nr_telefonezap = valor;
        }
    }

    async function salvarCliente() {
        try {
            loading.value.show();
            let result = null;
            const formDataConfigurado = {
                ...formData.value,
            };
            delete formDataConfigurado.checked;
            if (formData.value.cd_cliente) result = await ClientesServices.alterar(formDataConfigurado);
            else result = await ClientesServices.incluir(formDataConfigurado);

            if (result.statuscode === 200) {
                Object.assign(formData.value, result.data[0]);
                showNotifications.value.showSuccessNotification(result.message);
                emit('response:success', result.data[0]);
            }
        } catch (error) {
            console.error('Erro ao salvar cliente:', error);
        } finally {
            loading.value.hide();
            fecharModal();
        }
    }

    async function excluirCliente() {
        // Implementar lógica de exclusão de cliente
    }

    watch(
        () => props,
        (newValue) => {
            if (!newValue.estado) return;
            if (newValue) {
                formData.value = { ...props.dados };
                formData.value.nr_telefonezap = formatarTelefone(
                    { target: { value: formData.value.nr_telefonezap } },
                    false,
                    true
                );

                const estatabelecimentos = localStorage.getItem('estabelecimentos');

                if (estatabelecimentos) {
                    formData.value.cd_estabelecimento = JSON.parse(estatabelecimentos)[0]?.cd_estabelecimento;
                } else {
                    showNotifications.value.showWarningNotification('Estabelecimento não liberado!');
                }
            } else {
                formData.value = {
                    // cd_estabelecimento: '',
                    cd_cliente: '',
                    ds_uf: '',
                    ds_nome: '',
                    nr_telefonezap: '',
                    ds_obscliente: '',
                    ds_endereco: '',
                    nr_endereco: '',
                    ds_cidade: '',
                    ds_bairro: '',
                    ds_estado: '',
                    ds_pais: '',
                    ds_complemento: '',
                };
            }
        },
        { immediate: true, deep: true }
    );
</script>
