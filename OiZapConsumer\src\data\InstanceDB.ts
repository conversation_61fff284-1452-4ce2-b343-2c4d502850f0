import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { IInstance } from '@/interfaces/IInstance';
import { REDIS_TIMER, RedisServices } from '..//services/RedisServices';
import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import { PostgreSQLServices } from '../services/PostgreSQLServices';
//import { REDIS_TIMER, RedisServices } from '../services/RedisServices';

export class InstanceDB {
  static async listarInstancias(req: any): Promise<IRetorno> {
    try {
      //const keyRedis = `instancia-listar-${JSON.stringify(req)}`;
      // const resultRedis = await new RedisServices().get(keyRedis);
      //console.log('resultRedis :', resultRedis);
      // if (resultRedis) return sucesso(resultRedis);

      let sql = `select i.*,ei.cd_estabelecimento,ei.nr_hash 
from instances i
left join estabelecimento_instancias ei on ei.id_instancia = i.id 
where i.nameinstance = '${req.instance}'`;

      const result = await new PostgreSQLServices().query(sql);
      //console.log('result :', result);
      //if (result.statuscode == 200) {
      //  await new RedisServices().set(keyRedis, result.data, REDIS_TIMER);
      //  }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async instances(): Promise<IRetorno> {
    try {
      let sql = `select * from instances where 1=1`; // where status = 'active'

      if (process.env.AMBIENTE == 'DEV' || process.env.AMBIENTEPROD == 'PROD' || process.env.AMBIENTEPROD == 'CRM') {
        sql += ` and nameinstance in (${process.env.FILA_RABBITMQ_DEV})`;
      } else {
        sql += ` and nameinstance not in (${process.env.FILA_RABBITMQ_DEV})`;
      }

      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async getInstance(instanceName: string): Promise<IInstance> {
    // console.log('instanceName at line 60 in data/InstanceDB.ts:', instanceName);
    //console.log('TEM QUE TESTAR SE AO ALTERAR A INSTANCIA, O REDIS É REMOVIDO');
    let instance: IInstance;
    const keyRedis = `${instanceName}:instances:${instanceName}`;
    const resultRedis = await new RedisServices().get(keyRedis);
    // console.log('resultRedis at line 61 in data/InstanceDB.ts:', resultRedis);

    if (resultRedis) {
      instance = resultRedis;
      return instance;
    }

    const sql = `select * from instances where  nameinstance  in ('${instanceName}')`;

    const result = await new PostgreSQLServices().query(sql);
    // console.log('result at line 69 in data/InstanceDB.ts:', result);

    if (result.statuscode == 200) {
      instance = result.data[0];
      await new RedisServices().set(keyRedis, instance, REDIS_TIMER);
    }

    // console.log('instance at line 81 in data/InstanceDB.ts:', instance);
    return instance;
  }

  static async getMensagemForaHorario(cdEstabelecimento: number): Promise<IRetorno> {
    // console.log('instanceName at line 60 in data/InstanceDB.ts:', instanceName);

    const keyRedis = `${cdEstabelecimento}:mensagem_horarios-forahorario:${cdEstabelecimento}`;
    const resultRedis = await new RedisServices().get(keyRedis);

    if (resultRedis) return resultRedis;

    const sql = `select b.*,a.ds_mensagem
from fluxo_atendimento a
left join mensagem_horarios b on b.nr_contfluxo  = a.nr_controle 
where a.tp_mensagem = 'API'
and a.ds_titulo = 'Mensagem Fora do Horário'
and a.in_ativa = true
and a.cd_estabelecimento = '${cdEstabelecimento}'`;

    const result = await new PostgreSQLServices().query(sql);
    // console.log('result at line 69 in data/InstanceDB.ts:', result);

    if (result.statuscode == 200) {
      await new RedisServices().set(keyRedis, result, REDIS_TIMER);
    }

    // console.log('instance at line 81 in data/InstanceDB.ts:', instance);
    return result;
  }
}
