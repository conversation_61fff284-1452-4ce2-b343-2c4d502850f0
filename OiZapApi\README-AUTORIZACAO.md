# Sistema de Autorização - OiZap

Este documento explica como funciona o sistema de autorização implementado no OiZap, que controla o acesso às telas baseado nos perfis dos usuários.

## 📋 Visão Geral

O sistema de autorização do OiZap funciona através de três entidades principais:

1. **Perfis** (`perfis`) - Define os tipos de usuário (Administrador, Atendente, Gerente)
2. **Telas** (`telas`) - Define as telas/rotas do sistema
3. **Permissões** (`perfis_telas`) - Associa perfis às telas com permissões específicas

## 🔐 Estrutura de Permissões

### Perfis Disponíveis

| Perfil | Descrição | Acesso |
|--------|-----------|--------|
| **Administrador** | Acesso total ao sistema | Todas as telas |
| **Gerente** | Acesso gerencial | Telas operacionais + algumas administrativas |
| **Atendente** | Acesso operacional | Apenas telas básicas de atendimento |

### Telas Administrativas (Apenas Administradores)

As seguintes telas são restritas apenas ao perfil **Administrador**:

- ✅ `/cadPerfil` - Cadastro de Perfil
- ✅ `/cadTela` - Cadastro de Tela  
- ✅ `/cadPerfisTelas` - Associação Perfis x Telas
- ✅ `/listaUsuarios` - Lista de Usuários
- ✅ `/cadUsuario` - Cadastro de Usuário
- ✅ `/listaEstabelecimento` - Lista de Estabelecimentos
- ✅ `/cadEstabelecimento` - Cadastro de Estabelecimento
- ✅ `/statusAutorizacao` - Status do Sistema de Autorização

### Telas Operacionais (Todos os Perfis)

- ✅ `/dashboard` - Dashboard
- ✅ `/home` - Home
- ✅ `/chat` - Chat
- ✅ `/atendimento` - Atendimento
- ✅ `/listaClientes` - Clientes
- ✅ `/listaContatos` - Contatos
- ✅ `/listaMensagem` - Mensagens
- ✅ `/listaFluxos` - Fluxos
- ✅ `/integracao` - Configurações

## 🚀 Como Usar

### 1. Configuração Inicial

Execute o script de teste para configurar o sistema:

```sql
-- Execute o arquivo: teste-autorizacao.sql
```

### 2. Verificar Status

Acesse a tela de status para verificar as permissões:

```
http://localhost:8080/status-autorizacao
```

### 3. Testar Diferentes Usuários

O sistema está configurado com os seguintes usuários de teste:

| Usuário | Perfil | Acesso |
|---------|--------|--------|
| **87** | Administrador | Todas as telas |
| **88** | Atendente | Apenas telas operacionais |
| **89** | Gerente | Telas operacionais + algumas administrativas |

## 🔧 Implementação Técnica

### Backend (API)

#### Rotas de Autorização

```typescript
// Verificar permissão para uma rota
POST /oizap/perfis/check-permission
Body: { route: "/cadPerfil" }

// Buscar telas do usuário
GET /oizap/perfis/usuario/:cd_usuario/telas

// Buscar perfil do usuário
GET /oizap/perfis/usuario/:cd_usuario/perfil
```

#### Middleware de Autorização

```typescript
// authorizationTela.ts
export async function authorizationTela(req: Request, res: Response, next: NextFunction) {
  // Verifica se o usuário tem permissão para a rota
  // Retorna 403 se não tiver permissão
}
```

### Frontend (Vue.js)

#### Menu Dinâmico

O menu é carregado dinamicamente baseado nas permissões do usuário:

```javascript
// MenuServices.js
static async carregarMenuUsuario() {
  // Busca permissões do usuário
  // Constrói menu baseado nas permissões
  // Esconde telas administrativas de usuários não-admin
}
```

#### Store do Menu

```javascript
// simple-menu.js
export const useSimpleMenuStore = defineStore('simpleMenu', {
  actions: {
    async carregarMenu() {
      // Carrega menu dinamicamente
    }
  }
});
```

## 📊 Estrutura do Banco de Dados

### Tabela `perfis`
```sql
CREATE TABLE perfis (
  cd_perfil SERIAL PRIMARY KEY,
  nm_perfil VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabela `telas`
```sql
CREATE TABLE telas (
  cd_tela SERIAL PRIMARY KEY,
  nm_tela VARCHAR(100) NOT NULL,
  ds_rota VARCHAR(200) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabela `perfis_telas`
```sql
CREATE TABLE perfis_telas (
  cd_perfil INTEGER REFERENCES perfis(cd_perfil),
  cd_tela INTEGER REFERENCES telas(cd_tela),
  in_visualizar BOOLEAN DEFAULT false,
  in_inserir BOOLEAN DEFAULT false,
  in_alterar BOOLEAN DEFAULT false,
  in_excluir BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (cd_perfil, cd_tela)
);
```

### Tabela `usuarios_perfis`
```sql
CREATE TABLE usuarios_perfis (
  cd_usuario INTEGER,
  cd_perfil INTEGER REFERENCES perfis(cd_perfil),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (cd_usuario, cd_perfil)
);
```

## 🔍 Como Funciona o Controle de Acesso

### 1. Login do Usuário
- Usuário faz login
- Sistema carrega perfil e permissões
- Menu é construído dinamicamente

### 2. Navegação
- Usuário clica em item do menu
- Sistema verifica permissão para a rota
- Se permitido: acessa a tela
- Se negado: redireciona para "Acesso Negado"

### 3. Verificação de Permissão
```sql
SELECT 1
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = ?
  AND t.ds_rota = ?
  AND pt.in_visualizar = true
```

## 🛠️ Comandos Úteis

### Verificar Permissões de um Usuário
```sql
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 87 AND pt.in_visualizar = true
ORDER BY t.nm_tela;
```

### Adicionar Nova Tela
```sql
-- 1. Inserir tela
INSERT INTO telas (nm_tela, ds_rota) VALUES ('Nova Tela', '/novaTela');

-- 2. Associar ao perfil Administrador
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
VALUES (1, (SELECT cd_tela FROM telas WHERE ds_rota = '/novaTela'), true, true, true, true);
```

### Alterar Permissões
```sql
-- Dar permissão de visualização para Atendentes
UPDATE perfis_telas 
SET in_visualizar = true 
WHERE cd_perfil = 2 AND cd_tela = (SELECT cd_tela FROM telas WHERE ds_rota = '/novaTela');
```

## 🚨 Troubleshooting

### Problema: Menu não carrega
**Solução:**
1. Verificar se o usuário está autenticado
2. Verificar se existe associação usuário-perfil
3. Verificar se existem permissões para o perfil

### Problema: Tela administrativa aparece para usuário não-admin
**Solução:**
1. Verificar se a tela está marcada como `adminOnly: true` no MenuServices
2. Verificar se o usuário tem perfil correto
3. Verificar permissões na tabela `perfis_telas`

### Problema: Erro 403 ao acessar tela
**Solução:**
1. Verificar se a rota está cadastrada na tabela `telas`
2. Verificar se existe permissão na tabela `perfis_telas`
3. Verificar se o usuário está associado ao perfil correto

## 📝 Logs e Debug

Use a tela de status para:
- Verificar permissões do usuário atual
- Testar acesso às telas
- Visualizar logs de acesso
- Debugar problemas de autorização

## 🔄 Próximos Passos

1. **Implementar cache** de permissões para melhor performance
2. **Adicionar auditoria** de acessos
3. **Criar interface** para gerenciar permissões
4. **Implementar permissões granulares** (por ação)
5. **Adicionar notificações** de acesso negado 