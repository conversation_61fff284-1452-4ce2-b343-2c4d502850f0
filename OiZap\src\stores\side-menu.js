import { defineStore } from 'pinia';

export const useSideMenuStore = defineStore('sideMenu', {
  state: () => ({
    menu: [
      {
        icon: 'HomeIcon',
        pageName: 'side-menu-page-1',
        title: 'Page 1',
      },
      {
        icon: 'HomeIcon',
        pageName: 'side-menu-page-2',
        title: 'Page 2',
      },
      // Itens administrativos
      {
        icon: 'SettingsIcon',
        pageName: 'admin-cadPerfil',
        title: 'Cadastro de Perfil',
        to: '/cadPerfil',
      },
      {
        icon: 'SettingsIcon',
        pageName: 'admin-cadTela',
        title: 'Cadastro de Tela',
        to: '/cadTela',
      },
      {
        icon: 'SettingsIcon',
        pageName: 'admin-cadPerfisTelas',
        title: 'Associação Perfis x Telas',
        to: '/cadPerfisTelas',
      },
    ],
  }),
});
