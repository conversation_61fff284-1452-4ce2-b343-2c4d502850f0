/* eslint-disable camelcase */

exports.shorthands = undefined;

/**
 * TEMPLATE DE MIGRATION - node-pg-migrate
 *
 * DUAS FORMAS DE TRABALHAR:
 * 1. SQL PURO (recomendado para operações complexas)
 * 2. SCHEMA BUILDER (parecido com Knex)
 */

// =========================================
// FORMA 1: SCHEMA BUILDER (parecido com Knex)
// =========================================

exports.up = (pgm) => {
  // Criar tabela
  pgm.createTable('usuarios', {
    id: 'id',
    nome: { type: 'varchar(100)', notNull: true },
    email: { type: 'varchar(255)', notNull: true, unique: true },
    senha: { type: 'varchar(255)', notNull: true },
    ativo: { type: 'boolean', default: true },
    created_at: { type: 'timestamp', default: pgm.func('current_timestamp') },
    updated_at: { type: 'timestamp', default: pgm.func('current_timestamp') },
  });

  // Criar índices
  pgm.createIndex('usuarios', 'email');
  pgm.createIndex('usuarios', 'ativo');

  // Adicionar comentários
  pgm.addComment('usuarios', 'Tabela de usuários do sistema');
  pgm.addComment('usuarios', 'email', 'Email único do usuário');
};

exports.down = (pgm) => {
  pgm.dropTable('usuarios');
};

// =========================================
// FORMA 2: SQL PURO (mais flexível)
// =========================================

exports.up = (pgm) => {
  pgm.sql(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'usuarios') THEN
        CREATE TABLE usuarios (
          id SERIAL PRIMARY KEY,
          nome VARCHAR(100) NOT NULL,
          email VARCHAR(255) NOT NULL UNIQUE,
          senha VARCHAR(255) NOT NULL,
          ativo BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS usuarios_email_idx ON usuarios(email);
        CREATE INDEX IF NOT EXISTS usuarios_ativo_idx ON usuarios(ativo);
        
        COMMENT ON TABLE usuarios IS 'Tabela de usuários do sistema';
        COMMENT ON COLUMN usuarios.email IS 'Email único do usuário';
        
        RAISE NOTICE 'Tabela usuarios criada com sucesso';
      ELSE
        RAISE NOTICE 'Tabela usuarios já existe, pulando criação';
      END IF;
    END
    $$;
  `);
};

exports.down = (pgm) => {
  pgm.sql('DROP TABLE IF EXISTS usuarios CASCADE;');
};

// =========================================
// EXEMPLOS PRÁTICOS SCHEMA BUILDER
// =========================================

// ✅ CRIAR TABELA
exports.up = (pgm) => {
  pgm.createTable('produtos', {
    id: 'id',
    nome: { type: 'varchar(200)', notNull: true },
    preco: { type: 'decimal(10,2)', notNull: true },
    categoria_id: {
      type: 'integer',
      references: 'categorias(id)',
      onDelete: 'CASCADE',
    },
    ativo: { type: 'boolean', default: true },
  });
};

// ✅ ADICIONAR COLUNA
exports.up = (pgm) => {
  pgm.addColumn('produtos', {
    descricao: { type: 'text' },
    estoque: { type: 'integer', default: 0 },
  });
};

// ✅ CRIAR ÍNDICE
exports.up = (pgm) => {
  pgm.createIndex('produtos', 'categoria_id');
  pgm.createIndex('produtos', ['nome', 'ativo']); // índice composto
};

// ✅ ADICIONAR CONSTRAINT
exports.up = (pgm) => {
  pgm.addConstraint('produtos', 'produtos_preco_check', 'CHECK (preco > 0)');
};

// ✅ ALTERAR COLUNA
exports.up = (pgm) => {
  pgm.alterColumn('produtos', 'nome', { type: 'varchar(300)' });
  pgm.alterColumn('produtos', 'preco', { notNull: true });
};

// =========================================
// VANTAGENS DE CADA MÉTODO
// =========================================

/*
SCHEMA BUILDER:
✅ Mais limpo e legível
✅ Parecido com Knex
✅ Suporte a rollback automático
✅ Validação de sintaxe

SQL PURO:
✅ Mais flexível
✅ Suporte a IF NOT EXISTS
✅ Melhor para operações complexas
✅ Mensagens de log customizadas
*/
