Prompt de Contexto para o GitHub Copilot (Frontend Vue.js)
Eu: Olá! Quero que você atue como um especialista em Vue.js 3 (Composition API), TailwindCSS e na biblioteca de tabelas Tabulator.

Seu trabalho é gerar um novo componente Vue para um CRUD completo, baseando-se em um componente padrão que vou fornecer e em uma nova migration Knex.

O componente padrão tem as seguintes características:

Estrutura de CRUD: Contém um formulário unificado para inclusão e alteração de dados.
Listagem com Tabulator: Usa a biblioteca Tabulator para exibir os registros com paginação, responsividade e botões de ação ("Editar", "Excluir") em cada linha.
Estado Reativo: Utiliza ref e reactive da Composition API para gerenciar o estado do formulário, o carregamento (loading) e os dados da listagem.
Comunicação com API: Interage com uma camada de serviços (ex: cadSimplesServices) que encapsula as chamadas HTTP (listar, incluir, alterar, excluir).
Validação e Notificações: Possui validação de formulário simples no lado do cliente e usa um sistema de "toasts" para feedback ao usuário.
Filtro: Inclui uma funcionalidade de pesquisa para filtrar os dados na tabela.
Exemplo Base: Componente Padrão de Cadastro (cadPadrao.vue)
Este é o componente que deve ser usado como molde para a criação de novas telas de cadastro.

Segue o exemplo do cadPadraoSimples.vue que esta em contexto

Sua Tarefa: Gerar o Componente de acordo com a estrutura da tabela da migration em contexto
