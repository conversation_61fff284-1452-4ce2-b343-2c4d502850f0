import axios from 'axios';
import hosts from '@/utils/hosts';

const api = axios.create({
  baseURL: hosts.apiOiZap, // URL base da API OiZap
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(function (config) {
  var token = localStorage.getItem('token');
  config.headers.Authorization = token ? `Bearer ${token}` : '';
  return config;
});

export async function Api(method, url, headers, params, data) {
  try {
    const authToken = headers['authorization'];
    const token = authToken.split(' ')[1];

    if (token == 'undefined') return { statuscode: 500, message: 'Hash é obrigatório', data: [headers] };

    const result = await axios({
      url: url,
      method: method,
      headers: headers,
      params: params,
      data: data,
    });
    console.log('result at line 10 in whatsapp/WhatsAppServices.js:', result.data);

    const dataRes = result.data.data;
    const dataErrors = result.data.errors;

    return { statuscode: result.data.statuscode, message: result.data.message, data: dataRes, errors: dataErrors };
  } catch (error) {
    console.log('error at line 24 in utils/api.js:', error);
    if (error.response && error.response.data && error.response.data.statuscode) {
      return { statuscode: 500, message: 'ERRO', data: error.response.data };
    } else if (error.response && error.response.data) {
      return { statuscode: 500, message: 'ERRO', data: error.response.data };
    } else if (error.code && error.code == 'ECONNREFUSED') {
      return { statuscode: 500, message: 'ERRO', data: error };
    } else {
      return { statuscode: 500, message: 'ERRO', data: error };
    }
  }
}

export default api;
