-- Migration: add_tp_status_crm_messages
-- Created: 2025-06-20T02:26:10.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna tp_status_crm na tabela messages
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'tp_status_crm'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN tp_status_crm VARCHAR(30);
        COMMENT ON COLUMN messages.tp_status_crm IS 'Status da mensagem no CRM';
        RAISE NOTICE 'Coluna tp_status_crm adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna tp_status_crm já existe na tabela messages';
    END IF;
END $$; 