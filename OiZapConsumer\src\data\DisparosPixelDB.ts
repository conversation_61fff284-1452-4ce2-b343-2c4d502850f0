import { PostgreSQLServices, OperationObject } from '../services/PostgreSQLServices';

export class DisparosPixelDB {
  static async criarDisparo(data: any) {
    const opDb: OperationObject = {
      operacao: 'insert',
      tabelas: ['disparos_pixel'],
      dados: data,
      retorno: '*',
    };
    return await new PostgreSQLServices().executar(opDb);
  }

  static async findByHash(hash: string) {
    const xSQL = `select * from disparos_pixel where nr_hash = '${hash}' limit 1`;
    const result = await new PostgreSQLServices().query(xSQL);
    return result.data && result.data[0];
  }

  static async criarClique(data: any) {
    const opDb: OperationObject = {
      operacao: 'insert',
      tabelas: ['cliques'],
      dados: data,
      retorno: '*',
    };
    return await new PostgreSQLServices().executar(opDb);
  }

  static async criarLead(data: any) {
    const opDb: OperationObject = {
      operacao: 'insert',
      tabelas: ['leads'],
      dados: data,
      retorno: '*',
    };
    return await new PostgreSQLServices().executar(opDb);
  }

  static async listarDisparos() {
    const xSQL = `select * from disparos_pixel order by dt_cadastro desc`;
    const result = await new PostgreSQLServices().query(xSQL);
    return result.data;
  }
} 