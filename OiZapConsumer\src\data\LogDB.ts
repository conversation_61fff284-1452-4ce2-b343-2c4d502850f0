import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import { PostgreSQLServices } from '../services/PostgreSQLServices';
export class LogDB {
  static async incluir(logMessage: string, level?: string): Promise<IRetorno> {
    try {
      let ambiente = '';
      if (process.env.AMBIENTE != 'PROD') ambiente = 'sandbox-';

      const query = `
    INSERT INTO logs (message, level,system, created_at) 
    VALUES ('${logMessage}', '${level}','${ambiente}consumer', NOW())
  `;

      //console.log(opDb);
      return await new PostgreSQLServices().query(query);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
