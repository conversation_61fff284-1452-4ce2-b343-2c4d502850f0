# Script PowerShell para configurar perfis, telas e associações no OiZap
# Execute este script após o backend estar rodando

Write-Host "🚀 Configurando perfis, telas e associações no OiZap..." -ForegroundColor Green

# URL base da API
$API_URL = "http://localhost:3100"

Write-Host "📋 1. Criando perfis..." -ForegroundColor Yellow

# Criar perfil Administrador
Write-Host "   - Criando perfil Administrador..." -ForegroundColor Cyan
$ADMIN_RESPONSE = Invoke-RestMethod -Uri "$API_URL/perfis" -Method POST -ContentType "application/json" -Body '{"nm_perfil": "Administrador"}'
Write-Host "   Resposta: $($ADMIN_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

# Criar perfil Atendente
Write-Host "   - Criando perfil Atendente..." -ForegroundColor Cyan
$ATENDENTE_RESPONSE = Invoke-RestMethod -Uri "$API_URL/perfis" -Method POST -ContentType "application/json" -Body '{"nm_perfil": "Atendente"}'
Write-Host "   Resposta: $($ATENDENTE_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

# Criar perfil Gerente
Write-Host "   - Criando perfil Gerente..." -ForegroundColor Cyan
$GERENTE_RESPONSE = Invoke-RestMethod -Uri "$API_URL/perfis" -Method POST -ContentType "application/json" -Body '{"nm_perfil": "Gerente"}'
Write-Host "   Resposta: $($GERENTE_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "📋 2. Criando telas administrativas..." -ForegroundColor Yellow

# Criar tela de cadastro de perfil
Write-Host "   - Criando tela Cadastro de Perfil..." -ForegroundColor Cyan
$TELA_PERFIL_RESPONSE = Invoke-RestMethod -Uri "$API_URL/telas" -Method POST -ContentType "application/json" -Body '{"nm_tela": "Cadastro de Perfil", "ds_rota": "/cadPerfil"}'
Write-Host "   Resposta: $($TELA_PERFIL_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

# Criar tela de cadastro de tela
Write-Host "   - Criando tela Cadastro de Tela..." -ForegroundColor Cyan
$TELA_TELA_RESPONSE = Invoke-RestMethod -Uri "$API_URL/telas" -Method POST -ContentType "application/json" -Body '{"nm_tela": "Cadastro de Tela", "ds_rota": "/cadTela"}'
Write-Host "   Resposta: $($TELA_TELA_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

# Criar tela de associação perfis x telas
Write-Host "   - Criando tela Associação Perfis x Telas..." -ForegroundColor Cyan
$TELA_ASSOCIACAO_RESPONSE = Invoke-RestMethod -Uri "$API_URL/telas" -Method POST -ContentType "application/json" -Body '{"nm_tela": "Associação Perfis x Telas", "ds_rota": "/cadPerfisTelas"}'
Write-Host "   Resposta: $($TELA_ASSOCIACAO_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "📋 3. Listando perfis criados..." -ForegroundColor Yellow
$PERFIS = Invoke-RestMethod -Uri "$API_URL/perfis" -Method GET
Write-Host "   Perfis: $($PERFIS | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "📋 4. Listando telas criadas..." -ForegroundColor Yellow
$TELAS = Invoke-RestMethod -Uri "$API_URL/telas" -Method GET
Write-Host "   Telas: $($TELAS | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "📋 5. Associando usuário 87 ao perfil Administrador..." -ForegroundColor Yellow
# Assumindo que o perfil Administrador tem cd_perfil = 1
$USUARIO_PERFIL_RESPONSE = Invoke-RestMethod -Uri "$API_URL/usuarios/87/perfis" -Method POST -ContentType "application/json" -Body '{"cd_perfil": 1}'
Write-Host "   Resposta: $($USUARIO_PERFIL_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "📋 6. Associando telas administrativas ao perfil Administrador..." -ForegroundColor Yellow
# Assumindo que as telas têm cd_tela = 1, 2, 3
$PERFIL_TELAS_RESPONSE = Invoke-RestMethod -Uri "$API_URL/perfis/associar" -Method POST -ContentType "application/json" -Body '{"cd_perfil": 1, "telas": [1, 2, 3]}'
Write-Host "   Resposta: $($PERFIL_TELAS_RESPONSE | ConvertTo-Json)" -ForegroundColor Gray

Write-Host "✅ Configuração concluída!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Resumo do que foi criado:" -ForegroundColor White
Write-Host "   - 3 perfis: Administrador, Atendente, Gerente" -ForegroundColor White
Write-Host "   - 3 telas administrativas: /cadPerfil, /cadTela, /cadPerfisTelas" -ForegroundColor White
Write-Host "   - Usuário 87 associado ao perfil Administrador" -ForegroundColor White
Write-Host "   - Telas administrativas associadas ao perfil Administrador" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Agora você pode acessar:" -ForegroundColor White
Write-Host "   - http://localhost:8080/cadPerfil" -ForegroundColor Cyan
Write-Host "   - http://localhost:8080/cadTela" -ForegroundColor Cyan
Write-Host "   - http://localhost:8080/cadPerfisTelas" -ForegroundColor Cyan

Read-Host "Pressione Enter para continuar..." 