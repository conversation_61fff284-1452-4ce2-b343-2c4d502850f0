-- Migration: adicionar_coluna_nova_na_tabela_xpto
-- Created: 2025-01-07T00:00:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

-- Verificar se a tabela existe antes de adicionar a coluna
DO $$
BEGIN
    -- Verificar se a tabela 'xpto' existe
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'xpto' 
        AND table_schema = 'public'
    ) THEN
        
        -- Adicionar coluna 'nova_coluna' se não existir
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'xpto' 
            AND column_name = 'nova_coluna'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE xpto ADD COLUMN nova_coluna VARCHAR(100);
            RAISE NOTICE 'Coluna nova_coluna adicionada à tabela xpto';
        ELSE
            RAISE NOTICE 'Coluna nova_coluna já existe na tabela xpto';
        END IF;
        
    ELSE
        RAISE NOTICE 'Tabela xpto não existe - criando tabela completa...';
        
        -- Criar tabela completa se não existir
        CREATE TABLE xpto (
            id SERIAL PRIMARY KEY,
            nome VARCHAR(100) NOT NULL,
            nova_coluna VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Criar índice se necessário
        CREATE INDEX IF NOT EXISTS idx_xpto_nome ON xpto(nome);
        
        RAISE NOTICE 'Tabela xpto criada com sucesso';
    END IF;
END $$; 