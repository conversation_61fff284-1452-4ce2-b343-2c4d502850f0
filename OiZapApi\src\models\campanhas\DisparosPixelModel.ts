import crypto from 'crypto';
import { Request } from 'express';
import { DisparosPixelDB } from '../../data/campanhas/DisparosPixelDB';
import { BAD_REQUEST, erroInterno, sucesso } from '../../interfaces/IRetorno';

export class DisparosPixelModel {
  async criarDisparo(req: Request) {
    try {
      const { ds_disparo, tp_campanha, whatsapp_number, in_ativo } = req.body;
      if (!ds_disparo) {
        return {
          statuscode: BAD_REQUEST,
          message: 'O campo ds_disparo é obrigatório.',
          data: [],
          errors: ['O campo ds_disparo é obrigatório.'],
        };
      }
      const nr_hash = crypto.randomBytes(16).toString('hex');
      const baseUrl = process.env.BASE_URL;
      const ds_url = `${baseUrl}/cliques-pixel/v1?cd_disparo=${nr_hash}&utm_source=${tp_campanha}`;
      const data = {
        ds_disparo,
        ds_url,
        nr_hash,
        in_ativo: in_ativo,
        cd_usucad: null,
        whatsapp_number: whatsapp_number,
        dt_cadastro: new Date(),
      };
      const result = await DisparosPixelDB.criarDisparo(data);
      if (result.statuscode === 200 && result.data && result.data[0]) {
        // Adiciona o link ao retorno
        return { ...result, data: { disparo: result.data[0], link: ds_url } };
      }
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  // async capturarClique(req: Request, res: Response) {
  //   try {
  //     const { cd_disparo, utm_source } = req.query;
  //     const disparo = await DisparosPixelDB.findByHash(cd_disparo as string);
  //     if (!disparo) {
  //       return res.status(404).send('Disparo não encontrado');
  //     }
  //     await DisparosPixelDB.criarClique({
  //       cd_disparo: disparo.cd_disparo,
  //       utm_source: utm_source || 'utm_source_not_found',
  //       data_clique: new Date(),
  //     });
  //     //obter whatsapp number from DisparoPixelDB.listarDisparo
  //     const whatsappNumber = disparo.whatsapp_number;

  //     const mensagem = encodeURIComponent(`Quero saber mais sobre ${disparo.ds_disparo}! ID: ${disparo.cd_disparo}`);
  //     return res.redirect(`https://wa.me/${whatsappNumber}?text=${mensagem}`);
  //   } catch (error: any) {
  //     return res.status(500).send('Erro ao registrar clique');
  //   }
  // }

  // async webhookWhatsapp(req: Request, res: Response) {
  //   try {
  //     const { event, data } = req.body;
  //     if (event === 'MESSAGE_RECEIVED') {
  //       const { from, body } = data;
  //       const match = body.match(/ID: (\d+)/);
  //       const campanha_id = match ? match[1] : null;
  //       await DisparosPixelDB.criarLead({
  //         cd_disparo: campanha_id ? parseInt(campanha_id) : null,
  //         numero: from,
  //         mensagem: body,
  //         data_mensagem: new Date(),
  //       });
  //     }
  //     return res.status(200).send('OK');
  //   } catch (error: any) {
  //     return res.status(500).send('Erro no webhook');
  //   }
  // }

  async listarDisparos(req: Request) {
    try {
      const result = await DisparosPixelDB.listarDisparos();
      return sucesso(result);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
