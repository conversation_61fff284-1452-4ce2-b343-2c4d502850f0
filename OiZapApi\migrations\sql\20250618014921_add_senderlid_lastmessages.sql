-- Migration: add_senderlid_lastmessages
-- Created: 2025-06-18T01:49:21.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna sender_lid na tabela last_message
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'last_message' 
        AND column_name = 'sender_lid'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE last_message ADD COLUMN sender_lid VARCHAR(100);
        COMMENT ON COLUMN last_message.sender_lid IS 'ID do remetente da mensagem';
        RAISE NOTICE 'Coluna sender_lid adicionada à tabela last_message';
    ELSE
        RAISE NOTICE 'Coluna sender_lid já existe na tabela last_message';
    END IF;
END $$; 