const utils = require('./util');
const Sftp = require('./sftpService');
require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs-extra'); // fs-extra já está sendo importado

async function start() {
  const versao = process.argv[2] || 'patch';

  // Leia a versão atual
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf-8')); // Use fs.readFileSync

  // Verifica o servidor e lê a versão correta
  const [major, minor, patch] = pkg.version.split('.').map(Number);

  // Calcule a nova versão
  let newVersion;
  let tipo;
  if (versao == 'major') {
    newVersion = `${major + 1}.0.0`;
    tipo = 'Changes';
  } else if (versao == 'minor') {
    newVersion = `${major}.${minor + 1}.0`;
    tipo = 'Features';
  } else if (versao == 'patch') {
    newVersion = `${major}.${minor}.${patch + 1}`;
    tipo = 'Fixes';
  } else {
    console.error('Tipo de versão inválido. Use "major", "minor" ou "patch".');
  }
  console.log('newVersion :', newVersion);

  pkg.version = newVersion;

  fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
  const { data } = await utils.executar(`git rev-parse --abbrev-ref HEAD`);
  let resultGit = await utils.executar(`git c "compilação da versão ${newVersion}"`);
  resultGit = await utils.executar(`git push backup ${data[0]}`);
  if (resultGit.statuscode != 200) {
    console.log(resultGit.message);
    return;
  }

  //Salva no SandBox
  const message = process.argv[2];
  let client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASEDEV,
    database: process.env.DATABASE_SANDBOX,
    password: process.env.PASSWORD_DATABASEDEV,
    port: process.env.PORT_DATABASEDEV,
  });
  client.connect();
  await client.query(
    `update commits set versao = '${newVersion}',tipo='${tipo}', data_versao = current_timestamp where sistema = 'Consumer' and  versao is null `,
  );
  client.end();

  //Salva na crm
  client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASE,
    database: process.env.DATABASE_CRM,
    password: process.env.PASSWORD_DATABASE,
    port: process.env.PORT_DATABASE,
  });
  client.connect();
  await client.query(
    `update commits set versao = '${newVersion}',tipo='${tipo}', data_versao = current_timestamp where sistema = 'Consumer' and  versao is null `,
  );
  client.end();

  //Salva na Produção
  client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASE,
    database: process.env.DATABASE,
    password: process.env.PASSWORD_DATABASE,
    port: process.env.PORT_DATABASE,
  });
  client.connect();
  await client.query(
    `update commits set versao = '${newVersion}',tipo='${tipo}', data_versao = current_timestamp where sistema = 'Consumer' and  versao is null `,
  );
  client.end();

  console.log('Nova versão ' + newVersion + ' gerada com sucesso!');
}

start();
