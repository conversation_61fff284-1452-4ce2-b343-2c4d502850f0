import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel } from 'amqplib';
import Logger from '../../Logger';
const logger = Logger.getLogger();

export default class RabbitMQConnectionEvolution {
  private static instance: RabbitMQConnectionEvolution;
  private connection: any = null;

  private constructor() {}

  public static getInstance(): RabbitMQConnectionEvolution {
    if (!RabbitMQConnectionEvolution.instance) {
      RabbitMQConnectionEvolution.instance = new RabbitMQConnectionEvolution();
    }
    return RabbitMQConnectionEvolution.instance;
  }

  public async connect(): Promise<Channel> {
    try {
      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }
      if (!this.connection) {
        this.connection = await amqp.connect(HOST_RABBITMQ);
        logger.info('RabbitMQ Evolution Conectado 🚀');
      }
      return this.connection;
    } catch (error) {
      console.log('error at line 34 in Evolution/RabbitMQConnectionEvolution.ts:', error);
      logger.error('Error connecting to RabbitMQ Evolution:' + JSON.stringify(error));
      throw error;
    }
  }

  public async close() {
    if (this.connection) {
      await this.connection.close();
      console.log('RabbitMQ Evolution connection closed');
      this.connection = null;
    }
  }
}
