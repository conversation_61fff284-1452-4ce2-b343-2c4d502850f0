require('dotenv').config();
import { erroInterno, IRetorno } from '../interfaces/IRetorno';
import { PostgreSQLServices } from '../services/PostgreSQLServices';
export class DepartamentosDB {
  static async listar(req: any): Promise<IRetorno> {
    try {
      let xSQL = `select * from departamentos where 1=1`;
      if (req.ds_departamento) {
        xSQL += ` and ds_departamento like '%${req.ds_departamento}%'`;
      }
      if (req.cd_departamento) {
        xSQL += ` and cd_departamento = ${req.cd_departamento}`;
      }
      xSQL += ` order by ds_departamento`;
      return await new PostgreSQLServices().query(xSQL);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
