// Script para debugar as rotas da API
const express = require('express');
const app = express();

// Simular o que está acontecendo na API
app.use(express.json());

// Rota raiz
app.get('/oizap', (req, res) => {
  res.send('<h1>Oi Zap Api DEV</h1>');
});

// Teste de rota simples
app.get('/oizap/teste', (req, res) => {
  res.json({ message: 'Rota teste funcionando' });
});

// Teste de rota de perfis
app.get('/oizap/perfis', (req, res) => {
  res.json({ message: 'Rota perfis funcionando', data: [] });
});

app.post('/oizap/perfis', (req, res) => {
  res.json({ message: 'Perfil criado', data: req.body });
});

// Middleware de erro
app.use((req, res) => {
  res.status(404).json({ 
    statuscode: 404, 
    message: 'Endpoint não encontrado', 
    data: [],
    path: req.path,
    method: req.method
  });
});

const port = 3101;
app.listen(port, () => {
  console.log(`🚀 Servidor de teste rodando na porta ${port}`);
  console.log('Teste as rotas:');
  console.log('- GET http://localhost:3101/oizap');
  console.log('- GET http://localhost:3101/oizap/teste');
  console.log('- GET http://localhost:3101/oizap/perfis');
  console.log('- POST http://localhost:3101/oizap/perfis');
}); 