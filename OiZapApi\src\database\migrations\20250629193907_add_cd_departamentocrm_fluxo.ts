import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('fluxo_atendimento migration started');
    let hasColumn = await knex.schema.hasColumn('fluxo_atendimento', 'cd_departamento');

    if (!hasColumn) {
      await knex.schema.alterTable('fluxo_atendimento', (table) => {
        table.integer('cd_departamento').comment('Vinculada ao departamento CRM');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('fluxo_atendimento', (table) => {
      table.dropColumn('cd_departamento');
    });
  } catch (error) {
    throw error;
  }
}
