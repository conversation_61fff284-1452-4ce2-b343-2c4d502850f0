import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('adm_grupos migration started');
    let hasColumn = await knex.schema.hasColumn('adm_grupos', 'cd_estabelecimento');

    if (!hasColumn) {
      await knex.schema.alterTable('adm_grupos', (table) => {
        table.integer('cd_estabelecimento');
      });
    }
    hasColumn = await knex.schema.hasColumn('adm_grupotelas', 'cd_estabelecimento');

    if (!hasColumn) {
      await knex.schema.alterTable('adm_grupotelas', (table) => {
        table.integer('cd_estabelecimento');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('adm_grupos', (table) => {
      table.dropColumn('cd_estabelecimento');
    });
    await knex.schema.alterTable('adm_grupotelas', (table) => {
      table.dropColumn('cd_estabelecimento');
    });
  } catch (error) {
    throw error;
  }
}
