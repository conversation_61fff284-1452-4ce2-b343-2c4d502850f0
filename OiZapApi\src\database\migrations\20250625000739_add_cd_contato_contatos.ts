import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('contacts migration started');
    let hasColumn = await knex.schema.hasColumn('contacts', 'cd_contato');

    if (!hasColumn) {
      await knex.schema.alterTable('contacts', (table) => {
        table.increments('cd_contato').primary();
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('contacts', (table) => {
      table.dropColumn('cd_contato');
    });
  } catch (error) {
    throw error;
  }
}
