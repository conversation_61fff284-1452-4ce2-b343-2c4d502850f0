import { config } from 'dotenv';
import { Knex } from 'knex';
import { join } from 'path';
config();

// Função para obter __dirname em ambientes ES modules e CommonJS
const getDirname = (): string => {
  if (typeof __dirname !== 'undefined') {
    // CommonJS
    return __dirname;
  } else {
    // Fallback para quando __dirname não está disponível
    // Usa o diretório atual do processo
    return process.cwd() + '/config';
  }
};

const currentDir = getDirname();
const isDev = process.env.NODE_ENV === 'PROD' ? false : true;

// Ajusta o caminho das migrations baseado no ambiente
const getMigrationsPath = () => {
  // Em desenvolvimento, usa o caminho src
  if (isDev) {
    return join(currentDir, '..', 'database', 'migrations');
  }
  // Em produção/sandbox, usa o caminho dist
  return join(currentDir, '..', '..', 'database', 'migrations');
};

const baseConfig: Knex.Config = {
  client: 'pg',
  migrations: {
    directory: getMigrationsPath(),
    extension: isDev ? 'ts' : 'js',
  },
  pool: {
    min: 1,
    max: 5,
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
  },
  acquireConnectionTimeout: 60000,
};

interface KnexConfig {
  [key: string]: Knex.Config;
}

const configurations: KnexConfig = {
  DEV: {
    ...baseConfig,
    connection: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASEDEV),
    },
  },
  CRM: {
    ...baseConfig,
    connection: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
  },
  SANDBOX: {
    ...baseConfig,
    connection: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
  },
  PROD: {
    ...baseConfig,
    connection: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  },
};

// Exporta a configuração baseada no NODE_ENV
const environment = process.env.NODE_ENV || 'DEV';

export default configurations[environment as keyof typeof configurations];
