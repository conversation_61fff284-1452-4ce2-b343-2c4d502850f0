require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';
export class UsuariosEstabelecimentosDB {
  static async incluirUsuariosEstabelecimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['usuarios_estabelecimentos'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarUsuariosEstabelecimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['usuarios_estabelecimentos'],
        chaves: {
          cd_usuario: req.body.cd_usuario,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarUsuariosEstabelecimentos(req: Request): Promise<IRetorno> {
    try {
      // const opDb: OperationObject = {
      //   operacao: 'select',
      //   tabelas: ['usuarios_estabelecimentos'],
      //   chaves: req.query,
      //   retorno: '*',
      // };
      // const result = await new PostgreSQLServices().executar(opDb);
      let sql = `select a.nr_conrole,a.cd_usuario,a.cd_estabelecimento,b.nr_hash
      ,b.nm_estabelecimento,b.tp_calculo,b.ds_cidade,b.ds_uf 
from usuarios_estabelecimentos a
left join estabelecimento b on b.cd_estabelecimento = a.cd_estabelecimento where 1=1 `;
      if (req.query.cd_usuario != undefined) {
        sql += ` and a.cd_usuario = ${req.query.cd_usuario}`;
      }
      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and a.cd_estabelecimento = ${req.query.cd_estabelecimento}`;
      }
      //  console.log('sql at line 56 in usuarios/UsuarioEstabelecimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      // console.log('🚀 ~ UsuarioEstabelecimentosDB.ts:58 ~ listarUsuariosEstabelecimentos ~ result:', result);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async removerUsuariosEstabelecimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['usuarios_estabelecimentos'],
        chaves: {
          cd_usuario: req.body.cd_usuario,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
