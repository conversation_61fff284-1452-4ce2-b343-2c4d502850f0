<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />
    <div class="box p-4">
        <!-- <div class="intro-y flex flex-row items-center mt-1.5 sm:mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Usuários</h2>
        </div> -->

        <!-- Header <PERSON> seção de módulos -->
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <Building2Icon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Usuários</h3>
                        <p class="text-blue-100 text-sm">Usuários cadastrados no sistema.</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="chamaCadUsuario('')"
                    >
                        <PlusIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Novo Usuário</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- BEGIN: HTML Table Data -->
        <div class="intro-y box p-4">
            <!-- <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-4 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                       
                        <h2 class="text-lg font-semibold">Lista</h2>
                    </div>
                    <p class="text-md text-slate-600 dark:text-slate-400">
                        Gerencie os usuários do sistema, adicione, edite ou remova usuários conforme necessário.
                    </p>
                </div>
                <div class="cursos-pointer flex items-center w-full sm:w-fit gap-2">

                    <button class="btn btn-primary-soft shadow-md mr-2" @click="chamaCadUsuario('')">
                        <PlusIcon class="w-4 h-4 mr-1" style="stroke-width: 3" />
                        <span class="text-ellipsis whitespace-nowrap overflow-hidden">Novo Usuário</span>
                    </button>
                </div>
            </div> -->

            <!-- BEGIN: Campos -->
            <div class="w-full">
                <form
                    id="tabulator-html-filter-form"
                    class="flex flex-col sm:flex-row justify-start sm:items-end w-full gap-4"
                    @submit.prevent="onFilter"
                >
                    <div class="w-full sm:w-[300px] max-w-full">
                        <label class="w-12 flex-none xl:w-auto xl:flex-initial mr-2 text-slate-500">Pesquisar</label>
                        <div class="flex items-center shadow-md rounded-md sm:w-full sm:max-w-[310px]">
                            <select
                                id="tabulator-html-filter-field"
                                v-model="filter.field"
                                class="form-select w-min border-r-0 rounded-r-none cursor-pointer shadow-none focus:z-10"
                                @change="
                                    () => {
                                        inputFilterValue.focus();
                                    }
                                "
                            >
                                <option value="ds_login">Login</option>
                                <option value="ds_nome" selected>Nome</option>
                            </select>
                            <input
                                ref="inputFilterValue"
                                id="tabulator-html-filter-value"
                                v-model="filter.value"
                                type="text"
                                class="form-control border-l-0 rounded-l-none shadow-none min-w-28 sm:w-full"
                            />
                        </div>
                    </div>
                    <div class="flex items-center justify-start gap-2">
                        <button id="tabulator-html-filter-go" type="submit" class="btn btn-success-soft w-full sm:w-16">
                            Buscar
                        </button>
                        <button
                            id="tabulator-html-filter-reset"
                            type="reset"
                            class="btn btn-warning-soft w-full sm:w-16 sm:mt-0 sm:ml-1"
                            @click="onResetFilter"
                        >
                            Limpar
                        </button>
                    </div>
                </form>
            </div>
            <!-- END: Campos -->

            <div
                class="overflow-x-auto scrollbar-hidden border-l border-r border-b border-slate-200/60 rounded-lg mt-5 mb-2"
            >
                <div id="tabulator" class="table-report table-report--tabulator mb-2"></div>
            </div>
        </div>
    </div>
    <!-- END: HTML Table Data -->
</template>

<script setup>
    import { ref, reactive, onMounted, watch } from 'vue';
    import { useRouter } from 'vue-router';
    import { createIcons, icons } from 'lucide';
    //  import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import dom from '@left4code/tw-starter/dist/js/dom';
    import UsuariosServices from '@/services/administracao/UsuariosServices';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();
    import { usePagina } from '@/stores/pagina';
    let pagina = usePagina();

    const router = useRouter();
    const inputFilterValue = ref(null);
    const modulos = reactive({
        in_chat_pedidos: false,
        in_api: false,
        in_crm: false,
        qt_instancias: 0,
        qt_usuarios: 0,
    });
    const loading = ref(null);
    let usuarios = ref([]);
    let estabelecimentosLiberado = ref();
    const tabulator = ref();
    const filter = reactive({
        field: 'ds_nome',
        type: 'like',
        value: '',
    });

    const columns = [
        // {
        //   title: 'Código',
        //   minWidth: 20,
        //   responsive: 0,
        //   field: 'cd_usuario',
        //   hozAlign: 'left',
        //   headerHozAlign: 'left',
        //   vertAlign: 'middle',
        // },
        {
            title: 'Login',
            minWidth: 60,
            responsive: 0,
            field: 'ds_login',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Nome',
            minWidth: 150,
            field: 'ds_nome',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Privilégio',
            minWidth: 150,
            field: 'ds_privilegio',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Status',
            minWidth: 60,
            field: 'tp_status',
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                return `<div class="flex items-center lg:justify-center
           ${cell.getData().tp_status == 'A' ? 'text-success' : 'text-danger'}">
                ${cell.getData().tp_status == 'A' ? 'Ativo' : 'Inativo'}
              </div>`;
            },
        },
        {
            title: 'Ação',
            minWidth: 40,
            responsive: 1,
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                const a = dom(`
               <div class="flex lg:justify-center items-center">
                <a class="flex items-center mr-3" href="javascript:;">
                  <i data-lucide="check-square" class="w-4 h-4 mr-1"></i> Editar
                </a>
              </div>`);
                dom(a).on('click', function () {
                    // On click actions
                    chamaCadUsuario(cell.getData().cd_usuario);
                    //toast.showSuccessNotification("codigo="+);
                });

                return a[0];
            },
        },
    ];

    const reInitOnResizeWindow = () => {
        window.addEventListener('resize', () => {
            tabulator.value.redraw();
            createIcons({
                icons,
                'stroke-width': 1.5,
                nameAttr: 'data-lucide',
            });
        });
    };

    function chamaCadUsuario(cd_usuario) {
        if (!cd_usuario) {
            if (usuarios.value.length >= modulos.qt_usuarios) {
                toast.warning(`É permitido apenas ${modulos.qt_usuarios} usuário(s) por estabelecimento!`);
                return;
            }
        }
        //console.log('cadastroUsuario', cd_usuario);
        router.push({ name: 'cadUsuario', params: { codusuario: cd_usuario } });
    }

    function onFilter() {
        tabulator.value.setFilter(filter.field, filter.type, filter.value);
    }

    // On reset filter
    function onResetFilter() {
        filter.field = 'ds_nome';
        filter.type = 'like';
        filter.value = '';
        //tabulator.clearFilter();
        onFilter();
    }

    async function carregaUsuarios() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        //let loginUsuCad = await localStorage.getItem('login');
        loading.value.show();
        let tp_privilegio;
        if (localStorage.getItem('privilegio') == 'Administrador') {
            tp_privilegio = `'A','U'`;
        } else if (localStorage.getItem('privilegio') == 'Usuário') {
            tp_privilegio = `'U'`;
        } else {
            tp_privilegio = `'O'`;
        }

        let filtros = {
            //loginUsuCad: loginUsuCad,
            tp_privilegio: tp_privilegio,
            cd_estabelecimento:
                tp_privilegio == 'O'
                    ? undefined
                    : estabelecimentosLiberado.value
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };

        let respUsuarios;
        if (!filtros?.cd_estabelecimento) {
            respUsuarios = await UsuariosServices.listaUsuariosTodos(filtros);
        } else {
            respUsuarios = await UsuariosServices.listaUsuarios(filtros);
        }

        //console.log('carregaUsuarios', respUsuarios);

        if (respUsuarios.statuscode === 404) {
            toast.showErrorNotification(respUsuarios.message);
            loading.value.hide();
        } else if (respUsuarios.statuscode === 200) {
            usuarios.value = respUsuarios.data;

            tabulator.value = new Tabulator('#tabulator', {
                data: usuarios.value,
                //rowHeight: 25,
                layout: 'fitColumns',
                responsiveLayout: 'collapse',
                placeholder: 'Nenhum registro encontrado',
                columns: columns,
                pagination: 'local',
                paginationSize: 10,
                paginationSizeSelector: [10, 20, 50, 100],
            });

            tabulator.value.on('renderComplete', function () {
                createIcons({
                    icons,
                    'stroke-width': 1.5,
                    nameAttr: 'data-lucide',
                });
            });
            loading.value.hide();
        } else if (respUsuarios.statuscode === 401) {
            localStorage.removeItem('token');
            router.push({ path: `${hosts.app}` });
            toast.showErrorNotification(respUsuarios.message);
            loading.value.hide();
        } else {
            toast.showErrorNotification(respUsuarios.message);
            loading.value.hide();
        }
    }

    onMounted(async () => {
        pagina.pagename = 'Usuários';
        pagina.description = 'Usuários';
        pagina.module = 'Usuários';

        //console.log(localStorage.getItem('privilegio'));

        //estabelecimentosLiberado.value = JSON.parse(await localStorage.getItem('estabelecimentos'));
        //console.log('estabelecimentosLiberado.value at line 220:', estabelecimentosLiberado.value);

        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        //console.log('🚀 ~ listaInstancias.vue:882 ~ onMounted ~ respModulos:', respModulos);
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;
        modulos.qt_instancias = respModulos.qt_instancias;
        modulos.qt_usuarios = respModulos.qt_usuarios;

        const estatabelecimentos = localStorage.getItem('estabelecimentos');
        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);

            await carregaUsuarios();
        } else {
            if (localStorage.getItem('privilegio') == 'OiZap') {
                await carregaUsuarios();
            } else {
                toast.showWarningNotification('Estabelecimento não liberado!');
            }
        }
        reInitOnResizeWindow();
    });
</script>
