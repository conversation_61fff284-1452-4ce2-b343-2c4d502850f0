import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';
const host = `${hosts.api}/departamentos`;
const hostOiZap = `${hosts.apiOiZap}/departamentos`;

class DepartamentosServices {
    async listar(query) {
        try {
            const response = await callApi('get', `${hostOiZap}/v1`, query);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async incluir(body) {
        try {
            const response = await callApi('post', `${hostOiZap}/v1`, undefined, body);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async alterar(body) {
        try {
            const response = await callApi('put', `${hostOiZap}/v1`, undefined, body);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async excluir(body) {
        try {
            const response = await callApi('delete', `${hostOiZap}/v1`, undefined, body);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
}
export default new DepartamentosServices();
