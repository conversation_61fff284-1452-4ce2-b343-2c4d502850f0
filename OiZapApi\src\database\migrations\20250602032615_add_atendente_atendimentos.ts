import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    const hasColumn = await knex.schema.hasColumn('atendimentos', 'cd_atendente');

    if (!hasColumn) {
      await knex.schema.alterTable('atendimentos', (table) => {
        table.integer('cd_atendente').comment('Indica o atendente responsável pelo atendimento');
      });
    }

    // SEMPRE retorna - sem transação manual
    return;
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('atendimentos', (table) => {
      table.dropColumn('cd_atendente');
    });
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
