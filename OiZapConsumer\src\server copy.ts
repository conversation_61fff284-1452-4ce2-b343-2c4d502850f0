import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';

import bodyParser from 'body-parser';
import cors from 'cors';
import path from 'path';
import errorHandler from './middleware/errorHandler';
import LoggerMiddleware from './middleware/LoggerMiddleware';
import { RabbitMQRoutes } from './routes/RabbitMQRoutes';
import { WhatsAppRoutes } from './routes/WhatsAppRoutes';
import Logger from './services/Logger';
import NotifyPostgres from './services/NotifyPostgres';
import { connectRabbitMQ, disconnectRabbitMQ } from './services/rabbitmq/RabbitMQ';
import { setupSocketIO } from './services/Socket';

import dotenv, { config } from 'dotenv';
import { InstanceDB } from './data/InstanceDB';
import { connectedRabbitMQInstances } from './services/ConnectedRabbitMQInstances';
config();

// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

const logger = Logger.getLogger();
//import { setupSocketIO } from './socketio';

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer);

app.use(LoggerMiddleware.getCustomMorganMiddleware());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

app.use(cors());

const PORT = process.env.PORT || 3000;
let HOST_RABBITMQEVO: string = '';

let HOST_RABBITMQOIZAP = process.env.HOST_RABBITMQ_OIZAP as string;
if (process.env.AMBIENTE == 'PROD') {
  HOST_RABBITMQEVO = process.env.HOST_RABBITMQ as string;
} else if (process.env.AMBIENTE == 'CRM') {
  HOST_RABBITMQEVO = process.env.HOST_RABBITMQ as string;
} else {
  HOST_RABBITMQEVO = process.env.HOST_RABBITMQSANDBOX as string;
}
// console.log('HOST_RABBITMQEVO at line 51 in src/server.ts:', HOST_RABBITMQEVO);
// console.log('HOST_RABBITMQOIZAP at line 47 in src/server.ts:', HOST_RABBITMQOIZAP);

// Função para encerrar a API com segurança
function shutdown(signal: string) {
  logger.info(`Received ${signal}. Closing RabbitMQ connections and shutting down API gracefully...`);

  // Desconectar do RabbitMQ
  disconnectRabbitMQ()
    .then(() => {
      logger.info('All RabbitMQ connections closed.');
      process.exit(0); // Sair do processo após desconectar
    })
    .catch((error) => {
      logger.error(`Error during RabbitMQ disconnection: ${error.message}`);
      process.exit(1); // Sair com erro se falhar
    });
}

async function startServer() {
  try {
    //Notify para carregar uma nova instancia criada com a API executando.
    new NotifyPostgres('notification_instances', io).setupNotificationListener();
    //Notify para quando deletar uma instancia.
    new NotifyPostgres('notification_instances_deleted', io).deleteNotificationListener();

    /*Quando a instancia está criada, carrega todas ativas ao iniciar*/

    const respInstance = await InstanceDB.instances();
    //console.log('respInstance at line 81 in src/server.ts:', respInstance);

    if (respInstance?.statuscode === 200) {
      if (Array.isArray(respInstance.data)) {
        const instances = respInstance?.data.map((instance: any) => instance.nameinstance);
        //console.log('instances at line 47 in src/server.ts:', instances);

        // Adiciona as instâncias ao conjunto
        instances.forEach((instance) => connectedRabbitMQInstances.addInstance(instance));

        // Connect to both RabbitMQ servers
        await connectRabbitMQ('evolution', HOST_RABBITMQEVO);
        await connectRabbitMQ('oizap', HOST_RABBITMQOIZAP);
        // console.log('HOST_RABBITMQOIZAP at line 95 in src/server.ts:', HOST_RABBITMQOIZAP);
        setupSocketIO(io);
      }
    }

    /*Rotas*/
    //const rotaWhatsApp = require('./routes/WhatsApp');
    // const rotaMessage = require('./routes/Message.js');
    // const rotaChats = require('./routes/Chats.js');
    //const rotaRabbitMQ = require('./routes/RabbitMQ');

    const rotaPadrao = '/oizap';

    app.use(express.static(path.join(__dirname, 'public')));

    let midiaPath;
    if (process.env.AMBIENTE === 'DEV') {
      midiaPath = path.join(__dirname, '../midia');
    } else {
      midiaPath = path.join(__dirname, 'midia');
    }

    app.use(rotaPadrao + '/midia', express.static(midiaPath));

    app.use(rotaPadrao + '/whats', WhatsAppRoutes);
    app.use(rotaPadrao + '/', WhatsAppRoutes);
    // app.use(rotaPadrao + '/messages', rotaMessage);
    // app.use(rotaPadrao + '/chats', rotaChats);
    app.use(rotaPadrao + '/consumer', RabbitMQRoutes);

    app.get(rotaPadrao + '/', (req, res) => {
      res.send('<h1>Oi Zap Cosumer  ' + process.env.AMBIENTE + ' </h1>');
    });

    app.use((req, res, next) => {
      return res.status(404).send({ statuscode: 400, message: 'Rota não encontrada', data: [] });
    });

    // Middleware de erro deve vir por último
    app.use(errorHandler);

    app.use((error: any, req: any, res: any, next: any) => {
      //se não pegar nenhum status retorno o status 500
      // console.log(error);
      res.status(error.status || 500);
      return res.send({
        error: {
          message: error.message,
          error: error,
        },
      });
    });

    httpServer.listen(PORT, () => {
      logger.info(`**** Ambiente: ${process.env.AMBIENTE} ****`);
      logger.info(`Oi Zap Consumer iniciado na porta:  ${PORT}`);
    });

    process.on('uncaughtException', (err) => {
      console.log('err at line 152 in src/server.ts:', err);
      logger.error('Erro não capturado:' + JSON.stringify(err));
      // Adote medidas apropriadas aqui, se possível
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Rejeicao nao tratada:' + typeof reason == 'object' ? JSON.stringify(reason) : reason);
      logger.error('Rejeicao nao tratada:' + typeof reason == 'object' ? JSON.stringify(promise) : promise);
      // Adote medidas apropriadas aqui, se possível
    });

    // Capturar sinais de encerramento
    process.on('SIGINT', () => shutdown('SIGINT')); // Ctrl+C
    process.on('SIGTERM', () => shutdown('SIGTERM')); // Sinal de encerramento de container, por exemplo
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
