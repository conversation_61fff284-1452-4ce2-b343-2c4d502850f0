import { Router } from 'express';
import { ContatosController } from '../controllers/ContatosController';
import authApi from '../middleware/authApi';
import authorizationHash from '../middleware/authorizationHash';
export const ContatosRoutes = Router();

ContatosRoutes.post('/contatos/v1', authApi, ContatosController.incluir);
ContatosRoutes.put('/contatos/v1', authApi, ContatosController.alterar);
ContatosRoutes.get('/contatos/v1', authApi, ContatosController.listar);
ContatosRoutes.delete('/contatos/v1', authApi, ContatosController.remover);
