import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { CampanhasModel } from '../../models/campanhas/CampanhasModel';

export class CampanhasController {
  static async incluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.ds_campanha) {
        errors.push('O campo "ds_campanha" é obrigatório.');
      }
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.tp_campanha) {
        errors.push('O campo "tp_campanha" é obrigatório.');
      }
      if (typeof req.body.in_ativo !== 'undefined' && typeof req.body.in_ativo !== 'boolean') {
        errors.push('O campo "in_ativo" deve ser um booleano.');
      }
      if (req.body.tp_campanha == 'Meta Instagram' || req.body.tp_campanha == 'Meta Facebook') {
        if (!req.body.nr_whatsapp) {
          errors.push('O campo "nr_whatsapp" é obrigatório para campanhas Meta.');
        }
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new CampanhasModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async capturarClique(req: Request, res: Response) {
    try {
      // console.log('🚀 ~ CampanhasController.ts:39 ~ capturarClique ~ req:', req.query);
      await new CampanhasModel().capturarClique(req, res);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.query.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new CampanhasModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async alterar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.cd_campanha) {
        errors.push('O campo "cd_campanha" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new CampanhasModel().alterar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async excluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.cd_campanha) {
        errors.push('O campo "cd_campanha" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new CampanhasModel().excluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
