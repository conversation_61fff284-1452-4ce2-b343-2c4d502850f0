import { TelaModel } from '../../models/telas/TelaModel';

export class TelaDB {
  private model = new TelaModel();

  listarTelas() {
    return this.model.listarTelas();
  }
  criarTela(data: any) {
    return this.model.criarTela(data);
  }
  atualizarTela(cd_tela: number, data: any) {
    return this.model.atualizarTela(cd_tela, data);
  }
  deletarTela(cd_tela: number) {
    return this.model.deletarTela(cd_tela);
  }
} 