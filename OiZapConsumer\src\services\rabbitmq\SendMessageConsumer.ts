import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection, ConsumeMessage } from 'amqplib';
//import Messages from '../../models/Messages.js';
import { IMessageStatus } from '../../interfaces/IMessageStatus.js';
import Logger from '../../logs/Logger';
import { MessagesModel } from '../../models/MessagesModel';
const logger = Logger.getLogger();

export default class SendMessageConsumer {
  private queueName: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private io: any;
  //private messagesHandler: Messages;
  private messagesHandler: MessagesModel;
  private CONTINUE: boolean = false;

  constructor(queueName: string, io: any) {
    this.queueName = queueName;
    this.io = io;
    this.messagesHandler = new MessagesModel();
  }

  async connect() {
    try {
      let room = this.queueName.replace('.send.message', '');
      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }

      logger.debug('SendMessageConsumer > HOST_RABBITMQ' + HOST_RABBITMQ);

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();
      await this.channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });

      this.channel.consume(this.queueName, async (message: ConsumeMessage | null) => {
        if (!this.CONTINUE) {
          return;
        }

        if (message !== null) {
          logger.debug('SendMessageConsumer > message.content' + message.content.toString());
          const messageContent = JSON.parse(message.content.toString());
          const messageUpdate: IMessageStatus = {
            event: messageContent.event,
            instance: messageContent.instance,
            message_id: messageContent.data.key.id,
            messageId: messageContent.data.key.id,
            keyId: messageContent.data.key.id,
            remoteJid: messageContent.data.remoteJid,
            fromMe: messageContent.data.key.fromMe,
            participant: undefined,
            status: 'PENDING',
            instanceId: messageContent.data.instanceId,
            tp_status: 'Enviado',
            server_url: messageContent.server_url,
            date_time: messageContent.date_time,
            sender: messageContent.sender,
            apikey: messageContent.apikey,
          };

          try {
            const respMessage = await this.messagesHandler.updateMessage(messageUpdate);
            logger.debug('SendMessageConsumer > updateMessage:' + JSON.stringify(respMessage));

            if (respMessage.statuscode === 200) {
              //console.log('messageContent.data at line 56 in rabbitmq/SendMessageConsumer.ts:', messageContent);
              //messageContent.data.status_message = Funcoes.trataStatus(messageContent.data.status_message);

              // this.io.in(room).emit('send.message', messageContent);
              this.io.in(room).emit('send.message', messageUpdate);
              this.channel!.ack(message); // Confirme a mensagem após o sucesso.
            } else {
              //logger.error(`Error insert message PostgreSQL ${respMessage}`);
              this.channel!.reject(message, false);
            }
          } catch (error) {
            logger.error('SendMessageConsumer > Error handling the message:' + JSON.stringify(error));
            this.channel!.reject(message, false);
          }
        }
      });
    } catch (error) {
      logger.error('SendMessageConsumer > Error connecting to RabbitMQ-SendMessage:' + JSON.stringify(error));
    }
  }

  close() {
    if (this.connection) {
      this.connection.close();
    }
  }
}
