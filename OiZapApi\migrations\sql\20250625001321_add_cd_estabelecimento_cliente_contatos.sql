-- Migration: add_cd_estabelecimento_cliente_contatos
-- Created: 2025-06-25T00:13:21.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna cd_estabelecimento na tabela cliente_contatos
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cliente_contatos' 
        AND column_name = 'cd_estabelecimento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE cliente_contatos ADD COLUMN cd_estabelecimento INTEGER;
        COMMENT ON COLUMN cliente_contatos.cd_estabelecimento IS 'Código do estabelecimento relacionado ao cliente/contato';
        RAISE NOTICE 'Coluna cd_estabelecimento adicionada à tabela cliente_contatos';
    ELSE
        RAISE NOTICE 'Coluna cd_estabelecimento já existe na tabela cliente_contatos';
    END IF;
END $$; 