<template>
    <ShowLoading ref="loading" />
    <Modal :show="props.estado" @hidden="emit('modal:close')" class="!z-[100]">
        <ModalHeader class="flex justify-between items-center">
            <div class="text-lg font-medium text-slate-600">Departamentos</div>
            <button
                type="button"
                class="btn btn-icon btn-secondary h-[40px] w-[40px] bg-transparent border-none shadow-none -intro-y p-0"
                @click="emit('modal:close')"
            >
                <XIcon class="w-5 h-5" />
            </button>
        </ModalHeader>
        <ModalBody class="px-0 pt-2">
            <ModalHeader class="pb-4 mb-4">
                <form
                    class="w-full flex justify-start items-end gap-2"
                    @submit.prevent="!!cd_editando ? alterarDepartamento() : incluirDepartamento()"
                >
                    <div class="w-full text-left -intro-y">
                        <label for="validation-form-2" class="text-slate-500 text-left"> Departamento </label>
                        <input
                            v-model.trim="formData.ds_departamento"
                            type="text"
                            name="ds_nome"
                            class="form-control h-[40px]"
                            ref="inputDepartamento"
                        />
                    </div>
                    <div class="ml-1 self-start">
                        <label class="form-label m-0 self-center text-slate-500">Ativo</label>
                        <div class="form-check mr-2 mt-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" v-model="formData.in_ativo" />
                            </div>
                        </div>
                    </div>
                    <Tippy content="Enviar" class="z-[1000]">
                        <button type="submit" class="btn btn-primary-soft h-[40px] -intro-y">
                            <span v-if="!cd_editando">Incluir</span>
                            <span v-else>Alterar</span>
                        </button>
                    </Tippy>
                    <!-- <button type="button" class="btn btn-secondary h-[40px] -intro-y" @click="emit('modal:close')">
                    Cancelar
                </button> -->
                </form>
            </ModalHeader>
            <div class="px-4">
                <div class="overflow-x-auto w-full scrollbar-hidden border-l border-r border-slate-200/60 rounded-lg">
                    <div id="tabulator" class="table-report table-report--tabulator"></div>
                </div>
            </div>
        </ModalBody>
    </Modal>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import { createIcons, icons } from 'lucide';
    import DepartamentosServices from '@/services/administracao/DepartamentosServices';

    const loading = ref();
    const tabulator = ref();
    const cd_editando = ref(null);
    const inputDepartamento = ref();
    const props = defineProps({
        estado: {
            type: Boolean,
            required: true,
        },
        estabelecimento: {
            type: String,
            required: true,
        },
    });

    const emit = defineEmits(['departamento:select', 'modal:close']);

    const formData = ref({
        ds_departamento: '',
        in_ativo: true,
        cd_estabelecimento: null,
    });
    const listaDepartamentos = ref([]);

    function initTabulator() {
        tabulator.value = new Tabulator('#tabulator', {
            data: listaDepartamentos.value,
            layout: 'fitColumns',
            columns: [
                { title: 'Departamento', field: 'ds_departamento' },
                {
                    title: 'Ativo',
                    field: 'in_ativo',
                    formatter: 'tickCross',
                    maxWidth: 100,
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    headerSort: false,
                },
                {
                    title: ' ',
                    field: 'actions',
                    formatter: function (cell) {
                        const row = cell.getRow();
                        if (row.getData().cd_departamento === cd_editando.value) {
                            return `<button id="btn-cancel" class="hover:underline " title="Cancelar" >Cancelar</button>`;
                        }
                        return `
                        <button id="btn-edit" class="btn btn-primary-soft btn-sm p-0.5 mr-2" data-lucide="edit" title="Editar" ></button>
                        <button id="btn-delete" class="btn btn-danger-soft btn-sm p-0.5" data-lucide="trash" title="Excluir" ></button>`;
                    },
                    hozAlign: 'center',
                    headerHozAlign: 'center',
                    vertAlign: 'middle',
                    maxWidth: 110,
                    sorter: false,
                    headerSort: false,
                    cellClick: function (e, cell) {
                        const cellData = cell.getData();
                        let btn = e.target.id;
                        if (!btn) btn = e.target.parentElement.id;

                        if (!btn) return;
                        switch (btn) {
                            case 'btn-edit':
                                editarLinha(cellData);
                                break;
                            case 'btn-delete':
                                excluirDepartamento(cellData);
                                break;
                            case 'btn-cancel':
                                cd_editando.value = null;
                                formData.value.ds_departamento = '';
                                formData.value.in_ativo = true;
                                carregarDepartamentos();
                                break;
                        }
                    },
                },
            ],
        });

        tabulator.value.on('renderComplete', function () {
            createIcons({
                icons,
                'stroke-width': 1.5,
                nameAttr: 'data-lucide',
            });
        });
    }

    function editarLinha(departamento) {
        cd_editando.value = departamento.cd_departamento;
        formData.value.ds_departamento = departamento.ds_departamento;
        formData.value.in_ativo = departamento.in_ativo;
        formData.value.cd_estabelecimento = props.estabelecimento;

        tabulator.value.setData(listaDepartamentos.value);
    }

    async function alterarDepartamento() {
        try {
            if (!cd_editando.value) {
                console.error('Nenhum departamento selecionado para edição.');
                return;
            }
            const formDataConfigurado = {
                cd_departamento: cd_editando.value,
                ds_departamento: formData.value.ds_departamento,
                in_ativo: formData.value.in_ativo,
                cd_estabelecimento: props.estabelecimento,
            };
            console.log('Alterando departamento:', formDataConfigurado);

            loading.value.show();
            await DepartamentosServices.alterar(formDataConfigurado);
            carregarDepartamentos();
        } catch (error) {
            console.error('Erro ao alterar departamento:', error);
        } finally {
            cd_editando.value = null;
            formData.value.ds_departamento = '';
            formData.value.in_ativo = true;
            loading.value.hide(500);
        }
    }

    async function excluirDepartamento(departamento) {
        try {
            loading.value.show();
            const formDataConfigurado = {
                cd_estabelecimento: props.estabelecimento,
                cd_departamento: departamento.cd_departamento,
            };
            await DepartamentosServices.excluir(departamento);
            carregarDepartamentos();
        } catch (error) {
            console.error('Erro ao excluir departamento:', error);
        } finally {
            loading.value.hide(500);
        }
    }

    async function incluirDepartamento() {
        if (!formData.value.ds_departamento.trim()) {
            return;
        }
        try {
            loading.value.show();
            const formDataConfigurado = {
                ds_departamento: formData.value.ds_departamento,
                in_ativo: formData.value.in_ativo,
                cd_estabelecimento: props.estabelecimento,
            };
            const response = await DepartamentosServices.incluir(formDataConfigurado);
            if (response.data) {
                formData.value.ds_departamento = '';

                carregarDepartamentos();
                inputDepartamento.value.focus();
                emit('departamento:adicionado', response.data);
            }
        } catch (error) {
            console.error('Erro ao incluir departamento:', error);
        } finally {
            loading.value.hide(500);
        }
    }

    async function carregarDepartamentos() {
        try {
            loading.value.show();
            const response = await DepartamentosServices.listar();
            listaDepartamentos.value = response.data;
            if (tabulator.value) {
                tabulator.value.setData(listaDepartamentos.value);
            }
        } catch (error) {
            console.error('Erro ao carregar departamentos:', error);
        } finally {
            loading.value.hide(500);
        }
    }

    watch(
        () => props.estado,
        (newValue) => {
            if (newValue) {
                carregarDepartamentos();
                initTabulator();
                setTimeout(() => {
                    inputDepartamento.value.focus();
                }, 500);
            } else {
                if (tabulator.value) {
                    tabulator.value.destroy();
                    tabulator.value = null;
                }
                formData.value.ds_departamento = '';
                formData.value.in_ativo = true;
                formData.value.cd_estabelecimento = props.estabelecimento;
                listaDepartamentos.value = [];
            }
        },
        { immediate: true }
    );
</script>
