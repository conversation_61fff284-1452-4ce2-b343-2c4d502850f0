-- ========================================
-- Setup: Criar perfil Admin para usuário 87
-- Data: 2025-01-09
-- Descrição: Cria perfil Administrador e associa ao usuário 87
-- ========================================

-- 1. Criar perfil Administrador
INSERT INTO perfis (nm_perfil, created_at) 
VALUES ('Administrador', CURRENT_TIMESTAMP)
ON CONFLICT (nm_perfil) DO NOTHING;

-- 2. Criar telas administrativas
INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES 
  ('Cadastro de Perfil', '/cadPerfil', CURRENT_TIMESTAMP),
  ('Cadastro de Tela', '/cadTela', CURRENT_TIMESTAMP),
  ('Associação Perfis x Telas', '/cadPerfisTelas', CURRENT_TIMESTAMP)
ON CONFLICT (ds_rota) DO NOTHING;

-- 3. Associar usuário 87 ao perfil Administrador
-- Primeiro, vamos pegar o cd_perfil do Administrador
DO $$
DECLARE
    admin_perfil_id INTEGER;
BEGIN
    -- Buscar o ID do perfil Administrador
    SELECT cd_perfil INTO admin_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Administrador';
    
    -- Associar usuário 87 ao perfil Administrador
    INSERT INTO usuarios_perfis (cd_usuario, cd_perfil, created_at, updated_at)
    VALUES (87, admin_perfil_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (cd_usuario, cd_perfil) DO NOTHING;
    
    RAISE NOTICE 'Usuário 87 associado ao perfil Administrador (ID: %)', admin_perfil_id;
END $$;

-- 4. Associar telas administrativas ao perfil Administrador
DO $$
DECLARE
    admin_perfil_id INTEGER;
    tela_perfil_id INTEGER;
    tela_tela_id INTEGER;
    tela_associacao_id INTEGER;
BEGIN
    -- Buscar o ID do perfil Administrador
    SELECT cd_perfil INTO admin_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Administrador';
    
    -- Buscar os IDs das telas
    SELECT cd_tela INTO tela_perfil_id 
    FROM telas 
    WHERE ds_rota = '/cadPerfil';
    
    SELECT cd_tela INTO tela_tela_id 
    FROM telas 
    WHERE ds_rota = '/cadTela';
    
    SELECT cd_tela INTO tela_associacao_id 
    FROM telas 
    WHERE ds_rota = '/cadPerfisTelas';
    
    -- Associar telas ao perfil Administrador com todas as permissões
    INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
    VALUES 
      (admin_perfil_id, tela_perfil_id, true, true, true, true, CURRENT_TIMESTAMP),
      (admin_perfil_id, tela_tela_id, true, true, true, true, CURRENT_TIMESTAMP),
      (admin_perfil_id, tela_associacao_id, true, true, true, true, CURRENT_TIMESTAMP)
    ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
      in_visualizar = EXCLUDED.in_visualizar,
      in_inserir = EXCLUDED.in_inserir,
      in_alterar = EXCLUDED.in_alterar,
      in_excluir = EXCLUDED.in_excluir;
    
    RAISE NOTICE 'Telas administrativas associadas ao perfil Administrador (ID: %)', admin_perfil_id;
END $$;

-- 5. Verificar se tudo foi criado corretamente
SELECT 
    'Perfis criados:' as info,
    cd_perfil,
    nm_perfil
FROM perfis 
WHERE nm_perfil = 'Administrador';

SELECT 
    'Telas criadas:' as info,
    cd_tela,
    nm_tela,
    ds_rota
FROM telas 
WHERE ds_rota IN ('/cadPerfil', '/cadTela', '/cadPerfisTelas');

SELECT 
    'Usuário 87 associado ao perfil:' as info,
    up.cd_usuario,
    up.cd_perfil,
    p.nm_perfil
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
WHERE up.cd_usuario = 87;

SELECT 
    'Telas associadas ao perfil Administrador:' as info,
    pt.cd_perfil,
    p.nm_perfil,
    pt.cd_tela,
    t.nm_tela,
    t.ds_rota,
    pt.in_visualizar,
    pt.in_inserir,
    pt.in_alterar,
    pt.in_excluir
FROM perfis_telas pt
JOIN perfis p ON p.cd_perfil = pt.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE p.nm_perfil = 'Administrador';

-- ========================================
-- Resumo do que foi criado:
-- ========================================
-- ✅ Perfil "Administrador" criado
-- ✅ 3 telas administrativas criadas:
--    - Cadastro de Perfil (/cadPerfil)
--    - Cadastro de Tela (/cadTela) 
--    - Associação Perfis x Telas (/cadPerfisTelas)
-- ✅ Usuário 87 associado ao perfil Administrador
-- ✅ Todas as telas administrativas associadas ao perfil Administrador
-- ✅ Todas as permissões (visualizar, inserir, alterar, excluir) habilitadas
-- ======================================== 