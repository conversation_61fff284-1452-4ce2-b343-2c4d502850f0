import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    const hasColumn = await knex.schema.hasColumn('atendimentos', 'cd_departamento');

    if (!hasColumn) {
      await knex.schema.alterTable('atendimentos', (table) => {
        table.integer('cd_departamento').comment('Indica o departamento responsável pelo atendimento');
      });
    }

    // SEMPRE retorna - sem transação manual
    return;
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('atendimentos', (table) => {
      table.dropColumn('cd_departamento');
    });
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
