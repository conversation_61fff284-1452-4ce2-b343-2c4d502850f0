import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    let hasColumn = await knex.schema.hasTable('cliente_contatos');

    if (!hasColumn) {
      console.log('Tabela não existe, criando...');
      await knex.schema.createTable('cliente_contatos', (table) => {
        table.integer('cd_cliente').nullable();
        table.integer('cd_contato').nullable();
        table.timestamps(true, true);
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.dropTable('cliente_contatos');
  } catch (error) {
    throw error;
  }
}
