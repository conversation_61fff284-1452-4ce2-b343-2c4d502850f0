<template>
    <input
        ref="litepickerRef"
        v-picker-directive="{ props, emit }"
        type="text"
        :value="modelValue"
        autocomplete="one-time-code"
        autocorrect="off"
        autocapitalize="off"
        spellcheck="false"
        data-form-type="other"
        data-lpignore="true"
        data-1p-ignore="true"
        :name="randomName"
        role="textbox"
        aria-autocomplete="none"
    />
</template>

<script setup>
    import { inject, onMounted, ref, computed } from 'vue';
    import { setValue, init, reInit } from './index';

    const vPickerDirective = {
        mounted(el, { value }) {
            setValue(value.props, value.emit);
            init(el, value.props, value.emit);
        },
        updated(el, { oldValue, value }) {
            if (oldValue.props.modelValue !== value.props.modelValue) {
                reInit(el, value.props, value.emit);
            }
        },
    };

    const props = defineProps({
        options: {
            type: Object,
            default() {
                return {};
            },
        },
        modelValue: {
            type: String,
            default: '',
        },
        refKey: {
            type: String,
            default: null,
        },
    });

    const emit = defineEmits();

    const litepickerRef = ref();

    // Generate random name to prevent autocomplete
    const randomName = computed(() => `litepicker_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`);

    const bindInstance = () => {
        if (props.refKey) {
            const bind = inject(`bind[${props.refKey}]`);
            if (bind) {
                bind(litepickerRef.value);
            }
        }
    };

    onMounted(() => {
        bindInstance();
    });
</script>

<style scoped>
    textarea {
        margin-left: 1000000px;
    }
</style>
