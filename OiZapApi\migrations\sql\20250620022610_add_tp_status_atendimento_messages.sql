-- Migration: add_tp_status_atendimento_messages
-- Created: 2025-06-20T02:26:10.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna tp_status_atendimento na tabela messages
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'tp_status_atendimento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN tp_status_atendimento VARCHAR(30);
        COMMENT ON COLUMN messages.tp_status_atendimento IS 'Status do atendimento da mensagem';
        RAISE NOTICE 'Coluna tp_status_atendimento adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna tp_status_atendimento já existe na tabela messages';
    END IF;
END $$; 