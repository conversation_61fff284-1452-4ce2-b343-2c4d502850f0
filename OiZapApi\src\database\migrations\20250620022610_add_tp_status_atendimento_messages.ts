import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('messages migration started');
    let hasColumn = await knex.schema.hasColumn('messages', 'tp_status_atendimento');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.string('tp_status_atendimento', 30);
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('messages', (table) => {
      table.dropColumn('tp_status_atendimento');
    });
  } catch (error) {
    throw error;
  }
}
