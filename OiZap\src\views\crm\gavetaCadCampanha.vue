<template>
    <div>
        <ShowLoading ref="loading" />
        <Modal size="modal-lg" :show="props.estado" @hidden="fecharModal" key="props" slideOver>
            <ModalHeader class="flex justify-between items-center font-medium text-xl">
                Campanha
                <XIcon class="w-6 h-6 cursor-pointer" @click="fecharModal" />
            </ModalHeader>
            <ModalBody
                class="h-[calc(100vh-55px)] max-h-[calc(100vh-55px)] overflow-y-auto flex flex-col items-center justify-between p-0"
            >
                <div class="w-full flex flex-col items-center p-6">
                    <h3 class="text-md font-medium text-slate-500 self-start flex items-center mb-1">
                        <MegaphoneIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                        Dados da Campanha
                    </h3>
                    <div class="grid grid-cols-12 w-full gap-2">
                        <div class="form-control col-span-12 gap-1">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Nome da Campanha <span class="text-red-500">*</span>
                            </label>
                            <div class="relative w-full">
                                <BaseInput
                                    type="text"
                                    class="form-control col-span-1 block"
                                    placeholder="Nome da campanha"
                                    v-model="formData.ds_campanha"
                                />
                            </div>
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Tipo de Campanha <span class="text-red-500">*</span>
                            </label>

                            <Multiselect
                                class="w-full z-50"
                                v-model="formData.tp_campanha"
                                placeholder="Selecione o tipo da Campanha"
                                mode="single"
                                :canClear="false"
                                :close-on-select="false"
                                :searchable="true"
                                :createOption="false"
                                :options="listaTipoCampanha"
                                @emitEvent="selecionaTipo($event)"
                            />
                        </div>

                        <div class="col-span-12 md:col-span-6">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Status
                            </label>

                            <Multiselect
                                class="w-full z-50 -mt-1.5"
                                :customHeight="33"
                                v-model="formData.in_ativo"
                                placeholder="Selecione o Status"
                                mode="single"
                                :close-on-select="false"
                                :searchable="true"
                                :createOption="false"
                                :options="[
                                    { value: true, label: 'Ativo' },
                                    { value: false, label: 'Inativo' },
                                ]"
                            />
                        </div>
                        <!-- <div class="form-control col-span-12 md:col-span-6">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                                Data de Início
                            </label>
                            <div class="relative w-full">
                                <input
                                    type="datetime-local"
                                    class="form-control col-span-1 block"
                                    v-model="formData.dt_inicio"
                                />
                            </div>
                        </div>
                        <div class="form-control col-span-12 md:col-span-6">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                                Data de Fim
                            </label>
                            <div class="relative w-full">
                                <input
                                    type="datetime-local"
                                    class="form-control col-span-1 block"
                                    v-model="formData.dt_fim"
                                />
                            </div>
                        </div> -->

                        <div class="form-control col-span-12 md:col-span-6">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                                Período</label
                            >
                            <!-- <input id="filtro1" type="text" class="form-control" placeholder="Filtro 1" /> -->

                            <div class="relative w-full mx-auto">
                                <Litepicker
                                    class="form-control"
                                    v-model="periodo"
                                    @update:modelValue="(value) => selecionarData(value)"
                                    :options="{
                                        lang: 'pt-BR',
                                        footer: false,
                                        autoApply: true,
                                        singleMode: false,
                                        numberOfColumns: 3,
                                        numberOfMonths: 3,
                                        showWeekNumbers: false,
                                        dropdowns: {
                                            minYear: 2022,
                                            maxYear: 2050,
                                            months: true,
                                            years: true,
                                        },
                                        tooltipText: {
                                            one: 'dia',
                                            other: 'dias',
                                        },
                                        format: 'DD/MM/YYYY',
                                    }"
                                />
                                <XIcon
                                    v-if="formData.dt_inicio || formData.dt_fim"
                                    @click="
                                        formData.dt_inicio = null;
                                        formData.dt_fim = null;
                                        periodo = null;
                                    "
                                    class="w-4 h-8 absolute flex justify-center items-center rounded-full right-0 top-0 bottom-0 my-auto ml-auto mr-4 cursor-pointer"
                                />
                            </div>
                        </div>

                        <div class="col-span-12 md:col-span-6" v-if="inCampanhaMeta">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Número WhatsApp<span class="text-red-500">*</span>
                            </label>
                            <!--   <div class="relative w-full">
                             <input
                                    type="tel"
                                    class="form-control col-span-1 block"
                                    placeholder="(11) 99999-9999"
                                    v-model="formData.numero_whatsapp"
                                />  </div>-->

                            <Multiselect
                                :customHeight="33"
                                v-model="instanciaSelecionada"
                                placeholder="Selecione a Instância"
                                mode="single"
                                :close-on-select="false"
                                :searchable="true"
                                :createOption="false"
                                :options="listaInstancias"
                                @emitEvent="selecionaInstancia"
                            />
                        </div>
                        <div class="form-control col-span-12">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Mensagem encaminhada<span class="text-red-500" v-if="inCampanhaMeta">*</span>
                            </label>
                            <div class="relative w-full">
                                <textarea
                                    class="form-control col-span-1 block"
                                    rows="4"
                                    placeholder="Mensagem encaminhada"
                                    v-model="formData.ds_mensagem"
                                ></textarea>
                            </div>
                        </div>
                        <div class="form-control col-span-12">
                            <label for="validation-form-1" class="form-label text-slate-500 text-xs mb-0 pb-0">
                                Descrição da Campanha
                            </label>
                            <div class="relative w-full">
                                <textarea
                                    class="form-control col-span-1 block"
                                    rows="4"
                                    placeholder="Descrição da campanha"
                                    v-model="formData.ds_descricao"
                                ></textarea>
                            </div>
                        </div>

                        <div class="col-span-12">
                            <div class="text-slate-500 text-xs flex items-center gap-2" v-if="formData.ds_url">
                                <label class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">Link Gerado:</label>

                                <span
                                    v-if="formData.ds_url"
                                    class="break-all select-all bg-gray-100 px-2 py-1 rounded cursor-pointer"
                                    @click="copyToClipboard(formData.ds_url)"
                                >
                                    {{ formData.ds_url.substring(0, 30) }}{{ formData.ds_url.length > 30 ? '...' : '' }}
                                </span>
                                <Tippy v-if="formData.ds_url" content="Copiar URL">
                                    <button
                                        v-if="formData.ds_url"
                                        @click="copyToClipboard(formData.ds_url)"
                                        class="ml-1 p-1 rounded hover:bg-gray-200"
                                        :title="'Copiar URL'"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 text-slate-500"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M8 16h8a2 2 0 002-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v6a2 2 0 002 2zm0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2"
                                            />
                                        </svg>
                                    </button>
                                </Tippy>
                            </div>
                        </div>
                    </div>

                    <p class="text-xs text-slate-500 w-full py-2">
                        <span class="text-red-500">*</span> Campos obrigatórios
                    </p>
                </div>

                <!-- <div v-if="formData.ds_url" class="w-full flex flex-col items-center p-4">
                    <label class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">Link Gerado:</label>
                    <a :href="formData.ds_url" target="_blank" class="text-blue-500 hover:underline break-all">{{
                        formData.ds_url
                    }}</a>
                </div> -->
                <ModalFooter class="w-full flex justify-end items-center">
                    <button class="btn btn-secondary-soft border-none shadow-none mr-2" @click="fecharModal">
                        <XIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                        Cancelar
                    </button>
                    <button class="btn btn-primary-soft border-none shadow-none" @click="salvarCampanha">
                        <SaveIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                        Salvar
                    </button>
                </ModalFooter>
            </ModalBody>
        </Modal>
    </div>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';
    import CampanhasServices from '@/services/crm/CampanhasServices';
    import { MegaphoneIcon } from 'lucide-vue-next';
    import converters from '../../utils/converters';
    import { useToast } from '@/global-components/toastify/useToast';
    import UsuarioInstanciasServices from '@/services/administracao/UsuarioInstanciasServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';

    const toast = useToast();
    const codUsuario = ref();
    const estabelecimentosLiberado = ref([]);
    const loading = ref();
    const listaTipoCampanha = ref([
        { value: 'Meta Instagram', label: 'Meta Instagram' },
        { value: 'Meta Facebook', label: 'Meta Facebook' },
    ]);
    const inCampanhaMeta = ref(false);
    const props = defineProps({
        estado: {
            type: Boolean,
            default: false,
        },
        dados: {
            type: Object,
            default: () => ({}),
        },
    });
    const emit = defineEmits(['update:estado', 'response:success', 'response:error']);
    const listaInstancias = ref([]);
    const instanciaSelecionada = ref([]);
    const instanciaLiberada = ref([]);
    const periodo = ref(null);
    const formData = ref({
        cd_campanha: undefined,
        ds_campanha: undefined,
        tp_campanha: undefined,
        nr_whatsapp: undefined,
        ds_url: undefined,
        ds_descricao: undefined,
        in_ativo: true,
        dt_inicio: undefined,
        dt_fim: undefined,
        ds_mensagem: undefined,
        cd_estabelecimento: undefined,
    });

    function fecharModal() {
        emit('update:estado', false);
    }

    function formatarDataParaInput(dataString) {
        if (!dataString) return '';
        const data = new Date(dataString);
        const ano = data.getFullYear();
        const mes = String(data.getMonth() + 1).padStart(2, '0');
        const dia = String(data.getDate()).padStart(2, '0');
        const hora = String(data.getHours()).padStart(2, '0');
        const minuto = String(data.getMinutes()).padStart(2, '0');
        return `${ano}-${mes}-${dia}T${hora}:${minuto}`;
    }

    function selecionarData(valor) {
        if (!valor) return;
        const inicial = valor.split('-')[0].trim();
        const final = valor.split('-')[1].trim();
        formData.value.dt_inicio = converters.date('YYYY-MM-DD', inicial);
        formData.value.dt_fim = converters.date('YYYY-MM-DD', final);
    }

    const selecionaTipo = async (event) => {
        //console.log('🚀 ~ gavetaCadCampanha.vue:217 ~ selecionaTipo ~ event:', event);
        if (event.event == 'Select') {
            if (event.select.value == 'Meta Instagram' || event.select.value == 'Meta Facebook') {
                inCampanhaMeta.value = true;
            } else {
                inCampanhaMeta.value = false;
            }
        } else {
            inCampanhaMeta.value = false;
        }
    };
    const selecionaInstancia = (event) => {
        // console.log('🚀 ~ gavetaCadCampanha.vue:284 ~ selecionaInstancia ~ event:', event);
        // // console.log('🚀 ~ listaContatos.vue:585 ~ selecionaInstancia ~ event:', event);
        if (event.event == 'Select') {
            formData.value.nr_whatsapp = event.select.value;
        }
    };

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text);
        toast.showSuccessNotification('URL copiada!');
    }

    async function salvarCampanha() {
        try {
            loading.value.show();
            let result = null;

            formData.value.cd_estabelecimento = estabelecimentosLiberado.value[0].cd_estabelecimento;

            if (formData.value.cd_campanha) {
                result = await CampanhasServices.alterar(formData.value);
            } else {
                //    console.log('🚀 ~ gavetaCadCampanha.vue:346 ~ salvarCampanha ~ formData.value:', formData.value);
                result = await CampanhasServices.incluir(formData.value);
                //   console.log('🚀 ~ gavetaCadCampanha.vue:302 ~ salvarCampanha ~ result:', result);
            }
            if (result.statuscode === 200) {
                const data = result.data[0];
                toast.showSuccessNotification(result.message);
                // Exibe o link gerado, se disponível
                if (data && data.ds_url) {
                    formData.value.ds_url = data.ds_url;
                }
                emit('response:success', data);
                fecharModal();
            } else {
                toast.showErrorNotification(result.message);
            }
        } catch (error) {
            console.error('Erro ao salvar campanha:', error);
            toast.showErrorNotification('Erro ao salvar campanha');
        }
        loading.value.hide();
    }

    async function carregaUsuarioInstancias() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        const filtro = {
            cd_usuario: parseInt(codUsuario.value),
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        const respInst = await UsuarioInstanciasServices.listar(filtro);
        //  console.log('🚀 ~ listaContatos.vue:682 ~ carregaUsuarioInstancias ~ respInst:', respInst);

        if (respInst.statuscode === 500) {
            toast.showErrorNotification(respInst.message);
        } else if (respInst.statuscode === 200) {
            let inst = [];
            respInst.data.forEach((instancia) => {
                inst.push(instancia.nameinstance);
            });

            instanciaLiberada.value = inst;
            instanciaSelecionada.value = inst;
        }
    }

    async function carregaInstancias() {
        const filtros = {
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
            cd_usuario: parseInt(codUsuario.value),
            in_verificastatus: false,
        };

        listaInstancias.value = [];

        const result = await InstanciaServices.listar(filtros);

        if (result.statuscode == 200) {
            listaInstancias.value = result.data.map((instancia) => {
                return {
                    value: instancia.telefone,
                    label: instancia.nome + ' - ' + instancia.telefone,
                };
            });
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    onMounted(async () => {
        codUsuario.value = localStorage.getItem('codusuario');
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);
        } else {
            toast.showWarningNotification('Estabelecimento não liberado!');
        }

        formData.value.dt_inicio = null;
        formData.value.dt_fim = null;
        periodo.value = null;

        await carregaInstancias();
        await carregaUsuarioInstancias();
    });

    watch(
        () => props,
        (newValue) => {
            if (!newValue.estado) return;
            if (newValue.dados && Object.keys(newValue.dados).length > 0) {
                // console.log('🚀 ~ gavetaCadCampanha.vue:401 ~ newValue.dados:', newValue.dados);
                formData.value = {
                    ...props.dados,
                    dt_inicio: formatarDataParaInput(props.dados.dt_inicio),
                    dt_fim: formatarDataParaInput(props.dados.dt_fim),
                };
                if (formData.value.tp_campanha == 'Meta Instagram' || formData.value.tp_campanha == 'Meta Facebook') {
                    inCampanhaMeta.value = true;
                } else {
                    inCampanhaMeta.value = false;
                }
                // console.log('🚀 ~ gavetaCadCampanha.vue:399 ~ formData.value:', formData.value);
            } else {
                formData.value = {
                    cd_campanha: undefined,
                    ds_campanha: undefined,
                    tp_campanha: undefined,
                    nr_whatsapp: undefined,
                    ds_url: undefined,
                    ds_campanha: undefined,
                    in_ativo: true,
                    dt_inicio: undefined,
                    dt_fim: undefined,
                };
            }
        },
        { immediate: true, deep: true }
    );
</script>
