require('dotenv').config();
const { execSync } = require('child_process');
const path = require('path');

const environment = process.env.NODE_ENV || 'CRM';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
    SANDBOX: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
    PROD: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

const connectionConfig = getConnectionConfig(environment);
const databaseUrl = `postgres://${connectionConfig.user}:${connectionConfig.password}@${connectionConfig.host}:${connectionConfig.port}/${connectionConfig.database}`;

console.log(`🚀 Iniciando migrations automáticas - Ambiente: ${environment}`);
console.log(`📊 Verificando migrations pendentes...`);

try {
  // Definir DATABASE_URL como variável de ambiente
  process.env.DATABASE_URL = databaseUrl;

  const configFile = path.join(__dirname, 'migrate-config.json');
  const command = `npx node-pg-migrate --config-file="${configFile}" up`;

  console.log(`Executando: ${command}`);
  console.log(`Database URL: ${databaseUrl}`);

  const result = execSync(command, {
    encoding: 'utf8',
    stdio: 'pipe',
    env: { ...process.env, DATABASE_URL: databaseUrl },
  });

  console.log('✅ Migrations executadas com sucesso!');
  console.log('📄 Resultado:', result);
} catch (error) {
  console.error('❌ Erro ao executar migrations:', error.message);
  console.error('📄 Detalhes:', error.stdout || error.stderr);
  process.exit(1);
}
