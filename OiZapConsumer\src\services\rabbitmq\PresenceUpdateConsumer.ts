import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection, ConsumeMessage } from 'amqplib';
import Logger from '../../logs/Logger';
import { Funcoes } from '../Funcoes';
const logger = Logger.getLogger();

export default class PresenceUpdateConsumer {
  private queueName: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private io: any;
  private CONTINUE: boolean = false;

  constructor(queueName: string, io: any) {
    this.queueName = queueName;
    this.io = io;
  }

  async connect() {
    try {
      let room = this.queueName.replace('.presence.update', '');

      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }

      logger.debug('PresenceUpdateConsumer > HOST_RABBITMQ:' + HOST_RABBITMQ);

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();
      await this.channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });

      this.channel.consume(this.queueName, async (message: ConsumeMessage | null) => {
        //  console.log('PresenceUpdateConsumer');
        if (!this.CONTINUE) {
          return;
        }

        if (message !== null) {
          let messageContent = JSON.parse(message.content.toString());

          logger.debug(
            'messageContent at line 53 in rabbitmq/PresenceUpdateConsumer.ts: ' + message.content.toString(),
          );

          messageContent.data.status_message = Funcoes.trataStatus(messageContent.data.status);

          const dynamicNumber = messageContent?.data?.id;
          const lastKnownPresence = messageContent?.data?.presences[dynamicNumber]?.lastKnownPresence;

          if (lastKnownPresence === 'composing') {
            messageContent.data.presence = 'digitando';
          } else if (lastKnownPresence === 'recording') {
            messageContent.data.presence = 'gravando audio';
          } else if (lastKnownPresence === 'paused') {
            messageContent.data.presence = 'pausada';
          } else if (lastKnownPresence === 'available') {
            messageContent.data.presence = 'disponível';
          } else if (lastKnownPresence === 'unavailable') {
            messageContent.data.presence = 'indisponível';
          } else {
            messageContent.data.presence = lastKnownPresence;
          }

          this.io.in(room).emit('presence.update', messageContent);

          this.channel!.ack(message);
        }
      });
    } catch (error) {
      console.error('Error connecting to RabbitMQ-PresenceUpdate:', error);
    }
  }

  close() {
    if (this.connection) {
      this.connection.close();
    }
  }
}
