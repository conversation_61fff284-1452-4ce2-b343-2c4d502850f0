import { Request, Response } from 'express';
import { PerfilDB } from '../../data/perfis/PerfilDB';

export class PerfilController {
  static async listarPerfis(req: Request, res: Response) {
    try {
      const perfis = await new PerfilDB().listarPerfis();
      return res.json(perfis);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao listar perfis' });
    }
  }
  static async criarPerfil(req: Request, res: Response) {
    try {
      const perfil = await new PerfilDB().criarPerfil(req.body);
      return res.status(201).json(perfil);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao criar perfil' });
    }
  }
  static async atualizarPerfil(req: Request, res: Response) {
    try {
      const { cd_perfil } = req.params;
      const perfil = await new PerfilDB().atualizarPerfil(Number(cd_perfil), req.body);
      return res.json(perfil);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao atualizar perfil' });
    }
  }
  static async deletarPerfil(req: Request, res: Response) {
    try {
      const { cd_perfil } = req.params;
      await new PerfilDB().deletarPerfil(Number(cd_perfil));
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao deletar perfil' });
    }
  }
} 