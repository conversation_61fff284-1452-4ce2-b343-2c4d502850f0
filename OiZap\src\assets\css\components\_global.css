html {
    @apply bg-primary;
    &.dark {
        @apply bg-darkmode-800;
        body {
            @apply text-slate-300;
            *,
            ::before,
            ::after {
                @apply border-white/5;
            }
        }
    }
    ::-webkit-scrollbar {
        overflow-y: scroll;
        overflow-x: scroll;
        height: 6px;
        width: 6px;
        scrollbar-width: thin;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #b1b1b1;
        border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background-color: #919191;
    }

    /* ...existing code... */

    /* ========================================
   AUTOCOMPLETE & AUTOFILL PREVENTION
   ======================================== */

    /* Solução global para prevenir autocomplete */
    form {
        position: relative;
    }

    /* Inputs fake invisíveis para enganar o navegador */
    form::before {
        content: '';
        position: absolute;
        top: -100px;
        left: -100px;
        width: 1px;
        height: 1px;
        opacity: 0;
        pointer-events: none;
        background: url('data:text/html;charset=utf-8,<input type="text" name="fake_username" autocomplete="username"><input type="password" name="fake_password" autocomplete="current-password">')
            no-repeat;
    }

    /* Desabilitar autocomplete em todos os inputs por padrão */
    input:not([autocomplete='on']):not([data-keep-autocomplete='true']),
    textarea:not([autocomplete='on']):not([data-keep-autocomplete='true']),
    select:not([autocomplete='on']):not([data-keep-autocomplete='true']) {
        autocomplete: new-password !important;
        autocorrect: off !important;
        autocapitalize: off !important;
        spellcheck: false !important;
    }

    /* Específico para campos de busca/filtro */
    input[type='search'],
    input[placeholder*='buscar' i],
    input[placeholder*='filtrar' i],
    input[placeholder*='localizar' i],
    input[placeholder*='pesquisar' i] {
        autocomplete: off !important;
        data-form-type: other !important;
        data-lpignore: true !important;
    }

    /* Prevenir autofill visual do Chrome/Edge */
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active,
    textarea:-webkit-autofill,
    textarea:-webkit-autofill:hover,
    textarea:-webkit-autofill:focus,
    textarea:-webkit-autofill:active,
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus,
    select:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px white inset !important;
        -webkit-text-fill-color: #1f2937 !important;
        background-color: transparent !important;
        background-image: none !important;
        transition: background-color 5000s ease-in-out 0s !important;
    }

    /* Para modo escuro */
    .dark input:-webkit-autofill,
    .dark input:-webkit-autofill:hover,
    .dark input:-webkit-autofill:focus,
    .dark input:-webkit-autofill:active,
    .dark textarea:-webkit-autofill,
    .dark textarea:-webkit-autofill:hover,
    .dark textarea:-webkit-autofill:focus,
    .dark textarea:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px #1f2937 inset !important;
        -webkit-text-fill-color: #f9fafb !important;
    }

    /* Firefox autocomplete */
    input:-moz-autofill,
    textarea:-moz-autofill,
    select:-moz-autofill {
        background-color: transparent !important;
        background-image: none !important;
    }

    /* Prevenir zoom no iOS em inputs pequenos */
    @media screen and (max-width: 768px) {
        input[type='text'],
        input[type='email'],
        input[type='password'],
        input[type='number'],
        input[type='tel'],
        input[type='search'],
        textarea,
        select {
            font-size: 16px !important;
            transform-origin: left top;
        }
    }

    /* Desabilitar sugestões de senha */
    input[type='password'] {
        autocomplete: new-password !important;
    }

    input[type='email'] {
        autocomplete: new-password !important;
    }

    /* Prevenir preenchimento automático em campos específicos */
    .no-autofill,
    .no-autofill input,
    .no-autofill textarea,
    .no-autofill select {
        autocomplete: off !important;
        autocorrect: off !important;
        autocapitalize: off !important;
        spellcheck: false !important;
        data-form-type: other !important;
        data-lpignore: true !important;
    }

    /* Solução avançada com inputs fake globais */
    .form-container::before,
    .form-wrapper::before,
    form::before {
        content: '';
        position: absolute;
        top: -200px;
        left: -200px;
        width: 0;
        height: 0;
        overflow: hidden;
        opacity: 0;
        pointer-events: none;
        z-index: -9999;
    }

    /* Criar inputs invisíveis para enganar o navegador */
    .autocomplete-off::before {
        content: '';
        position: absolute;
        top: -100px;
        left: -100px;
        width: 1px;
        height: 1px;
        opacity: 0;
        pointer-events: none;
        background: transparent;
    }

    /* Hack mais avançado para formulários */
    .prevent-autofill {
        position: relative;
    }

    .prevent-autofill::after {
        content: '';
        position: absolute;
        top: -1000px;
        left: -1000px;
        width: 1px;
        height: 1px;
        background: url("data:text/html,%3Cinput type='text' name='fake1' autocomplete='username'%3E%3Cinput type='password' name='fake2' autocomplete='current-password'%3E")
            no-repeat;
        opacity: 0;
        pointer-events: none;
        z-index: -1;
    }

    /* ========================================
   AUTOCOMPLETE SUGGESTIONS PREVENTION
   ======================================== */

    /* Desabilitar sugestões do navegador completamente */
    input:not([autocomplete='on']):not([data-keep-autocomplete='true']),
    textarea:not([autocomplete='on']):not([data-keep-autocomplete='true']),
    select:not([autocomplete='on']):not([data-keep-autocomplete='true']) {
        autocomplete: new-password !important;
        autocorrect: off !important;
        autocapitalize: off !important;
        spellcheck: false !important;
        /* Adicionar estas propriedades para eliminar sugestões */
        autocomplete: nope !important;
        autocomplete: disable !important;
        autocomplete: false !important;
    }

    /* Específico para campos de busca/filtro - MAIS AGRESSIVO */
    input[type='search'],
    input[placeholder*='buscar' i],
    input[placeholder*='filtrar' i],
    input[placeholder*='localizar' i],
    input[placeholder*='pesquisar' i],
    .search-input {
        autocomplete: nope !important;
        autocorrect: off !important;
        autocapitalize: off !important;
        spellcheck: false !important;
        data-form-type: other !important;
        data-lpignore: true !important;
        /* Propriedades adicionais para eliminar dropdown de sugestões */
        autocomplete: chrome-off !important;
        autocomplete: disable !important;
        autocomplete: false !important;
        autocomplete: nada !important;
        list: none !important;
    }

    /* Hack mais agressivo para Chrome/Edge */
    input[type='text']:not([list]):not([autocomplete='on']):not([data-keep-autocomplete='true']) {
        autocomplete: one-time-code !important;
    }

    /* Para campos específicos de pesquisa/filtro */
    .search-input,
    input[class*='search'],
    input[class*='filtro'],
    input[placeholder*='buscar' i],
    input[placeholder*='search' i] {
        autocomplete: one-time-code !important;
        autocomplete: new-password !important;
        autocomplete: nope !important;
    }

    /* Solução mais drástica - remove histórico de formulários */
    form {
        autocomplete: off !important;
    }

    form input:not([autocomplete='on']):not([data-keep-autocomplete='true']) {
        autocomplete: new-password !important;
        name: random !important;
    }

    body {
        /*@apply antialiased overflow-x-hidden py-3 px-3 font-roboto text-sm text-slate-800;*/
        @apply antialiased overflow-x-hidden /*py-2 px-2*/ font-roboto text-sm text-slate-800;
        @include media-breakpoint-down(sm) {
            /*@apply px-3;*/
        }
    }
}
