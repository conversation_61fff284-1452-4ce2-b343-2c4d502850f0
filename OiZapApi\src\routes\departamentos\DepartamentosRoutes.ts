import { Router } from 'express';
import authorizationApi from '../../middleware/authorizationApi';
import authApi from '../../middleware/authApi';
import { DepartamentosController } from '../../controllers/departamentos/DepartamentosController';
export const DepartamentosRoutes = Router();

DepartamentosRoutes.post('/departamentos/v1', authApi, DepartamentosController.incluir);
DepartamentosRoutes.put('/departamentos/v1', authApi, DepartamentosController.alterar);
DepartamentosRoutes.get('/departamentos/v1', authApi, DepartamentosController.listar);
DepartamentosRoutes.delete('/departamentos/v1', authApi, DepartamentosController.excluir);
