//const PostgreSQL = require('../services/PostgreSQL');
import { Request } from 'express';
import { AtendimentoHistoricosDB } from '../../data/atendimentos/AtendimentoHistoricosDB';
import { AtendimentosDB } from '../../data/atendimentos/AtendimentosDB';
import { ClienteContatosDB } from '../../data/ClienteContatosDB';
import { ClientesDB } from '../../data/ClientesDB';
import { MessagesDB } from '../../data/MessagesDB';
import { IRetorno, conflito, erroInterno, parametrosInvalidos } from '../../interfaces/IRetorno';
import { Funcoes } from '../../services/Funcoes';
import { WhatsServices } from '../../services/WhatsServices';
export class AtendimentosModel {
  async incluirAtendimentos(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.incluirAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterarAtendimentos(req: Request): Promise<IRetorno> {
    try {
      // console.log('req.body at line 17 in atendimentos/AtendimentosModel.ts:', req.body);
      const resp = await AtendimentosDB.alterarAtendimentos(req);
      if (req.body.tp_status == 'Atendimento Finalizado' || req.body.tp_status == 'Pedido Realizado') {
        req.body = {
          cd_atendimento: req.body.cd_atendimento,
          tp_status: req.body.tp_status,
          cd_estabelecimento: req.body.cd_estabelecimento,
          ds_comentario: req.body?.ds_comentario,
        };
        await AtendimentoHistoricosDB.incluirAtendimentoHistoricos(req);
      }

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async novoAtendimento(req: Request): Promise<IRetorno> {
    try {
      const body = req.body;
      // console.log('🚀 ~ AtendimentosModel.ts:39 ~ novoAtendimento ~ body:', body);

      if (!body.cd_cliente) {
        req.query = {
          cd_estabelecimento: body.cd_estabelecimento,
          nr_telefonezap: body.nr_telefone,
          cd_contato: body.cd_contato,
        };
        let respCliente = await ClientesDB.listarClientes(req);
        if (respCliente.statuscode != 200) {
          req.body = {
            cd_estabelecimento: body.cd_estabelecimento,
            ds_nome: body.ds_nome,
            nr_telefonezap: body.nr_telefone,
          };

          respCliente = await ClientesDB.incluirCliente(req);
        }
        body.cd_cliente = respCliente.data[0].cd_cliente;
        if (respCliente.statuscode == 200 && respCliente.data.length > 0) {
          if (!respCliente.data[0].cd_contato) {
            req.body = {
              cd_estabelecimento: body.cd_estabelecimento,
              cd_cliente: body.cd_cliente,
              cd_contato: body.cd_contato,
            };

            await ClienteContatosDB.incluir(req);
            //console.log('🚀 ~ AtendimentosModel.ts:68 ~ novoAtendimento ~ respCliCont:', respCliCont);
          }
        }
      }

      const hash: String = Funcoes.gerarHashUnica(body.nr_telefone, 20);

      // Create a date with the local time
      const now = new Date();
      // Format it correctly without the timezone adjustment
      const localISOString = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString();

      req.body = {
        tp_status: 'Em Atendimento',
        tp_situacao: 'Em Atendimento',
        tp_etapachat: 'Em Atendimento',
        in_stop_bot: false,
        instance: body.instance,
        nr_telefone: body.nr_telefone,
        dt_cadastro: localISOString,
        ds_contato: body.ds_nome,
        ds_foto: body.ds_foto,
        cd_cliente: body.cd_cliente,
        cd_estabelecimento: body.cd_estabelecimento,
        cd_atendente: body.cd_atendente,
        cd_departamento: body.cd_departamento,
        ds_hash: hash,
      };

      const resp = await AtendimentosDB.incluirAtendimentos(req);

      if (resp.statuscode !== 200) return resp;

      req.body = {
        cd_atendimento: resp.data[0].cd_atendimento,
        cd_estabelecimento: body.cd_estabelecimento,
        tp_status: 'Iniciado',
        cd_atendente: body.cd_atendente,
      };

      await AtendimentoHistoricosDB.incluirAtendimentoHistoricos(req);

      // req.body = {
      //   event: 'messages.upsert',
      //   tp_status_atendimento: 'Iniciado',
      //   cd_atendente: body.cd_atendente,
      //   telefone: body.nr_telefone,
      //   instance: body.instance,
      //   cd_estabelecimento: body.cd_estabelecimento,
      //   cd_atendimento: resp.data[0].cd_atendimento,
      //   message_timestamp: localISOString,
      //   from_me: true,
      //   nome: body.ds_nome,
      //   mensagem: body.mensagem,
      //   idunico: body.idunico,
      //   remote_jid: `${body.nr_telefone}@s.whatsapp.net`,
      // };
      // await MessagesDB.incluir(req);

      // req.body = {
      //   cd_atendimento: resp.data[0].cd_atendimento,
      //   message_timestamp: localISOString,
      //   url_profile_picture: body.ds_foto,
      //   cd_estabelecimento: body.cd_estabelecimento,
      //   telefone: body.nr_telefone,
      //   from_me: true,
      //   nome: body.ds_nome,
      //   mensagem: body.mensagem,
      //   idunico: body.idunico,
      //   message_id: body.idunico,
      //   remote_jid: `${body.nr_telefone}@s.whatsapp.net`,
      //   event: 'messages.upsert',
      //   tp_status_atendimento: 'Inicio',
      //   cd_atendente: body.cd_atendente,
      //   instance: body.instance,
      // };
      // await MessagesDB.incluiUltimMensagem(req);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async iniciarAtendimento(req: Request): Promise<IRetorno> {
    try {
      const resp = await AtendimentosDB.iniciarAtendimento(req);

      req.body = {
        cd_atendimento: req.body.cd_atendimento,
        tp_status: 'Iniciado',
        cd_atendente: req.body.cd_atendente,
        cd_estabelecimento: req.body.cd_estabelecimento,
      };
      await AtendimentoHistoricosDB.incluirAtendimentoHistoricos(req);

      // console.log('VAMOS VERIFICAR SE O SOCKET ESTÁ SENDO EMITIDO PELO SENDMESSAGE');

      //req.body = { event: 'status.atendimento', data: req.body };
      //await WhatsServices.emitEventSocket(req);
      // console.log('🚀 ~ AtendimentosModel.ts:159 ~ iniciarAtendimento ~ respSkt:', respSkt);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async encerraAtendimento(req: Request): Promise<IRetorno> {
    try {
      const body = req.body;

      req.body = {
        tp_status: 'Atendimento Finalizado',
        tp_situacao: 'Atendimento Finalizado',
        tp_etapachat: 'Atendimento Finalizado',
        in_stop_bot: false,
        cd_atendimento: body.cd_atendimento,
        cd_estabelecimento: body.cd_estabelecimento,
        cd_motivo: body.cd_motivo,
        ds_resumo: body.ds_resumo,
        cd_usuario_encerramento: body.cd_usuario_encerramento,
      };

      const resp = await AtendimentosDB.alterarAtendimentos(req);

      req.body = {
        cd_atendimento: body.cd_atendimento,
        cd_estabelecimento: body.cd_estabelecimento,
        tp_status: 'Encerrado',
        cd_atendente: body.cd_usuario_encerramento,
        ds_resumo: body.ds_resumo,
      };

      await AtendimentoHistoricosDB.incluirAtendimentoHistoricos(req);
      //  const resp2 =  console.log('🚀 ~ AtendimentosModel.ts:193 ~ encerraAtendimento ~ resp2:', resp2);

      // Create a date with the local time
      const now = new Date();
      // Format it correctly without the timezone adjustment
      const localISOString = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString();

      req.body = {
        event: 'Encerrado',
        tp_status_atendimento: 'Encerrado',
        cd_usuario_encerramento: body.cd_usuario_encerramento,
        cd_atendente: body.cd_usuario_encerramento,
        cd_motivo: body.cd_motivo,
        ds_resumo: body.ds_resumo,
        telefone: body.telefone,
        instance: body.instance,
        cd_estabelecimento: body.cd_estabelecimento,
        cd_atendimento: body.cd_atendimento,
        message_timestamp: localISOString,
        from_me: true,
      };
      await MessagesDB.incluir(req);
      // const resp1 = console.log('🚀 ~ AtendimentosModel.ts:214 ~ encerraAtendimento ~ resp1:', resp1);

      req.body = { event: 'status.atendimento', data: req.body };
      await WhatsServices.emitEventSocket(req);
      //  const respSkt = console.log('🚀 ~ AtendimentosModel.ts:213 ~ encerraAtendimento ~ respSkt:', respSkt);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async transferirAtendimento(req: Request): Promise<IRetorno> {
    try {
      const body = req.body;
      // console.log('🚀 ~ AtendimentosModel.ts:227 ~ transferirAtendimento ~ body:', body);
      req.body = {
        cd_atendimento: body.cd_atendimento,
        cd_departamento: body.cd_departamento,
        cd_atendente: body.cd_atendente,
        ds_comentario: body.ds_comentario,
        cd_estabelecimento: body.cd_estabelecimento,
      };
      // console.log('req.body at line 17 in atendimentos/AtendimentosModel.ts:', req.body);
      const resp = await AtendimentosDB.transferenciaAtendimentos(req);
      // console.log('resp at line 35 in atendimentos/AtendimentosModel.ts:', resp);

      req.body = {
        cd_atendimento: body.cd_atendimento,
        tp_status: 'Transferido',
        cd_atendente: body.cd_atendente,
        cd_departamento: body.cd_departamento,
        ds_comentario: body.ds_comentario,
        cd_estabelecimento: body.cd_estabelecimento,
      };
      await AtendimentoHistoricosDB.incluirAtendimentoHistoricos(req);

      // Create a date with the local time
      const now = new Date();
      // Format it correctly without the timezone adjustment
      const localISOString = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString();
      const horario = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toTimeString().substring(0, 5); // Format: HH:MM

      const dataString = `${localISOString}${body.nr_telefone}`;
      const idunico = Funcoes.gerarHashUnica(dataString, 20);

      req.body = {
        event: 'Transferido',
        tp_status_atendimento: 'Transferido',
        cd_atendente: body.cd_atendente,
        cd_departamento: body.cd_departamento,
        ds_comentario: body.ds_comentario,
        cd_atendimento: body.cd_atendimento,
        cd_estabelecimento: body.cd_estabelecimento,
        message_timestamp: localISOString,
        instance: body.instance,
        telefone: body.nr_telefone,
        from_me: true,
        idunico: idunico,
        nome: body.ds_nome,
        url_profile_picture: body.url_profile_picture,
        mensagem: body.mensagem,
      };
      await MessagesDB.incluir(req);

      const data = {
        instance: body.instance,
        event: 'Transferido',
        nome: body.ds_nome,
        from_me: true,
        type_message: 'textMessage',
        mensagem: body.mensagem,
        message_timestamp: localISOString,
        horario: horario,
        nr_telefone: body.nr_telefone,
        remote_jid: `${body.nr_telefone}@s.whatsapp.net`,
        url_profile_picture: body.url_profile_picture,
        cd_estabelecimento: body.cd_estabelecimento,
        in_stop_bot_geral: false,
        cd_atendimento: body.cd_atendimento,
        cd_cliente: body.cd_cliente,
        nm_cliente: body.nm_cliente,
        ds_hash: body.ds_hash,
        in_stop_bot: false,
        tp_status_atendimento: 'Transferido',
        origem: 'bot',
        tp_status: 'Fila',
        cd_departamento: body.cd_departamento,
        ds_departamento: body.ds_departamento,
        cd_atendente: body.cd_atendente,
        nm_atendente: body.nm_atendente,
        ds_comentario: body.ds_comentario,
      };

      req.body = { event: 'status.atendimento', data: data };

      await WhatsServices.emitEventSocket(req);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async atendido(req: Request): Promise<IRetorno> {
    try {
      const resp = await AtendimentosDB.atendido(req);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async statusAtendimentos(req: Request): Promise<IRetorno> {
    try {
      const reqBody = req.body;
      req.query = {
        ds_hash: reqBody.hash,
      };
      const respAtendimento = await AtendimentosDB.validaEtapaAtendimento(req);
      if (respAtendimento.statuscode != 200) {
        return respAtendimento;
      }

      const dataAtendimento = respAtendimento.data[0];

      if (dataAtendimento.tp_etapachat == 'Pedido Realizado') {
        return conflito('', 'Não permitido alteração, Pedido está com o status Pedido Realizado.');
      }

      let status;
      let acessouLink = false;
      if (reqBody.status === '1') {
        status = 'Acessou';
        acessouLink = true;
      } else if (reqBody.status === '2') {
        status = 'Pedido Realizado';
        acessouLink = true;
      } else {
        return parametrosInvalidos('', 'Status inválido!');
      }

      req.body = {
        tp_etapachat: status,
        ds_hash: reqBody.hash,
        in_acessoulink: acessouLink,
      };

      return await AtendimentosDB.alterarEtapaAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async stopAtendimentos(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.stopAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarAtendimentos(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.listarAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarAtendimentoPorTelefone(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.listarAtendimentoPorTelefone(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listaAtendimentosHistorico(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.listaAtendimentosHistorico(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarAtendimentosFluxo(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.listarAtendimentosFluxo(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async removerAtendimentos(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.removerAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async importaAtendimentos(req: Request): Promise<IRetorno> {
    try {
      return await AtendimentosDB.importaAtendimentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async encerraAtendimentoAberto(): Promise<IRetorno> {
    try {
      return await AtendimentosDB.encerraAtendimentoAberto();
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaAtendimentoData(req: Request): Promise<IRetorno> {
    try {
      const respPedidos = await AtendimentosDB.listaAtendimentoData(req);

      return respPedidos;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaAtendimentoHora(req: Request): Promise<IRetorno> {
    try {
      const respPedidos = await AtendimentosDB.listaAtendimentoHora(req);

      return respPedidos;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
