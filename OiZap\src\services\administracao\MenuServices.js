import api from '@/utils/api';

/**
 * Serviço para gerenciar menu dinamicamente baseado nas permissões do usuário
 */
export default class MenuServices {
  /**
   * Carrega o menu do usuário baseado nas suas permissões
   */
  static async carregarMenuUsuario() {
    try {
      const cd_usuario = localStorage.getItem('codusuario');
      if (!cd_usuario) {
        throw new Error('Usuário não autenticado');
      }

      // Buscar permissões do usuário
      const response = await api.get(`/perfis/usuario/${cd_usuario}/telas`);
      
      if (response.status === 200) {
        return this.construirMenu(response.data);
      } else {
        throw new Error('Erro ao carregar permissões');
      }
    } catch (error) {
      console.error('Erro ao carregar menu:', error);
      // Retorna menu básico em caso de erro
      return this.getMenuBasico();
    }
  }

  /**
   * Constrói o menu baseado nas permissões do usuário
   */
  static construirMenu(permissoes) {
    const menu = [];

    // Menu básico para todos os usuários
    menu.push({
      icon: 'HomeIcon',
      pageName: 'Home',
      title: 'Home',
    });

    // Adicionar telas baseadas nas permissões
    permissoes.forEach(permissao => {
      const itemMenu = this.mapearTelaParaMenu(permissao);
      if (itemMenu) {
        menu.push(itemMenu);
      }
    });

    return menu;
  }

  /**
   * Mapeia uma tela para um item do menu
   */
  static mapearTelaParaMenu(permissao) {
    const mapeamento = {
      '/dashboard': {
        icon: 'LayoutDashboardIcon',
        pageName: 'dashboard',
        title: 'Dashboard',
      },
      '/chat': {
        icon: 'MessageSquareIcon',
        pageName: 'Chat',
        title: 'Chat',
      },
      '/atendimento': {
        icon: 'HeadphonesIcon',
        pageName: 'Atendimento',
        title: 'Atendimento',
      },
      '/listaClientes': {
        icon: 'UsersRoundIcon',
        pageName: 'listaClientes',
        title: 'Clientes',
      },
      '/listaContatos': {
        icon: 'BookUserIcon',
        pageName: 'listaContatos',
        title: 'Contatos',
      },
      '/listaMensagem': {
        icon: 'MessageSquareTextIcon',
        pageName: 'listaMensagem',
        title: 'Mensagens',
      },
      '/listaFluxos': {
        icon: 'GitBranchIcon',
        pageName: 'listaFluxos',
        title: 'Fluxos',
      },
      '/integracao': {
        icon: 'SettingsIcon',
        pageName: 'Integracao',
        title: 'Configurações',
      },
      // Telas administrativas (só para Administradores)
      '/cadPerfil': {
        icon: 'UserCheckIcon',
        pageName: 'cadPerfil',
        title: 'Cadastro de Perfil',
        adminOnly: true,
      },
      '/cadTela': {
        icon: 'MonitorIcon',
        pageName: 'cadTela',
        title: 'Cadastro de Tela',
        adminOnly: true,
      },
      '/cadPerfisTelas': {
        icon: 'LinkIcon',
        pageName: 'cadPerfisTelas',
        title: 'Associação Perfis x Telas',
        adminOnly: true,
      },
      '/listaUsuarios': {
        icon: 'UsersIcon',
        pageName: 'listaUsuarios',
        title: 'Usuários',
        adminOnly: true,
      },
      '/cadUsuario': {
        icon: 'UserPlusIcon',
        pageName: 'cadUsuario',
        title: 'Cadastro de Usuário',
        adminOnly: true,
      },
      '/listaEstabelecimento': {
        icon: 'BuildingIcon',
        pageName: 'listaEstabelecimento',
        title: 'Estabelecimentos',
        adminOnly: true,
      },
      '/cadEstabelecimento': {
        icon: 'Building2Icon',
        pageName: 'cadEstabelecimento',
        title: 'Cadastro de Estabelecimento',
        adminOnly: true,
      },
    };

    return mapeamento[permissao.ds_rota];
  }

  /**
   * Retorna menu básico para usuários sem permissões específicas
   */
  static getMenuBasico() {
    return [
      {
        icon: 'HomeIcon',
        pageName: 'Home',
        title: 'Home',
      },
      {
        icon: 'LayoutDashboardIcon',
        pageName: 'dashboard',
        title: 'Dashboard',
      },
    ];
  }

  /**
   * Verifica se o usuário tem permissão para uma tela específica
   */
  static async verificarPermissao(rota) {
    try {
      const response = await api.post('/perfis/check-permission', { route: rota });
      return response.data.hasPermission;
    } catch (error) {
      console.error('Erro ao verificar permissão:', error);
      return false;
    }
  }

  /**
   * Verifica se o usuário é administrador
   */
  static async isAdministrador() {
    try {
      const cd_usuario = localStorage.getItem('codusuario');
      if (!cd_usuario) return false;

      const response = await api.get(`/perfis/usuario/${cd_usuario}/perfil`);
      return response.data?.nm_perfil === 'Administrador';
    } catch (error) {
      console.error('Erro ao verificar se é administrador:', error);
      return false;
    }
  }
} 