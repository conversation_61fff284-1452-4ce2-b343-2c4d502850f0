<template>
  <div class="flex items-center justify-center min-h-screen bg-gray-100">
    <div class="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
      <div class="mb-6">
        <div class="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
      </div>
      
      <h1 class="text-2xl font-bold text-gray-900 mb-4"><PERSON>sso Negado</h1>
      
      <p class="text-gray-600 mb-6">
        Você não tem permissão para acessar esta página. 
        Entre em contato com o administrador do sistema para solicitar acesso.
      </p>
      
      <div class="space-y-3">
        <button 
          @click="goBack"
          class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
        >
          Voltar
        </button>
        
        <button 
          @click="goHome"
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
        >
          Ir para Home
        </button>
        
        <button 
          @click="logout"
          class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
        >
          Fazer Logout
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { useAuthMiddleware } from '@/middleware/auth';

export default {
  name: 'AccessDenied',
  setup() {
    const router = useRouter();
    const { logout: logoutUser } = useAuthMiddleware();

    const goBack = () => {
      router.go(-1);
    };

    const goHome = () => {
      router.push('/home');
    };

    const logout = () => {
      logoutUser();
    };

    return {
      goBack,
      goHome,
      logout
    };
  }
};
</script> 