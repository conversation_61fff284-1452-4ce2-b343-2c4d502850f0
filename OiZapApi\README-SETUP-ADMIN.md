# Setup do Perfil Administrador - OiZap

Este documento explica como configurar apenas o perfil Administrador no sistema de perfis e telas do OiZap.

## 📋 Pré-requisitos

1. **Backend rodando**: Certifique-se de que o OiZapApi está rodando na porta 3100
2. **Frontend rodando**: Certifique-se de que o OiZap está rodando na porta 8080
3. **Usuário existente**: Deve existir um usuário com `cd_usuario = 87` no banco de dados

## 🚀 Scripts Disponíveis

### Para Windows (PowerShell - Recomendado)
```powershell
.\setup-admin-only.ps1
```

### Para Windows (Batch)
```cmd
setup-admin-only.bat
```

## 📝 O que os scripts fazem

1. **Criar 1 perfil**:
   - Administrador

2. **Criar 3 telas administrativas**:
   - `/cadPerfil` - Cadastro de Perfil
   - `/cadTela` - Cadastro de Tela
   - `/cadPerfisTelas` - Associação Perfis x Telas

3. **Associar usuário 87 ao perfil Administrador**

4. **Associar as 3 telas administrativas ao perfil Administrador**

## 🔧 Execução Manual

Se preferir executar manualmente, use os seguintes comandos curl:

### 1. Criar Perfil Administrador
```bash
curl -X POST http://localhost:3100/perfis \
  -H "Content-Type: application/json" \
  -d '{"nm_perfil": "Administrador"}'
```

### 2. Criar Telas Administrativas
```bash
curl -X POST http://localhost:3100/telas \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Cadastro de Perfil", "ds_rota": "/cadPerfil"}'

curl -X POST http://localhost:3100/telas \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Cadastro de Tela", "ds_rota": "/cadTela"}'

curl -X POST http://localhost:3100/telas \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Associação Perfis x Telas", "ds_rota": "/cadPerfisTelas"}'
```

### 3. Associar Usuário ao Perfil
```bash
curl -X POST http://localhost:3100/usuarios/87/perfis \
  -H "Content-Type: application/json" \
  -d '{"cd_perfil": 1}'
```

### 4. Associar Telas ao Perfil
```bash
curl -X POST http://localhost:3100/perfis/associar \
  -H "Content-Type: application/json" \
  -d '{"cd_perfil": 1, "telas": [1, 2, 3]}'
```

## 🔍 Verificação

Após executar o script, você pode verificar se tudo foi criado corretamente:

### Listar Perfis
```bash
curl http://localhost:3100/perfis
```

### Listar Telas
```bash
curl http://localhost:3100/telas
```

### Verificar Rotas Permitidas do Usuário
```bash
curl http://localhost:3100/usuarios/87/rotas-permitidas
```

## 🎯 Acesso às Telas

Após a configuração, você pode acessar:

- **Cadastro de Perfil**: http://localhost:8080/cadPerfil
- **Cadastro de Tela**: http://localhost:8080/cadTela
- **Associação Perfis x Telas**: http://localhost:8080/cadPerfisTelas

## ⚠️ Observações

1. **ID Assumido**: O script assume que o perfil Administrador será criado com `cd_perfil = 1`
2. **IDs das Telas**: O script assume que as telas serão criadas com IDs sequenciais (1, 2, 3)
3. **Usuário 87**: Certifique-se de que o usuário com `cd_usuario = 87` existe no banco
4. **Portas**: Verifique se as portas 3100 (API) e 8080 (Frontend) estão corretas

## 🆘 Solução de Problemas

### Erro de Conexão
- Verifique se o backend está rodando na porta 3100
- Verifique se não há firewall bloqueando a conexão

### Erro de Usuário não Encontrado
- Verifique se o usuário com `cd_usuario = 87` existe na tabela `usuarios`
- Se necessário, crie o usuário primeiro

### Erro de IDs
- Se os IDs não forem sequenciais, ajuste manualmente os valores nos scripts
- Verifique os IDs retornados nas respostas das APIs

## 🔄 Próximos Passos

Após configurar o perfil Administrador, você pode:

1. **Acessar as telas administrativas** para gerenciar perfis e telas
2. **Criar outros perfis** através da interface administrativa
3. **Associar usuários a perfis** através da interface administrativa
4. **Configurar permissões** para diferentes rotas do sistema

## 📞 Suporte

Se encontrar problemas, verifique:
1. Logs do backend
2. Respostas das APIs
3. Status do banco de dados
4. Configuração das portas 