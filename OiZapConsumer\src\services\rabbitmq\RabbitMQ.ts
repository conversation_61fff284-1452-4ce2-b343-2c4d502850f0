import { Channel } from 'amqplib';
import { erroInterno, IRetorno, sucesso } from '../../interfaces/IRetorno';
import { connectedRabbitMQInstances } from '../ConnectedRabbitMQInstances';
import Logger from '../Logger';
import RabbitMQConnectionManager from './RabbitMQConnectionManager';
import { queueTypesEvolution, queueTypesOiZap } from './RabbitMQQueue';

const logger = Logger.getLogger();
const connectionManager = RabbitMQConnectionManager.getInstance();

// Constantes para DLX e TTL (apenas para oizap.sendmessage)
const DEAD_LETTER_EXCHANGE = 'dlx.oizap.sendmessage';
const RETRY_EXCHANGE = 'retry.oizap.sendmessage';
const MAX_RETRIES = 3;

// Faixas de delay
const RETRY_DELAY_RANGES = [
  { min: 60000, max: 120000 }, // 1ª tentativa: 1-2min
  { min: 180000, max: 360000 }, // 2ª tentativa: 3-6min
  { min: 600000, max: 900000 }, // 3ª tentativa: 10-15min
];

function generateRandomDelay(retryCount: number): number {
  const range = RETRY_DELAY_RANGES[retryCount] || RETRY_DELAY_RANGES[RETRY_DELAY_RANGES.length - 1];
  let baseDelay = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;

  const jitter = Math.random() * 0.3 - 0.15;
  baseDelay = Math.floor(baseDelay * (1 + jitter));

  logger.debug(
    `🎲 Generated delay for attempt ${retryCount + 1}: ${Math.floor(baseDelay / 1000)}s (range: ${Math.floor(range.min / 1000)}-${Math.floor(range.max / 1000)}s)`,
  );

  return baseDelay;
}

async function setupQueues(serverName: string, ch: Channel, instanceName: string) {
  let queueTypes: string[] = [];

  if (serverName === 'evolution') {
    queueTypes = queueTypesEvolution;
  } else if (serverName === 'oizap') {
    queueTypes = queueTypesOiZap;
  }

  for (const queueType of queueTypes) {
    const queueName = `${instanceName}.${queueType}`;
    const deadLetterQueueName = `${queueName}.dlq`;
    const retryQueueName = `${queueName}.retry`;

    try {
      if (queueType === 'oizap.sendmessage') {
        logger.debug(`Iniciando setup de filas para ${queueName}`);

        // Criar exchanges
        await ch.assertExchange(DEAD_LETTER_EXCHANGE, 'direct', { durable: true });
        await ch.assertExchange(RETRY_EXCHANGE, 'direct', { durable: true });

        // Criar fila principal
        await ch.assertQueue(queueName, {
          durable: true,
          arguments: {
            'x-queue-type': 'quorum',
            'x-dead-letter-exchange': DEAD_LETTER_EXCHANGE,
            'x-dead-letter-routing-key': deadLetterQueueName,
          },
        });

        // Criar Dead Letter Queue
        await ch.assertQueue(deadLetterQueueName, {
          durable: true,
          arguments: {
            'x-queue-type': 'quorum',
          },
        });

        // Criar Retry Queue
        await ch.assertQueue(retryQueueName, {
          durable: true,
          arguments: {
            'x-queue-type': 'quorum',
            'x-dead-letter-exchange': '',
            'x-dead-letter-routing-key': queueName,
          },
        });

        // Fazer os bindings
        await ch.bindQueue(deadLetterQueueName, DEAD_LETTER_EXCHANGE, deadLetterQueueName);
        await ch.bindQueue(retryQueueName, RETRY_EXCHANGE, retryQueueName);

        await ch.prefetch(1);
        logger.info(`✅ Setup completo para ${queueName} com DLX e Retry`);
      } else {
        await ch.assertQueue(queueName, {
          durable: true,
          arguments: { 'x-queue-type': 'quorum' },
        });
        await ch.prefetch(1);
        logger.debug(`✅ Fila ${queueName} criada com sucesso`);
      }
    } catch (error: any) {
      logger.error(`❌ Erro ao configurar fila ${queueName}: ${error.message}`);
      throw error;
    }
  }
}

/**
 * Conecta ao RabbitMQ usando o novo gerenciador de conexões
 */
export async function connectRabbitMQ(serverName: string, url: string): Promise<void> {
  console.log('🚀 ~ RabbitMQ.ts:112 ~ connectRabbitMQ ~ url:', url);
  console.log('🚀 ~ RabbitMQ.ts:112 ~ connectRabbitMQ ~ serverName:', serverName);
  try {
    // Conectar ao servidor (uma única conexão)
    await connectionManager.connectToServer(serverName, url);

    const instances = connectedRabbitMQInstances.getInstances();

    for (const instanceName of instances) {
      // Criar canal para esta instância
      const channel = await connectionManager.createChannelForInstance(serverName, instanceName);

      // Setup das filas para esta instância
      await setupQueues(serverName, channel, instanceName);

      logger.info(`✅ Instância ${instanceName} configurada no servidor ${serverName}`);
    }

    logger.info(`🎉 Servidor ${serverName} totalmente configurado com ${instances.length} instâncias`);
  } catch (error: any) {
    logger.error(`❌ Falha ao conectar no servidor ${serverName}: ${error.message}`);
    throw error;
  }
}

export async function consumeQueue(
  serverName: string,
  instanceName: string,
  queueType: string,
  callback: (msg: any) => Promise<{ message: string; data: any }>,
) {
  const channel = connectionManager.getChannel(serverName, instanceName);

  if (!channel) {
    throw new Error(`Canal não encontrado para ${serverName}-${instanceName}`);
  }

  const queueName = `${instanceName}.${queueType}`;

  if (
    (serverName === 'evolution' && queueTypesEvolution.includes(queueType)) ||
    (serverName === 'oizap' && queueTypesOiZap.includes(queueType))
  ) {
    await channel.consume(queueName, async (msg) => {
      if (msg !== null) {
        const content = msg.content.toString();

        try {
          const respCalBack = await callback(JSON.parse(content));

          if (respCalBack.message === 'ack') {
            channel.ack(msg);
          } else if (respCalBack.message === 'reject') {
            channel.reject(msg, false);
            logger.error(`❌ Message rejected for ${queueName}: ${content}`);
          } else if (respCalBack.message === 'nack-true' && queueType === 'oizap.sendmessage') {
            const currentRetryCount = (msg.properties.headers?.['x-retry-count'] || 0) as number;
            const nextRetryCount = currentRetryCount + 1;

            if (currentRetryCount >= MAX_RETRIES) {
              const deadLetterQueueName = `${queueName}.dlq`;
              channel.publish(DEAD_LETTER_EXCHANGE, deadLetterQueueName, msg.content, {
                headers: {
                  ...msg.properties.headers,
                  'x-retry-count': currentRetryCount,
                  'x-dlq-reason': 'MAX_RETRIES_EXCEEDED',
                  'x-dlq-timestamp': new Date().toISOString(),
                },
              });
              channel.ack(msg);
              logger.error(
                `🚫 Message exceeded retry limit (${MAX_RETRIES}), moved to DLQ after ${currentRetryCount} attempts: ${queueName}`,
              );
            } else {
              const delayMs = generateRandomDelay(currentRetryCount);
              const retryQueueName = `${queueName}.retry`;

              channel.publish(RETRY_EXCHANGE, retryQueueName, msg.content, {
                headers: {
                  ...msg.properties.headers,
                  'x-retry-count': nextRetryCount,
                  'x-retry-delay': delayMs,
                  'x-retry-timestamp': new Date().toISOString(),
                  'x-original-error': JSON.stringify(respCalBack.data || {}),
                },
                expiration: delayMs.toString(),
              });
              channel.ack(msg);
              logger.info(
                `🔄 Message scheduled for retry ${nextRetryCount}/${MAX_RETRIES} in ${Math.floor(delayMs / 1000)}s: ${queueName}`,
              );
            }
          } else if (respCalBack.message === 'nack-true') {
            channel.nack(msg, false, true);
          } else {
            channel.nack(msg, false, false);
            logger.error(`❌ Error processing message, nack: ${queueName} ${content}`);
          }
        } catch (callbackError: any) {
          logger.error(`❌ Error executing callback for ${queueName}: ${callbackError.message}`);
          channel.nack(msg, false, false);
        }
      }
    });
  } else {
    logger.warn(`⚠️ Queue type ${queueType} not allowed for server ${serverName}`);
  }
}

export async function publishToQueue(serverName: string, instanceName: string, queueType: string, message: any) {
  const channel = connectionManager.getChannel(serverName, instanceName);

  if (!channel) {
    throw new Error(`Canal não encontrado para ${serverName}-${instanceName}`);
  }

  const queueName = `${instanceName}.${queueType}`;

  const headers =
    queueType === 'oizap.sendmessage'
      ? {
          'x-retry-count': 0,
          'x-original-timestamp': new Date().toISOString(),
        }
      : {};

  channel.sendToQueue(queueName, Buffer.from(JSON.stringify(message)), { headers });

  if (queueType === 'oizap.sendmessage') {
    logger.debug(`📤 Message published to ${queueName}`);
  }
}

export async function disconnectRabbitMQ(): Promise<void> {
  await connectionManager.closeAllConnections();
}

export async function removeInstanceQueues(serverName: string, instanceName: string): Promise<IRetorno> {
  try {
    const channel = connectionManager.getChannel(serverName, instanceName);

    if (!channel) {
      throw new Error(`Canal não encontrado para instância: ${instanceName}`);
    }

    const queueTypes = serverName === 'evolution' ? queueTypesEvolution : queueTypesOiZap;

    for (const queueType of queueTypes) {
      const queueName = `${instanceName}.${queueType}`;

      if (queueType === 'oizap.sendmessage') {
        const deadLetterQueueName = `${queueName}.dlq`;
        const retryQueueName = `${queueName}.retry`;

        await channel.deleteQueue(queueName);
        await channel.deleteQueue(deadLetterQueueName);
        await channel.deleteQueue(retryQueueName);
      } else {
        await channel.deleteQueue(queueName);
      }

      logger.debug(`🗑️ Queues removed for ${queueName}`);
      connectedRabbitMQInstances.removeInstance(queueName);
    }

    // Remover canal da instância
    await connectionManager.removeChannel(serverName, instanceName);

    logger.info(`✅ Instance ${instanceName} disconnected from RabbitMQ server ${serverName}`);
    return sucesso('Queue deleted successfully');
  } catch (error: any) {
    logger.error(`❌ Error removing queues for instance: ${instanceName} on server: ${serverName}: ${error.message}`);
    return erroInterno(error, 'Error deleting queue');
  }
}

/**
 * Função para obter estatísticas das conexões
 */
export function getConnectionStats(): any {
  return connectionManager.getConnectionStats();
}
