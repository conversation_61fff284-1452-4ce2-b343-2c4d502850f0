-- Migration: add_qt_instancia_qt_users_estabelecimento_modulos
-- Created: 2025-06-25T04:02:16.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar colunas qt_instancias e qt_usuarios na tabela estabelecimento_modulos
-- ========================================

-- Adicionar qt_instancias
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'estabelecimento_modulos' 
        AND column_name = 'qt_instancias'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE estabelecimento_modulos ADD COLUMN qt_instancias INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN estabelecimento_modulos.qt_instancias IS 'Quantidade de instâncias contratada no estabelecimento';
        RAISE NOTICE 'Coluna qt_instancias adicionada à tabela estabelecimento_modulos';
    ELSE
        RAISE NOTICE 'Coluna qt_instancias já existe na tabela estabelecimento_modulos';
    END IF;
END $$;

-- Adicionar qt_usuarios
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'estabelecimento_modulos' 
        AND column_name = 'qt_usuarios'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE estabelecimento_modulos ADD COLUMN qt_usuarios INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN estabelecimento_modulos.qt_usuarios IS 'Quantidade de usuários contratados no estabelecimento';
        RAISE NOTICE 'Coluna qt_usuarios adicionada à tabela estabelecimento_modulos';
    ELSE
        RAISE NOTICE 'Coluna qt_usuarios já existe na tabela estabelecimento_modulos';
    END IF;
END $$; 