-- Migration: add_index_messages
-- Created: 2025-06-22T01:03:20.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar índices na tabela messages
-- ========================================

-- Criar índices na tabela messages para otimizar consultas
CREATE INDEX IF NOT EXISTS messages_cd_departamento_idx ON messages(cd_departamento);
CREATE INDEX IF NOT EXISTS messages_cd_atendente_idx ON messages(cd_atendente);
CREATE INDEX IF NOT EXISTS messages_cd_motivo_idx ON messages(cd_motivo);

-- Coment<PERSON><PERSON>s
COMMENT ON INDEX messages_cd_departamento_idx IS 'Índice para busca por departamento';
COMMENT ON INDEX messages_cd_atendente_idx IS 'Índice para busca por atendente';
COMMENT ON INDEX messages_cd_motivo_idx IS 'Índice para busca por motivo'; 