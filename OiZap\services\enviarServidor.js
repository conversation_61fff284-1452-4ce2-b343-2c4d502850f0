const { NodeSSH } = require('node-ssh');
const { execSync } = require('child_process');
const inquirer = require('inquirer');
const deploy = require('./deploy.js');
const utils = require('./utils.js');
const dotenv = require('dotenv');
const fs = require('fs-extra');

dotenv.config();

// Função simples para colorir texto
const colors = {
    black: (text) => `\x1b[38;2;0;0;0m${text}\x1b[0m`, // Preto (RGB: 0,0,0)
    white: (text) => `\x1b[38;2;255;255;255m${text}\x1b[0m`, // Branco (RGB: 255,255,255)
    gray: (text) => `\x1b[38;2;128;128;128m${text}\x1b[0m`, // Cinza (RGB: 128,128,128)
    red: (text) => `\x1b[38;2;255;0;0m${text}\x1b[0m`, // Vermelho (RGB: 255,0,0)
    green: (text) => `\x1b[38;2;0;255;0m${text}\x1b[0m`, // Verde (RGB: 0,255,0)
    blue: (text) => `\x1b[38;2;0;0;255m${text}\x1b[0m`, // Azul (RGB: 0,0,255)
    yellow: (text) => `\x1b[38;2;255;255;0m${text}\x1b[0m`, // Amarelo (RGB: 255,255,0)
    orange: (text) => `\x1b[38;2;255;165;0m${text}\x1b[0m`, // Laranja (RGB: 255,165,0)
    cyan: (text) => `\x1b[38;2;0;255;255m${text}\x1b[0m`, // Ciano (RGB: 0,255,255)
    magenta: (text) => `\x1b[38;2;255;0;255m${text}\x1b[0m`, // Magenta (RGB: 255,0,255)
    pink: (text) => `\x1b[38;2;255;192;203m${text}\x1b[0m`, // Rosa (RGB: 255,192,203)
    purple: (text) => `\x1b[38;2;128;0;128m${text}\x1b[0m`, // Roxo (RGB: 128,0,128)
    brown: (text) => `\x1b[38;2;165;42;42m${text}\x1b[0m`, // Marrom (RGB: 165,42,42)
    lime: (text) => `\x1b[38;2;50;205;50m${text}\x1b[0m`, // Verde-limão (RGB: 50,205,50)
    gold: (text) => `\x1b[38;2;255;215;0m${text}\x1b[0m`, // Dourado (RGB: 255,215,0)
    silver: (text) => `\x1b[38;2;192;192;192m${text}\x1b[0m`, // Prata (RGB: 192,192,192)
};

const ssh = new NodeSSH();

async function getConfiguracoes() {
    const questions = [
        {
            type: 'list',
            name: 'ambiente',
            message: 'Selecione o ambiente:',
            choices: [
                {
                    name: colors.green('sandbox'),
                    value: 'sandbox',
                },
                {
                    name: colors.blue('oizap'),
                    value: 'oizap',
                },
                {
                    name: colors.yellow('devzap'),
                    value: 'devzap',
                },
                {
                    name: colors.orange('crm'),
                    value: 'crm',
                },
            ],
        },
        {
            type: 'list',
            name: 'compilar',
            message: 'Deseja compilar antes de enviar:',
            choices: [
                {
                    name: colors.green('Sim'),
                    value: true,
                },
                {
                    name: colors.red('Não'),
                    value: false,
                },
            ],
            default: true,
        },
        {
            type: 'list',
            name: 'servidor',
            message: 'Selecione o servidor:',
            choices: [
                {
                    name: colors.cyan('get'),
                    value: 'get',
                },
                {
                    name: colors.magenta('hetzner'),
                    value: 'hetzner',
                },
            ],
            default: 'get',
        },
        {
            type: 'list',
            name: 'versao',
            message: 'Deseja alterar a versão?',
            choices: [
                {
                    name: colors.red('Não'),
                    value: 'nao',
                },
                {
                    name: colors.green('Patch (correções)'),
                    value: 'patch',
                },
                {
                    name: colors.yellow('Minor (funcionalidades)'),
                    value: 'minor',
                },
                {
                    name: colors.blue('Major (mudanças grandes)'),
                    value: 'major',
                },
            ],
            default: 'nao',
        },
        {
            type: 'list',
            name: 'confirmar', // Mudando o nome aqui para corresponder à verificação
            message: (answers) => {
                // Define as cores para cada tipo de ambiente
                const ambienteColors = {
                    sandbox: colors.green,
                    oizap: colors.blue,
                    dev: colors.yellow,
                    crm: colors.orange,
                };

                // Define as cores para cada tipo de versão
                const versaoColors = {
                    nao: colors.red,
                    patch: colors.green,
                    minor: colors.yellow,
                    major: colors.blue,
                };

                return colors.white(
                    '\nConfirma o envio para:\n' +
                        'Ambiente: ' +
                        ambienteColors[answers.ambiente](answers.ambiente) +
                        '\n' +
                        'Compilar: ' +
                        (answers.compilar ? colors.green('Sim') : colors.red('Não')) +
                        '\n' +
                        'Servidor: ' +
                        (answers.servidor === 'get' ? colors.cyan('get') : colors.magenta('hetzner')) +
                        '\n' +
                        'Alterar versão: ' +
                        (answers.versao !== 'nao' ? versaoColors[answers.versao](answers.versao) : colors.red('Não')) +
                        '\nDeseja continuar?\n'
                );
            },
            choices: [
                {
                    name: colors.green('Sim'),
                    value: true,
                },
                {
                    name: colors.red('Não'),
                    value: false,
                },
            ],
            default: true,
        },
    ];

    return await inquirer.prompt(questions);
}

// Modifique a função start para usar as configurações
async function start() {
    try {
        const config = await getConfiguracoes();
        if (!config.confirmar) {
            console.log(colors.red('Operação cancelada pelo usuário'));
            return;
        }

        // Executa o script de versão se necessário
        if (config.versao !== 'nao') {
            console.log(colors.yellow(`\nAtualizando versão (${config.versao})...`));
            try {
                execSync(`npm run version ${config.versao}`, { stdio: 'inherit' });

                const pkg = JSON.parse(fs.readFileSync('package.json', 'utf-8')); // Use fs.readFileSync
                const version = pkg.version;
                console.log(colors.green(`Nova versão: ${version}`));
            } catch (err) {
                console.error(colors.red('Erro ao atualizar versão:'), err);
                return;
            }
        }

        const ambiente = config.ambiente;
        const compila = config.compilar ? 'compila' : '';
        const servidor = config.servidor;

        let hostAPI;
        let hostServer;
        let userServer;
        let passServer;
        let portServer;
        await deploy.start(ambiente);

        if (ambiente == 'oizap') {
            hostAPI = process.env.VITE_API_HOST + '/oizap';
        } else if (ambiente == 'sandbox') {
            hostAPI = process.env.VITE_API_HOST_SANDBOX + '/oizap';
        } else if (ambiente == 'dev') {
            hostAPI = process.env.VITE_API_HOST_DEV + '/oizap';
        } else if (ambiente == 'crm') {
            hostAPI = process.env.VITE_API_HOST_CRM + '/oizap';
        } else {
            console.log('ambiente não informado');
            return;
        }

        if (compila == 'compila') {
            console.log('Preparando build para ' + ambiente);
            console.log(`Executando build... enviar${ambiente}`, `npm run enviar${ambiente}`);
            try {
                execSync(`npm run enviar${ambiente}`, { stdio: 'inherit' }); // Executa o comando sincronamente
            } catch (err) {
                console.error('Erro ao executar o comando de build:', err);
                return;
            }
        } else {
            console.log('Compilação não solicitada, utilizando a versão já compilada');
        }

        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf-8')); // Use fs.readFileSync
        const versao = pkg.version;

        console.log(
            'Enviando a versão ' +
                versao +
                ' para o ambiente ' +
                ambiente +
                ' no servidor ' +
                servidor +
                ' ... aguarde ...'
        );
        //console.log('Enviando a versão para o ambiente ' + ambiente + ' ... aguarde ...');
        try {
            if (ambiente == 'oizap') {
                var destino = process.env.DESTINY;
            } else if (ambiente == 'sandbox') {
                var destino = process.env.DESTINY_SANDBOX;
            } else if (ambiente == 'dev') {
                var destino = process.env.DESTINY_DEV;
            } else if (ambiente == 'crm') {
                var destino = process.env.DESTINY_CRM;
            } else {
                console.log('ambiente não informado');
                return;
            }
            // console.log('destino at line 57 in services/enviarServidor.js:', destino);

            if (servidor == 'get') {
                hostServer = process.env.SERVERGET;
                portServer = process.env.PORTGET;
                userServer = process.env.USERGET;
                passServer = process.env.PASSGET;
            } else {
                hostServer = process.env.SERVER;
                portServer = process.env.PORT;
                userServer = process.env.USER;
                passServer = process.env.PASS;
            }
            console.log('hostServer at line 64 in services/enviarServidor.js:', hostServer);
            // console.log('portServer at line 65 in services/enviarServidor.js:', portServer);
            // console.log('userServer at line 66 in services/enviarServidor.js:', userServer);
            // console.log('passServer at line 67 in services/enviarServidor.js:', passServer);

            let totalFiles = 0;
            let filesProcessed = 0;

            async function countFiles(directory) {
                try {
                    // Usa powershell para contar arquivos de forma mais eficiente
                    const result = execSync(
                        `powershell -Command "(Get-ChildItem -Path '${directory}' -Recurse -File).Count"`,
                        {
                            encoding: 'utf-8',
                            cwd: process.cwd(),
                        }
                    );
                    const count = parseInt(result.trim());
                    return count || 0;
                } catch (error) {
                    console.error('Erro ao contar arquivos:', error.message);
                    return 0;
                }
            }

            await ssh.connect({
                host: hostServer,
                port: portServer,
                username: userServer,
                password: passServer,
            });

            console.log(colors.blue('\nIniciando envio dos arquivos...'));

            const distPath = `${process.cwd()}/dist`;
            totalFiles = await countFiles(distPath);

            if (totalFiles === 0) {
                throw new Error(`Nenhum arquivo encontrado na pasta dist (${distPath})`);
            }

            console.log(colors.yellow(`Total de arquivos a enviar: ${totalFiles}\n`));

            await ssh.putDirectory('dist', destino, {
                recursive: true,
                concurrency: 10,
                tick: function (localPath, remotePath, error) {
                    filesProcessed++;
                    // Ajusta o cálculo para garantir que o último arquivo mostre 100%
                    const percentage = Math.min((filesProcessed / totalFiles) * 100, 100).toFixed(2);
                    const progressBar =
                        '='.repeat(Math.floor(50 * (filesProcessed / totalFiles))) +
                        (filesProcessed < totalFiles ? '>' : '') +
                        ' '.repeat(Math.max(0, 50 - Math.floor(50 * (filesProcessed / totalFiles))));

                    // Obtém o tamanho do terminal
                    const terminalWidth = process.stdout.columns || 80;

                    // Limpa a linha inteira com espaços
                    process.stdout.write('\r' + ' '.repeat(terminalWidth) + '\r');

                    // Formata o nome do arquivo para exibição
                    const fileName = localPath.replace('dist\\', '').split('\\').pop();

                    // Define texto baseado no progresso
                    const statusText = filesProcessed === totalFiles ? 'Concluído!' : fileName;
                    const displayText = `[${progressBar}] ${percentage}% | ${statusText}`;

                    if (error) {
                        process.stdout.write(colors.red(`[${progressBar}] ${percentage}% | Erro: ${fileName}`));
                    } else {
                        process.stdout.write(colors.green(displayText));
                    }
                },
            });

            // Força exibição de 100% após concluir
            const terminalWidth = process.stdout.columns || 80;
            process.stdout.write('\r' + ' '.repeat(terminalWidth) + '\r');
            process.stdout.write(colors.green(`[${'='.repeat(50)}] 100.00% | Concluído!\n`));

            // Adiciona uma nova linha após concluir
            console.log('\n');
            console.log(colors.green('\nArquivos copiados com sucesso! ✨'));
            console.log(colors.blue(`Total de ${totalFiles} arquivos enviados para ${ambiente}`));
            console.log('Versao atualizada com sucesso no ' + ambiente);
            // console.log('Versao ' + versao + ' atualizada com sucesso no ' + ambiente);

            console.log('hostAPI at line 352 in services/enviarServidor.js:', hostAPI);
            const resultAlterarVersao = await utils.alterarVersao(hostAPI, ambiente.toUpperCase() + '_OIZAP', versao);
            if (resultAlterarVersao.statuscode != 200) {
                console.log('resultAlterarVersao', resultAlterarVersao.message);
                //return;
            }
        } catch (err) {
            console.error('Erro ao copiar arquivos:', err);
        } finally {
            ssh.dispose();
        }

        console.log('Fim');
    } catch (error) {
        console.error('Erro na execução:', error);
    }
}

start();
