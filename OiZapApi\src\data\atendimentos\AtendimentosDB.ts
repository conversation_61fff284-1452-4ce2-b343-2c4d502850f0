require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno, parametrosInvalidos } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';
export class AtendimentosDB {
  static async incluirAtendimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['atendimentos'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarAtendimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['atendimentos'],
        chaves: { cd_atendimento: req.body.cd_atendimento },
        dados: req.body,
        retorno: '*',
      };
      //console.log('req.body at line 27 in atendimentos/AtendimentosDB.ts:', req.body);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async iniciarAtendimento(req: any): Promise<IRetorno> {
    // console.log('req at line 25 in data/AtendimentosDB.ts:', req);
    try {
      let sql = `update atendimentos set tp_status = 'Em Atendimento',tp_etapachat='Em Atendimento',dt_alteracao=current_timestamp,cd_atendente = ${req.body.cd_atendente}`;
      sql += ` where cd_atendimento = ${req.body.cd_atendimento}`;

      //console.log('sql at line 50 in data/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async transferenciaAtendimentos(req: Request): Promise<IRetorno> {
    try {
      let sql = '';
      sql += `update atendimentos set dt_alteracao = current_timestamp, tp_etapachat = 'Transferido' `;
      if (req.body.cd_departamento) {
        sql += `, cd_departamento = ${req.body.cd_departamento}`;
      }
      if (req.body.cd_atendente) {
        sql += `, cd_atendente = ${req.body.cd_atendente}`;
      }
      if (req.body.ds_comentario) {
        sql += `  , ds_comentario = '${req.body.ds_comentario}'`;
      }
      sql += ` where cd_atendimento = ${req.body.cd_atendimento}
      and cd_estabelecimento = ${req.body.cd_estabelecimento} returning cd_atendimento;`;

      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alteraSituacaoAtendimento(req: Request): Promise<IRetorno> {
    try {
      const sql = `update atendimentos set tp_situacao='${req.body.tp_situacao}', in_stop_bot = ${req.body.in_stop_bot}, dt_alteracao = current_timestamp where cd_atendimento = ${req.body.cd_atendimento} `;
      //console.log('req.body at line 27 in atendimentos/AtendimentosDB.ts:', req.body);
      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async encerraAtendimentoAberto(): Promise<IRetorno> {
    try {
      let sql = `INSERT INTO atendimento_historicos (cd_atendimento, tp_status, dt_cadastro, cd_estabelecimento,ds_comentario)
SELECT a.cd_atendimento,'Atendimento Finalizado' tp_status,current_timestamp dt_cadastro,a.cd_estabelecimento,'Atendimento Finalizado Automaticamente' ds_comentario
FROM atendimentos a
inner join messages m on m.cd_atendimento = a.cd_atendimento and m.cd_estabelecimento = a.cd_estabelecimento 
LEFT JOIN  parametros e  ON e.cd_estabelecimento = a.cd_estabelecimento
WHERE a.tp_situacao NOT IN ('Atendimento Finalizado', 'Atendimento Cancelado')
AND (CASE WHEN cast(e.hr_tempo_encerra_atendimento as int) > 0 THEN 
     a.dt_alteracao < NOW() - CAST(e.hr_tempo_encerra_atendimento || ' hours' AS INTERVAL) 
      ELSE a.dt_alteracao < NOW() - INTERVAL '24 hours' END)
and m.from_me=false 
and m.date_read is null   `;

      await new PostgreSQLServices().query(sql);

      sql = `update messages set date_read = current_timestamp
from (
SELECT m.id,m.date_read 
FROM atendimentos a
inner join messages m on m.cd_atendimento = a.cd_atendimento and m.cd_estabelecimento = a.cd_estabelecimento 
LEFT JOIN  parametros e  ON e.cd_estabelecimento = a.cd_estabelecimento
WHERE a.tp_situacao NOT IN ('Atendimento Finalizado', 'Atendimento Cancelado')
AND (CASE WHEN cast(e.hr_tempo_encerra_atendimento as int) > 0 THEN 
     a.dt_alteracao < NOW() - CAST(e.hr_tempo_encerra_atendimento || ' hours' AS INTERVAL) 
      ELSE a.dt_alteracao < NOW() - INTERVAL '24 hours' END)
and m.from_me=false 
and m.date_read is null  
) as z
where z.cd_estabelecimento = messages.cd_estabelecimento
and z.id = messages.id`;

      await new PostgreSQLServices().query(sql);

      sql = `UPDATE atendimentos SET tp_situacao = 'Atendimento Finalizado', tp_status = 'Atendimento Finalizado', tp_etapachat = 'Atendimento Finalizado', dt_alteracao = current_timestamp
from (SELECT e.hr_tempo_encerra_atendimento,a.cd_atendimento,a.cd_estabelecimento
FROM atendimentos a
LEFT JOIN  parametros e  ON e.cd_estabelecimento = a.cd_estabelecimento
WHERE a.tp_situacao NOT IN ('Atendimento Finalizado', 'Atendimento Cancelado')
AND (CASE WHEN cast(e.hr_tempo_encerra_atendimento as int) > 0 THEN 
     a.dt_alteracao < NOW() - CAST(e.hr_tempo_encerra_atendimento || ' hours' AS INTERVAL) 
     ELSE a.dt_alteracao < NOW() - INTERVAL '24 hours' END)
) as z
where z.cd_estabelecimento = atendimentos.cd_estabelecimento
and z.cd_atendimento = atendimentos.cd_atendimento   `;

      //console.log('req.body at line 27 in atendimentos/AtendimentosDB.ts:', req.body);
      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async atendido(req: Request): Promise<IRetorno> {
    try {
      let sql = `update atendimentos set tp_etapachat = tp_status,in_stop_bot = false, dt_start_stop_bot = current_timestamp `;
      sql += ` where cd_atendimento = '${req.body.cd_atendimento}' returning cd_atendimento;`;
      // console.log('sql at line 41 in atendimentos/AtendimentosDB.ts:', sql);
      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarEtapaAtendimentos(req: Request): Promise<IRetorno> {
    try {
      let sql = `update atendimentos set tp_etapachat = '${req.body.tp_etapachat}', in_acessoulink=${req.body.in_acessoulink} `;
      sql += ` where ds_hash = '${req.body.ds_hash}' returning ds_hash,tp_etapachat;`;
      // console.log('sql at line 41 in atendimentos/AtendimentosDB.ts:', sql);
      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async stopAtendimentos(req: Request): Promise<IRetorno> {
    try {
      let sql = `update atendimentos set in_stop_bot = ${req.body.in_stop_bot} `;
      sql += ` where tp_status not in ('Pedido Realizado','Pedido Cancelado')`;
      sql += ` and instance = '${req.body.instance}'`;
      sql += ` and cast(dt_cadastro as date) = current_date returning *;`;
      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarAtendimentos(req: Request): Promise<IRetorno> {
    try {
      //console.log('req.query at line 53 in atendimentos/AtendimentosDB.ts:', req.query);
      const opDb: OperationObject = {
        operacao: 'select',
        tabelas: ['atendimentos'],
        chaves: req.query,
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarAtendimentoPorTelefone(req: any): Promise<IRetorno> {
    try {
      let telefone = req.query.telefone;

      if (telefone.startsWith('55')) {
        telefone = telefone.slice(2);
      } else {
        telefone = telefone;
      }

      let oitoUltimosDigitos = telefone.slice(-8);

      let sql = `SELECT a.*,d.ds_departamento,au.ds_nome nm_atendente
FROM atendimentos a 
left join departamentos d on d.cd_departamento = a.cd_departamento and d.cd_estabelecimento = a.cd_estabelecimento
left join adm_usuarios au on au.cd_usuario = a.cd_atendente 
INNER JOIN parametros e ON e.cd_estabelecimento = a.cd_estabelecimento
WHERE a.cd_estabelecimento = ${req.query.cd_estabelecimento} AND  (a.nr_telefone= '${telefone}' or a.nr_telefone like '%${oitoUltimosDigitos}')
  AND (a.tp_situacao IN ('Pedido Cancelado', 'Pedido Entregue', 'Atendimento Finalizado', 'Atendimento Cancelado')
  AND a.tp_status IN ('Pedido Cancelado', 'Pedido Entregue', 'Atendimento Finalizado', 'Atendimento Cancelado')
  and (CURRENT_TIMESTAMP > COALESCE(a.dt_cadastro, a.dt_alteracao) + CAST(e.hr_tempo_novo_atendimento || ' hours' AS INTERVAL)) = false
       or  (a.tp_situacao not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
           and a.tp_status not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
           or a.tp_situacao is null)) ORDER BY a.cd_atendimento DESC LIMIT 1`;

      console.log('sql at line 16 in data/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      console.log('🚀 ~ AtendimentosDB.ts:207 ~ listarAtendimentoPorTelefone ~ result:', result);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async validaEtapaAtendimento(req: Request): Promise<IRetorno> {
    try {
      if (req.query.ds_hash == undefined) return parametrosInvalidos('Hash do atendimento não informado!');
      let sql = `select cd_atendimento,tp_etapachat from atendimentos `;
      sql += ` where ds_hash ='${req.query.ds_hash}'`;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarAtendimentosFluxo(req: Request): Promise<IRetorno> {
    try {
      let sql = `select e.nr_controle,e.nr_ordem, f.tp_funcionalidade ds_etapa
,coalesce(e.in_ativo,true) in_ativo
,(select json_agg(atendimento)
  from (
         select cd_atendimento,instance,nr_telefone,ds_hash
                ,tp_status,dt_cadastro,ds_contato,id_ultimo_fluxo
                ds_foto,dt_alteracao 
                , EXTRACT(DAY FROM age(current_timestamp, dt_alteracao)) || 'd ' ||
                  EXTRACT(HOUR FROM age(current_timestamp, dt_alteracao)) || 'h ' ||
                  EXTRACT(MINUTE FROM age(current_timestamp, dt_alteracao)) || 'min ' ||
                    trunc(EXTRACT(SECOND FROM age(current_timestamp, dt_alteracao))) || 's' AS hr_tempoatendimento
         from atendimentos a
         where a.tp_status = e.ds_etapa 
         and a.instance=u.instance
         and a.cd_estabelecimento = f.cd_estabelecimento
         order by a.dt_alteracao desc
       ) as atendimento 
 ) as atendimentos 
from fluxo_atendimento f
left join fluxo_etapas e on e.ds_etapa = f.tp_funcionalidade 
inner join adm_usuarios u on u.cd_usuario = f.cd_usuario
where f.tp_funcionalidade not in ('Opções')
and coalesce(e.in_ativo,true) = true`;

      if (req.query.cd_usuario != undefined) {
        sql += ` and f.cd_usuario ='${req.query.cd_usuario}'`;
      }
      sql += ` group by e.nr_controle,e.nr_ordem, f.tp_funcionalidade,e.in_ativo,e.ds_etapa,u.instance 
order by e.nr_ordem `;
      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async removerAtendimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['atendimentos'],
        chaves: { cd_atendimento: req.body.cd_atendimento },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async importaAtendimentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['atendimentos'],
        chaves: { cd_atendimento: req.body.cd_atendimento },
        retorno: '*',
      };
      await new PostgreSQLServices().executar(opDb);
      req.body.forEach((element: any) => {
        element.cd_estabelecimento = req.query.cd_estabelecimento;
      });

      const result = await this.incluirAtendimentos(req);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaAtendimentosHistorico(req: Request): Promise<IRetorno> {
    try {
      let telefone: string = '';
      telefone = req.query.telefone as string;
      let oitoUltimosDigitos = telefone.slice(-8);
      // let telefoneSemDDD = telefone.slice(2);
      // let DDD = telefone.substr(0, 2);
      // let telefoneSem9DDD = telefoneSemDDD.slice(1);
      // let telefoneSem9 = DDD + telefoneSem9DDD;

      // Definir limite e offset com valores padrão caso não sejam passados na query string
      const limit = parseInt(req.query.limit as string) || 5; // Valor padrão de 20 se não for passado
      const offset = parseInt(req.query.offset as string) || 0; // Começa do primeiro registro (página 1)

      let sql = `select a.cd_atendimento,a.tp_status,a.dt_cadastro
,(select h.dt_cadastro from atendimento_historicos h where h.cd_atendimento = a.cd_atendimento and h.tp_status = 'Inicio' limit 1) as dt_inicio
,coalesce((select h.dt_cadastro from atendimento_historicos h 
          where h.cd_atendimento = a.cd_atendimento and h.tp_status in ('Atendimento Finalizado','Pedido Entregue') limit 1),a.dt_cadastro) as dt_finalizado
from atendimentos a
where a."instance"  in ('${req.query.instance}')  `;
      // and (a.nr_telefone  = '${telefone}' or a.nr_telefone  = '${telefoneSemDDD}' or a.nr_telefone  = '${telefoneSem9}'
      //  or a.nr_telefone  = '${telefoneSem9}' or a.nr_telefone  = '${telefoneSem9DDD}')

      //sql += `and (nr_telefonezap  = '${req.query.nr_telefonezap}' or nr_telefonezap  = '${telefoneSemDDD}')`;
      sql += ` and (trim(replace(a.nr_telefone,'-',''))  = '${telefone}' or
                       trim(replace(a.nr_telefone,'-',''))  like '%${oitoUltimosDigitos}') `;

      sql += `
      ORDER BY a.cd_atendimento desc
      LIMIT ${limit}
      OFFSET ${offset};
    `;
      //--and a.cd_atendimento not in (${req.query.cd_atendimento})
      //console.log('sql at line 219 in atendimentos/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listaAtendimentoData(req: Request): Promise<IRetorno> {
    try {
      let sql = `select * from (select a.cd_estabelecimento,e.nm_estabelecimento,cast(a.dt_cadastro as date) data,count(*) qt_total
from atendimentos a, estabelecimento e 
where e.cd_estabelecimento = a.cd_estabelecimento  `;

      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and a.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.dt_inicial != undefined) {
        sql += `and cast(a.dt_cadastro as date) between ('${req.query.dt_inicial}') and ('${req.query.dt_final}') `;
      }

      sql += ` group by  a.cd_estabelecimento,e.nm_estabelecimento,cast(a.dt_cadastro as date) 
 ) as z order by qt_total  desc `;

      //console.log('sql at line 66 in pedidos/PedidosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaAtendimentoHora(req: Request): Promise<IRetorno> {
    try {
      let sql = `select * from (select a.cd_estabelecimento,e.nm_estabelecimento,to_char(a.dt_cadastro,'HH24') hora,count(*) qt_total
from atendimentos a, estabelecimento e 
where e.cd_estabelecimento = a.cd_estabelecimento  `;

      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and a.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.dt_inicial != undefined) {
        sql += `and cast(a.dt_cadastro as date) between ('${req.query.dt_inicial}') and ('${req.query.dt_final}') `;
      }

      sql += ` group by  a.cd_estabelecimento,e.nm_estabelecimento,to_char(a.dt_cadastro,'HH24') 
 ) as z order by qt_total  desc  `;

      //console.log('sql at line 66 in pedidos/PedidosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
