<template>
    <ShowLoading ref="loading" />
    <notifications ref="showNotifications" />
    <div class="">
        <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Me<PERSON></h2>
        </div>

        <TabGroup class="py-4 px-1 md:px-2">
            <TabList
                class="box nav-pills rounded-md p-1 w-max intro-y border border-slate-300 dark:border-darkmode-300 border-dashed"
            >
                <Tab class="hover:bg-slate-800/10 mr-1 cursor-pointer">
                    <button class="w-max px-3" as="button">Dados</button>
                </Tab>
                <Tab class="hover:bg-slate-800/10 mr-1 cursor-pointer">
                    <button class="w-max px-3" as="button">Pedido</button>
                </Tab>
                <Tab class="hover:bg-slate-800/10 cursor-pointer">
                    <button class="w-max px-3" as="button">Mensagens</button>
                </Tab>
            </TabList>

            <TabPanels class="mt-2 intro-y">
                <TabPanel class="box p-4 md:p-6 grid grid-cols-12">
                    <div class="col-span-12 lg:pr-4">
                        <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4">
                            <div>
                                <h2 class="text-lg font-semibold">Dados do Estabelecimento</h2>
                                <p class="text-md text-slate-600 dark:text-slate-400">
                                    As informações contidas nesta página são pessoais. Tenha cuidado ao compartilhar.
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-12">
                            <div class="intro-y col-span-12">
                                <PreviewComponent class="box">
                                    <div class="p-3 text-left">
                                        <div class="grid grid-cols-12 gap-1">
                                            <div class="col-span-4 gap-1">
                                                <label for="validation-form-1" class="form-label w-20"
                                                    >Estabelecimento</label
                                                >

                                                <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        v-model.trim="formData.nm_estabelecimento"
                                                        placeholder="Informe o Estabelecimento"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-2 gap-1">
                                                <label for="validation-form-1" class="form-label w-20">CNPJ</label>

                                                <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        v-model.trim="formData.nr_cnpj"
                                                        placeholder="Informe seu CNPJ"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-6 gap-1">
                                                <label for="validation-form-1" class="form-label">Link Cardápio</label>

                                                <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        v-model.trim="formData.ds_hostentregadochef"
                                                        placeholder="https://entregadochef.com/meunegocio"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-1 mt-2">
                                            <div class="col-span-6">
                                                <Tippy content="Valor de Entrega" class="tooltip w-5 h-5">
                                                    <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                        <TruckIcon
                                                            class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0"
                                                        />
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block pl-9"
                                                            v-model.trim="formData.vl_entrega"
                                                            placeholder="Valor de Entrega"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>
                                            <div class="col-span-6">
                                                <Tippy content="Hash Estabelecimento" class="tooltip w-5 h-5">
                                                    <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                        <KeyRoundIcon
                                                            class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0"
                                                        />
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block pl-9"
                                                            v-model.trim="formData.nr_hash"
                                                            placeholder="Hash Estabelecimento"
                                                            disabled="true"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>
                                        </div>
                                    </div>
                                </PreviewComponent>
                            </div>
                        </div>

                        <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4 mt-4">
                            <div>
                                <h2 class="text-lg font-semibold">Endereço</h2>
                                <p class="text-md text-slate-600 dark:text-slate-400">
                                    Informe o endereço correto para localização do estabelecimento.
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-12">
                            <div class="intro-y col-span-12">
                                <PreviewComponent class="box">
                                    <div class="p-3 text-left">
                                        <div class="grid grid-cols-12 gap-1">
                                            <div class="form-controlcol-span-1 gap-1">
                                                <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                    <label for="validation-form-1" class="form-label w-20"
                                                        >Estado</label
                                                    >

                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="UF"
                                                        ref="inputCidade"
                                                        v-model="formData.ds_uf"
                                                        maxlength="2"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-3 gap-1">
                                                <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                    <label for="validation-form-1" class="form-label w-20"
                                                        >Cidade</label
                                                    >
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Cidade"
                                                        ref="inputCidade"
                                                        v-model="formData.ds_cidade"
                                                        maxlength="60"
                                                    />
                                                </div>
                                            </div>
                                            <div
                                                class="gap-1"
                                                :class="{
                                                    'col-span-8': isPesquisandoEndereco == true,
                                                    'col-span-6': isPesquisandoEndereco == false,
                                                }"
                                            >
                                                <div class="gap-1">
                                                    <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                        <label for="validation-form-1" class="form-label w-20"
                                                            >Endereço</label
                                                        >
                                                        <selPesquisaEndereco
                                                            v-model="listaEnderecos"
                                                            :mode="'single'"
                                                            ref="selectEndereco"
                                                            @emitEvent="selecionaEndereco"
                                                            @keydown.esc="focusInputEndereco"
                                                            v-show="isPesquisandoEndereco"
                                                            :cidade="formData.ds_cidade"
                                                            :uf="formData.ds_uf"
                                                        />
                                                    </div>
                                                    <div
                                                        class="sm:ml-0 mt-3 sm:mt-0 relative w-full"
                                                        v-show="!isPesquisandoEndereco"
                                                    >
                                                        <Tippy content="Abrir Mapa" class="tooltip w-5 h-5">
                                                            <MapPinnedIcon
                                                                class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0 cursor-pointer"
                                                                @click="abreModalMapa"
                                                            />
                                                        </Tippy>
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block pl-9"
                                                            placeholder="Informe o endereço Cliente"
                                                            ref="inputEndereco"
                                                            v-model="formData.ds_endereco_curto"
                                                            @keydown.enter="focusInputNumero"
                                                            maxlength="60"
                                                            @input="focusSelectEndereco"
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-span-2 gap-1">
                                                <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                    <label
                                                        for="validation-form-1"
                                                        class="form-label w-20"
                                                        v-show="isPesquisandoEndereco == false"
                                                        >Número</label
                                                    >
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Número"
                                                        ref="inputNumero"
                                                        v-model="formData.nr_endereco"
                                                        @keydown.enter="focusInputBairro"
                                                        maxlength="8"
                                                        v-show="isPesquisandoEndereco == false"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-12 gap-1 mt-2">
                                            <div class="col-span-4 gap-1">
                                                <Tippy content="Bairro" class="tooltip w-5 h-5">
                                                    <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block"
                                                            placeholder="Bairro"
                                                            ref="inputBairro"
                                                            v-model="formData.ds_bairro"
                                                            @keydown.enter="focusInputComplemento"
                                                            maxlength="60"
                                                            v-show="isPesquisandoEndereco == false"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>

                                            <div class="col-span-4 gap-1">
                                                <Tippy content="Complemento" class="tooltip w-5 h-5">
                                                    <div class="sm:ml-0 sm:mt-0 relative w-full">
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block"
                                                            placeholder="Complemento"
                                                            ref="inputComplemento"
                                                            v-model="formData.ds_complemento"
                                                            @keydown.enter="focusInputCidade"
                                                            maxlength="60"
                                                            v-show="isPesquisandoEndereco == false"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>
                                            <div class="col-span-2">
                                                <Tippy content="Latitude" class="tooltip w-5 h-5">
                                                    <div
                                                        class="sm:ml-0 mt-3 sm:mt-0 relative w-full"
                                                        v-show="!isPesquisandoEndereco"
                                                    >
                                                        <CompassIcon
                                                            class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0"
                                                            v-show="!isPesquisandoEndereco"
                                                        />
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block pl-9"
                                                            v-model.trim="formData.nr_latitude"
                                                            placeholder="Informe a Latitude"
                                                            v-show="!isPesquisandoEndereco"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>
                                            <div class="col-span-2">
                                                <Tippy content="Longitude" class="tooltip w-5 h-5">
                                                    <div
                                                        class="sm:ml-0 mt-3 sm:mt-0 relative w-full"
                                                        v-show="!isPesquisandoEndereco"
                                                    >
                                                        <CompassIcon
                                                            class="w-4 h-4 z-10 absolute my-auto inset-y-0 ml-3 left-0"
                                                            v-show="!isPesquisandoEndereco"
                                                        />
                                                        <input
                                                            type="text"
                                                            class="form-control col-span-1 block pl-9"
                                                            v-model.trim="formData.nr_longitude"
                                                            placeholder="Informe a Longitude"
                                                            v-show="!isPesquisandoEndereco"
                                                        />
                                                    </div>
                                                </Tippy>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-6 mt-2">
                                            <div class="col-span-6">
                                                <button
                                                    type="button"
                                                    class="btn w-20 h-10"
                                                    :class="`${
                                                        darkMode ? 'btn-outline-success ' : 'btn-outline-primary'
                                                    }`"
                                                    @click="salvar()"
                                                >
                                                    Salvar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </PreviewComponent>
                            </div>
                        </div>
                    </div>
                </TabPanel>
                <TabPanel class="box p-4 md:p-6 grid grid-cols-12">
                    <div class="col-span-12 lg:pr-4">
                        <!-- <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4">
                            <div>
                                <h2 class="text-lg font-semibold">Financeiro</h2>
                                <p class="text-md text-slate-600 dark:text-slate-400">
                                    Configure as opções de pagamento e integração financeira do seu estabelecimento.
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-12">
                            <div class="intro-y col-span-12">
                                <PreviewComponent class="box">
                                    <div class="p-5">
                                        <div class="grid grid-cols-12 gap-4">
                                            <div class="col-span-12 md:col-span-2">
                                                <h3 class="font-medium text-base mb-3">Forma de Pagamento</h3>
                                                <div class="space-y-4">
                                                    <div class="form-check">
                                                        <input
                                                            id="payment-pix"
                                                            class="form-check-input"
                                                            type="radio"
                                                            v-model="formData.forma_pagamento_padrao"
                                                            value="pix"
                                                        />
                                                        <label class="form-check-label" for="payment-pix">PIX</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-span-12 md:col-span-6">
                                                <div
                                                    v-if="formData.forma_pagamento_padrao === 'pix'"
                                                    class="border rounded-md p-3 bg-slate-50 dark:bg-slate-700/20"
                                                >
                                                    <h3 class="font-medium text-base mb-3">Configurações do PIX</h3>

                                                    <div class="space-y-4">
                                                        <div class="form-check">
                                                            <input
                                                                id="pix-resulti"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                v-model="formData.pix_resulti_pay"
                                                            />
                                                            <label class="form-check-label" for="pix-resulti"
                                                                >Gerar e enviar PIX ResultiPay</label
                                                            >
                                                        </div>

                                                        <div class="form-check">
                                                            <input
                                                                id="pix-await"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                v-model="formData.pix_aguardar_pagamento"
                                                            />
                                                            <label class="form-check-label" for="pix-await"
                                                                >Aguardar pagamento e liberar pedido</label
                                                            >
                                                        </div>

                                                        <div class="form-check">
                                                            <input
                                                                id="pix-send"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                v-model="formData.pix_enviar_dados"
                                                            />
                                                            <label class="form-check-label" for="pix-send"
                                                                >Enviar dados do PIX e não aguardar</label
                                                            >
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </PreviewComponent>
                            </div>
                        </div> -->

                        <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4">
                            <div>
                                <h2 class="text-lg font-semibold">Produtos</h2>
                                <p class="text-md text-slate-600 dark:text-slate-400">
                                    Configure as opções do produto no Pedido de acordo com sua regra de negócio.
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 gap-5">
                            <div class="intro-y col-span-12">
                                <PreviewComponent class="box">
                                    <div class="p-5">
                                        <div class="grid grid-cols-12 gap-6">
                                            <div class="col-span-6">
                                                <h3 class="font-medium text-base mb-2">Tipo de cálculo de pizza</h3>

                                                <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.tp_calculopizza"
                                                        placeholder="Selecione o tipo de cálculo"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaCalculoPizza"
                                                        ref="selectCalculoPizza"
                                                        @emitEvent="selecionaCalculoPizza($event)"
                                                    />
                                                </div>
                                            </div>

                                            <div class="col-span-6">
                                                <h3 class="font-medium text-base mb-2">
                                                    Permite lançar produto com valor zerado
                                                </h3>
                                                <div class="form-check">
                                                    <input
                                                        id="pix-await"
                                                        class="form-check-input"
                                                        type="checkbox"
                                                        v-model="formData.in_permite_item_zerado"
                                                        disabled
                                                    />
                                                    <label class="form-check-label" for="pix-await">{{
                                                        formData.in_permite_item_zerado ? 'Permite' : 'Não Permite'
                                                    }}</label>
                                                </div>
                                                <!-- <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.in_permite_item_zerado"
                                                        placeholder="Selecione o item zerado"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaItemZerado"
                                                        @emitEvent="selecionaItemZerado($event)"
                                                        :isDisabled="false"
                                                    /> 
                                                </div>-->
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-12 gap-6 mt-4">
                                            <div class="col-span-12 md:col-span-6">
                                                <h3 class="font-medium text-base mb-3">Tipo de pesquisa do produto</h3>
                                                <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.tp_pesquisa_produto"
                                                        placeholder="Selecione o tipo de pesquisa"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaPesquisaProduto"
                                                        @emitEvent="selecionaTpPesquisaProduto($event)"
                                                    />
                                                </div>
                                            </div>

                                            <div class="col-span-12 md:col-span-6">
                                                <h3 class="font-medium text-base mb-3">
                                                    Permite aplicar desconto no produto
                                                </h3>
                                                <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.in_aplicadesconto"
                                                        placeholder="Selecione se permite aplicar desconto"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaTipoSimNao"
                                                        @emitEvent="selecionaAplicaDesconto($event)"
                                                    />
                                                </div>
                                            </div>
                                            <!-- <div class="col-span-12 md:col-span-6">
                                                <h3 class="font-medium text-base mb-3">
                                                    Tipo de pesquisa do adicional
                                                </h3>
                                                <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.tp_pesquisa_adicionais"
                                                        placeholder="Selecione o tipo de pesquisa"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaPesquisaAdicionais"
                                                        @emitEvent="selecionaTpPesquisaAdicionais($event)"
                                                    />
                                                </div> -->
                                        </div>

                                        <div class="grid grid-cols-12 gap-6 mt-4">
                                            <div class="col-span-12 md:col-span-6">
                                                <h3 class="font-medium text-base mb-3">
                                                    Tipo de alteração de valor de produto
                                                </h3>
                                                <div class="space-y-4">
                                                    <Multiselect
                                                        class="w-full z-50"
                                                        v-model="formData.tp_alteracao_valor_produto"
                                                        placeholder="Selecione o tipo de alteração"
                                                        mode="single"
                                                        :canClear="false"
                                                        :close-on-select="false"
                                                        :searchable="true"
                                                        :createOption="false"
                                                        :options="listaTpAlteracaoValorProduto"
                                                        @emitEvent="selecionaTpAlteracaoValorProduto($event)"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </PreviewComponent>
                            </div>
                        </div>
                    </div>
                </TabPanel>
                <TabPanel class="box p-4 md:p-6 grid grid-cols-12">
                    <div class="col-span-12 lg:pr-4">
                        <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-y-4 gap-x-4">
                            <div>
                                <h2 class="text-lg font-semibold">Configurações de Mensagens</h2>
                                <p class="text-md text-slate-600 dark:text-slate-400">
                                    Configure as opções de automação de inicio de atendimento e encerramento de
                                    mensagens.
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 gap-5">
                            <div class="intro-y col-span-12">
                                <PreviewComponent class="box">
                                    <div class="p-5">
                                        <div class="grid grid-cols-12 gap-4">
                                            <div class="col-span-12">
                                                <h3 class="font-medium text-base mb-3">
                                                    Tempo para iniciar um novo atendimento
                                                </h3>

                                                <div class="grid grid-cols-12 gap-4 mt-3">
                                                    <div class="col-span-6">
                                                        Iniciar novo atendimento após
                                                        <strong>{{ formData.hr_tempo_novo_atendimento }} </strong>
                                                        hora(s)
                                                        <VueSlider
                                                            v-model="formData.hr_tempo_novo_atendimento"
                                                            :included="true"
                                                            :min="0"
                                                            :max="12"
                                                            :tooltip-formatter="'{value} hr(s)'"
                                                            :marks="true"
                                                            @change="changeTempoNovo"
                                                        />
                                                    </div>

                                                    <!-- 
                                                    <div class="col-span-6 md:col-span-3">
                                                        <label for="horas_envio" class="form-label">Minutos</label>
                                                        <input
                                                            type="number"
                                                            id="horas_envio"
                                                            class="form-control"
                                                            v-model.number="formData.horas_envio_mensagem"
                                                            min="0"
                                                            max="23"
                                                            placeholder="0"
                                                        />
                                                    </div> -->
                                                </div>
                                            </div>
                                        </div>

                                        <div class="border-t border-slate-200/60 dark:border-slate-700 mt-8 pt-5 mb-2">
                                            <div class="col-span-12">
                                                <h3 class="font-medium text-base mb-3">Encerramento automático</h3>

                                                <!-- <div class="form-check form-switch">
                                                    <input
                                                        id="habilita_encerra"
                                                        class="form-check-input"
                                                        type="checkbox"
                                                        v-model="formData.in_habilita_encerra_atendimento"
                                                    />
                                                    <label class="form-check-label" for="habilita_encerra">
                                                        Habilitar encerramento automático dos atendimentos
                                                    </label>
                                                </div> -->

                                                <div class="grid grid-cols-12 gap-1 mt-2">
                                                    <div class="col-span-6">
                                                        Encerrar atendimentos automaticamente após
                                                        <strong>{{ formData.hr_tempo_encerra_atendimento }} </strong>
                                                        hora(s)
                                                        <VueSlider
                                                            v-model="formData.hr_tempo_encerra_atendimento"
                                                            :included="true"
                                                            :min="0"
                                                            :max="12"
                                                            :tooltip-formatter="'{value} hr(s)'"
                                                            :marks="true"
                                                            @change="changeTempoEncerra"
                                                        />
                                                    </div>
                                                </div>

                                                <!-- <div v-if="formData.in_habilita_encerra_atendimento" class="mt-3">
                                                    <label for="tempo_encerra" class="form-label"
                                                        >Tempo para encerramento (horas)</label
                                                    >
                                                    <input
                                                        type="number"
                                                        id="tempo_encerra"
                                                        class="form-control w-full md:w-1/3"
                                                        v-model.number="formData.hr_tempo_encerra_atendimento"
                                                        min="1"
                                                        placeholder="24"
                                                    />
                                                </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </PreviewComponent>
                            </div>
                        </div>
                    </div>
                </TabPanel>
            </TabPanels>
        </TabGroup>
        <modalMapa ref="modalEndMapa" />
    </div>
</template>

<script setup>
    import { ref, reactive, onMounted, toRefs, computed, nextTick } from 'vue';
    //import hosts from '@/utils/hosts';
    import { required } from '@vuelidate/validators';
    import { useVuelidate } from '@vuelidate/core';
    import ModulosServices from '@/services/administracao/ModulosServices';
    import notifications from '@/components/show-notifications/Main.vue';
    import EstabelecimentoServices from '@/services/administracao/EstabelecimentoServices';
    import selPesquisaEndereco from '@/global-components/multi-select/selPesquisaEndereco.vue';
    import { KeyRoundIcon } from 'lucide-vue-next';
    import { useDarkModeStore } from '@/stores/dark-mode';
    import modalMapa from '@/views/chat/modalMapa.vue';
    import ParametrosServices from '../../../services/administracao/ParametrosServices';
    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);

    //import converters from '../../../utils/converters';

    let listaEnderecos = ref([]);
    let listaModulos = ref([]);
    let cdEstabelecimento = ref([]);
    let isPesquisandoEndereco = ref(false);
    const selectEndereco = ref();
    const inputEndereco = ref();
    const inputNumero = ref();
    const inputBairro = ref();
    const inputComplemento = ref();
    const inputCidade = ref();
    const modalEndMapa = ref();
    const listaCalculoPizza = ref([
        { value: 1, label: 'Maior valor de sabor' },
        { value: 2, label: 'Por rateio de cada valor do sabor' },
    ]);
    const listaItemZerado = ref([
        { value: true, label: 'Sim' },
        { value: false, label: 'Não' },
    ]);
    const listaPesquisaProduto = ref([
        { value: 'Codigo', label: 'Código' },
        { value: 'Nome', label: 'Nome do Produto' },
    ]);
    const listaPesquisaAdicionais = ref([
        { value: 'Codigo', label: 'Código' },
        { value: 'Nome', label: 'Nome do Adicional' },
    ]);
    const listaTpAlteracaoValorProduto = ref([
        { value: 'Permite', label: 'Permite alteração de valor' },
        { value: 'Não Permite', label: 'Não permite alteração de valor' },
        //{ value: 'Somente com Permissão', label: 'Somente usuários com permissão' },
    ]);
    const listaTipoSimNao = ref([
        { value: true, label: 'Sim' },
        { value: false, label: 'Não' },
    ]);

    //const disabled = ref(false);
    const formData = reactive({
        cd_estabelecimento: undefined,
        nr_hash: undefined,
        nm_estabelecimento: undefined,
        nr_cnpj: undefined,
        ds_endereco: undefined,
        ds_endereco_curto: undefined,
        nr_endereco: undefined,
        ds_bairro: undefined,
        ds_cidade: undefined,
        ds_pais: undefined,
        ds_uf: undefined,
        nr_latitude: undefined,
        nr_longitude: undefined,
        ds_hostentregadochef: undefined,
        vl_entrega: undefined,
        hr_tempo_novo_atendimento: 0,
        hr_tempo_encerra_atendimento: 0,
        tp_calculopizza: undefined,
        tp_pesquisa_produto: undefined,
        tp_pesquisa_adicionais: undefined,
        in_permite_item_zerado: undefined,
        tp_alteracao_valor_produto: undefined,
        in_aplicadesconto: undefined,
    });
    // const rulesEst = {
    //     nm_estabelecimento: {
    //         required,
    //     },
    //     nr_cnpj: {
    //         required,
    //     },
    // };
    const loading = ref();
    const showNotifications = ref();

    let codUsuario = ref();
    //let listaUsuarioEstabelecimentos = ref([]);

    //const validate = useVuelidate(rulesEst, toRefs(formData));

    async function limpaFormData() {
        Object.keys(formData).forEach((key) => (formData[key] = undefined));
    }

    const changeTempoEncerra = async (value) => {
        formData.hr_tempo_encerra_atendimento = value;
        salvarParametros();
    };

    const changeTempoNovo = async (value) => {
        formData.hr_tempo_novo_atendimento = value;

        salvarParametros();
    };

    async function salvarParametros() {
        let reqObj = {
            cd_estabelecimento: formData.cd_estabelecimento == undefined ? undefined : formData.cd_estabelecimento,
            hr_tempo_encerra_atendimento: formData.hr_tempo_encerra_atendimento,
            hr_tempo_novo_atendimento: formData.hr_tempo_novo_atendimento,
            tp_calculopizza: formData.tp_calculopizza,
            tp_pesquisa_produto: formData.tp_pesquisa_produto,
            tp_pesquisa_adicionais: formData.tp_pesquisa_adicionais,
            in_permite_item_zerado: formData.in_permite_item_zerado,
            tp_alteracao_valor_produto: formData.tp_alteracao_valor_produto,
            in_aplicadesconto: formData.in_aplicadesconto,
        };
        // console.log('reqObj at line 705 in estabelecimento/cadEstabelecimento.vue:', reqObj);

        const result = await ParametrosServices.alterar(reqObj);
        // console.log('result at line 706 in estabelecimento/cadEstabelecimento.vue:', result);

        if (result.statuscode == 200) {
            showNotifications.value.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
        } else {
            showNotifications.value.showErrorNotification(result.message);
        }
    }

    async function salvar() {
        //let modulos = listaModulos.value.filter((modulo) => modulo.in_contratado == true);
        // console.log('salvar', formData.cd_estabelecimento);
        let endereco = formData.ds_endereco_curto;
        if (formData.nr_endereco != undefined || formData.nr_endereco != '') endereco += ', ' + formData.nr_endereco;
        if (formData.ds_bairro != undefined || formData.ds_bairro != '') endereco += ' - ' + formData.ds_bairro;
        let reqObj = {
            cd_estabelecimento: formData.cd_estabelecimento == undefined ? undefined : formData.cd_estabelecimento,
            cd_usuario: parseInt(codUsuario.value),
            nm_estabelecimento: formData.nm_estabelecimento,
            nr_cnpj: formData.nr_cnpj,
            ds_endereco: endereco,
            ds_endereco_curto: formData.ds_endereco_curto,
            nr_endereco: formData.nr_endereco,
            ds_bairro: formData.ds_bairro,
            ds_cidade: formData.ds_cidade,
            ds_pais: formData.ds_pais,
            ds_uf: formData.ds_uf,
            nr_latitude: formData.nr_latitude,
            nr_longitude: formData.nr_longitude,
            ds_hostentregadochef: formData.ds_hostentregadochef,
            // hr_tempo_encerra_atendimento: formData.hr_tempo_encerra_atendimento,
            // hr_tempo_novo_atendimento: formData.hr_tempo_novo_atendimento,
            vl_entrega: formData.vl_entrega.replace(',', '.'),
            // tp_calculopizza: formData.tp_calculopizza,
            // tp_pesquisa_produto: formData.tp_pesquisa_produto,
            // tp_pesquisa_adicionais: formData.tp_pesquisa_adicionais,
            // modulos: modulos,
        };

        let result;
        if (formData.cd_estabelecimento == undefined) {
            result = await EstabelecimentoServices.incluir(reqObj);
        } else {
            result = await EstabelecimentoServices.alterar(reqObj);
        }

        // console.log('EstabelecimentoServices', result);

        if (result.statuscode == 200) {
            const data = result.data[0];
            formData.cd_estabelecimento = data.cd_estabelecimento;
            formData.nr_hash = data.nr_hash;

            showNotifications.value.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
        } else {
            showNotifications.value.showErrorNotification(result.message);
        }
    }

    async function carregaEstabelecimento() {
        loading.value.show();
        let filtros = {
            cd_estabelecimento: cdEstabelecimento.value,
        };

        await limpaFormData();
        //disabled.value = true;

        let result = await EstabelecimentoServices.listar(filtros);
        //console.log('result at line 264 in estabelecimento/cadEstabelecimento.vue:', result);

        if (result.statuscode == 200) {
            // disabled.value = false;

            const data = result.data[0];

            formData.cd_estabelecimento = data.cd_estabelecimento;
            formData.nm_estabelecimento = data.nm_estabelecimento;
            formData.nr_cnpj = data.nr_cnpj;
            formData.ds_endereco = data.ds_endereco;
            formData.ds_endereco_curto = data.ds_endereco_curto;
            formData.nr_endereco = data.nr_endereco;

            formData.ds_bairro = data.ds_bairro;
            formData.nr_latitude = data.nr_latitude;
            formData.nr_longitude = data.nr_longitude;
            formData.ds_cidade = data.ds_cidade;
            formData.ds_pais = data.ds_pais;
            formData.ds_uf = data.ds_uf;

            formData.ds_hostentregadochef = data.ds_hostentregadochef;
            formData.vl_entrega = data.vl_entrega;
            formData.nr_hash = data.nr_hash;
            //formData.in_habilita_encerra_atendimento = data.in_habilita_encerra_atendimento;
            //formData.hr_tempo_encerra_atendimento = data.hr_tempo_encerra_atendimento;
            //formData.hr_tempo_novo_atendimento = data.hr_tempo_novo_atendimento;
            //formData.tp_calculopizza = data.tp_calculopizza;
            //formData.tp_pesquisa_produto = data.tp_pesquisa_produto == null ? 'Nome' : data.tp_pesquisa_produto;
            //formData.tp_pesquisa_adicionais =
            //    data.tp_pesquisa_adicionais == null ? 'Nome' : data.tp_pesquisa_adicionais;
            //showNotifications.value.showSuccessNotification('Dados salvo com sucesso!');

            await carregaParametros();
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
        } else {
            showNotifications.value.showErrorNotification(result.message);
        }

        loading.value.hide();
    }
    async function carregaParametros() {
        let filtros = {
            cd_estabelecimento: cdEstabelecimento.value,
        };

        let result = await ParametrosServices.listar(filtros);
        //console.log('result at line 264 in estabelecimento/cadEstabelecimento.vue:', result);

        if (result.statuscode == 200) {
            // disabled.value = false;

            const data = result.data[0];
            // console.log('data at line 912 in estabelecimento/cadEstabelecimento.vue:', data);

            formData.in_habilita_encerra_atendimento = data.in_habilita_encerra_atendimento;
            formData.hr_tempo_encerra_atendimento = data.hr_tempo_encerra_atendimento;
            formData.hr_tempo_novo_atendimento = data.hr_tempo_novo_atendimento;
            formData.tp_calculopizza = data.tp_calculopizza;
            formData.tp_pesquisa_produto = data.tp_pesquisa_produto == null ? 'Nome' : data.tp_pesquisa_produto;
            formData.tp_pesquisa_adicionais =
                data.tp_pesquisa_adicionais == null ? 'Nome' : data.tp_pesquisa_adicionais;
            formData.in_permite_item_zerado = data.in_permite_item_zerado;
            formData.tp_alteracao_valor_produto =
                data.tp_alteracao_valor_produto == null ? 'Permite' : data.tp_alteracao_valor_produto;
            formData.in_aplicadesconto = data.in_aplicadesconto;
            //showNotifications.value.showSuccessNotification('Dados salvo com sucesso!');
        }
    }
    async function carregaModulos() {
        const result = await ModulosServices.listar();

        if (result.statuscode == 200) {
            listaModulos.value = result.data;
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
        } else {
            showNotifications.value.showErrorNotification(result.message);
        }
    }
    const focusEndereco = async () => {
        let input = inputEndereco.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const selecionaEndereco = async (event) => {
        // console.log('selecionaEndereco', event, listaEnderecos.value);

        if (event.event == 'Select') {
            await nextTick();

            formData.ds_endereco_curto = event.select.endereco;
            formData.nr_endereco = event.select.numero;
            formData.ds_bairro = event.select.bairro;
            formData.ds_complemento = event.select.complemento;
            formData.nr_latitude = event.select.latitude;
            formData.nr_longitude = event.select.longitude;
            formData.ds_cidade = event.select.cidade;
            // formData.ds_pais = event.select.pais;
            formData.ds_uf = event.select.estado;

            isPesquisandoEndereco.value = false;
            let input = inputNumero.value;
            await nextTick();
            if (input) {
                input.select();
                input.focus();
            }
            await nextTick();

            listaEnderecos.value = [];
        }
    };
    const focusInputEndereco = async () => {
        let input = inputEndereco.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const focusInputNumero = async () => {
        let input = inputNumero.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const focusInputBairro = async () => {
        let input = inputBairro.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const focusInputComplemento = async () => {
        let input = inputComplemento.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const focusInputCidade = async () => {
        let input = inputCidade.value;
        await nextTick();
        if (input) {
            input.select();
            input.focus();
        }
    };
    const focusSelectEndereco = async () => {
        if (!formData.ds_cidade || !formData.ds_uf) {
            return; // Interrompe a execução se cidade ou UF estiverem vazios ou indefinidos
        }

        let dsEndereco = formData.ds_endereco_curto.trim();
        // Se o endereço já tem mais de uma letra, não faça nada
        if (dsEndereco.length > 1) return;

        // Armazena a primeira letra digitada
        const firstLetter = dsEndereco;

        // Ativa o componente de pesquisa
        isPesquisandoEndereco.value = true;

        await nextTick(async () => {
            if (selectEndereco.value) {
                // Obtém o campo de input dentro do componente selPesquisaEndereco
                const input = selectEndereco.value.$refs.input || selectEndereco.value.$el.querySelector('input');

                if (input) {
                    // Foca o input e adiciona a primeira letra digitada
                    input.focus();
                    input.value = firstLetter;

                    // Emite um evento de input para garantir que o componente reconheça a alteração
                    input.dispatchEvent(new Event('input'));
                }
            }
        });
    };
    async function abreModalMapa() {
        modalEndMapa.value.abreMapa(formData.nr_latitude, formData.nr_longitude, formData.ds_endereco_curto);
    }

    const selecionaCalculoPizza = async (event) => {
        // console.log('event at line 971 in estabelecimento/cadEstabelecimento.vue:', event);
        if (event.event == 'Select') {
            formData.tp_calculopizza = event.select.value;
            await salvarParametros();
        }
    };
    const selecionaItemZerado = async (event) => {
        // console.log('event at line 971 in estabelecimento/cadEstabelecimento.vue:', event);
        if (event.event == 'Select') {
            formData.in_permite_item_zerado = event.select.value;
            await salvarParametros();
        }
    };
    const selecionaTpPesquisaProduto = async (event) => {
        if (event.event == 'Select') {
            formData.tp_pesquisa_produto = event.select.value;
            await salvarParametros();
        }
    };
    const selecionaTpPesquisaAdicionais = async (event) => {
        if (event.event == 'Select') {
            formData.tp_pesquisa_adicionais = event.select.value;
            await salvarParametros();
        }
    };

    const selecionaTpAlteracaoValorProduto = async (event) => {
        if (event.event == 'Select') {
            formData.tp_alteracao_valor_produto = event.select.value;
            await salvarParametros();
        }
    };

    const selecionaAplicaDesconto = async (event) => {
        if (event.event == 'Select') {
            formData.in_aplicadesconto = event.select.value;
            await salvarParametros();
        }
    };

    onMounted(async () => {
        // incluir a opçao de desconto no pedido na configuração do estabelecimento, parecido com esse de alterar valor

        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            codUsuario.value = localStorage.getItem('codusuario');
            const estabelecimentosLiberado = JSON.parse(estatabelecimentos);
            cdEstabelecimento.value = estabelecimentosLiberado[0].cd_estabelecimento;

            //await carregaUsuarioEstabelecimentos();
            await carregaEstabelecimento();
            await carregaModulos();
        } else {
            showNotifications.value.showWarningNotification('Estabelecimento não liberado!');
        }
        //console.log(localStorage.getItem('instancia'));
    });
</script>
<style scoped>
    .custom-disabled-multiselect {
        pointer-events: none;
        opacity: 0.5;
    }
</style>
