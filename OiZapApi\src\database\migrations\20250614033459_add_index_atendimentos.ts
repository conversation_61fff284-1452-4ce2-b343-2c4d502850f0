import { Knex } from 'knex';
import { safeCreateIndex } from '../../scripts/indexUtils';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('Iniciando criação dos índices na tabela messages');

    await safeCreateIndex(knex, 'atendimentos', 'cd_departamento', 'atendimentos_cd_departamento_idx');
    console.log('Índice cd_departamento criado com sucesso na tabela atendimentos');

    await safeCreateIndex(knex, 'atendimentos', 'cd_atendente', 'atendimentos_cd_atendente_idx');
    console.log('Índice cd_atendente criado com sucesso na tabela atendimentos');
    return;
  } catch (error) {
    console.error('Erro ao criar indice cd_motivo:', error);
  }
}

export async function down(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  try {
    await trx.schema.alterTable('atendimentos', (table) => {
      table.dropIndex([], 'atendimentos_cd_departamento_idx');
      table.dropIndex([], 'atendimentos_cd_atendente_idx');
    });
    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}
