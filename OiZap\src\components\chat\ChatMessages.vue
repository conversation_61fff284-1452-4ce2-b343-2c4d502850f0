<template>
    <div
        class="inline-block bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-3 py-2 text-xs w-full rounded-lg mb-2 text-center shadow-sm border border-blue-200 dark:border-blue-800/50"
        v-if="mensagens.tp_status_atendimento != 'Em Atendimento' && mensagens.tp_status_atendimento != null"
    >
        <div class="flex items-center justify-center gap-2">
            <div class="w-4 h-4 bg-blue-100 dark:bg-blue-800/50 rounded-full flex items-center justify-center">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-2 w-2 text-blue-600 dark:text-blue-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                </svg>
            </div>
            <span>
                Atendimento <strong>{{ mensagens.tp_status_atendimento }}</strong>
                {{ mensagens.tp_status_atendimento == 'Transferido' ? 'para' : 'por' }}
                <strong>{{ mensagens.nm_atendente }}</strong> •
                {{ converters.date('DD/MM/YYYY HH:MM:SS', mensagens.message_timestamp) }}
                <span v-if="mensagens.ds_motivo" class="italic"> • {{ mensagens.ds_motivo }}</span>
            </span>
        </div>
    </div>

    <div
        class="inline-block bg-green-100 dark:bg-emerald-900/30 text-green-800 dark:text-emerald-300 px-4 py-2 text-sm w-full rounded-lg mb-3 text-center shadow-sm border border-green-200 dark:border-emerald-800/50"
        v-if="mensagens.ds_resumo"
    >
        <div class="flex items-center justify-center gap-2">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
            </svg>
            <span class="font-medium">Resumo da conversa:</span>
        </div>
        <p class="mt-1">{{ mensagens.ds_resumo }}</p>
    </div>

    <div
        class="inline-block bg-amber-50 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 px-4 py-3 text-sm w-full rounded-lg mb-3 text-left shadow-sm border-l-4 border-amber-400 dark:border-amber-500"
        v-if="mensagens.ds_comentario"
    >
        <div class="flex items-start gap-3">
            <div class="flex-shrink-0 mt-0.5">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 text-amber-600 dark:text-amber-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                    />
                </svg>
            </div>
            <div class="flex-1">
                <div class="flex items-center gap-2 mb-1">
                    <span class="text-xs font-semibold text-amber-700 dark:text-amber-300 uppercase tracking-wide">
                        Comentário Interno
                    </span>
                    <div class="h-1 w-1 bg-amber-400 rounded-full"></div>
                    <span class="text-xs text-amber-600 dark:text-amber-400 opacity-75">
                        Visível apenas para atendentes
                    </span>
                </div>
                <p class="text-sm leading-relaxed text-amber-800 dark:text-amber-200">
                    {{ mensagens.ds_comentario }}
                </p>
            </div>
        </div>
    </div>

    <div v-if="mensagens.type_message" :class="['mb-3  flex', mensagens.from_me ? 'justify-end' : 'justify-start']">
        <!-- Avatar (apenas para mensagens recebidas) 
        <div v-if="!mensagens.from_me && showAvatar" class="flex-shrink-0 mr-3">
            <div class="w-8 h-8 bg-gray-300 dark:bg-darkmode-400 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-gray-600 dark:text-slate-300">
                    {{ getInitials(mensagens.pushName || 'U') }}
                </span>
            </div>
        </div>
        -->

        <!-- Balão da Mensagem -->
        <div
            :class="[
                'max-w-xs lg:max-w-md xl:max-w-lg relative break-words',
                mensagens.from_me
                    ? 'bg-emerald-500 dark:bg-emerald-700 text-white rounded-l-xl rounded-tr-xl '
                    : 'bg-white dark:bg-darkmode-400 text-gray-900 dark:text-slate-300 rounded-r-xl rounded-tl-xl  shadow-sm border border-gray-100 dark:border-darkmode-300',
            ]"
            style="word-wrap: break-word; overflow-wrap: break-word; hyphens: auto"
        >
            <!-- Mensagem Citada -->
            <div
                v-if="mensagens.type_message_quoted"
                :class="[
                    'p-2 border-l-4 rounded-t-xl',
                    mensagens.from_me
                        ? 'border-emerald-300 bg-emerald-600/50'
                        : 'border-blue-400 bg-blue-50 dark:bg-darkmode-300',
                ]"
            >
                <div class="text-xs opacity-75 mb-1">Respondendo:</div>

                <!-- Texto Citado -->
                <p
                    v-if="
                        mensagens.type_message_quoted === 'textMessage' ||
                        mensagens.type_message_quoted === 'listMessage'
                    "
                    class="text-sm opacity-90 line-clamp-2"
                >
                    {{ mensagens.quotedmessage }}
                </p>

                <!-- Imagem Citada -->
                <img
                    v-else-if="mensagens.type_message_quoted === 'imageMessage'"
                    :src="mensagens.url_midia_quotedcompleta"
                    class="w-16 h-16 rounded object-cover"
                    alt="Imagem citada"
                />

                <!-- Áudio Citado -->
                <div v-else-if="mensagens.type_message_quoted === 'audioMessage'" class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gray-200 dark:bg-darkmode-200 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 011 1v8a1 1 0 01-2 0V6a1 1 0 011-1z" />
                        </svg>
                    </div>
                    <span class="text-sm">Mensagem de áudio</span>
                </div>
            </div>

            <!-- Mensagem Editada -->
            <div
                v-if="mensagens.in_mensagem_edited"
                :class="[
                    'p-2 border-l-4 mb-2 rounded',
                    mensagens.from_me
                        ? 'border-yellow-300 bg-yellow-500/20'
                        : 'border-orange-400 bg-orange-50 dark:bg-orange-900/20',
                ]"
            >
                <div class="text-xs opacity-75 mb-1">Mensagem anterior:</div>
                <p class="text-sm line-through opacity-60">
                    {{ mensagens.mensagem_edited }}
                </p>
            </div>

            <!-- Conteúdo Principal -->
            <div class="p-3">
                <!-- Imagem -->
                <div v-if="mensagens.type_message === 'imageMessage'" class="space-y-2">
                    <!-- Placeholder enquanto a imagem carrega -->
                    <div
                        v-if="!mensagens.loaded"
                        class="w-full h-40 bg-gray-200 dark:bg-darkmode-400 animate-pulse rounded-lg"
                    ></div>

                    <!-- Imagem com transição suave -->
                    <img
                        :src="getImageSrc(mensagens)"
                        @load="mensagens.loaded = true"
                        :class="{
                            'rounded-lg max-w-full cursor-pointer hover:opacity-90 transition-all duration-300': true,
                            'opacity-0 h-0': !mensagens.loaded,
                            'opacity-100': mensagens.loaded,
                        }"
                        data-action="zoom"
                        alt="Imagem enviada"
                    />

                    <div
                        v-if="mensagens.mensagem"
                        class="text-sm break-words"
                        style="word-wrap: break-word; overflow-wrap: break-word; white-space: pre-wrap"
                        v-html="mensagens.mensagem"
                    ></div>
                </div>

                <!-- Documento/PDF -->
                <div
                    v-else-if="mensagens.type_message === 'documentMessage' || mensagens.mimetype === 'application/pdf'"
                    class="space-y-2"
                >
                    <a
                        :href="mensagens?.url_midiacompleta || mensagens?.url_midia"
                        target="_blank"
                        class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-darkmode-300 rounded-lg hover:bg-gray-100 dark:hover:bg-darkmode-200 transition-colors"
                    >
                        <div
                            class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
                        >
                            <FileDownIcon class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-sm truncate">{{ mensagens.file_name }}</div>
                            <div class="text-xs text-gray-500 dark:text-slate-400">
                                {{ mensagens.extension }} • {{ mensagens.formattedFileLength }}
                            </div>
                        </div>
                    </a>

                    <div
                        v-if="mensagens.mensagem"
                        class="text-sm break-words"
                        style="word-wrap: break-word; overflow-wrap: break-word; white-space: pre-wrap"
                        v-html="mensagens.mensagem"
                    ></div>
                </div>

                <!-- Áudio -->
                <div v-else-if="mensagens.type_message === 'audioMessage'" class="space-y-2">
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-darkmode-300 rounded-lg">
                        <audio controls class="flex-1">
                            <source :src="getAudioSrc(mensagens)" type="audio/ogg" />
                        </audio>
                    </div>

                    <div
                        v-if="mensagens.mensagem"
                        class="text-sm break-words"
                        style="word-wrap: break-word; overflow-wrap: break-word; white-space: pre-wrap"
                        v-html="mensagens.mensagem"
                    ></div>
                </div>

                <!-- Localização -->
                <div v-else-if="mensagens.type_message === 'locationMessage'" class="space-y-2">
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-darkmode-300 rounded-lg">
                        <div
                            class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center"
                        >
                            <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    fill-rule="evenodd"
                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-sm">Localização compartilhada</div>
                            <div class="text-xs text-gray-500 dark:text-slate-400">{{ mensagens.address }}</div>
                        </div>
                    </div>
                </div>

                <!-- Texto Normal -->
                <div v-else>
                    <div
                        :class="[
                            'text-sm leading-relaxed break-words',
                            mensagens.in_mensagem_delete ? 'line-through opacity-60' : '',
                            mensagens.from_me ? 'text-white' : 'text-gray-900 dark:text-slate-300',
                        ]"
                        style="word-wrap: break-word; overflow-wrap: break-word; white-space: pre-wrap"
                        v-html="mensagens.mensagem"
                    ></div>
                </div>

                <!-- Footer: Horário + Status -->
                <div class="flex items-end justify-end mt-2 space-x-1">
                    <!-- Indicadores de edição/exclusão -->
                    <div v-if="mensagens.in_mensagem_edited || mensagens.in_mensagem_delete" class="text-xs opacity-60">
                        <span v-if="mensagens.in_mensagem_edited">Editada</span>
                        <span v-if="mensagens.in_mensagem_delete">Excluída</span>
                        <span class="mx-1">•</span>
                    </div>

                    <!-- Horário -->
                    <span
                        :class="[
                            'text-xs',
                            mensagens.from_me ? 'text-emerald-100' : 'text-gray-500 dark:text-slate-400',
                        ]"
                    >
                        {{ mensagens?.horario }}
                    </span>

                    <!-- Status de entrega (apenas para mensagens enviadas) -->
                    <div v-if="mensagens.from_me" class="flex items-center">
                        <ClockIcon
                            v-if="!mensagens.status_message"
                            class="w-3 h-3 text-white"
                            style="stroke-width: 3"
                        />
                        <CheckIcon
                            v-else-if="mensagens.status_message === 'Enviado'"
                            class="w-3 h-3 text-white"
                            style="stroke-width: 3"
                        />
                        <CheckCheckIcon
                            v-else-if="mensagens.status_message === 'Recebido'"
                            class="w-3 h-3 text-white"
                            style="stroke-width: 3"
                        />
                        <CheckCheckIcon
                            v-else-if="mensagens.status_message === 'Lido'"
                            class="w-3 h-3 text-blue-700 font-bold"
                            style="stroke-width: 3"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import converters from '../../utils/converters';

    const props = defineProps({
        mensagens: {
            type: Object,
            required: true,
        },
        hosts: {
            type: Object,
            required: true,
        },
        dark: {
            type: Boolean,
            default: false,
        },
        showAvatar: {
            type: Boolean,
            default: true,
        },
    });

    // Métodos auxiliares
    const getInitials = (name) => {
        return name
            .split(' ')
            .map((n) => n[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const getImageSrc = (mensagem) => {
        return mensagem.ds_base64
            ? `data:${mensagem.mimetype};base64,${mensagem.ds_base64}`
            : mensagem.url_midiacompleta;
    };

    const getAudioSrc = (mensagem) => {
        return `${props.hosts.apiOiZap}/midia/${mensagem.instance}/audioMessage/${mensagem.file_name}`;
    };
</script>

<style scoped>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
