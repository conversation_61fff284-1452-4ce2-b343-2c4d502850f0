import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { Client } from 'pg';

import { connectedRabbitMQInstances } from './ConnectedRabbitMQInstances';
import Logger from './Logger';
import { connectRabbitMQ, removeInstanceQueues } from './rabbitmq/RabbitMQ';
import { setupSocketIO } from './Socket';

const logger = Logger.getLogger();

const HOST_RABBITMQOIZAP = process.env.HOST_RABBITMQ_OIZAP as string;
let HOST_RABBITMQEVO =
  process.env.AMBIENTE === 'PROD'
    ? (process.env.HOST_RABBITMQ as string)
    : process.env.AMBIENTE === 'CRM'
      ? (process.env.HOST_RABBITMQ as string)
      : (process.env.HOST_RABBITMQSANDBOX as string);
const pass =
  process.env.AMBIENTE === 'PROD'
    ? process.env.PASSWORD_DATABASE
    : process.env.AMBIENTE === 'CRM'
      ? process.env.PASSWORD_DATABASE
      : process.env.PASSWORD_DATABASEDEV;
const port =
  process.env.AMBIENTE === 'PROD'
    ? process.env.PORT_DATABASE
    : process.env.AMBIENTE === 'CRM'
      ? process.env.PORT_DATABASE
      : process.env.PORT_DATABASEDEV;
const host =
  process.env.AMBIENTE === 'PROD'
    ? process.env.HOST_DATABASE
    : process.env.AMBIENTE === 'CRM'
      ? process.env.HOST_DATABASE
      : process.env.HOST_DATABASEDEV;
const base =
  process.env.AMBIENTE === 'PROD'
    ? process.env.DATABASE
    : process.env.AMBIENTE === 'CRM'
      ? process.env.DATABASE_CRM
      : process.env.DATABASE_SANDBOX;
const connectionString = `postgres://${process.env.USER_DATABASE}:${pass}@${host}:${port}/${base}`;
// logger.debug('connectionString at line 33 in services/NotifyPostgres.ts: ' + connectionString);
//console.log('🚀 ~ NotifyPostgres.ts:33 ~ connectionString:', connectionString);
// logger.debug('HOST_RABBITMQOIZAP at line 33 in services/NotifyPostgres.ts: ' + HOST_RABBITMQOIZAP);

class NotifyPostgres {
  private notificationChannel: string;
  private client: Client;
  private io: any;
  private retryCount: number = 0;
  private maxRetries: number = 5;
  private retryInterval: number = 5000;
  private isConnected: boolean = false; // Adicionado para controlar estado da conexão

  private async cleanup() {
    logger.info('Iniciando limpeza de recursos...');
    try {
      await this.disconnect();
      logger.info('Recursos limpos com sucesso');
      process.exit(0);
    } catch (error) {
      logger.error('Erro durante cleanup:', error instanceof Error ? error.stack : error);
      process.exit(1);
    }
  }

  constructor(notificationChannel: string, io: any) {
    this.notificationChannel = notificationChannel;
    this.io = io;

    this.client = new Client({
      connectionString: connectionString,
      keepAlive: true,
      keepAliveInitialDelayMillis: 10000,
      connectionTimeoutMillis: 5000,
      application_name: 'NotifyPostgres_Listener',
    });

    this.startHeartbeat(); // Inicia o heartbeat ao criar a instância

    // Handlers de eventos mais robustos
    this.client.on('error', (err) => {
      this.isConnected = false;
      const errorMessage = err instanceof Error ? err.stack : err;
      logger.error('PostgreSQL client error:' + errorMessage);
      this.handleDisconnect();
    });

    this.client.on('end', () => {
      this.isConnected = false;
      logger.debug('Cliente PostgreSQL desconectado');
      this.handleDisconnect();
    });

    process.on('SIGINT', this.cleanup.bind(this));
    process.on('SIGTERM', this.cleanup.bind(this));
  }

  private startHeartbeat() {
    let lastHeartbeatSuccess = true;

    setInterval(async () => {
      try {
        if (this.isConnected) {
          await this.client.query('SELECT 1');
          if (!lastHeartbeatSuccess) {
            logger.info('Heartbeat: conexão PostgreSQL restaurada');
          }
          lastHeartbeatSuccess = true;
        }
      } catch (error) {
        lastHeartbeatSuccess = false;
        const errorMessage = error instanceof Error ? error.stack : error;
        logger.error('Erro no heartbeat:' + errorMessage);

        this.handleDisconnect();
      }
    }, 30000);
  }

  async setupNotificationListener() {
    try {
      // Desconecta se já estiver conectado
      if (this.isConnected) {
        await this.disconnect();
      }

      // Tenta conectar com timeout
      const connectPromise = this.client.connect();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout ao conectar')), 5000),
      );

      await Promise.race([connectPromise, timeoutPromise]);
      this.isConnected = true;

      // Configura o listener
      await this.client.query(`LISTEN ${this.notificationChannel}`);

      // Adicionar handler para erro de conexão
      this.client.on('error', (err) => {
        logger.error('Erro na conexão do listener:' + err);
        this.handleDisconnect();
      });

      this.client.on('notification', async (msg) => {
        logger.debug('Received notification:' + JSON.stringify(msg.payload));

        if (msg.payload) {
          try {
            const notificationData = JSON.parse(msg.payload);
            logger.debug('NotifyPostgres > Created Instance:' + JSON.stringify(notificationData));
            if (notificationData.status === 'Ativo') {
              // Verifique se a instância já está conectada
              if (!connectedRabbitMQInstances.hasInstance(notificationData.nameinstance)) {
                connectedRabbitMQInstances.addInstance(notificationData.nameinstance);

                //console.log('HOST_RABBITMQEVO at line 66 in services/NotifyPostgres.ts:', HOST_RABBITMQEVO);
                //console.log('HOST_RABBITMQOIZAP at line 66 in services/NotifyPostgres.ts:', HOST_RABBITMQOIZAP);
                await connectRabbitMQ('evolution', HOST_RABBITMQEVO);
                await connectRabbitMQ('oizap', HOST_RABBITMQOIZAP);

                // Adiciona instância ao conjunto

                // Atualiza o socket apenas com as instâncias conectadas
                setupSocketIO(this.io, notificationData.nameinstance);
              }
            }
          } catch (error) {
            console.error('Error parsing notification data:', error);
          }
        }
      });

      logger.debug(`Listener configurado para o canal: ${this.notificationChannel}`);
    } catch (error) {
      this.isConnected = false;
      logger.error('Erro ao configurar listener de notificações:' + error);
      throw error;
    }
  }

  private async handleDisconnect() {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      logger.warn(`Tentando reconectar... Tentativa ${this.retryCount} de ${this.maxRetries}`);

      try {
        // Verifica se está conectado usando isConnected
        if (this.isConnected) {
          await this.disconnect();
        }

        // Espera um tempo antes de tentar reconectar
        await new Promise((resolve) => setTimeout(resolve, this.retryInterval * this.retryCount));

        // Cria um novo cliente com configurações atualizadas
        this.client = new Client({
          connectionString: connectionString,
          keepAlive: true,
          keepAliveInitialDelayMillis: 10000,
          connectionTimeoutMillis: 5000,
          application_name: 'NotifyPostgres_Listener',
        });

        await this.setupNotificationListener();
        this.retryCount = 0;
        logger.info('Reconectado com sucesso!');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.stack : error;
        logger.error('Falha na tentativa de reconexão:', errorMessage);
        console.error('Erro durante a reconexão:', {
          erro: error instanceof Error ? error.stack : error,
          tentativa: this.retryCount,
          maxTentativas: this.maxRetries,
        });
        this.handleDisconnect();
      }
    } else {
      logger.error('Máximo de tentativas de reconexão atingido');
      process.exit(1);
    }
  }

  async deleteNotificationListener() {
    try {
      if (this.isConnected) {
        await this.disconnect();
      }

      await this.client.connect();
      this.isConnected = true;
      await this.client.query(`LISTEN ${this.notificationChannel}`);

      this.client.on('notification', async (msg) => {
        if (!msg.payload) return;

        try {
          const notificationData = JSON.parse(msg.payload);
          logger.debug('Deleted Instance:', notificationData);

          // Usar a nova função para remover as filas da instância
          await Promise.all([
            this.disconnectInstanceChannel('evolution', notificationData.nameinstance),
            this.disconnectInstanceChannel('oizap', notificationData.nameinstance),
          ]);

          connectedRabbitMQInstances.removeInstance(notificationData.nameinstance);
        } catch (error) {
          logger.error('Erro ao processar notificação de delete:', error);
        }
      });
    } catch (error) {
      logger.error('Erro ao configurar listener de delete:', error);
      throw error;
    }
  }

  private async disconnectInstanceChannel(serverName: string, instanceName: string) {
    try {
      // Usar a nova função removeInstanceQueues
      const result = await removeInstanceQueues(serverName, instanceName);

      if (result.statuscode === 200) {
        logger.info(`✅ ${serverName} - Filas da instância ${instanceName} removidas com sucesso!`);
      } else {
        logger.error(`❌ ${serverName} - Erro ao remover filas da instância ${instanceName}: ${result.message}`);
      }
    } catch (error: any) {
      logger.error(`❌ ${serverName} - Erro ao desconectar instância ${instanceName}: ${error.message}`);
    }
  }

  async disconnect() {
    try {
      if (this.isConnected) {
        await this.client.end();
        this.isConnected = false;
        logger.debug('Cliente PostgreSQL desconectado com sucesso');
      }
    } catch (error) {
      logger.error('Erro ao desconectar cliente PostgreSQL:', error);
      throw error;
    }
  }
}

export default NotifyPostgres;
