import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { DepartamentosDB } from '../../data/departamentos/DepartamentosDB';
import { IToken } from '@/interfaces/IToken';
export class DepartamentosModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      const token = req.query.token as unknown as IToken;
      req.body.cd_usucad = token?.cd_usuario || 0;
      return await DepartamentosDB.incluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listar(req: Request): Promise<IRetorno> {
    try {
      return await DepartamentosDB.listar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterar(req: Request): Promise<IRetorno> {
    try {
      return await DepartamentosDB.alterar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async excluir(req: Request): Promise<IRetorno> {
    try {
      return await DepartamentosDB.excluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
