-- Migration: create_adm_usuarios
-- Created: 2025-07-10T12:00:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

CREATE TABLE IF NOT EXISTS adm_usuarios (
  cd_usuario SERIAL PRIMARY KEY,
  ds_nome VARCHAR(255) NOT NULL,
  ds_email VARCHAR(255) NOT NULL,
  ds_senha VARCHAR(255) NOT NULL,
  tp_privilegio VARCHAR(2) NOT NULL,
  tp_status VARCHAR(2) NOT NULL,
  cd_usuario_cadastro INT,
  dt_cadastro TIMESTAMP,
  ds_email_recuperacao VARCHAR(255),
  ds_login VARCHAR(255),
  dt_ultimo_login TIMESTAMP,
  cd_token VARCHAR(255),
  dt_token TIMESTAMP,
  cd_hash VARCHAR(255),
  dt_hash TIMESTAMP,
  nr_cpf VARCHAR(20),
  cd_hash_senha VARCHAR(255),
  in_ativo BOOLEAN DEFAULT true,
  in_bloqueado BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
); 