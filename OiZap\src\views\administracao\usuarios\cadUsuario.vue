<template>
    <ShowLoading ref="loading" />
    <div class="box p-4">
        <!-- <div class="intro-y flex flex-row items-center py-1 sm:mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Usuários</h2>
            <button
                @click="router.push({ name: 'listaUsuarios' })"
                class="btn btn-primary-soft col-span-4"
                :class="`${darkMode ? 'btn-success-soft ' : 'btn-secondary-soft'}`"
            >
                Voltar
            </button>
        </div> -->

        <!-- Header da se<PERSON> de módulos -->
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <Building2Icon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Usuários</h3>
                        <p class="text-blue-100 text-sm">Cadastrado do usuário.</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="router.push({ name: 'listaUsuarios' })"
                    >
                        <ChevronLeftIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Voltar</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="box intro-y p-4">
            <div class="flex flex-row justify-between items-center mb-4 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <!-- <LinkIcon class="w-6 h-6 text-secondary" /> -->
                        <h2 class="text-lg font-semibold">
                            {{ novoUsuario ? 'Novo Usuário' : 'Edição de Usuário' }}
                        </h2>
                    </div>
                    <p class="text-md text-slate-600 dark:text-slate-400">
                        {{ novoUsuario ? 'Cadastre um novo usuário' : 'Edite os dados do usuário' }}
                    </p>
                </div>
                <div class="cursos-pointer flex items-center fit gap-2">
                    <button
                        type="button"
                        class="btn"
                        :class="`${darkMode ? 'btn-success ' : 'btn-primary-soft'}`"
                        @click="save()"
                    >
                        <SaveIcon class="w-4 h-4 mr-1" style="stroke-width: 3" />
                        <span class="text-ellipsis whitespace-nowrap overflow-hidden">Salvar</span>
                    </button>
                </div>
            </div>
            <div class="intro-y col-span-12">
                <!-- BEGIN: Form Validation -->
                <PreviewComponent class="intro-y">
                    <Preview>
                        <!-- BEGIN: Validation Form -->
                        <form class="validate-form grid grid-cols-12 gap-2 gap-y-3 mb-2" @submit.prevent="save">
                            <div class="col-span-12 sm:col-span-6">
                                <label for="validation-form-3" class="sm:w-20 self-center text-slate-500">
                                    Login
                                    <span class="text-danger">*</span>
                                </label>
                                <BaseInput
                                    :disabled="bloqueiaCampos"
                                    autofocus
                                    id="validation-form-3"
                                    v-model.trim="formData.ds_login"
                                    type="text"
                                    name="ds_login"
                                />
                            </div>
                            <div class="col-span-12 sm:col-span-6">
                                <label for="validation-form-4" class="forceLeft sm:w-20 self-center text-slate-500">
                                    Senha
                                    <span class="text-danger">*</span>
                                </label>
                                <BaseInput
                                    :disabled="bloqueiaCampos"
                                    id="validation-form-4"
                                    v-model.trim="formData.ds_senha"
                                    type="password"
                                    name="ds_senha"
                                />
                            </div>
                            <div class="col-span-12 sm:col-span-2">
                                <label for="validation-form-2" class="forceLeft sm:w-20 self-center text-slate-500">
                                    Nome
                                    <span class="text-danger">*</span>
                                </label>
                                <BaseInput
                                    :disabled="bloqueiaCampos"
                                    id="validation-form-2"
                                    v-model.trim="formData.ds_nome"
                                    type="text"
                                    name="ds_nome"
                                />
                            </div>
                            <!-- </div>
                            <div class="col-span-1">
                                <label for="selStatus" class="form-label self-center">
                                    Status
                                    <span class="text-danger">*</span>
                                </label>

                                <div class="form-control col-span-2">
                                    <select
                                        :disabled="bloqueiaCampos"
                                        id="selStatus"
                                        data-placeholder="Selecione"
                                        class="tom-select w-full"
                                        v-model.trim="formData.tp_status"
                                    >
                                        <option value="A">Ativo</option>
                                        <option value="I">Inativo</option>
                                    </select>
                                </div>
                            </div> -->
                            <div class="col-span-6 sm:col-span-2">
                                <label for="selStatus" class="sm:w-20 self-center text-slate-500">
                                    Status
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="form-control col-span-2 -my-1.5">
                                    <Multiselect
                                        :customHeight="33"
                                        v-model="formData.tp_status"
                                        mode="single"
                                        :close-on-select="false"
                                        :searchable="false"
                                        :createOption="false"
                                        :options="[
                                            { value: 'A', label: 'Ativo' },
                                            { value: 'I', label: 'Inativo' },
                                        ]"
                                        ref="selStatusRef"
                                        :isDisabled="acessoUsuario"
                                    />
                                </div>
                            </div>
                            <div class="col-span-6 sm:col-span-2">
                                <label for="selPrivilegio" class="sm:w-20 self-center text-slate-500">
                                    Privilégio
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="form-control col-span-2 -my-1.5">
                                    <Multiselect
                                        :customHeight="33"
                                        v-model="formData.tp_privilegio"
                                        mode="single"
                                        :close-on-select="false"
                                        :searchable="true"
                                        :createOption="false"
                                        :options="listaPrivilegio"
                                        ref="selPrivilegioRef"
                                        :isDisabled="acessoUsuario"
                                    />
                                </div>
                            </div>
                            <!--  
              <div class="grid grid-cols-4 gap-2 mt-2">
                <div class="col-span-4">
                  <label class="forceLeft sm:w-20 self-center">Acessos</label>
                  <div class="form-control col-span-2">
                    <Multiselect
                      v-model="acessosLiberado"
                      :close-on-select="false"
                      :searchable="true"
                      :createOption="false"
                      :options="acessos"
                      @emitEvent="selecionaAcesso"
                    />
                  </div>
                </div>
              </div>
              -->
                            <div class="col-span-12 sm:col-span-6">
                                <label for="selInstancia" class="sm:w-20 self-center text-slate-500"> Instância </label>

                                <div class="form-control col-span-2 -my-1.5">
                                    <Multiselect
                                        :customHeight="33"
                                        v-model="instanciaLiberada"
                                        :close-on-select="false"
                                        :searchable="true"
                                        :createOption="false"
                                        :options="listaInstancias"
                                        @emitEvent="selecionaInstancia"
                                        :isDisabled="formData.cd_usuario == undefined || acessoUsuario"
                                    />
                                </div>
                            </div>
                            <div class="col-span-10 sm:col-span-5">
                                <label for="selInstancia" class="sm:w-20 self-center text-slate-500">
                                    Departamento
                                    <button
                                        type="button"
                                        class="btn btn-secondary-soft h-4 ml-2"
                                        @click="modalDepartamentos = true"
                                    >
                                        <PlusIcon class="w-4 h-4 mr-1" style="stroke-width: 3" />
                                        <span class="text-ellipsis whitespace-nowrap overflow-hidden hidden lg:block">
                                            Adicionar
                                        </span>
                                    </button>
                                </label>

                                <div class="form-control col-span-2 -my-1.5">
                                    <Multiselect
                                        v-model="formData.cd_departamento"
                                        :close-on-select="false"
                                        :searchable="false"
                                        :createOption="false"
                                        :options="listaDepartamentos"
                                        mode="tags"
                                        :customHeight="33"
                                    />
                                </div>
                            </div>

                            <!-- 14/06/2025 00:14 - Robinho - Configuração foi transferida para a configuração do estabelecimento 
                            <div class="col-span-12 sm:col-span-6 flex items-center gap-2 pt-4 ml-1">
                                <div class="form-check mr-2 mt-0">
                                    <div class="form-check form-switch">
                                        <input
                                            id="inAplicaDesconto"
                                            name="inAplicaDesconto"
                                            class="form-check-input"
                                            type="checkbox"
                                            :value="formData.in_aplicadesconto"
                                            :checked="formData.in_aplicadesconto"
                                            @change="onChangeAplicaDesconto($event)"
                                        />
                                    </div>
                                </div>
                                <label class="form-label m-0 self-center text-slate-500"
                                    >Aplica desconto no Pedido</label
                                >
                            </div> -->
                            <div class="col-span-12 sm:col-span-6 flex items-center gap-2 pt-4 ml-1"></div>
                            <cite class="text-xs text-slate-500 dark:text-slate-400 col-span-6">
                                <span class="text-danger">*</span> Campos obrigatórios
                            </cite>
                        </form>
                        <!-- END: Validation Form -->
                    </Preview>

                    <!-- <div class="grid grid-cols-4 mt-2">
                            <div class="col-span-1">
                                <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full"></div>
                            </div>
                            <div class="col-span-3 flex flex-row-reverse">
                                <button
                                    type="button"
                                    class="btn w-20 h-10"
                                    :class="`${darkMode ? 'btn-outline-success ' : 'btn-outline-primary'}`"
                                    @click="save()"
                                >
                                    Salvar
                                </button>
                            </div>
                        </div> -->
                </PreviewComponent>
                <!-- END: Form Validation -->
            </div>
        </div>
    </div>
    <ModalDepartamentos
        ref="modalDepartamentosRef"
        :estado="modalDepartamentos"
        @modal:close="modalDepartamentos = false"
        :estabelecimento="
            estabelecimentosLiberado[0]?.cd_estabelecimento ? estabelecimentosLiberado[0]?.cd_estabelecimento : null
        "
        :departamento:adicionado="carregarDepartamentos(false)"
    />
</template>

<script setup>
    import { ref, reactive, onMounted, provide, computed } from 'vue';
    import { useRouter, useRoute } from 'vue-router';
    import ModalDepartamentos from '@/components/modal-departamentos/Main.vue';
    import UsuariosServices from '@/services/administracao/UsuariosServices';
    import DepartamentosServices from '@/services/administracao/DepartamentosServices';
    import UsuarioInstanciasServices from '@/services/administracao/UsuarioInstanciasServices';
    import UsuarioAcessosServices from '@/services/administracao/UsuarioAcessosServices';
    import AcessosServices from '@/services/administracao/AcessosServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';

    import { useDarkModeStore } from '@/stores/dark-mode';
    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();

    const router = useRouter();
    const route = useRoute();
    const loading = ref();

    //let usuarios = ref([]);
    let estabelecimentosLiberado = ref([]);
    let instanciaLiberada = ref([]);
    let listaInstancias = ref([]);
    let listaDepartamentos = ref([]);
    const modalDepartamentos = ref(false);
    let codUsuario = ref(null);
    //let habilitaCodigo = ref(false);
    let novoUsuario = ref(false);
    let bloqueiaCampos = ref(false);
    let acessoUsuario = ref(false);
    let acessos = ref([
        { label: 'Chat', value: 'Chat' },
        { label: 'Kanban', value: 'Kanban' },
        { label: 'Instâncias', value: 'Instâncias' },
        { label: 'Fluxos', value: 'Fluxos' },
        { label: 'Integração', value: 'Integração' },
    ]);
    let acessosLiberado = ref([]);
    let listaPrivilegio = ref([
        { value: 'A', label: 'Administrador' },
        { value: 'U', label: 'Usuário' },
    ]);

    const formData = reactive({
        cd_usuario: undefined,
        cd_estabelecimento: undefined,
        ds_nome: '',
        ds_login: '',
        ds_email: '',
        tp_privilegio: '',
        //ds_privilegio: '',
        tp_status: '',
        ds_senha: '',
        cd_usucad: 0,
        in_aplicadesconto: false,
        cd_departamento: [],
    });

    const selectStatusRef = ref();
    provide('bind[selectStatusRef]', (el) => {
        selectStatusRef.value = el;
    });
    const selectAcessoRef = ref();
    provide('bind[selectAcessoRef]', (el) => {
        selectAcessoRef.value = el;
    });
    const selPrivilegioRef = ref();
    provide('bind[selPrivilegioRef]', (el) => {
        selPrivilegioRef.value = el;
    });

    async function limpaDados() {
        formData.cd_usuario = undefined;
        formData.cd_estabelecimento = undefined;
        formData.ds_nome = '';
        formData.ds_login = '';
        formData.ds_email = '';
        //formData.ds_privilegio = '';
        formData.tp_privilegio = '';
        formData.tp_status = '';
        formData.ds_senha = '';
        formData.cd_usucad = '';
        formData.in_aplicadesconto = false;
    }

    const selecionaAcesso = async (event) => {
        if (event.event == 'Select') {
            const obj = {
                cd_usuario: formData.cd_usuario,
                ds_acesso: event.option,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respAcesso = await UsuarioAcessosServices.incluir(obj);
        } else if (event.event == 'deSelect') {
            const obj = {
                cd_usuario: formData.cd_usuario,
                ds_acesso: event.option,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respAcesso = await UsuarioAcessosServices.remover(obj);
        } else if (event.event == 'Clear') {
            const obj = {
                cd_usuario: formData.cd_usuario,

                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respAcesso = await UsuarioAcessosServices.remover(obj);
        }
    };

    const selecionaInstancia = async (event) => {
        if (event.event == 'Select') {
            const obj = {
                cd_usuario: formData.cd_usuario,
                id_instancia: event.option,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respInstancia = await UsuarioInstanciasServices.incluir(obj);
        } else if (event.event == 'deSelect') {
            const obj = {
                cd_usuario: formData.cd_usuario,
                id_instancia: event.option,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respInstancia = await UsuarioInstanciasServices.remover(obj);
        } else if (event.event == 'Clear') {
            const obj = {
                cd_usuario: formData.cd_usuario,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };
            const respInstancia = await UsuarioInstanciasServices.remover(obj);
        }
    };

    /*
      async function salvaGrupoAcesso(cdUsuario) {
        let usuario = await localStorage.getItem('codusuario');
        let lstGrupo = [];
        if (acessosLiberado.value != '') {
          for (let i = 0; i < acessosLiberado.value.length; i++) {
            //lstGrupo.push(acessosLiberado.value[i]);
            lstGrupo.push({
              cd_usuario: cdUsuario,
              cd_grupo: acessosLiberado.value[i],
              cd_usucad: usuario,
            });
          }
        }

        let respAcesso = await UsuariosServices.grupoAcessoUsuario(lstGrupo);

        if (respAcesso.statuscode === 404) {
          toast.showErrorNotification(respAcesso.message);
        } else if (respAcesso.statuscode === 200) {
          //toast.showSuccessNotification('');
        } else if (respAcesso.statuscode === 401) {
          localStorage.removeItem('token');
          router.push({ path: `${hosts.app}` });
          toast.showErrorNotification(respAcesso.message);
        } else {
          toast.showErrorNotification(respAcesso.message);
        }
      }
    */

    // const onChangeAplicaDesconto = (event) => {
    //     formData.in_aplicadesconto = event.target.checked;
    // };
    async function save() {
        if (formData.ds_login == undefined) {
            toast.showErrorNotification('Login é obrigatório.');
            return;
        }
        if (formData.ds_nome == undefined) {
            toast.showErrorNotification('Nome é obrigatório.');
            return;
        }
        if (formData.tp_privilegio == undefined) {
            toast.showErrorNotification('Privilégio é obrigatório.');
            return;
        }
        if (formData.tp_status == undefined) {
            toast.showErrorNotification('Status é obrigatório.');
            return;
        }

        formData.cd_usucad = localStorage.getItem('codusuario');
        formData.ds_email = formData.ds_login;
        formData.cd_estabelecimento =
            estabelecimentosLiberado.value.length > 0
                ? estabelecimentosLiberado.value[0].cd_estabelecimento
                : undefined;

        // 14/06/2025 00:14 - Robinho - Configuração foi transferida para a configuração do estabelecimento
        // localStorage.setItem('aplicadesconto', formData.in_aplicadesconto);

        //console.log(formData);
        let msgAlerta;
        let respUsuario;
        if (novoUsuario.value) {
            if (formData.ds_senha == undefined) {
                toast.showErrorNotification('Senha é obrigatória.');
                return;
            }
            msgAlerta = 'Usuário cadastrado com sucesso!';
            const body = {
                ...formData,
                cd_departamento: !formData.cd_departamento ? undefined : formData.cd_departamento.join(','),
            };
            // console.log('🚀 ~ cadUsuario.vue:469 ~ save ~ body:', body);
            respUsuario = await UsuariosServices.cadastraUsuario(body);
        } else {
            const body = {
                ...formData,
                cd_departamento: !formData.cd_departamento ? undefined : formData.cd_departamento.join(','),
            };

            msgAlerta = 'Dados alterado com sucesso!';
            //console.log('🚀 ~ cadUsuario.vue:486 ~ save ~ body:', body);
            respUsuario = await UsuariosServices.alteraUsuario(body);
        }

        if (respUsuario.statuscode === 404) {
            toast.showErrorNotification(respUsuario.message);
        } else if (respUsuario.statuscode === 200) {
            //usuarios.value = data;
            formData.cd_usuario = respUsuario.data[0].cd_usuario;
            //await cadastraGrupoAcesso(data[0].cd_usuario);
            //console.log("formData.ds_nome",formData.ds_nome);
            toast.showSuccessNotification(msgAlerta);
            //router.push({ name: 'listaUsuarios' });
        } else if (respUsuario.statuscode === 401) {
            localStorage.removeItem('token');
            router.push({ path: `${hosts.app}` });
            toast.showErrorNotification(respUsuario.message);
        } else {
            toast.showErrorNotification(respUsuario.message);
        }
    }

    async function carregaGrupoAcessos() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        let respGrupos = await AcessosServices.listaGrupos();

        if (respGrupos.statuscode === 404) {
            toast.showErrorNotification(respGrupos.message);
        } else if (respGrupos.statuscode === 200) {
            acessos.value = respGrupos.data;
        } else if (respGrupos.statuscode === 401) {
            localStorage.removeItem('token');
            router.push({ path: `${hosts.app}` });
            toast.showErrorNotification(respGrupos.message);
        } else {
            toast.showErrorNotification(respGrupos.message);
        }
    }

    async function carregaGruposAcessoUsuario(cdUsuario) {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        /* let respGrupoAcesso = await UsuariosServices.listaGrupoAcessoUsuario(cdUsuario);

        if (respGrupoAcesso.statuscode === 404) {
          toast.showErrorNotification(respGrupoAcesso.message);
        } else if (respGrupoAcesso.statuscode === 200) {
          let aces = [];
          for (let i = 0; i < respGrupoAcesso.data.length; i++) {
            aces.push(respGrupoAcesso.data[i].cd_grupo);
          }
          acessosLiberado.value = aces;
        }*/
    }

    async function carregaUsuarioAcessos() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        const filtro = {
            cd_usuario: formData.cd_usuario,
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        const respAcesso = await UsuarioAcessosServices.listar(filtro);

        if (respAcesso.statuscode === 500) {
            toast.showErrorNotification(respAcesso.message);
        } else if (respAcesso.statuscode === 200) {
            let aces = [];
            respAcesso.data.forEach((acesso) => {
                aces.push(acesso.ds_acesso);
            });

            acessosLiberado.value = aces;
        }
    }

    async function carregaUsuarioDepartamentos() {
        const filtro = {
            cd_usuario: formData.cd_usuario,
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        const respAcesso = await UsuariosServices.listaUsuariosDepartamentos(filtro);
        //console.log('🚀 ~ cadUsuario.vue:566 ~ carregaUsuarioDepartamentos ~ respAcesso:', respAcesso);

        if (respAcesso.statuscode === 500) {
            toast.showErrorNotification(respAcesso.message);
        } else if (respAcesso.statuscode === 200) {
            let departamento = [];
            respAcesso.data.forEach((dep) => {
                departamento.push(dep.cd_departamento);
            });

            formData.cd_departamento = departamento;
        }
    }

    async function carregaUsuarioInstancias() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        const filtro = {
            cd_usuario: formData.cd_usuario,
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        const respInst = await UsuarioInstanciasServices.listar(filtro);

        if (respInst.statuscode === 500) {
            toast.showErrorNotification(respInst.message);
        } else if (respInst.statuscode === 200) {
            let inst = [];
            respInst.data.forEach((instancia) => {
                inst.push(instancia.id_instancia);
            });

            instanciaLiberada.value = inst;
        }
    }

    async function carregaUsuario(cd_usuario) {
        //cdUsuario.value,dsLogin.value,dsUsuario.value

        let filtros = {
            cd_usuario: cd_usuario,
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        // logger.debug('carregaUsuario > filtros:', filtros);
        let respUsuarios = await UsuariosServices.listaUsuarios(filtros);
        //logger.debug('carregaUsuario > respUsuarios', respUsuarios);

        if (respUsuarios.statuscode === 404) {
            toast.showErrorNotification(respUsuarios.message);
        } else if (respUsuarios.statuscode === 200) {
            //usuarios.value = data;
            formData.cd_usuario = respUsuarios.data[0].cd_usuario;
            formData.ds_nome = respUsuarios.data[0].ds_nome;
            formData.ds_login = respUsuarios.data[0].ds_login;
            formData.ds_email = respUsuarios.data[0].ds_email;
            //formData.ds_privilegio = respUsuarios.data[0].ds_privilegio;
            formData.tp_privilegio = respUsuarios.data[0].tp_privilegio;

            formData.tp_status = respUsuarios.data[0].tp_status;
            formData.ds_senha = respUsuarios.data[0].ds_senha;
            formData.cd_usucad = respUsuarios.data[0].cd_usucad;
            formData.in_aplicadesconto = respUsuarios.data[0].in_aplicadesconto;

            //console.log('formData at line 401:', formData);
            /*if (respUsuarios.data[0].ds_login == 'ROBINHO') {
            bloqueiaCampos.value = true;
            //console.log(bloqueiaCampos.value);
          }*/
            acessoUsuario.value = false;
            if (localStorage.getItem('privilegio') == 'Usuário') {
                acessoUsuario.value = true;
            }

            await carregaInstancias();

            //await carregaUsuarioAcessos();
            await carregaUsuarioInstancias();
            await carregaUsuarioDepartamentos();
        } else if (respUsuarios.statuscode === 401) {
            localStorage.removeItem('token');
            router.push({ path: `${hosts.app}` });
            toast.showErrorNotification(respUsuarios.message);
        } else {
            toast.showErrorNotification(respUsuarios.message);
        }
    }

    async function carregarDepartamentos(useLoad = true) {
        try {
            if (useLoad) loading.value.show();
            listaDepartamentos.value = [];
            formData.cd_departamento = undefined;
            const response = await DepartamentosServices.listar();
            listaDepartamentos.value = response.data.map((departamento) => {
                return {
                    value: departamento.cd_departamento,
                    label: departamento.ds_departamento,
                };
            });
        } catch (error) {
            console.error('Erro ao carregar departamentos:', error);
        } finally {
            if (useLoad) loading.value.hide(500);
        }
    }

    async function carregaInstancias() {
        const filtros = {
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
            cd_usuario: parseInt(formData.cd_usuario),
            in_verificastatus: false,
        };

        listaInstancias.value = [];

        const result = await InstanciaServices.listar(filtros);

        if (result.statuscode == 200) {
            listaInstancias.value = result.data.map((instancia) => {
                return {
                    value: instancia.id_instancia,
                    label: instancia.nome,
                };
            });

            // console.log('listaEstabelecimentoModulos.value:', listaEstabelecimentoModulos.value);
            //toast.showSuccessNotification('Dados salvo com sucesso!');
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    onMounted(async () => {
        //estabelecimentosLiberado.value = JSON.parse(await localStorage.getItem('estabelecimentos'));
        const estatabelecimentos = localStorage.getItem('estabelecimentos');
        if (estatabelecimentos) estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);

        if (localStorage.getItem('privilegio') == 'OiZap') {
            listaPrivilegio.value.push({ value: 'O', label: 'OiZap' });
        }

        //await carregaGrupoAcessos();
        bloqueiaCampos.value = false;
        formData.in_aplicadesconto = false;
        codUsuario.value = route.params.codusuario;
        await limpaDados();
        await carregarDepartamentos();
        if (!!codUsuario.value) {
            novoUsuario.value = false;
            await carregaUsuario(codUsuario.value);
        } else {
            codUsuario.value = '';
            novoUsuario.value = true;
            formData.tp_privilegio = 'U';
            formData.tp_status = 'A';
        }
    });
</script>
