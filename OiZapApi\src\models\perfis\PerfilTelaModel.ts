import db from '../../config/knex';

export class PerfilTelaModel {
  async listarTelasPorPerfil(cd_perfil: number) {
    return db('perfis_telas').where({ cd_perfil });
  }
  async associarTelaPerfil(data: any) {
    return db('perfis_telas').insert(data).returning('*');
  }
  async desassociarTelaPerfil(cd_perfil: number, cd_tela: number) {
    return db('perfis_telas').where({ cd_perfil, cd_tela }).del();
  }
} 