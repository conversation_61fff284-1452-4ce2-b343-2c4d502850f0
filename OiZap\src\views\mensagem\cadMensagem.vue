<template>
    <ShowLoading ref="loading" />
    <PreviewComponent class="intro-y box" hidden style="z-index: 100">
        <div id="blank-slide-over">
            <Preview>
                <!-- BEGIN: Slide Over Content  -->
                <Modal
                    :slideOver="slideOver"
                    backdrop="static"
                    :show="abreModalMensagem"
                    size="modal-xl"
                    @hidden="fechaModalMensagem"
                >
                    <!-- style="background-color: #f9f9f9; border-radius: 7px" -->
                    <ModalHeader>
                        <h2 class="font-medium text-base mr-auto">
                            {{ titulo }}
                            <label class="font-medium text-orange-600 ml-5" v-show="inFluxoPadrao">
                                Mensagem PADRÃO</label
                            >
                        </h2>
                        <XIcon class="w-6 h-6 mr-0" style="cursor: pointer" @click="fechaModalMensagem" />
                    </ModalHeader>
                    <ModalBody>
                        <!-- BEGIN: Dados Cliente -->
                        <div class="flex sm:flex-row items-center border-b border-slate-200/60 bg-gray">
                            <div class="text-left w-full">
                                <div
                                    class="grid grid-cols-12 gap-2 mb-5 cursor-pointer"
                                    v-show="formDataMensagem.ds_titulopai != undefined"
                                >
                                    <div class="relative w-full">
                                        <div class="text-slate-500 text-xs cursor-pointer whitespace-nowrap">
                                            Mensagem vinculada
                                        </div>
                                        <div class="flex items-center mt-2">
                                            <span class="flex items-center">
                                                <MessageSquareIcon class="w-4 h-4" />
                                                <div class="text-slate-500 font-medium whitespace-nowrap ml-2">
                                                    {{ formDataMensagem.ds_titulopai }}
                                                </div>
                                            </span>
                                            <span
                                                class="flex items-center ml-3 text-green-500 cursor-pointer"
                                                @click="carregaDados()"
                                            >
                                                <EyeIconIcon class="w-4 h-4 mr-1" />
                                                Visualizar
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <TabGroup class="flex-1 flex flex-col">
                                    <TabList
                                        class="nav-pills w-full border border-slate-300 dark:border-darkmode-300 border-dashed rounded-md p-1 box"
                                    >
                                        <Tab class="w-full py-1.5 px-2" tag="button">Mensagem</Tab>
                                        <Tab class="w-full py-1.5 px-2" tag="button">Imagens</Tab>
                                        <Tab class="w-full py-1.5 px-2" tag="button">Fluxo</Tab>
                                    </TabList>
                                    <TabPanels class="mt-2 flex-1 overflow-auto h-screen">
                                        <!--BEGIN: Dados da Mensagem-->
                                        <TabPanel class="h-screen">
                                            <div class="grid grid-cols-12 gap-2 box p-2">
                                                <div
                                                    class="form-control col-span-6"
                                                    :class="{
                                                        'col-span-9': tpPrivilegio != 'OiZap',
                                                        'col-span-6': tpPrivilegio == 'OiZap',
                                                    }"
                                                >
                                                    <label for="validation-form-1" class="form-label">Titulo</label>

                                                    <input
                                                        type="text"
                                                        class="form-control block"
                                                        placeholder="Informe o titulo da mensagem"
                                                        v-model="formDataMensagem.ds_titulo"
                                                        ref="inputTitulo"
                                                    />
                                                </div>
                                                <div class="col-span-3" v-show="tpPrivilegio == 'OiZap'">
                                                    <label for="validation-form-1" class="form-label"
                                                        >Tipo de Mensagem</label
                                                    >

                                                    <div class="form-control col-span-2">
                                                        <Multiselect
                                                            v-model="formDataMensagem.tp_mensagem"
                                                            mode="single"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :createOption="false"
                                                            :options="listaTpMensagens"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="col-span-3">
                                                    <label for="validation-form-1" class="form-label">Situação</label>

                                                    <div class="form-check mr-2 mt-2">
                                                        <div class="form-check form-switch -mt-1.5">
                                                            <input
                                                                id="inAtiva"
                                                                name="inAtiva"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                :value="formDataMensagem.in_ativa"
                                                                :checked="formDataMensagem.in_ativa"
                                                                @change="onChangeAtiva($event)"
                                                            />
                                                        </div>
                                                        <label class="forceLeft form-label self-center ml-2">{{
                                                            formDataMensagem.in_ativa ? 'Ativada' : 'Desativada'
                                                        }}</label>
                                                    </div>
                                                </div>

                                                <div class="form-control col-span-9 gap-1">
                                                    <label for="validation-form-1" class="form-label">Descritivo</label>

                                                    <input
                                                        type="text"
                                                        class="form-control block"
                                                        placeholder="Descritivo da mensagem, um breve comentário..."
                                                        v-model="formDataMensagem.ds_descritivo"
                                                    />
                                                </div>
                                                <div class="col-span-3">
                                                    <label for="validation-form-1" class="form-label">Tempo</label>

                                                    <div class="form-control col-span-2">
                                                        <Multiselect
                                                            v-model="formDataMensagem.hr_tempo"
                                                            mode="single"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :createOption="false"
                                                            :options="tempoMensagem"
                                                        />
                                                    </div>
                                                </div>
                                                <!--justify-between-->
                                                <div class="form-control col-span-8 gap-1">
                                                    <div class="form-check mr-2 mt-2 flex w-full items-center">
                                                        <label for="validation-form-1" class="form-label self-center"
                                                            >Mensagem</label
                                                        >
                                                        <button
                                                            @click="toggleEmojiPicker"
                                                            ref="emojiButton"
                                                            class="btn btn-secondary-soft w-25 h-6 -mt-1.5 ml-2 text-xs"
                                                        >
                                                            😊 Emoji
                                                        </button>
                                                        <!-- Adicione 'ml-auto' para empurrar o próximo botão para a direita -->
                                                        <button
                                                            @click="abreModalEdicaoMsg"
                                                            class="w-10 h-10 -mt-1.5 ml-auto"
                                                        >
                                                            <Maximize2Icon class="w-4 h-4" />
                                                        </button>
                                                    </div>

                                                    <textarea
                                                        class="chat__box__input form-control h-40 resize-none focus:ring-0"
                                                        v-model="formDataMensagem.ds_mensagem"
                                                        placeholder="Informe a mensagem que enviaremos para nossos clientes"
                                                        ref="refMensagem"
                                                        v-on:dragover="handleDragOver"
                                                        v-on:drop="handleDrop"
                                                        @dragenter="handleDragEnter"
                                                        @input="updateCursorPosition"
                                                        @click="updateCursorPosition"
                                                    ></textarea>

                                                    <div
                                                        v-if="showEmojiPicker"
                                                        :style="emojiPickerStyles"
                                                        class="emoji-picker-popup"
                                                    >
                                                        <EmojiPicker @select="onSelectEmoji" />
                                                    </div>
                                                    <div
                                                        class="drop-target"
                                                        v-if="showDropTarget"
                                                        :style="{
                                                            top: dropTargetPosition.top + 'px',
                                                            left: dropTargetPosition.left + 'px',
                                                        }"
                                                    ></div>
                                                </div>
                                                <div class="col-span-4">
                                                    <label for="selStatus" class="self-center">Variáveis</label>

                                                    <TabGroup class="flex-1 flex flex-col">
                                                        <TabList
                                                            class="nav-pills w-full border border-slate-300 dark:border-darkmode-300 border-dashed rounded-md p-1 box"
                                                            style="font-size: 11px !important"
                                                        >
                                                            <Tab
                                                                class="w-full py-1.5 px-2"
                                                                tag="button"
                                                                @click="listaVariaveis('Empresa')"
                                                                >Empresa</Tab
                                                            >
                                                            <Tab
                                                                class="w-full py-1.5 px-2"
                                                                tag="button"
                                                                @click="listaVariaveis('Cliente')"
                                                                >Cliente</Tab
                                                            >
                                                            <Tab
                                                                class="w-full py-1.5 px-2"
                                                                tag="button"
                                                                @click="listaVariaveis('Atendimento')"
                                                                >Atendimento</Tab
                                                            >
                                                            <Tab
                                                                class="w-full py-1.5 px-2"
                                                                tag="button"
                                                                @click="listaVariaveis('Pedido')"
                                                                >Pedido</Tab
                                                            >
                                                        </TabList>
                                                        <TabPanels class="mt-3 flex-1 overflow-auto">
                                                            <TabPanel class="h-full">
                                                                <div
                                                                    class="inbox box mt-1 overflow-y-auto"
                                                                    style="overflow-y: auto; max-height: 174px"
                                                                >
                                                                    <div v-for="variavel in variaveis" :key="variavel">
                                                                        <div
                                                                            class="inbox__item inline-block sm:block text-slate-600 dark:text-white border-b border-slate-200/60 p-1"
                                                                            :draggable="true"
                                                                            @dragstart="
                                                                                (event) =>
                                                                                    handleDragStart(event, variavel)
                                                                            "
                                                                        >
                                                                            {{ variavel }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </TabPanel>
                                                        </TabPanels>
                                                    </TabGroup>
                                                </div>
                                                <div
                                                    class="form-control mb-2"
                                                    :class="{
                                                        'col-span-12': tpPrivilegio != 'OiZap',
                                                        'col-span-9': tpPrivilegio == 'OiZap',
                                                    }"
                                                    v-show="
                                                        formDataMensagem.tp_mensagem == 'Personalizada' ||
                                                        tpPrivilegio == 'OiZap'
                                                    "
                                                >
                                                    <label for="validation-form-1" class="form-label"
                                                        >Quando devo encaminhar a mensagem ?</label
                                                    >

                                                    <div class="form-control col-span-2">
                                                        <Multiselect
                                                            v-model="formDataMensagem.tp_funcionalidade"
                                                            placeholder="Opcional"
                                                            mode="single"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :createOption="false"
                                                            :options="listaFuncionalidades"
                                                            @emitEvent="selecionaFuncionalidade"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="mt-7 col-span-3">
                                                    <button
                                                        type="button"
                                                        class="btn btn-success-soft w-20 h-10"
                                                        :disabled="formDataMensagem.nr_controle ? false : true"
                                                        @click="abreModalToken()"
                                                    >
                                                        Tokens
                                                    </button>
                                                </div>
                                                <div class="col-span-6">
                                                    <div class="form-check mr-2 mt-2 sm:mt-0">
                                                        <div class="form-check form-switch -mt-1.5">
                                                            <input
                                                                id="inAtalhoChat"
                                                                name="inAtalhoChat"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                :value="formDataMensagem.in_adicionaatalho"
                                                                :checked="formDataMensagem.in_adicionaatalho"
                                                                @change="onChangeAtalhoPedido($event)"
                                                            />
                                                        </div>
                                                        <label class="ml-2 forceLeft form-label self-center"
                                                            >Listar Atalho no Chat</label
                                                        >
                                                        <div
                                                            class="form-control w-40 ml-5 -mt-2"
                                                            v-show="formDataMensagem.in_adicionaatalho"
                                                        >
                                                            <Multiselect
                                                                v-model="formDataMensagem.ds_teclaatalho"
                                                                mode="single"
                                                                :close-on-select="false"
                                                                :searchable="true"
                                                                :createOption="false"
                                                                :options="teclaAtalho"
                                                                @emitEvent="selecionaAtalho"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-span-3">
                                                    <div class="form-check mr-2 mt-2 sm:mt-0">
                                                        <div class="form-check form-switch -mt-1.5">
                                                            <input
                                                                id="inFinalizaAtendimento"
                                                                name="inFinalizaAtendimento"
                                                                class="form-check-input"
                                                                type="checkbox"
                                                                :value="formDataMensagem.in_finalizaatendimento"
                                                                :checked="formDataMensagem.in_finalizaatendimento"
                                                                @change="onChangeFinalizaAtendimento($event)"
                                                            />
                                                        </div>
                                                        <label class="ml-2 forceLeft form-label self-center"
                                                            >Finalizar Atendimento</label
                                                        >
                                                    </div>
                                                </div>

                                                <div class="col-span-6">
                                                    <div class="form-check mr-2 mt-2 sm:mt-0">
                                                        <label class="ml-2 forceLeft form-label self-center"
                                                            >Departamentos CRM</label
                                                        >
                                                        <div class="form-control w-40 ml-5 -mt-2">
                                                            <Multiselect
                                                                v-model="formDataMensagem.cd_departamento"
                                                                mode="single"
                                                                :close-on-select="false"
                                                                :searchable="true"
                                                                :createOption="false"
                                                                :options="departamentos"
                                                                @emitEvent="selecionaDepartamento"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="grid grid-cols-5 mt-2 mb-2 box p-2">
                                                <div class="col-span-1">
                                                    <div class="sm:ml-0 mt-3 sm:mt-0 relative w-full">
                                                        <button
                                                            type="button"
                                                            class="btn w-40 h-10"
                                                            :class="`${
                                                                darkMode ? 'btn-success-soft ' : 'btn-dark-soft'
                                                            }`"
                                                            @click="novaMensagem()"
                                                            v-show="
                                                                formDataMensagem.tp_mensagem == 'Personalizada' ||
                                                                tpPrivilegio == 'OiZap'
                                                            "
                                                        >
                                                            Nova Mensagem
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="col-span-1 flex flex-row-reverse">
                                                    <button
                                                        type="button"
                                                        class="btn btn-warning-soft w-20 h-10"
                                                        @click="abreModalRemover()"
                                                        v-show="
                                                            formDataMensagem.tp_mensagem == 'Personalizada' ||
                                                            tpPrivilegio == 'OiZap'
                                                        "
                                                        :disabled="formDataMensagem.nr_controle ? false : true"
                                                    >
                                                        Remover
                                                    </button>
                                                </div>
                                                <div class="col-span-3 flex flex-row-reverse">
                                                    <button
                                                        type="button"
                                                        class="btn w-20 h-10"
                                                        :class="`${
                                                            darkMode ? 'btn-success-soft ' : 'btn-primary-soft'
                                                        }`"
                                                        @click="salvar()"
                                                    >
                                                        Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </TabPanel>
                                        <!--END: Dados da Mensagem-->
                                        <!--BEGIN: Imagens-->
                                        <TabPanel class="h-full box p-2">
                                            <div class="grid grid-cols-12 gap-2">
                                                <!--BEGIN: Imagens -->
                                                <div class="col-span-12 gap-1 cursor-pointer">
                                                    <div class="flex items-center border-b border-slate-300">
                                                        <div class="text-md truncate text-slate-500">Imagens</div>
                                                    </div>
                                                    <div class="mt-3">
                                                        <div class="col-span-5">
                                                            <div class="form-check mr-2 mt-2 sm:mt-0">
                                                                <div class="form-check form-switch -mt-1.5">
                                                                    <input
                                                                        id="inTextoImagem"
                                                                        name="inTextoImagem"
                                                                        class="form-check-input"
                                                                        type="checkbox"
                                                                        :value="
                                                                            formDataMensagem.in_adicionatexto_imagem
                                                                        "
                                                                        :checked="
                                                                            formDataMensagem.in_adicionatexto_imagem
                                                                        "
                                                                        @change="onChangeTextoImagem($event)"
                                                                    />
                                                                </div>
                                                                <label class="ml-2 forceLeft form-label self-center"
                                                                    >Adicionar mensagem no comentário da imagem</label
                                                                >
                                                            </div>
                                                        </div>
                                                        <div
                                                            class="border-2 border-dashed dark:border-darkmode-400 rounded-md pt-4"
                                                        >
                                                            <div
                                                                class="px-4 pb-4 flex items-center cursor-pointer relative"
                                                            >
                                                                <ImageIcon class="w-4 h-4 mr-2 cursor-pointer" />
                                                                <span class="text-primary mr-1 cursor-pointer"
                                                                    >Selecione a Imagem</span
                                                                >
                                                                ou Arraste aqui
                                                                <input
                                                                    type="file"
                                                                    class="w-full h-full top-0 left-0 absolute opacity-0 cursor-pointer"
                                                                    ref="fileInput"
                                                                    multiple
                                                                    @change="handleDropImage"
                                                                    @drop="handleDropImage"
                                                                />
                                                            </div>
                                                            <div class="flex flex-wrap px-4">
                                                                <div
                                                                    v-for="imagem in listaImagens"
                                                                    :key="imagem.nr_controle"
                                                                    class="w-24 h-24 relative image-fit mb-5 mr-5 cursor-pointer zoom-in"
                                                                >
                                                                    <span
                                                                        class="block font-medium text-left cursor-pointer zoom-in"
                                                                        v-show="imagem.ds_extensao == 'pdf'"
                                                                    >
                                                                        <a :href="imagem.url_imagem" target="_blank">
                                                                            <FileDownIcon
                                                                                class="w-5 h-5 inline-block mr-1"
                                                                            />
                                                                            PDF
                                                                        </a></span
                                                                    >

                                                                    <img
                                                                        class="rounded-md"
                                                                        :src="imagem.url_imagem"
                                                                        v-show="imagem.ds_extensao != 'pdf'"
                                                                    />
                                                                    <Tippy
                                                                        tag="div"
                                                                        content="Remover?"
                                                                        class="w-5 h-5 flex items-center justify-center absolute rounded-full text-white bg-danger right-0 top-0 -mr-2 -mt-2"
                                                                    >
                                                                        <xIcon
                                                                            class="w-4 h-4"
                                                                            @click="excluiImagem(imagem)"
                                                                        />
                                                                    </Tippy>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--END: Imagens -->
                                        </TabPanel>
                                        <!--END: Imagens-->
                                        <!--BEGIN: Mensagens relacionadas-->
                                        <TabPanel class="h-full box p-2">
                                            <!-- BEGIN: Lista Mensagens -->
                                            <div class="grid grid-cols-12 gap-2" style="height: calc(98vh - 200px)">
                                                <div class="col-span-12 intro-y inbox mt-1">
                                                    <div class="flex items-center border-b border-slate-300">
                                                        <div class="text-md truncate text-slate-500">
                                                            Fluxo de Mensagens
                                                        </div>
                                                    </div>
                                                    <div class="col-span-12 form-control mt-3 mb-3">
                                                        <div class="grid grid-cols-12">
                                                            <div class="col-span-5">
                                                                <div class="form-check mr-2 mt-2 sm:mt-0">
                                                                    <div class="form-check form-switch -mt-1.5">
                                                                        <input
                                                                            id="inLista"
                                                                            name="inLista"
                                                                            class="form-check-input"
                                                                            type="checkbox"
                                                                            :value="formDataMensagem.in_tipolista"
                                                                            :checked="formDataMensagem.in_tipolista"
                                                                            @change="onChangeTipoLista($event)"
                                                                        />
                                                                    </div>
                                                                    <label class="ml-2 forceLeft form-label self-center"
                                                                        >Fluxo de Mensagens como lista</label
                                                                    >
                                                                </div>
                                                            </div>
                                                            <div
                                                                class="form-control col-span-7"
                                                                v-show="formDataMensagem.in_tipolista"
                                                            >
                                                                <div class="form-check">
                                                                    <label for="validation-form-1" class="form-label"
                                                                        >Botão</label
                                                                    >

                                                                    <input
                                                                        type="text"
                                                                        class="form-control block -mt-1 ml-2"
                                                                        placeholder="Informe o titulo do botão que irá mostrar na lista"
                                                                        v-model="formDataMensagem.ds_titulobotao"
                                                                        ref="inputTituloBotao"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-control col-span-2 mt-2">
                                                            <Multiselect
                                                                v-model="filtroMensagemRelacionada"
                                                                placeholder="Selecione a próxima mensagem automática"
                                                                mode="single"
                                                                :close-on-select="false"
                                                                :searchable="true"
                                                                :createOption="false"
                                                                :options="listaMensagensCadastradas"
                                                                @emitEvent="selecionaMensagem"
                                                            />
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="overflow-x-auto sm:overflow-x-visible mt-2 border-b border-slate-300"
                                                    >
                                                        <div
                                                            class="intro-y"
                                                            v-for="(mensagem, index) in listaMensagensRelacionada"
                                                            :key="mensagem.nr_controle"
                                                            draggable="true"
                                                            @dragstart="onDragStart(index)"
                                                            @dragover.prevent="onDragOver(index)"
                                                            @drop="onDrop(index)"
                                                        >
                                                            <div
                                                                class="inbox__item inline-block sm:block text-slate-600 border-b border-slate-200/60"
                                                            >
                                                                <div class="grid grid-cols-12 py-1">
                                                                    <div class="col-span-3">
                                                                        <div class="flex items-center">
                                                                            <div class="truncate w-5/6">
                                                                                {{ mensagem.ds_titulo }}
                                                                                <div
                                                                                    v-show="mensagem.in_ativa == false"
                                                                                    class="text-slate-500 text-xs whitespace-nowrap mt-0.5"
                                                                                >
                                                                                    Mensagem desativada
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-span-2 mt-2">
                                                                        <span
                                                                            class="flex items-center mr-3"
                                                                            @click="abreModalTokenAtalho(mensagem)"
                                                                        >
                                                                            <KeyIcon class="w-4 h-4 mr-1" />
                                                                            Tokens
                                                                        </span>
                                                                    </div>
                                                                    <div class="col-span-3">
                                                                        <div
                                                                            class="flex justify-center items-center cursor-pointer"
                                                                        >
                                                                            <span
                                                                                class="flex items-center mr-3"
                                                                                href=""
                                                                            >
                                                                                <div class="form-check form-switch">
                                                                                    <input
                                                                                        :disabled="
                                                                                            !mensagem.in_opcoes_habilitado
                                                                                        "
                                                                                        class="form-check-input"
                                                                                        type="checkbox"
                                                                                        :value="mensagem.in_opcoesrel"
                                                                                        :checked="mensagem.in_opcoesrel"
                                                                                        @change="
                                                                                            onChangeOpcoes(
                                                                                                mensagem,
                                                                                                $event
                                                                                            )
                                                                                        "
                                                                                    />
                                                                                </div>

                                                                                <label
                                                                                    class="forceLeft form-label self-center ml-2 mt-1.5"
                                                                                    >Lista de Opções</label
                                                                                >
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-span-2">
                                                                        <div
                                                                            class="flex justify-center items-center cursor-pointer text-green-500"
                                                                            @click="carregaDados(mensagem)"
                                                                        >
                                                                            <span
                                                                                class="flex items-center mr-3"
                                                                                href=""
                                                                            >
                                                                                <EyeIconIcon class="w-4 h-4 mr-1" />
                                                                                Visualizar
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-span-2 ml-3">
                                                                        <span
                                                                            class="flex items-center mr-3 text-orange-500"
                                                                            @click="desvincular(mensagem)"
                                                                        >
                                                                            <PinOffIcon class="w-4 h-4 mr-1" />
                                                                            Desvincular
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- END: Lista Mensagens -->
                                        </TabPanel>
                                        <!--END: Mensagens relacionadas-->
                                    </TabPanels>
                                </TabGroup>
                            </div>
                        </div>
                        <!-- END: Dados Cliente -->
                    </ModalBody>
                    <!-- <ModalFooter class="w-full absolute bottom-0">
          </ModalFooter>-->
                </Modal>
                <!-- END: Slide Over Content -->
            </Preview>
        </div>
    </PreviewComponent>

    <!-- Modal  confirma remover-->
    <Modal :show="modalRemoveMensagem" @hidden="fechaModalRemover">
        <ModalBody class="p-0">
            <div class="p-5 text-center">
                <XCircleIcon class="w-16 h-16 text-danger mx-auto mt-3" />
                <div class="text-3xl mt-5">Confirmação</div>
                <div class="text-slate-500 mt-2">
                    Deseja remover essa mensagem
                    <strong class="text-danger">{{ formDataMensagem.ds_titulo }} </strong> ?
                </div>
            </div>
            <div class="px-5 pb-8 text-center">
                <button type="button" @click="fechaModalRemover" class="btn btn-outline-secondary w-24 mr-20">
                    Não
                </button>
                <button type="button" class="btn btn-danger w-24" @click="remover()">Sim</button>
            </div>
        </ModalBody>
    </Modal>
    <!-- END: confirma remover -->

    <cadTokens ref="cadTokensRef" />

    <!-- Modal  Edição de msg-->
    <Modal
        :show="modalEdicaoMsg"
        :hidden="fechaModalEdicaoMsg"
        size="modal-xl"
        class="h-screen flex flex-col justify-center"
    >
        <!--  style="background-color: #f9f9f9; border-radius: 7px"background-color: #f9f9f9;  -->
        <ModalHeader style="border-radius: 7px">
            <h2 class="font-medium text-base mr-auto">Mensagem</h2>
            <XIcon class="w-6 h-6 mr-0" style="cursor: pointer" @click="fechaModalEdicaoMsg" />
        </ModalHeader>
        <ModalBody class="flex-1 overflow-y-auto" style="border-radius: 7px; margin-top: -25px">
            <div class="intro-y box p-5 grid grid-cols-12 h-screen">
                <div class="form-control col-span-9 gap-1 h-screen">
                    <div class="form-check mr-2 mt-2 flex items-center">
                        <label for="validation-form-1" class="forceLeft form-label self-center">Mensagem</label>
                        <button @click="toggleEmojiPicker" ref="emojiButton" class="w-10 h-10 -mt-1.5">😊</button>
                    </div>
                    <textarea
                        class="chat__box__input form-control h-screen resize-none focus:ring-0"
                        v-model="formDataMensagem.ds_mensagem"
                        placeholder="Informe a mensagem que enviaremos para nossos clientes"
                        ref="refMensagem"
                        v-on:dragover="handleDragOver"
                        v-on:drop="handleDrop"
                        @dragenter="handleDragEnter"
                        @input="updateCursorPosition"
                        @click="updateCursorPosition"
                    ></textarea>

                    <div v-if="showEmojiPicker" :style="emojiPickerStyles" class="emoji-picker-popup">
                        <EmojiPicker @select="onSelectEmoji" />
                    </div>
                    <div
                        class="drop-target"
                        v-if="showDropTarget"
                        :style="{ top: dropTargetPosition.top + 'px', left: dropTargetPosition.left + 'px' }"
                    ></div>
                </div>
                <div class="col-span-3 h-screen">
                    <label for="selStatus" class="form-label self-center">Variaveis</label>

                    <TabGroup class="flex-1 flex flex-col">
                        <TabList
                            class="nav-pills w-full border border-slate-300 dark:border-darkmode-300 border-dashed rounded-md p-1 box"
                        >
                            <Tab class="w-full py-1.5 px-2" tag="button" @click="listaVariaveis('Empresa')"
                                >Empresa</Tab
                            >
                            <Tab class="w-full py-1.5 px-2" tag="button" @click="listaVariaveis('Cliente')"
                                >Cliente</Tab
                            >
                            <Tab class="w-full py-1.5 px-2" tag="button" @click="listaVariaveis('Atendimento')"
                                >Atendimento</Tab
                            >
                            <Tab class="w-full py-1.5 px-2" tag="button" @click="listaVariaveis('Pedido')">Pedido</Tab>
                        </TabList>
                        <TabPanels class="mt-3 flex-1 overflow-auto">
                            <TabPanel class="h-full">
                                <div class="inbox box mt-1 overflow-y-auto" style="overflow-y: auto; max-height: 174px">
                                    <div v-for="variavel in variaveis" :key="variavel" class="intro-y">
                                        <div
                                            class="inbox__item inline-block sm:block text-slate-600 border-b dark:text-white border-slate-200/60 p-1"
                                            :draggable="true"
                                            @dragstart="(event) => handleDragStart(event, variavel)"
                                        >
                                            {{ variavel }}
                                        </div>
                                    </div>
                                </div>
                            </TabPanel>
                        </TabPanels>
                    </TabGroup>
                </div>
            </div>
        </ModalBody>
    </Modal>

    <!-- END: Edição de msg -->
</template>

<script setup>
    import { computed, onMounted, reactive, ref, toRefs, defineEmits, nextTick, onBeforeUnmount } from 'vue';
    import { usePagina } from '@/stores/pagina';
    import 'vue3-emoji-picker/css';
    import EmojiPicker from 'vue3-emoji-picker';
    import FluxoAtendimentoServices from '@/services/fluxos/FluxoAtendimentoServices';
    import FluxoImagemServices from '@/services/fluxos/FluxoImagemServices';
    import UploadServices from '@/services/configuracao/UploadServices';
    import MensagemRelacionadaServices from '@/services/fluxos/MensagemRelacionadaServices';
    import EstabelecimentoModulosServices from '@/services/administracao/EstabelecimentoModulosServices';
    import { useRouter, useRoute } from 'vue-router';
    import hosts from '@/utils/hosts';
    import cadTokens from '@/views/mensagem/cadTokens.vue';
    import { KeyIcon } from 'lucide-vue-next';
    import { useDarkModeStore } from '@/stores/dark-mode';
    import { useToast } from '@/global-components/toastify/useToast';
    import DepartamentosServices from '../../services/administracao/DepartamentosServices';
    const toast = useToast();
    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);

    let pagina = usePagina();

    const loading = ref();
    const router = useRouter();
    const formDataMensagem = reactive({
        nr_controle: undefined,
        nr_fluxopai: undefined,
        tp_mensagem: undefined,
        ds_titulo: undefined,
        hr_tempo: '00:00:00',
        ds_titulopai: undefined,
        ds_descritivo: undefined,
        ds_titulobotao: undefined,
        ds_mensagem: undefined,
        tp_funcionalidade: undefined,
        ds_evento: undefined,
        nr_contfluxo: undefined,
        cd_estabelecimento: undefined,
        in_resposta: false,
        in_ativa: true,
        in_adicionatexto_imagem: false,
        in_adicionaatalho: false,
        in_finalizaatendimento: false,
        in_tipolista: false,
        ds_teclaatalho: undefined,
        cd_departamento: undefined,
    });
    const formDataMensagemPai = reactive({
        nr_controle: undefined,
        nr_fluxopai: undefined,
        tp_mensagem: undefined,
        ds_titulo: undefined,
        hr_tempo: '00:00:00',
        ds_titulopai: undefined,
        ds_descritivo: undefined,
        ds_titulobotao: undefined,
        ds_mensagem: undefined,
        tp_funcionalidade: undefined,
        ds_evento: undefined,
        nr_contfluxo: undefined,
        cd_estabelecimento: undefined,
        in_resposta: false,
        in_ativa: true,
        in_adicionatexto_imagem: false,
        in_adicionaatalho: false,
        in_finalizaatendimento: false,
        in_tipolista: false,
        ds_teclaatalho: undefined,
        cd_departamento: undefined,
    });
    const inputTitulo = ref();
    const cadTokensRef = ref();
    const departamentos = ref([]);
    const emit = defineEmits([
        'eventMensagemSalva',
        'eventMensagemVinculadaSalva',
        'eventDesvinculaMensagem',
        'eventMensagemRemovida',
    ]);

    let codUsuario = ref();
    let abreModalMensagem = ref(false);
    let titulo = ref();
    let listaImagens = ref([]);
    let slideOver = ref(false);
    let inFluxoPadrao = ref(false);
    let modalRemoveMensagem = ref(false);
    let modalEdicaoMsg = ref(false);
    let listaFuncionalidades = ref([
        { value: 'Inicio', label: 'Inicio' },
        { value: 'Cardápio', label: 'Cardápio' },
        { value: 'Cardápio Online', label: 'Cardápio Online' },
        { value: 'Cliente Existente', label: 'Cliente Existente' },
        { value: 'Consultar Pedido', label: 'Consultar Pedido' },
        { value: 'Comprovante de Pedido', label: 'Comprovante de Pedido' },
        { value: 'Endereço', label: 'Endereço' },
        { value: 'Fazer Pedido', label: 'Fazer Pedido' },
        { value: 'Falar com Atendente', label: 'Falar com Atendente' },
        { value: 'Verifica Pedido', label: 'Verifica Pedido' },
        { value: 'Novo Cliente', label: 'Novo Cliente' },
        { value: 'Opções', label: 'Opções' },
        { value: 'Pedido Realizado', label: 'Pedido Realizado' },
        { value: 'Pedido Entregue', label: 'Pedido Entregue' },
        { value: 'Pedido Confirmado', label: 'Pedido Confirmado' },
        { value: 'Pedido Pronto', label: 'Pedido Pronto' },
        { value: 'Saiu para Entrega', label: 'Saiu para Entrega' },
        { value: 'Pedido Cancelado', label: 'Pedido Cancelado' },
    ]);
    const listaTpMensagens = ref([
        { value: 'Fixa', label: 'Fixa' },
        { value: 'Personalizada', label: 'Personalizada' },
    ]);

    let variaveisEmpresa = ref(['Fantasia', 'EndereçoEmpresa', 'LinkCardapio', 'pularLinha']);
    let variaveisCliente = ref(['Contato', 'Telefone', 'Endereço', 'Numero', 'Complemento', 'Bairro', 'pularLinha']);
    let variaveisAtendimento = ref(['Hash', 'pularLinha']);
    let variaveisPedido = ref([
        'HashPedido',
        'VlUnitario',
        'VlUnitarioSubTotal',
        'VlTotalProduto',
        'VlProdutos',
        'VlEntrega',
        'VlSubTotal',
        'VlTotal',
        'VlTrocoPara',
        'VlTroco',
        'VlPago',
        'VlPagar',
        'VlDescontoPedido',
        'VlDescontoItem',
        'VlUnitarioTotalBruto',
        //'VlProdutoMaisAdicional',
        //'VlTotalProdutoSemDesconto',
        'VlDescontoTotal',
        'Produto',
        'Quantidade',
        'Adicionais',
        'Bordas',
        'FormaPagamento',
        'ObservacaoPedido',
        'ObservacaoItem',
        'ObservacaoSaborPizza',
        'pularLinha',
    ]);
    let variaveis = ref([]);
    let tempoMensagem = ref([
        { value: '00:00:00', label: '00:00:00' },
        { value: '00:00:05', label: '00:00:05' },
        { value: '00:00:10', label: '00:00:10' },
        { value: '00:00:15', label: '00:00:15' },
    ]);
    let teclaAtalho = ref([
        { value: 'CRTL+1', label: 'CRTL+1' },
        { value: 'CRTL+2', label: 'CRTL+2' },
        { value: 'CRTL+3', label: 'CRTL+3' },
        { value: 'CRTL+4', label: 'CRTL+4' },
        { value: 'CRTL+5', label: 'CRTL+5' },
        { value: 'CRTL+6', label: 'CRTL+6' },
        { value: 'CRTL+7', label: 'CRTL+7' },
        { value: 'CRTL+8', label: 'CRTL+8' },
        { value: 'CRTL+9', label: 'CRTL+9' },
    ]);
    let showDropTarget = ref(false);
    let dropTargetPosition = ref({ top: 0, left: 0 });
    let listaMensagensRelacionada = ref([]);
    let tpPrivilegio = ref([]);
    let disabled = ref(false);
    let listaMensagensCadastradas = ref([]);
    let filtroMensagemRelacionada = ref();
    let draggedIndex = ref(null);
    let showEmojiPicker = ref(false);
    let emojiPickerStyles = ref({});
    const emojiButton = ref();
    let cursorPosition = ref(0);
    const refMensagem = ref();
    const modulos = reactive({ in_chat_pedidos: false, in_api: false, in_crm: false });
    const onChangeStatus = async (mensagem, event) => {
        mensagem.in_ativa = event.target.checked;
        const findMensagem = listaMensagensRelacionada.value.find((msg) => msg.nr_controle == mensagem.nr_controle);
        if (findMensagem) {
            findMensagem.in_ativa = mensagem.in_ativa;
        }
        const req = {
            nr_controle: mensagem.nr_controle,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : mensagem.cd_estabelecimento,
            in_ativa: mensagem.in_ativa,
        };
        await alterar(req);
    };

    async function alterar(msg) {
        // loading.value.show();
        let filtros = {
            nr_controle: msg.nr_controle,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : msg.cd_estabelecimento,
            in_ativa: msg.in_ativa,
        };

        let result = await FluxoAtendimentoServices.alterar(filtros);

        if (result.statuscode == 200) {
            if (msg.in_ativa) {
                toast.showSuccessNotification('Mensagem Ativada com sucesso!');
            } else {
                toast.showWarningNotification('Mensagem Desativada com sucesso!');
            }
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
        //loading.value.hide();
    }

    const onChangeResp = (event) => {
        formDataMensagem.in_resposta = event.target.checked;
    };

    const onChangeAtiva = (event) => {
        formDataMensagem.in_ativa = event.target.checked;
    };

    const onChangeTextoImagem = async (event) => {
        if (formDataMensagem.in_tipolista) {
            toast.showWarningNotification('Não é possivel adicionar imagem em mensagem do tipo lista!');

            formDataMensagem.in_adicionatexto_imagem = false;
            event.target.checked = formDataMensagem.in_adicionatexto_imagem;
            return;
        } else {
            formDataMensagem.in_adicionatexto_imagem = event.target.checked;

            await salvar();
        }
    };

    const onChangeAtalhoPedido = (event) => {
        formDataMensagem.in_adicionaatalho = event.target.checked;
        if (!formDataMensagem.in_adicionaatalho) {
            formDataMensagem.ds_teclaatalho = '';
        }
    };

    const onChangeFinalizaAtendimento = (event) => {
        formDataMensagem.in_finalizaatendimento = event.target.checked;
    };

    const onChangeTipoLista = async (event) => {
        if (listaImagens.value.length > 0 || formDataMensagem.in_adicionatexto_imagem) {
            toast.showWarningNotification(
                'Não é possivel criar mensagem do tipo lista com imagens! <br> Remove as imagens para salvar como lista. '
            );
            formDataMensagem.in_tipolista = false;
            event.target.checked = formDataMensagem.in_tipolista;
            return;
        } else {
            listaMensagensRelacionada.value.forEach((msg) => {
                msg.in_opcoes_habilitado = event.target.checked;
            });
            formDataMensagem.in_tipolista = event.target.checked;

            await salvar();
        }
    };

    function handleDragStart(event, variavel) {
        event.dataTransfer.setData('text/plain', variavel);
        showDropTarget.value = true;
    }

    function handleDragOver(event) {
        event.preventDefault();
    }

    function handleDrop(event) {
        event.preventDefault();
        const variavel = event.dataTransfer.getData('text/plain');
        const textarea = event.target;
        const startPos = textarea.selectionStart;
        const endPos = textarea.selectionEnd;

        const textoAntes = formDataMensagem.ds_mensagem.substring(0, startPos);
        const textoDepois = formDataMensagem.ds_mensagem.substring(endPos);

        formDataMensagem.ds_mensagem = `${textoAntes} {${variavel}} ${textoDepois}`;
        //  emit('mensagemEvent', dsMensagem.value);
    }

    async function handleDropImage(event) {
        if (formDataMensagem.nr_controle == undefined) {
            await salvar();
        }
        //console.log('handleDropImage at line 530 in mensagem/cadMensagem.vue:', handleDropImage);
        event.preventDefault();

        const formData = new FormData(); // Criar FormData fora do loop

        //formData.append('file', file);
        formData.append('tp_funcionalidade', formDataMensagem.tp_funcionalidade);
        formData.append('tp_mensagem', formDataMensagem.tp_mensagem);
        formData.append('ds_evento', formDataMensagem.ds_evento);
        if (!inFluxoPadrao.value) {
            formData.append('cd_estabelecimento', formDataMensagem.cd_estabelecimento);
        } else {
            formData.append('cd_estabelecimento', 0);
        }

        formData.append('nr_contfluxo', formDataMensagem.nr_contfluxo);
        formData.append('nr_contfluxoatendimento', formDataMensagem.nr_controle);
        formData.append('ds_pasta', 'cardapio');
        formData.append('is_category', true);

        if (event?.dataTransfer?.items) {
            for (let i = 0; i < event.dataTransfer.items.length; i++) {
                if (event.dataTransfer.items[i].kind === 'file') {
                    const file = event.dataTransfer.items[i].getAsFile();
                    formData.append('files', file);
                }
            }
        } else if (event.target.files) {
            for (const file of event.target.files) {
                formData.append('files', file);
            }
        }

        let result = await UploadServices.upload(formData);
        // console.log('result at line 1089 in mensagem/cadMensagem.vue:', result);

        if (result.statuscode == 200) {
            for (const arquivo of result.data) {
                for (const files of arquivo.files) {
                    let reqObj = {
                        cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
                        nr_contfluxo: formDataMensagem.nr_contfluxo,
                        nr_contfluxoatendimento: formDataMensagem.nr_controle,
                        tp_funcionalidade: formDataMensagem.tp_funcionalidade,
                        ds_host: files.ds_caminho,
                        nm_arquivo: files.file_name,
                        ds_evento: formDataMensagem.ds_evento,
                        ds_extensao: files.extension,
                        ds_categoria: files.category,
                    };

                    const respFluxo = await FluxoImagemServices.incluir(reqObj);

                    if (respFluxo.statuscode == 200) {
                        listaImagens.value.push({
                            nr_controle: respFluxo.data[0].nr_controle,
                            tp_funcionalidade: respFluxo.data[0].tp_funcionalidade,
                            nm_arquivo: files.file_name,
                            ds_host: files.ds_caminho,
                            ds_evento: formDataMensagem.ds_evento,
                            url_imagem: respFluxo.data[0].url_imagem,
                            ds_extensao: files.extension,
                            ds_categoria: files.category,
                        });
                    }
                }
            }
            toast.showSuccessNotification('Imagem salva com sucesso!');
        }
    }

    async function excluiImagem(imagem) {
        let reqObj = {
            nr_controle: imagem.nr_controle,
            nm_arquivo: imagem.nm_arquivo,
            ds_host: imagem.ds_host,
        };

        const result = await FluxoImagemServices.remover(reqObj);

        if (result.statuscode == 200) {
            toast.showSuccessNotification(result.message);

            const novaListaImagem = listaImagens.value.filter((imag) => imag.nr_controle !== imagem.nr_controle);
            listaImagens.value = novaListaImagem;
        } else {
            toast.showWarningNotification(result.message);
        }
    }

    async function carregaImagens() {
        const filtros = {
            nr_contfluxo: formDataMensagem.nr_contfluxo,
            nr_contfluxoatendimento: formDataMensagem.nr_controle,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };

        listaImagens.value = [];
        const result = await FluxoImagemServices.listar(filtros);

        if (result.statuscode == 200) {
            listaImagens.value = result.data.map((imagem) => {
                return {
                    nr_controle: imagem.nr_controle,
                    tp_funcionalidade: imagem.tp_funcionalidade,
                    nm_arquivo: imagem.nm_arquivo,
                    ds_evento: imagem.ds_evento,
                    ds_host: imagem.ds_host,
                    url_imagem: imagem.url_imagem,
                    ds_extensao: imagem.ds_extensao,
                    ds_categoria: imagem.ds_categoria,
                };
            });
        }
    }

    async function carregaDados(dados) {
        if (dados) {
            if (dados.nr_contrel != undefined && formDataMensagem.ds_titulopai == undefined) {
                formDataMensagemPai.nr_controle = formDataMensagem.nr_controle;
                formDataMensagemPai.tp_mensagem = formDataMensagem.tp_mensagem;
                formDataMensagemPai.ds_titulo = formDataMensagem.ds_titulo;
                formDataMensagemPai.ds_titulobotao = formDataMensagem.ds_titulobotao;
                formDataMensagemPai.ds_descritivo = formDataMensagem.ds_descritivo;
                formDataMensagemPai.ds_mensagem = formDataMensagem.ds_mensagem;
                formDataMensagemPai.ds_evento = formDataMensagem.ds_evento;
                formDataMensagemPai.tp_funcionalidade = formDataMensagem.tp_funcionalidade;
                formDataMensagemPai.in_ativa = formDataMensagem.in_ativa;
                formDataMensagemPai.in_resposta = formDataMensagem.in_resposta;
                formDataMensagemPai.nr_contfluxo = formDataMensagem.nr_contfluxo;
                formDataMensagemPai.cd_estabelecimento = formDataMensagem.cd_estabelecimento;
                formDataMensagemPai.in_adicionatexto_imagem = formDataMensagem.in_adicionatexto_imagem;
                formDataMensagemPai.in_adicionaatalho = formDataMensagem.in_adicionaatalho;
                formDataMensagemPai.in_finalizaatendimento = formDataMensagem.in_finalizaatendimento;
                formDataMensagemPai.in_tipolista = formDataMensagem.in_tipolista;
                formDataMensagemPai.ds_titulopai = formDataMensagem.ds_titulo;
                formDataMensagemPai.ds_teclaatalho = formDataMensagem.ds_teclaatalho;
                formDataMensagemPai.cd_departamento = formDataMensagem.cd_departamento;
            }
            formDataMensagem.nr_controle = dados.nr_controle;
            formDataMensagem.tp_mensagem = dados.tp_mensagem;
            formDataMensagem.ds_titulo = dados.ds_titulo;
            formDataMensagem.ds_titulobotao = dados.ds_titulobotao;
            formDataMensagem.ds_descritivo = dados.ds_descritivo;
            formDataMensagem.ds_mensagem = dados.ds_mensagem;
            formDataMensagem.ds_evento = dados.ds_evento;
            formDataMensagem.tp_funcionalidade = dados.tp_funcionalidade;
            formDataMensagem.in_ativa = dados.in_ativa;
            formDataMensagem.in_resposta = dados.in_resposta;
            formDataMensagem.nr_contfluxo = dados.nr_contfluxo;
            formDataMensagem.cd_estabelecimento = dados.cd_estabelecimento;
            formDataMensagem.in_adicionatexto_imagem = dados.in_adicionatexto_imagem;
            formDataMensagem.in_adicionaatalho = dados.in_adicionaatalho;
            formDataMensagem.in_finalizaatendimento = dados.in_finalizaatendimento;
            formDataMensagem.in_tipolista = dados.in_tipolista;
            formDataMensagem.ds_teclaatalho = dados.ds_teclaatalho;
            formDataMensagem.cd_departamento = dados.cd_departamento;
        } else if (dados == undefined) {
            formDataMensagem.ds_titulopai = undefined;
            formDataMensagem.nr_controle = formDataMensagemPai.nr_controle;
            formDataMensagem.tp_mensagem = formDataMensagemPai.tp_mensagem;
            formDataMensagem.ds_titulo = formDataMensagemPai.ds_titulo;
            formDataMensagem.ds_titulobotao = formDataMensagemPai.ds_titulobotao;
            formDataMensagem.ds_descritivo = formDataMensagemPai.ds_descritivo;
            formDataMensagem.ds_mensagem = formDataMensagemPai.ds_mensagem;
            formDataMensagem.ds_evento = formDataMensagemPai.ds_evento;
            formDataMensagem.tp_funcionalidade = formDataMensagemPai.tp_funcionalidade;
            formDataMensagem.in_ativa = formDataMensagemPai.in_ativa;
            formDataMensagem.in_resposta = formDataMensagemPai.in_resposta;
            formDataMensagem.nr_contfluxo = formDataMensagemPai.nr_contfluxo;
            formDataMensagem.cd_estabelecimento = formDataMensagemPai.cd_estabelecimento;
            formDataMensagem.in_adicionatexto_imagem = formDataMensagemPai.in_adicionatexto_imagem;
            formDataMensagem.in_adicionaatalho = formDataMensagemPai.in_adicionaatalho;
            formDataMensagem.in_finalizaatendimento = formDataMensagemPai.in_finalizaatendimento;
            formDataMensagem.in_tipolista = formDataMensagemPai.in_tipolista;
            formDataMensagem.ds_teclaatalho = formDataMensagemPai.ds_teclaatalho;
            formDataMensagem.cd_departamento = formDataMensagemPai.cd_departamento;

            Object.keys(formDataMensagemPai).forEach((key) => (formDataMensagemPai[key] = undefined));
        }
    }

    async function abreModal(tituloModal, dados, openModal, tpMensagem, fluxoPadrao) {
        codUsuario.value = localStorage.getItem('codusuario');
        tpPrivilegio.value = localStorage.getItem('privilegio');
        inFluxoPadrao.value = fluxoPadrao;
        // if (tpPrivilegio.value != 'OiZap') {
        //   disabled.value = true;
        // }

        if (!inFluxoPadrao.value) {
            const estabelecimentosLiberado = JSON.parse(localStorage.getItem('estabelecimentos'));
            formDataMensagem.cd_estabelecimento = estabelecimentosLiberado[0].cd_estabelecimento;
        } else {
            formDataMensagem.cd_estabelecimento = 0;
        }
        variaveis.value = variaveisEmpresa.value;

        if (dados) {
            await carregaDados(dados);
            await carregaImagens();
            //Lista mensagens relacionadas
            listaMensagensRelacionada.value = await listaMensagemRelacionadas();
        }

        slideOver.value = openModal;
        titulo.value = tituloModal;

        formDataMensagem.tp_mensagem = tpMensagem;

        if (titulo.value == undefined) {
            titulo.value = 'Nova Mensagem ' + formDataMensagem.tp_mensagem;
        }
        await carregaMensagens();
        abreModalMensagem.value = true;
    }

    defineExpose({
        abreModal,
    });

    async function remover() {
        modalRemoveMensagem.value = false;
        const filtro = {
            nr_controle: formDataMensagem.nr_controle,
        };

        const result = await FluxoAtendimentoServices.remover(filtro);

        if (result.statuscode == 200) {
            fechaModalMensagem();
            toast.showSuccessNotification('Mensagem removida com sucesso!');
            emit('eventMensagemRemovida', filtro);
        }
    }

    function fechaModalMensagem() {
        Object.keys(formDataMensagem).forEach((key) => (formDataMensagem[key] = undefined));
        listaImagens.value = [];
        listaMensagensRelacionada.value = [];
        abreModalMensagem.value = false;
    }

    function abreModalRemover() {
        modalRemoveMensagem.value = true;
    }

    function fechaModalRemover() {
        modalRemoveMensagem.value = false;
    }

    async function salvar() {
        if (
            formDataMensagem.in_tipolista &&
            (formDataMensagem.ds_titulobotao == '' || formDataMensagem.ds_titulobotao == undefined)
        ) {
            toast.showWarningNotification('Titulo do botão é obrigatório!');
            return;
        }

        loading.value.show();

        let reqObj = {
            nr_contfluxo: formDataMensagem.nr_contfluxo,
            nr_fluxopai: formDataMensagem.nr_fluxopai,
            tp_mensagem: formDataMensagem.tp_mensagem,
            ds_titulo: formDataMensagem.ds_titulo,
            hr_tempo: formDataMensagem.hr_tempo,
            ds_descritivo: formDataMensagem.ds_descritivo,
            ds_titulobotao: formDataMensagem.ds_titulobotao,
            ds_mensagem: formDataMensagem.ds_mensagem,
            tp_funcionalidade: formDataMensagem.tp_funcionalidade,
            ds_evento: formDataMensagem.ds_evento,
            in_resposta: formDataMensagem.in_resposta,
            in_ativa: formDataMensagem.in_ativa,
            in_adicionatexto_imagem: formDataMensagem.in_adicionatexto_imagem,
            in_adicionaatalho: formDataMensagem.in_adicionaatalho,
            in_finalizaatendimento: formDataMensagem.in_finalizaatendimento,
            in_tipolista: formDataMensagem.in_tipolista,
            cd_usucad: codUsuario.value,
            ds_teclaatalho: formDataMensagem.ds_teclaatalho,
            in_fluxopadrao: inFluxoPadrao.value,
            cd_departamento: formDataMensagem.cd_departamento,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };

        let result;
        if (formDataMensagem.nr_controle == undefined) {
            result = await FluxoAtendimentoServices.incluir(reqObj);
        } else {
            reqObj.nr_controle = formDataMensagem.nr_controle;
            result = await FluxoAtendimentoServices.alterar(reqObj);
        }
        //console.log('🚀 ~ cadMensagem.vue:1370 ~ salvar ~ result:', result);

        if (result.statuscode == 200) {
            const data = result.data[0];
            if (formDataMensagem.nr_controle == undefined) {
                formDataMensagem.nr_controle = data.nr_controle;
                data.qt_filhos = listaMensagensRelacionada.value.length;
            }
            emit('eventMensagemSalva', data);
            toast.showSuccessNotification(result.message);
        } else if (result.statuscode == 409) {
            toast.showWarningNotification(result.errors[0]);
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.errors[0]);
        }
        loading.value.hide();
    }

    async function carregaMensagens() {
        let filtros = {};
        if (!inFluxoPadrao.value) {
            filtros.cd_estabelecimento = formDataMensagem.cd_estabelecimento;
        } else {
            filtros.cd_estabelecimento = 0;
            filtros.in_fluxopadrao = true;
        }

        if (tpPrivilegio.value != 'OiZap') {
            filtros.tp_mensagem = 'Personalizada';
        }
        listaMensagensCadastradas.value = [];
        let result = await FluxoAtendimentoServices.listar(filtros);

        if (result.statuscode == 200) {
            const mensagensRelacionadasIds = new Set(
                listaMensagensRelacionada.value.map((msg) => msg.nr_contmensagem_relacionada)
            );

            listaMensagensCadastradas.value = result.data
                .filter((msg) => !mensagensRelacionadasIds.has(msg.nr_controle))
                .map((msg) => ({
                    ...msg, // Copia todos os campos existentes
                    value: msg.nr_controle,
                    label: msg.ds_titulo,
                }));
        } else if (result.statuscode != 404) {
            toast.showErrorNotification(result.message);
        }
    }

    const carregaDepartamentos = async () => {
        const respDepartamentos = await DepartamentosServices.listar({
            cd_estabelecimento: formDataMensagem.cd_estabelecimento,
            in_ativo: true,
        });

        if (respDepartamentos.statuscode == 200) {
            departamentos.value = respDepartamentos.data.map((msg) => ({
                value: msg.cd_departamento,
                label: msg.ds_departamento,
            }));
        }
    };

    async function novaMensagem() {
        formDataMensagem.nr_fluxopai = undefined;
        formDataMensagem.ds_titulopai = undefined;
        formDataMensagem.hr_tempo = '00:00:00';
        formDataMensagem.nr_controle = undefined;
        formDataMensagem.ds_titulo = undefined;
        formDataMensagem.ds_descritivo = undefined;
        formDataMensagem.ds_mensagem = undefined;
        formDataMensagem.in_resposta = false;
        formDataMensagem.in_ativa = true;
        formDataMensagem.in_adicionatexto_imagem = false;
        formDataMensagem.in_adicionaatalho = false;
        formDataMensagem.in_finalizaatendimento = false;
        formDataMensagem.in_tipolista = false;
        formDataMensagem.ds_teclaatalho = undefined;
        formDataMensagem.cd_departamento = undefined;

        listaMensagensRelacionada.value = [];

        if (tpPrivilegio.value == 'OiZap') {
            formDataMensagem.tp_funcionalidade = undefined;
            formDataMensagem.ds_evento = undefined;

            titulo.value = 'Nova Mensagem ' + formDataMensagem.tp_mensagem;
        }

        await nextTick();

        const input = inputTitulo.value;

        if (input && input.focus) {
            input.focus();
        }
    }

    async function abreModalToken() {
        cadTokensRef.value.abreModal(formDataMensagem);
    }

    async function abreModalTokenAtalho(dataMensagem) {
        //console.log('dataMensagem at line 1239 in mensagem/cadMensagem.vue:', dataMensagem);

        cadTokensRef.value.abreModal(dataMensagem);
    }

    const selecionaAtalho = async (event) => {
        if (event.event == 'Clear') {
            formDataMensagem.ds_teclaatalho = undefined;
        }
    };

    const selecionaDepartamento = async (event) => {
        if (event.event == 'Clear') {
            formDataMensagem.cd_departamento = undefined;
        }
    };

    const selecionaMensagem = async (event) => {
        //  console.log('event at line 1054 in mensagem/cadMensagem.vue:', event);
        if (event.event == 'Select') {
            if (formDataMensagem.nr_controle == undefined) {
                await salvar();
            }
            const resp = await listaMensagemRelacionadas(event.select.value);
            if (resp.length > 0) {
                toast.showWarningNotification('Mensagem já está relacionada!');
                return;
            }
            await salvaMensagemRelacionada(event.select);
        }
    };

    const selecionaFuncionalidade = async (event) => {
        // console.log('event at line 1054 in mensagem/cadMensagem.vue:', event);
        /* if (event.event == 'Select') {
         if (event.label == 'Comprovante de Pedido') {
           variaveis.value = [...variaveisOriginal.value, ...variaveisComprovante.value];
         }
       } else {
         variaveis.value = [...variaveisOriginal.value];
       }
         */
    };

    async function salvaMensagemRelacionada(msgRelacionada) {
        //console.log('msgRelacionada at line 1068 in mensagem/cadMensagem.vue:', msgRelacionada);
        let reqObj = {
            nr_contmensagem: formDataMensagem.nr_controle,
            nr_contmensagem_relacionada: msgRelacionada.value,
            nr_ordem: String(listaMensagensRelacionada.value.length).padStart(6, '0'),
            in_opcoes: formDataMensagem.in_tipolista,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };

        const result = await MensagemRelacionadaServices.incluir(reqObj);

        if (result.statuscode == 200) {
            const data = result.data[0];
            //console.log('data at line 910 in mensagem/cadMensagem.vue:', data);

            const tmpMensagem = msgRelacionada;
            tmpMensagem.nr_contrel = data.nr_controle;
            tmpMensagem.nr_contmensagem = formDataMensagem.nr_controle;
            tmpMensagem.nr_contmensagem_relacionada = msgRelacionada.value;
            tmpMensagem.nr_ordem = data.nr_ordem;
            tmpMensagem.in_opcoesrel = formDataMensagem.in_tipolista;

            listaMensagensRelacionada.value.push(tmpMensagem);
            emit('eventMensagemVinculadaSalva', tmpMensagem);

            filtroMensagemRelacionada.value = [];

            const novaLista = listaMensagensCadastradas.value.filter((msg) => msg.value !== msgRelacionada.value);

            listaMensagensCadastradas.value = novaLista;

            toast.showSuccessNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    async function desvincular(dados) {
        let filtros = {
            nr_controle: dados.nr_contrel,
            in_fluxopadrao: true,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };

        const result = await MensagemRelacionadaServices.remover(filtros);

        if (result.statuscode == 200) {
            listaMensagensCadastradas.value.push(dados);

            const novaLista = listaMensagensRelacionada.value.filter((msg) => msg.nr_contrel !== dados.nr_contrel);

            listaMensagensRelacionada.value = novaLista;
            emit('eventDesvinculaMensagem', dados);
        }
    }

    async function atualizaOrdem(nrControle, ordem) {
        let reqObj = {
            nr_controle: nrControle,
            nr_ordem: ordem,
        };
        const result = await MensagemRelacionadaServices.alterar(reqObj);
    }

    async function listaMensagemRelacionadas(nrContRelacionada) {
        let filtros = {
            nr_contmensagem: formDataMensagem.nr_controle,
            nr_contmensagem_relacionada: nrContRelacionada,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };
        let result = await MensagemRelacionadaServices.listar(filtros);
        return result.data;
    }

    const onChangeOpcoes = async (mensagem, event) => {
        mensagem.in_opcoesrel = event.target.checked;

        const req = {
            nr_controle: mensagem.nr_contrel,
            in_opcoes: mensagem.in_opcoesrel,
            cd_estabelecimento: inFluxoPadrao.value ? 0 : formDataMensagem.cd_estabelecimento,
        };
        const result = await MensagemRelacionadaServices.alterar(req);
    };

    async function onDragStart(index) {
        draggedIndex.value = index;
    }

    async function onDragOver(index) {
        if (draggedIndex.value !== null && index !== draggedIndex.value) {
            const draggedElement = listaMensagensRelacionada.value.splice(draggedIndex.value, 1)[0];
            listaMensagensRelacionada.value.splice(index, 0, draggedElement);
            draggedIndex.value = index;

            listaMensagensRelacionada.value.forEach(async (msg, index) => {
                msg.nr_ordem = String(index).padStart(6, '0');
                await atualizaOrdem(msg.nr_contrel, msg.nr_ordem);
            });
        }
    }

    async function onDrop(index) {
        draggedIndex.value = null;
    }

    async function toggleEmojiPicker() {
        showEmojiPicker.value = !showEmojiPicker.value;
        if (showEmojiPicker.value) {
            nextTick(() => {
                const buttonRect = emojiButton.value.getBoundingClientRect();
                emojiPickerStyles.value = {
                    top: `${buttonRect.bottom + window.scrollY}px`,
                    left: `0px`,
                };
            });
        }
    }

    async function onSelectEmoji(event) {
        const emoji = event.i;
        const textArea = refMensagem.value;
        const text = formDataMensagem.ds_mensagem;
        const start = text.slice(0, cursorPosition.value);
        const end = text.slice(cursorPosition.value);

        formDataMensagem.ds_mensagem = start + emoji + end;
        showEmojiPicker.value = false;

        // Atualiza a posição do cursor após a inserção do emoji
        nextTick(() => {
            cursorPosition.value += emoji.length;
            textArea.setSelectionRange(cursorPosition.value, cursorPosition.value);
        });
    }

    async function updateCursorPosition() {
        const textArea = refMensagem.value;
        cursorPosition.value = textArea.selectionStart;
    }

    const handleClickOutside = (event) => {
        if (
            emojiButton.value &&
            !emojiButton.value.contains(event.target) &&
            refMensagem.value &&
            !refMensagem.value.contains(event.target)
        ) {
            showEmojiPicker.value = false;
        }
    };

    const handleKeyDown = (event) => {
        if (event.key === 'Escape') {
            showEmojiPicker.value = false;
        }
    };

    async function abreModalEdicaoMsg() {
        modalEdicaoMsg.value = true;
    }

    async function fechaModalEdicaoMsg() {
        modalEdicaoMsg.value = false;
    }

    async function listaVariaveis(variavel) {
        if (variavel == 'Empresa') {
            variaveis.value = variaveisEmpresa.value;
        } else if (variavel == 'Cliente') {
            variaveis.value = variaveisCliente.value;
        } else if (variavel == 'Atendimento') {
            variaveis.value = variaveisAtendimento.value;
        } else if (variavel == 'Pedido') {
            variaveis.value = variaveisPedido.value;
        }
    }

    onMounted(async () => {
        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;

        if (modulos.in_crm) {
            await carregaDepartamentos();
        }

        variaveis.value = variaveisEmpresa.value;
        document.addEventListener('click', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);
    });
    onBeforeUnmount(() => {
        document.removeEventListener('click', handleClickOutside);
        document.removeEventListener('keydown', handleKeyDown);
    });
</script>

<style scoped>
    .emoji-picker-popup {
        position: absolute;
        z-index: 1000;
        background: white;
        border: 1px solid #ccc;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 10px;
    }
    .intro-y.box {
        z-index: 100;
    }
</style>
