-- Migration: add_contacts_indexes
-- Created: 2025-06-01T14:43:29.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar índices na tabela contacts
-- ========================================

-- Criar índices na tabela contacts para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_contacts_telefone ON contacts(telefone);
CREATE INDEX IF NOT EXISTS idx_contacts_push_name ON contacts(push_name);
CREATE INDEX IF NOT EXISTS idx_contacts_cd_estabelecimento ON contacts(cd_estabelecimento);

-- Coment<PERSON><PERSON>s
COMMENT ON INDEX idx_contacts_telefone IS 'Índice para busca por telefone';
COMMENT ON INDEX idx_contacts_push_name IS 'Índice para busca por nome do contato';
COMMENT ON INDEX idx_contacts_cd_estabelecimento IS 'Índice para busca por estabelecimento'; 