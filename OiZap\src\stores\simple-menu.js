import { defineStore } from 'pinia';
import MenuServices from '@/services/administracao/MenuServices';

export const useSimpleMenuStore = defineStore('simpleMenu', {
    state: () => ({
        menu: [],
        isLoading: false,
        error: null,
    }),
    persist: true,
    
    actions: {
        /**
         * Carrega o menu do usuário baseado nas suas permissões
         */
        async carregarMenu() {
            this.isLoading = true;
            this.error = null;
            
            try {
                const menu = await MenuServices.carregarMenuUsuario();
                this.menu = menu;
            } catch (error) {
                console.error('Erro ao carregar menu:', error);
                this.error = error.message;
                // Em caso de erro, carrega menu básico
                this.menu = MenuServices.getMenuBasico();
            } finally {
                this.isLoading = false;
            }
        },

        /**
         * Atualiza o menu (útil após mudanças de permissão)
         */
        async atualizarMenu() {
            await this.carregarMenu();
        },

        /**
         * Define menu manualmente (para compatibilidade)
         */
        setMenu(menu) {
            this.menu = menu;
        },

        /**
         * Limpa o menu
         */
        limparMenu() {
            this.menu = [];
        },
    },
});
