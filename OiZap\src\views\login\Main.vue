<template>
    <div class="min-h-screen bg-gray-50 dark:bg-slate-800 flex items-center justify-center p-5">
        <notifications ref="showNotifications" />
        <ShowLoading ref="loading" />

        <div
            class="flex max-w-7xl w-full overflow-hidden rounded-2xl shadow-xl bg-white dark:bg-slate-900 min-h-[600px]"
        >
            <!-- <PERSON><PERSON> (oculto em dispositivos móveis) -->
            <div
                class="hidden md:flex md:flex-1 bg-gradient-to-br from-emerald-400 to-teal-600 p-10 flex-col justify-center text-white relative overflow-hidden"
            >
                <div class="welcome-text text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">Bem-vindo ao OiZap</h1>
                    <p class="text-lg opacity-90">Sua plataforma completa de atendimento e gestão de mensagens</p>
                </div>

                <div class="flex flex-col space-y-6">
                    <div class="flex items-start gap-4">
                        <MessageSquareIcon class="w-6 h-6 text-white" />
                        <div>
                            <h3 class="text-lg font-semibold mb-1">Atendimento rápido</h3>
                            <p class="text-sm opacity-85">Responda seus clientes em tempo real</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <UsersIcon class="w-6 h-6 text-white" />
                        <div>
                            <h3 class="text-lg font-semibold mb-1">Chatbot</h3>
                            <p class="text-sm opacity-85">Atendimento automatizado</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <BarChartIcon class="w-6 h-6 text-white" />
                        <div>
                            <h3 class="text-lg font-semibold mb-1">Análise completa</h3>
                            <p class="text-sm opacity-85">Acompanhe métricas e desempenho</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lado Direito - Formulário -->
            <div class="flex-1 p-4 sm:p-6 md:p-10 flex items-center justify-center">
                <div class="w-full max-w-md">
                    <!-- Logo -->
                    <div class="flex justify-center mb-8">
                        <img src="@/assets/images/logo_oizap2.png" alt="OiZap" class="max-w-[200px]" />
                    </div>

                    <!-- Bem-vindo (visível apenas em mobile) -->
                    <div class="md:hidden text-center mb-6">
                        <h1 class="text-xl font-bold text-gray-800 dark:text-white">Bem-vindo ao OiZap</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Sua plataforma de atendimento</p>
                    </div>

                    <!-- Cabeçalho do formulário -->
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-100">
                            Acesse sua conta
                        </h2>
                        <div>
                            <button
                                @click="switchMode"
                                class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
                            >
                                <SunIcon v-if="!darkMode" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
                                <MoonIcon v-else class="w-5 h-5 text-gray-600 dark:text-gray-300" />
                            </button>
                        </div>
                    </div>

                    <!-- Formulário de Login -->
                    <div v-show="!inEsqueciSenha && !inConfirmacao && !inNovaSenha">
                        <div class="mb-5">
                            <label for="login" class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >E-mail</label
                            >
                            <div class="relative">
                                <AtSignIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="login"
                                    type="text"
                                    v-model="dsLogin"
                                    ref="dsLoginRef"
                                    placeholder="Seu e-mail ou nome de usuário"
                                    autocomplete="username"
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                            </div>
                        </div>

                        <div class="mb-5">
                            <label
                                for="password"
                                class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >Senha</label
                            >
                            <div class="relative">
                                <KeyIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="password"
                                    type="password"
                                    v-model="dsSenha"
                                    placeholder="Sua senha"
                                    autocomplete="current-password"
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                            </div>
                        </div>

                        <div class="flex justify-between items-center mb-6">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" v-model="lembrarMe" @change="switchLembrar" class="sr-only" />
                                <div
                                    class="relative w-5 h-5 rounded bg-white dark:bg-slate-800 border border-gray-300 dark:border-gray-600 mr-2 flex items-center justify-center"
                                >
                                    <div v-if="lembrarMe" class="absolute w-3 h-3 bg-teal-500 rounded-sm"></div>
                                </div>
                                <span class="text-sm text-gray-600 dark:text-gray-400">Lembrar-me</span>
                            </label>
                            <a
                                href="#"
                                @click.prevent="esqueciSenha"
                                class="text-sm text-teal-600 dark:text-teal-400 hover:underline"
                            >
                                Esqueci minha senha
                            </a>
                        </div>

                        <button
                            @click="login"
                            class="w-full py-3 px-4 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors"
                        >
                            Entrar
                        </button>
                    </div>

                    <!-- Esqueci a Senha -->
                    <div v-show="inEsqueciSenha && !inNovaSenha" class="animate-fadeIn">
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-3">Recuperar Senha</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-6">
                            Digite seu e-mail para receber as instruções de recuperação de senha.
                        </p>

                        <div class="mb-5">
                            <label
                                for="recovery-email"
                                class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >E-mail</label
                            >
                            <div class="relative">
                                <MailIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="recovery-email"
                                    type="email"
                                    v-model="dsEmail"
                                    placeholder="Seu e-mail cadastrado"
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                            </div>
                        </div>

                        <div class="flex flex-col space-y-3 mt-6">
                            <button
                                @click="redefinirSenha"
                                class="w-full py-3 px-4 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors"
                            >
                                Enviar código
                            </button>
                            <button
                                @click="cancelar"
                                class="w-full py-3 px-4 bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 text-gray-800 dark:text-gray-200 font-medium rounded-lg border border-gray-300 dark:border-slate-600 transition-colors"
                            >
                                Voltar
                            </button>
                        </div>
                    </div>

                    <!-- Confirmação de Código -->
                    <div v-show="inConfirmacao && !inNovaSenha" class="animate-fadeIn">
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-3">
                            Confirmação de Código
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-6">
                            Digite o código de verificação enviado para seu e-mail.
                        </p>

                        <div class="mb-5">
                            <label class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >E-mail</label
                            >
                            <div class="relative">
                                <MailIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    type="email"
                                    v-model="dsEmail"
                                    disabled
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-gray-50 dark:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-gray-500 dark:text-gray-400"
                                />
                            </div>
                        </div>

                        <div class="mb-5">
                            <label
                                for="confirmation-code"
                                class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >Código de Verificação</label
                            >
                            <div class="relative">
                                <LockIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="confirmation-code"
                                    type="text"
                                    v-model="codigo"
                                    placeholder="Digite o código recebido"
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                            </div>
                        </div>

                        <div class="flex flex-col space-y-3 mt-6">
                            <button
                                @click="confirmaCadastro"
                                class="w-full py-3 px-4 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors"
                            >
                                Confirmar
                            </button>
                            <button
                                @click="cancelar"
                                class="w-full py-3 px-4 bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 text-gray-800 dark:text-gray-200 font-medium rounded-lg border border-gray-300 dark:border-slate-600 transition-colors"
                            >
                                Voltar
                            </button>
                            <button
                                @click="geraNovoCodigo"
                                class="text-teal-600 dark:text-teal-400 text-sm underline hover:text-teal-700 dark:hover:text-teal-300"
                            >
                                Reenviar código
                            </button>
                        </div>
                    </div>

                    <!-- Nova Senha -->
                    <div v-show="inNovaSenha" class="animate-fadeIn">
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-3">Definir Nova Senha</h3>

                        <div class="mb-5">
                            <label class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >E-mail</label
                            >
                            <div class="relative">
                                <MailIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    type="email"
                                    v-model="dsEmail"
                                    disabled
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-gray-50 dark:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-gray-500 dark:text-gray-400"
                                />
                            </div>
                        </div>

                        <div class="mb-5">
                            <label
                                for="new-password"
                                class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >Nova Senha</label
                            >
                            <div class="relative">
                                <LockIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="new-password"
                                    :type="mostrarSenha ? 'text' : 'password'"
                                    v-model="senha"
                                    placeholder="Digite sua nova senha"
                                    @input="feedbackSenha"
                                    class="w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                                <button
                                    @click="toggleMostrarSenha"
                                    type="button"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400"
                                >
                                    <EyeOffIcon v-if="!mostrarSenha" class="w-5 h-5" />
                                    <EyeIcon v-else class="w-5 h-5" />
                                </button>
                            </div>

                            <div class="mt-3">
                                <div class="flex space-x-1 mb-2">
                                    <div
                                        class="h-1 flex-1 rounded-sm"
                                        :class="{
                                            'bg-red-500': peso > 0 || peso <= 4,
                                            'bg-gray-200 dark:bg-slate-700': senha === '',
                                        }"
                                    ></div>
                                    <div
                                        class="h-1 flex-1 rounded-sm"
                                        :class="{
                                            'bg-red-500': peso > 0 || peso <= 3,
                                            'bg-gray-200 dark:bg-slate-700': peso === 4 || senha === '',
                                        }"
                                    ></div>
                                    <div
                                        class="h-1 flex-1 rounded-sm"
                                        :class="{
                                            'bg-yellow-500': (peso > 0 && peso <= 2) || (peso <= 2 && senha !== ''),
                                            'bg-gray-200 dark:bg-slate-700': peso === 4 || peso === 3 || senha === '',
                                        }"
                                    ></div>
                                    <div
                                        class="h-1 flex-1 rounded-sm"
                                        :class="{
                                            'bg-yellow-500': peso > 0 || peso <= 1,
                                            'bg-gray-200 dark:bg-slate-700':
                                                peso === 4 || peso === 3 || peso === 2 || senha === '',
                                        }"
                                    ></div>
                                    <div
                                        class="h-1 flex-1 rounded-sm"
                                        :class="{
                                            'bg-green-500': senha != '' && peso == 0,
                                            'bg-gray-200 dark:bg-slate-700':
                                                (senha === '' && peso === 0) || (senha !== '' && peso !== 0),
                                        }"
                                    ></div>
                                </div>
                                <div class="text-xs text-red-500" v-html="feedback"></div>
                            </div>
                        </div>

                        <div class="mb-5">
                            <label
                                for="confirm-password"
                                class="block mb-2 text-sm font-medium text-gray-600 dark:text-gray-400"
                                >Confirme a Senha</label
                            >
                            <div class="relative">
                                <LockIcon
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
                                />
                                <input
                                    id="confirm-password"
                                    :type="mostrarSenha ? 'text' : 'password'"
                                    v-model="confirma"
                                    placeholder="Confirme sua nova senha"
                                    @input="feedbackConfirmaSenha"
                                    class="w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-slate-600 rounded-lg text-base bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:text-gray-100"
                                />
                            </div>
                            <div class="text-xs text-red-500 mt-1" v-html="feedbackConfirma"></div>
                        </div>

                        <div class="flex flex-col space-y-3 mt-6">
                            <button
                                @click="alteraSenha"
                                class="w-full py-3 px-4 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors"
                            >
                                Salvar nova senha
                            </button>
                            <button
                                @click="cancelar"
                                class="w-full py-3 px-4 bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 text-gray-800 dark:text-gray-200 font-medium rounded-lg border border-gray-300 dark:border-slate-600 transition-colors"
                            >
                                Voltar
                            </button>
                        </div>
                    </div>

                    <div class="text-center mt-8 text-gray-500 dark:text-gray-400 text-xs">
                        <p>&copy; {{ new Date().getFullYear() }} OiZap - Todos os direitos reservados</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { computed, nextTick, onMounted, provide, ref } from 'vue';
    import { useRouter, useRoute } from 'vue-router';
    import notifications from '@/components/show-notifications/Main.vue';
    import { useLoginStore } from '@/stores/login-store';
    import { useSimpleMenuStore } from '@/stores/simple-menu';
    import { useEstabelecimento } from '@/stores/estabelecimento';
    import { obtemMenu } from '@/services/administracao/useMenu';
    import UsuariosServices from '@/services/administracao/UsuariosServices';
    import CryptoJS from 'crypto-js';
    import hosts from '@/utils/hosts';
    import AdminServices from '@/services/administracao/AdminServices';
    import { useDarkModeStore } from '@/stores/dark-mode';
    import dom from '@left4code/tw-starter/dist/js/dom';

    // Importando os ícones necessários
    import {
        AtSign as AtSignIcon,
        Key as KeyIcon,
        Sun as SunIcon,
        Moon as MoonIcon,
        Mail as MailIcon,
        Lock as LockIcon,
        Eye as EyeIcon,
        EyeOff as EyeOffIcon,
        MessageSquare as MessageSquareIcon,
        BarChart as BarChartIcon,
        Users as UsersIcon,
    } from 'lucide-vue-next';

    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);

    const showNotifications = ref();
    let simpleMenuStore = useSimpleMenuStore();
    let loginStore = useLoginStore();
    const router = useRouter();
    const route = useRoute();
    let dsUsuario = ref('');
    let dsLogin = ref('');
    let dsEmail = ref('');
    let dsSenha = ref('');
    let codigo = ref('');
    let lembrarMe = ref(false);
    let inEsqueciSenha = ref(false);
    let inConfirmacao = ref(false);
    let loading = ref();
    const tpAmbiente = ref('');

    let senha = ref('');
    let confirma = ref('');
    const feedback = ref('');
    const feedbackConfirma = ref('');
    const forcaSenha = ref('');
    let peso = ref(0);
    let mostrarSenha = ref(false);
    let cdUsuario = ref('');
    let inCadastro = ref(true);
    let inExiste = ref(false);
    let inNovaSenha = ref(false);
    let idUser = ref('');
    const formCheckInputChecked = ref(false);

    // Manter as funções existentes do arquivo original
    async function alteraCookies(data, menu) {
        // console.log('🚀 ~ Main.vue:461 ~ alteraCookies ~ data:', data);
        localStorage.setItem('token', data[0].token);
        localStorage.setItem('dataExptoken', data[0].expires_in);
        localStorage.setItem('codusuario', data[0].cd_usuario);
        localStorage.setItem('usuario', data[0].ds_nome);
        localStorage.setItem('login', data[0].ds_login);
        localStorage.setItem('privilegio', data[0].ds_privilegio);
        // localStorage.setItem('aplicadesconto', data[0].in_aplicadesconto);

        loginStore.perfisDoUsuario = data[0].tp_perfil;
        simpleMenuStore.menu = menu.menu;

        if (lembrarMe.value == true) {
            localStorage.setItem('password', CryptoJS.AES.encrypt(dsSenha.value, 'xpto'));
            localStorage.setItem('login', data[0].ds_login);
            localStorage.setItem('lembrarMe', true);
        } else {
            localStorage.removeItem('lembrarMe');
        }
    }

    function esqueciSenha() {
        inEsqueciSenha.value = true;
        inConfirmacao.value = false;
        inNovaSenha.value = false;
    }

    function cancelar() {
        inEsqueciSenha.value = false;
        inConfirmacao.value = false;
        inNovaSenha.value = false;
    }

    async function geraNovoCodigo() {
        let reqObj = {
            ds_email: dsEmail.value,
        };

        let result = await AdminServices.geraNovoCodigo(reqObj);
        if (result.statuscode == 200) {
            inConfirmacao.value = true;
            inEsqueciSenha.value = false;
            inNovaSenha.value = false;
            showNotifications.value.showSuccessNotification('Verifique sua caixa de e-mail!');
        } else {
            showNotifications.value.showWarningNotification(result.message);
        }
    }

    async function redefinirSenha() {
        loading.value.show();
        let reqObj = {
            ds_email: dsEmail.value,
        };

        let result = await AdminServices.redefinirSenha(reqObj);
        if (result.statuscode == 200) {
            inConfirmacao.value = true;
            inEsqueciSenha.value = false;
            inNovaSenha.value = false;
            showNotifications.value.showSuccessNotification('Verifique sua caixa de e-mail!');
        } else {
            showNotifications.value.showWarningNotification(result.message);
        }
        loading.value.hide();
    }

    async function confirmaCadastro() {
        let reqObj = {
            ds_email: dsEmail.value,
            ds_senha: senha.value,
            nr_codigo: codigo.value,
        };

        let result = await AdminServices.confirmaCadastro(reqObj);
        if (result.statuscode == 200) {
            dsUsuario.value = result.data[0].ds_nome;
            inCadastro.value = false;
            inExiste.value = false;
            inNovaSenha.value = true;
            showNotifications.value.showSuccessNotification('Cadastro confirmado com sucesso!');
        } else {
            showNotifications.value.showWarningNotification(result.message);
        }
    }

    function toggleMostrarSenha() {
        mostrarSenha.value = !mostrarSenha.value;
    }

    function feedbackConfirmaSenha() {
        feedbackConfirma.value = '';
        if (senha.value != confirma.value) {
            feedbackConfirma.value = '• Senha incompatível.';
        }
    }

    function feedbackSenha() {
        feedback.value = '';
        peso.value = 0;

        const regexCaracterEspecial = /[!@#$%^&*(),.?":{}|<>]/;
        const regexNumero = /\d/;
        const regexLetra = /[a-zA-Z]/;

        if (!regexCaracterEspecial.test(senha.value)) {
            feedback.value += '• Falta caracter especial.';
            peso.value += 1;
        }

        if (!regexNumero.test(senha.value)) {
            feedback.value += '<br/>• Falta número. ';
            peso.value += 1;
        }

        if (!regexLetra.test(senha.value)) {
            feedback.value += '<br/>• Falta letra. ';
            peso.value += 1;
        }

        if (senha.value.length < 6) {
            feedback.value += '<br/>• Mínimo 6 caracteres. ';
            peso.value += 1;
        }
    }

    async function alteraSenha() {
        let reqObj = {
            ds_email: dsEmail.value,
            ds_senha: senha.value,
        };

        let result = await AdminServices.alteraSenha(reqObj);
        if (result.statuscode == 200) {
            inCadastro.value = false;
            inConfirmacao.value = false;
            inExiste.value = false;
            inNovaSenha.value = false;
            showNotifications.value.showSuccessNotification('Senha atualizada com sucesso!');
        } else {
            showNotifications.value.showWarningNotification(result.message);
        }
    }

    async function login() {
        localStorage.removeItem('instancia');

        if (dsLogin.value.trim() == '') {
            showNotifications.value.showWarningNotification('Login é Obrigatório');
            return;
        }
        if (dsSenha.value.trim() == '') {
            showNotifications.value.showWarningNotification('Senha é Obrigatório');
            return;
        }
        loading.value.show();
        let filtros = {
            ds_login: dsLogin.value,
            ds_senha: dsSenha.value,
        };

        let respLogin = await UsuariosServices.login(filtros);
        //console.log('🚀 ~ Main.vue:621 ~ login ~ respLogin:', respLogin);

        if (respLogin.statuscode == 200 && respLogin.data[0].token != undefined) {
            if (
                localStorage.getItem('token') == null ||
                localStorage.getItem('token') == undefined ||
                localStorage.getItem('token') == undefined < 10
            ) {
                localStorage.setItem('token', respLogin.data[0].token);
            }

            localStorage.setItem('codusuario', respLogin.data[0].cd_usuario);
            localStorage.setItem('hashunica', respLogin.data[0].nr_hashunica);
            localStorage.setItem('email', respLogin.data[0].ds_login);
            localStorage.setItem('instancia', respLogin.data[0].instance);
            localStorage.setItem('privilegio', respLogin.data[0].tp_privilegio);
            localStorage.setItem('aplicadesconto', respLogin.data[0].in_aplicadesconto);

            dsLogin.value = respLogin.data[0].ds_login;
            dsUsuario.value = respLogin.data[0].ds_nome;

            // console.log('respLogin.data[0] at line 641 in login/Main.vue:', respLogin.data[0]);
            var menu = await obtemMenu(respLogin.data[0]);

            if (menu.menu == undefined || menu.menu.length == 1) {
                showNotifications.value.showErrorNotification('Ocorreu um erro ao obter menu.');
            }

            await alteraCookies(respLogin.data, menu);

            if (respLogin.data[0].cd_usuario != '') {
                if (respLogin.data[0].ds_privilegio == 'OiZap') {
                    localStorage.removeItem('estabelecimentos');
                    router.push({ path: `${hosts.app}/estabelecimentos` });
                } else {
                    localStorage.setItem('estabelecimentos', JSON.stringify(respLogin.data[0].estabelecimento));
                    useEstabelecimento.value = respLogin.data[0];
                    router.push({ path: `${hosts.app}/chat` });
                    localStorage.setItem('chat', 'Chat');
                }
            }
        } else if (respLogin.statuscode == 401) {
            showNotifications.value.showWarningNotification('Senha Incorreta!');
        } else {
            showNotifications.value.showWarningNotification('Login não localizado!');
        }

        loading.value.hide();
    }

    const dsLoginRef = ref();
    provide('bind[dsLoginRef]', async (el) => {
        dsLoginRef.value = el;
    });

    const setDarkModeClass = () => {
        darkMode.value ? dom('html').addClass('dark') : dom('html').removeClass('dark');
    };

    const switchMode = () => {
        formCheckInputChecked.value = !formCheckInputChecked.value;
        darkModeStore.setDarkMode(!darkMode.value);
        setDarkModeClass();
    };

    const switchLembrar = () => {
        formCheckInputChecked.value = !formCheckInputChecked.value;
    };

    onMounted(async () => {
        tpAmbiente.value = import.meta.env.MODE;

        simpleMenuStore.menu = [];
        dom('body').removeClass('main').removeClass('error-page').addClass('login');

        if (localStorage.getItem('lembrarMe')) {
            var bytes = await CryptoJS.AES.decrypt(localStorage.getItem('password'), 'xpto');
            var originalText = await bytes.toString(CryptoJS.enc.Utf8);

            dsUsuario.value = localStorage.getItem('usuario');
            dsLogin.value = localStorage.getItem('login');

            dsSenha.value = originalText;
            lembrarMe.value = true;

            await nextTick();
            dsLoginRef.value.focus();
        } else {
            localStorage.removeItem('codusuario');
            localStorage.removeItem('email');
            localStorage.removeItem('usuario');
            localStorage.removeItem('login');
            localStorage.removeItem('password');
        }

        localStorage.removeItem('hashunica');
        localStorage.removeItem('instancia');
        localStorage.removeItem('token');
        localStorage.removeItem('aplicadesconto');

        setDarkModeClass();
    });
</script>

<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fadeIn {
        animation: fadeIn 0.3s;
    }
</style>
