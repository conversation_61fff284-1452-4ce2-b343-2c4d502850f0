-- Migration: add_dadoscrm_messages
-- Created: 2025-06-20T03:24:52.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar colunas CRM na tabela messages
-- ========================================

-- Adicionar cd_departamento
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'cd_departamento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN cd_departamento INTEGER;
        COMMENT ON COLUMN messages.cd_departamento IS 'Indica o departamento responsável pela mensagem';
        RAISE NOTICE 'Coluna cd_departamento adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna cd_departamento já existe na tabela messages';
    END IF;
END $$;

-- Adicionar cd_atendente
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'cd_atendente'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN cd_atendente INTEGER;
        COMMENT ON COLUMN messages.cd_atendente IS 'Indica o atendente responsável pela mensagem';
        RAISE NOTICE 'Coluna cd_atendente adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna cd_atendente já existe na tabela messages';
    END IF;
END $$;

-- Adicionar ds_comentario
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'ds_comentario'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN ds_comentario TEXT;
        COMMENT ON COLUMN messages.ds_comentario IS 'Indica o comentário da mensagem';
        RAISE NOTICE 'Coluna ds_comentario adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna ds_comentario já existe na tabela messages';
    END IF;
END $$; 