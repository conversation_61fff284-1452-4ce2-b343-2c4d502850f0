-- Migration: add_create_table_perfis_telas
-- Created: 2025-07-07T21:42:03.032Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

-- Exemplo: Criar tabela
CREATE TABLE IF NOT EXISTS perfis_telas (
  nr_perfil_tela SERIAL PRIMARY KEY,
  cd_perfil INTEGER NOT NULL,
  cd_tela INTEGER NOT NULL,
  in_visualizar BOOLEAN DEFAULT TRUE,
  in_inserir BOOLEAN DEFAULT TRUE,
  in_alterar BOOLEAN DEFAULT TRUE,
  in_excluir BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Exemplo: Adicionar coluna
-- ALTER TABLE tabela_existente 
-- ADD COLUMN IF NOT EXISTS nova_coluna VARCHAR(50);

-- Exemplo: Criar índice  
-- CREATE INDEX IF NOT EXISTS idx_exemplo_nome 
-- ON exemplo_tabela(nome);

-- ========================================
-- Adicione seu SQL aqui:
-- ========================================


