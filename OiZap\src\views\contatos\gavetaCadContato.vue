<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <Modal size="modal-lg" :show="props.estado" @hidden="fecharModal" key="props" slideOver>
        <ModalHeader class="flex justify-between items-center font-medium text-xl">
            Contato
            <XIcon class="w-6 h-6 cursor-pointer" @click="fecharModal" />
        </ModalHeader>
        <ModalBody
            class="h-[calc(100vh-55px)] max-h-[calc(100vh-55px)] overflow-y-auto flex flex-col items-center justify-between p-0"
        >
            <div class="w-full flex flex-col items-center p-6">
                <h3 class="text-md font-medium text-slate-500 self-start flex items-center mb-1">
                    <UserIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Dad<PERSON> pessoais
                </h3>
                <div class="grid grid-cols-12 w-full gap-2">
                    <!-- <div class="form-control col-span-12 md:col-span-3">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Cod.
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder="Código"
                                v-model="formData.cd_cliente"
                                disabled
                            />
                        </div>
                    </div> -->
                    <div class="form-control col-span-12 md:col-span-12 gap-1">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Telefone <span class="text-red-500">*</span>
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                v-model="formData.telefone"
                                @input="formatarTelefone"
                                placeholder="(00) 00000-0000"
                            />
                        </div>
                    </div>
                    <div class="form-control col-span-12 md:col-span-12">
                        <label for="validation-form-1" class="form-label text-slate-500 text-xs w-20 mb-0 pb-0">
                            Nome <span class="text-red-500">*</span>
                        </label>
                        <div class="relative w-full">
                            <input
                                type="text"
                                class="form-control col-span-1 block"
                                placeholder=" Nome do Contato"
                                v-model="formData.push_name"
                            />
                        </div>
                    </div>
                </div>

                <p class="text-xs text-slate-500 w-full py-2">
                    <span class="text-red-500">*</span> Campos obrigatórios
                </p>
            </div>
            <ModalFooter class="w-full flex justify-end items-center">
                <button class="btn btn-secondary-soft border-none shadow-none mr-2" @click="fecharModal">
                    <XIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Cancelar
                </button>
                <button class="btn btn-primary-soft border-none shadow-none" @click="salvarContato">
                    <SaveIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                    Salvar
                </button>
            </ModalFooter>
        </ModalBody>
    </Modal>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import ContatosServices from '@/services/pedidos/ContatosServices';
    import notifications from '@/components/show-notifications/Main.vue';
    import { id } from 'date-fns/locale';

    const showNotifications = ref();
    const loading = ref();

    const props = defineProps({
        estado: {
            type: Boolean,
            default: false,
        },
        dados: {
            type: Object,
            default: () => ({}),
        },
    });
    const emit = defineEmits(['update:estado', 'response:success', 'response:error']);

    const formData = ref({
        cd_estabelecimento: '',
        id: '',
        push_name: '',
        telefone: '',
    });

    const listaUF = ref([
        '',
        'AC',
        'AL',
        'AM',
        'AP',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MG',
        'MS',
        'MT',
        'PA',
        'PB',
        'PE',
        'PI',
        'PR',
        'RJ',
        'RN',
        'RO',
        'RR',
        'RS',
        'SC',
        'SE',
        'SP',
        'TO',
    ]);

    function fecharModal() {
        emit('update:estado', false);
    }

    function formatarTelefone(event, removerEspeciais, retornarValor) {
        let valor = typeof event === 'string' ? event : event.target?.value;
        valor = valor?.replace(/\D/g, ''); // Remove todos os caracteres não numéricos

        if (removerEspeciais) {
            return valor;
        }

        // Aplica a formatação: +CC (AA) NNNNN-NNNN
        if (valor?.length < 11) {
            valor = valor?.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
        } else if (valor?.length >= 11) {
            valor = valor?.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
        }

        if (retornarValor) {
            return valor;
        } else {
            formData.value.telefone = valor;
        }
    }

    async function salvarContato() {
        try {
            loading.value.show();
            let result = null;
            const formDataConfigurado = {
                ...formData.value,
                telefone: formatarTelefone(formData.value.telefone, true, true),
                id: `55${formatarTelefone(formData.value.telefone, true, true)}@s.whatsapp.net`,
            };
            delete formDataConfigurado.checked;
            if (!!formData.value.id) result = await ContatosServices.alterar(formDataConfigurado);
            else result = await ContatosServices.incluir(formDataConfigurado);

            if (result.statuscode === 200) {
                Object.assign(formData.value, result.data[0]);
                showNotifications.value.showSuccessNotification(result.message);
                emit('response:success', result.data[0]);
            }
        } catch (error) {
            console.error('Erro ao salvar contato:', error);
        } finally {
            loading.value.hide();
            fecharModal();
        }
    }

    async function excluirContato() {
        // Implementar lógica de exclusão de contato
    }

    watch(
        () => props,
        (newValue) => {
            if (!newValue.estado) return;
            if (newValue) {
                formData.value = { ...props.dados };
                formData.value.nr_telefonezap = formatarTelefone(
                    { target: { value: formData.value.nr_telefonezap } },
                    false,
                    true
                );

                const estatabelecimentos = localStorage.getItem('estabelecimentos');

                if (estatabelecimentos) {
                    formData.value.cd_estabelecimento = JSON.parse(estatabelecimentos)[0]?.cd_estabelecimento;
                } else {
                    showNotifications.value.showWarningNotification('Estabelecimento não liberado!');
                }
            } else {
                formData.value = {
                    // cd_estabelecimento: '',
                    id: '',
                    push_name: '',
                    telefone: '',
                };
            }
        },
        { immediate: true, deep: true }
    );
</script>
