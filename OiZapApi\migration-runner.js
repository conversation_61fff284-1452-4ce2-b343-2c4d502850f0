require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

const environment = process.env.NODE_ENV || 'DEV';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_DEV,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
    SANDBOX: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
    PROD: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

function getMigrationFiles() {
  const migrationsDir = path.join(__dirname, 'migrations/sql');

  // Criar pasta se não existir
  if (!fs.existsSync(migrationsDir)) {
    fs.mkdirSync(migrationsDir, { recursive: true });
  }

  const files = fs
    .readdirSync(migrationsDir)
    .filter((file) => file.endsWith('.sql'))
    .sort(); // Ordem alfabética garante execução correta

  return files.map((file) => ({
    name: path.basename(file, '.sql'),
    path: path.join(migrationsDir, file),
    sql: fs.readFileSync(path.join(migrationsDir, file), 'utf8'),
  }));
}

async function runMigrations() {
  const config = getConnectionConfig(environment);
  const client = new Client(config);

  try {
    await client.connect();
    console.log(`🚀 Executando migrations INTELIGENTES - Ambiente: ${environment}`);

    // Criar tabela de controle
    await client.query(`
      CREATE TABLE IF NOT EXISTS migration_history (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(255),
        environment VARCHAR(50) DEFAULT '${environment}'
      );
    `);

    const migrations = getMigrationFiles();

    if (migrations.length === 0) {
      console.log('📋 Nenhuma migration encontrada em migrations/sql/');
      console.log('💡 Crie arquivos .sql na pasta migrations/sql/ para começar');
      return;
    }

    console.log(`📋 Encontradas ${migrations.length} migrations:`);
    migrations.forEach((m) => console.log(`   - ${m.name}`));

    // Executar cada migration
    for (const migration of migrations) {
      // Verificar se já foi executada
      const result = await client.query('SELECT name FROM migration_history WHERE name = $1', [migration.name]);

      if (result.rows.length > 0) {
        console.log(`⏭️  Migration ${migration.name} já executada`);
        continue;
      }

      console.log(`🔄 Executando migration: ${migration.name}`);

      try {
        // Executar a migration
        await client.query(migration.sql);

        // Registrar como executada
        await client.query('INSERT INTO migration_history (name, checksum) VALUES ($1, $2)', [
          migration.name,
          generateChecksum(migration.sql),
        ]);

        console.log(`✅ Migration ${migration.name} executada com sucesso!`);
      } catch (error) {
        console.error(`❌ Erro na migration ${migration.name}:`, error.message);
        throw error;
      }
    }

    console.log(`🎉 Todas as migrations foram executadas com sucesso!`);
  } catch (error) {
    console.error('❌ Erro geral:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

function generateChecksum(content) {
  const crypto = require('crypto');
  return crypto.createHash('md5').update(content).digest('hex');
}

// Função para criar nova migration
function createMigration(name) {
  if (!name) {
    console.error('❌ Nome da migration é obrigatório');
    console.log('💡 Uso: npm run migration:create nome_da_migration');
    return;
  }

  const timestamp = Date.now();
  const fileName = `${timestamp}_${name.replace(/[^a-zA-Z0-9]/g, '_')}.sql`;
  const migrationsDir = path.join(__dirname, 'migrations/sql');
  const filePath = path.join(migrationsDir, fileName);

  // Criar pasta se não existir
  if (!fs.existsSync(migrationsDir)) {
    fs.mkdirSync(migrationsDir, { recursive: true });
  }

  const template = `-- Migration: ${name}
-- Created: ${new Date().toISOString()}
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

-- Exemplo: Criar tabela
CREATE TABLE IF NOT EXISTS exemplo_tabela (
  id SERIAL PRIMARY KEY,
  nome VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Exemplo: Adicionar coluna
-- ALTER TABLE tabela_existente 
-- ADD COLUMN IF NOT EXISTS nova_coluna VARCHAR(50);

-- Exemplo: Criar índice  
-- CREATE INDEX IF NOT EXISTS idx_exemplo_nome 
-- ON exemplo_tabela(nome);

-- ========================================
-- Adicione seu SQL aqui:
-- ========================================


`;

  fs.writeFileSync(filePath, template);
  console.log(`✅ Migration criada: ${fileName}`);
  console.log(`📁 Caminho: migrations/sql/${fileName}`);
  console.log(`📝 Edite o arquivo e adicione seu SQL`);
}

// Verificar se é comando de criação
const args = process.argv.slice(2);
if (args[0] === 'create') {
  createMigration(args[1]);
} else {
  runMigrations();
}
