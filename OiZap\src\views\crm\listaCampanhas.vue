<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <div class="p-4">
        <!-- Header <PERSON> <PERSON><PERSON> de campanhas -->
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-4 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <MegaphoneIcon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Campanhas</h3>
                        <p class="text-purple-100 text-sm">Campanhas de marketing cadastradas no sistema.</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="gavetaCampanha.estado = true"
                    >
                        <PlusIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Nova Campanha</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="box intro-y p-4">
            <div class="flex flex-col justify-between items-center py-4">
                <!-- BEGIN: Filtros -->
                <div
                    class="w-full dark:border-darkmode-800 pb-4 flex flex-col md:flex-row justify-between items-center gap-2"
                >
                    <div class="relative cursor-pointer w-full md:max-w-[360px]">
                        <div class="relative flex-1">
                            <BaseInput
                                v-model="filtros.search"
                                placeholder="Pesquisar por nome da campanha..."
                                @input="pesquisarFiltro()"
                                iconLeft="SearchIcon"
                            />
                        </div>
                    </div>
                    <div class="flex items-center gap-2 w-full md:w-fit">
                        <button
                            class="btn btn-secondary-soft border-none bg-transparent shadow-none intro-y"
                            @click="limparFiltros()"
                            v-if="filtros.search || filtros.tp_campanha || filtros.in_ativo"
                        >
                            <XIcon class="w-5 h-5" style="stroke-width: 2.6" />
                        </button>
                        <TomSelect
                            v-model="filtros.tp_campanha"
                            :options="{
                                allowEmptyOption: true,
                                create: false,
                            }"
                            size="small"
                            class="w-full !cursor-pointer"
                            @change="
                                () => {
                                    filtros.page = 1;
                                    carregarListaCampanhas();
                                }
                            "
                        >
                            <option value="">Todos os tipos</option>
                            <option value="email">E-mail</option>
                            <option value="whatsapp">WhatsApp</option>
                            <option value="sms">SMS</option>
                            <option value="promocional">Promocional</option>
                            <option value="informativa">Informativa</option>
                        </TomSelect>
                        <TomSelect
                            v-model="filtros.in_ativo"
                            :options="{
                                allowEmptyOption: true,
                                create: false,
                            }"
                            size="small"
                            class="w-full !cursor-pointer"
                            @change="
                                () => {
                                    filtros.page = 1;
                                    carregarListaCampanhas();
                                }
                            "
                        >
                            <option value="">Todos os status</option>
                            <option value="true">Ativo</option>
                            <option value="false">Inativo</option>
                        </TomSelect>
                    </div>
                </div>
                <!-- END: Filtros -->
                <div class="box-content w-full relative sm:mt-2">
                    <div class="border border-slate-200/60 w-full rounded-t-xl overflow-x-auto">
                        <!-- BEGIN: Ações em lote -->
                        <div
                            class="absolute top-0 right-0 w-[calc(100%-50px)] h-[2.8rem] backdrop-blur-[5px] bg-gray-200/60 dark:bg-darkmode-800 rounded-tr-xl flex justify-between items-center z-50"
                            :class="{
                                'animate-fade-in-left': checkAll || campanhasSelecionadas.length > 0,
                                'animate-fade-out-left': !checkAll && campanhasSelecionadas.length < 1,
                            }"
                        >
                            <h3 class="text-lg font-medium text-slate-500 ml-4 opacity-0 md:opacity-0">Menu</h3>

                            <div class="flex items-center gap-2 mr-2">
                                <button
                                    class="btn btn-danger-soft bg-transparent border-none shadow-none"
                                    @click="excluirCampanhas(true)"
                                >
                                    <Trash2Icon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                    <span class="text-ellipsis whitespace-nowrap overflow-hidden">Excluir</span>
                                </button>
                            </div>
                        </div>
                        <!-- END: Ações em lote -->

                        <!-- BEGIN: Tabela -->
                        <table class="table custom-table intro-y rounded-xl">
                            <thead v-if="listaCampanhas.length < 1" class="bg-gray-200/60 dark:bg-darkmode-800">
                                <tr>
                                    <th class="">Lista vazia...</th>
                                </tr>
                            </thead>
                            <thead class="relative" v-else>
                                <tr>
                                    <th
                                        class="w-[20px] whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 rounded-tl-xl"
                                    >
                                        <!-- CHECKBOX -->
                                        <div class="form-check w-[20px]">
                                            <input
                                                type="checkbox"
                                                class="form-check-input"
                                                id="check-all"
                                                v-model="checkAll"
                                                @change="(e) => selcionarCampanhas(true, e.target.checked)"
                                            />
                                        </div>
                                        <!-- END CHECKBOX -->
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        Nome
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        Tipo
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        Status
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        Período
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        URL
                                    </th>
                                    <th
                                        class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 rounded-tr-xl w-[140px]"
                                    >
                                        Ações
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="listaCampanhas.length < 1" class="text-center">
                                    <td colspan="7" class="py-4 text-slate-500">
                                        Nenhuma campanha encontrada com os filtros aplicados.
                                    </td>
                                </tr>
                                <tr
                                    v-else
                                    v-for="campanha in listaCampanhas"
                                    :key="campanha.cd_campanha"
                                    class="even:bg-slate-100/30 odd:bg-white hover:bg-slate-200/50 transition-all duration-200"
                                >
                                    <td>
                                        <!-- CHECKBOX -->
                                        <div class="form-check w-[20px] h-full">
                                            <input
                                                type="checkbox"
                                                class="form-check-input"
                                                :id="'check-' + campanha.cd_campanha"
                                                v-model="campanha.checked"
                                                @change="selcionarCampanhas(false)"
                                            />
                                            <label
                                                class="form-check-label"
                                                :for="'check-' + campanha.cd_campanha"
                                            ></label>
                                        </div>
                                        <!-- END CHECKBOX -->
                                    </td>
                                    <td>
                                        <div class="text-slate-700 font-medium text-sm">
                                            {{ campanha.ds_campanha }}
                                        </div>
                                        <div class="text-slate-500 text-xs mt-1" v-if="campanha.ds_descricao">
                                            {{ campanha.ds_descricao.substring(0, 50)
                                            }}{{ campanha.ds_descricao.length > 50 ? '...' : '' }}
                                        </div>
                                    </td>
                                    <td>
                                        <span
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                            :class="getBadgeClass(campanha.tp_campanha)"
                                        >
                                            {{ formatarTipo(campanha.tp_campanha) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                            :class="
                                                campanha.in_ativo
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            "
                                        >
                                            {{ campanha.in_ativo ? 'Ativo' : 'Inativo' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-slate-500 text-xs">
                                            <div v-if="campanha.dt_inicio">
                                                <strong>Início:</strong>
                                                {{ converters.date('DD/MM/YYYY', campanha.dt_inicio) }}
                                            </div>
                                            <div v-if="campanha.dt_fim">
                                                <strong>Fim:</strong>
                                                {{ converters.date('DD/MM/YYYY', campanha.dt_fim) }}
                                            </div>
                                            <div v-if="!campanha.dt_inicio && !campanha.dt_fim">
                                                <span class="text-slate-400">Não definido</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-slate-500 text-xs flex items-center gap-2">
                                            <span
                                                v-if="campanha.ds_url"
                                                class="break-all select-all bg-gray-100 px-2 py-1 rounded cursor-pointer"
                                                @click="copyToClipboard(campanha.ds_url)"
                                            >
                                                {{ campanha.ds_url.substring(0, 30)
                                                }}{{ campanha.ds_url.length > 30 ? '...' : '' }}
                                            </span>
                                            <button
                                                v-if="campanha.ds_url"
                                                @click="copyToClipboard(campanha.ds_url)"
                                                class="ml-1 p-1 rounded hover:bg-gray-200"
                                                :title="'Copiar URL'"
                                            >
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    class="h-4 w-4 text-slate-500"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8 16h8a2 2 0 002-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v6a2 2 0 002 2zm0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2"
                                                    />
                                                </svg>
                                            </button>
                                            <span v-else class="text-slate-400">Não informado</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button
                                            class="btn btn-sm btn-dark-soft bg-transparent border-none shadow-none text-slate-500 hover:text-slate-700"
                                            @click="acionarEdicao(campanha)"
                                        >
                                            <PenBoxIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                            <span class="text-ellipsis whitespace-nowrap overflow-hidden">Editar</span>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- END: Tabela -->
                    </div>
                    <!-- BEGIN: Paginação -->
                    <div
                        class="w-full p-4 flex justify-between items-center border border-slate-200/60 border-t-0 rounded-b-xl"
                    >
                        <div class="">
                            <TomSelect
                                v-model="filtros.limit"
                                :options="{}"
                                size="small"
                                class="w-full !cursor-pointer"
                                @change="
                                    () => {
                                        carregarListaCampanhas();
                                    }
                                "
                            >
                                <option v-for="item in listaItensPorPagina" :key="item" :value="item">
                                    {{ item }}
                                </option>
                            </TomSelect>
                        </div>
                        <div class="flex justify-center items-center">
                            <button
                                class="btn btn-sm btn-secondary-soft border-none shadow-none"
                                @click="
                                    filtros.page = filtros.page - 1;
                                    carregarListaCampanhas();
                                "
                                :disabled="filtros.page <= 1"
                            >
                                <ChevronLeftIcon class="w-4 h-4" />
                            </button>
                            <div class="mx-2 text-sm text-slate-500">
                                {{ filtros.page }}
                            </div>
                            <button
                                class="btn btn-sm btn-secondary-soft border-none shadow-none"
                                @click="
                                    filtros.page = filtros.page + 1;
                                    carregarListaCampanhas();
                                "
                                :disabled="listaCampanhas.length < filtros.limit"
                            >
                                <ChevronRightIcon class="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                    <!-- END: Paginação -->
                    <cite class="text-xs text-slate-500 px-4 py-2 self-start">
                        <span>*</span> Selecione as campanhas para editar ou excluir.
                    </cite>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal confirma exclusão -->
    <Modal :show="modalExcluir" @hidden="modalExcluir = false">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center gap-4">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center mt-6">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Excluir campanhas?</div>
            <div class="text-slate-500 mt-2">
                <p class="text-sm">Você está prestes a excluir as campanhas selecionadas.</p>
                <p class="text-sm font-semibold">Esta ação não pode ser desfeita!</p>
                <p class="text-sm">{{ campanhasSelecionadas.length }} campanha(s) selecionada(s).</p>
                <p class="text-sm font-semibold">Deseja realmente excluir?</p>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="modalExcluir = false" class="btn btn-outline-secondary w-24 mr-2">Não</button>
            <button type="button" class="btn btn-danger w-24" @click="excluirCampanhas(false)">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma exclusão -->

    <!-- Modal Gaveta de campanha -->
    <gavetaCadCampanha
        :estado="gavetaCampanha.estado"
        :dados="gavetaCampanha.dados"
        @update:estado="
            (estado) => {
                gavetaCampanha.estado = estado;
                if (!estado) {
                    gavetaCampanha.dados = {};
                }
            }
        "
        @response:success="carregarListaCampanhas()"
    />
    <!-- END: Gaveta de campanha -->
</template>

<script setup>
    import { nextTick, onMounted, ref } from 'vue';
    import notifications from '@/components/show-notifications/Main.vue';
    import CampanhasServices from '@/services/crm/CampanhasServices';
    import gavetaCadCampanha from './gavetaCadCampanha.vue';
    import { debounce } from 'lodash';
    import { MegaphoneIcon } from 'lucide-vue-next';
    import converters from '../../utils/converters';

    const showNotifications = ref();
    const loading = ref();
    const modalExcluir = ref(false);
    const gavetaCampanha = ref({
        estado: false,
        dados: {},
    });
    const checkAll = ref(false);
    const campanhasSelecionadas = ref([]);
    const estabelecimentosLiberado = ref([]);
    const listaCampanhas = ref([]);
    const listaItensPorPagina = ref([10, 25, 50, 100]);
    const filtros = ref({
        ds_campanha: '',
        tp_campanha: '',
        in_ativo: '',
        search: '',
        limit: listaItensPorPagina.value[0],
        page: 1,
    });

    function limparFiltros() {
        filtros.value = {
            ds_campanha: '',
            tp_campanha: '',
            in_ativo: '',
            search: '',
            limit: listaItensPorPagina.value[0],
            page: 1,
        };
        carregarListaCampanhas();
    }

    async function selcionarCampanhas(all, value) {
        if (all) {
            listaCampanhas.value.forEach((campanha) => {
                campanha.checked = value;
            });
            checkAll.value = value;
            campanhasSelecionadas.value = value ? listaCampanhas.value.filter((campanha) => campanha.checked) : [];
        } else {
            checkAll.value = listaCampanhas.value.every((campanha) => campanha.checked) ? true : false;
            campanhasSelecionadas.value = listaCampanhas.value.filter((campanha) => campanha.checked);
        }
    }

    function acionarEdicao(campanha) {
        delete campanha.checked;
        gavetaCampanha.value.dados = { ...campanha };
        gavetaCampanha.value.estado = true;
    }

    const pesquisarFiltro = debounce(async () => {
        filtros.value.page = 1;
        filtros.value.search = filtros.value.search?.trim();
        await carregarListaCampanhas();
    }, 500);

    function formatarTipo(tipo) {
        const tipos = {
            email: 'E-mail',
            whatsapp: 'WhatsApp',
            sms: 'SMS',
            promocional: 'Promocional',
            informativa: 'Informativa',
        };
        return tipos[tipo] || tipo;
    }

    function getBadgeClass(tipo) {
        const classes = {
            email: 'bg-blue-100 text-blue-800',
            whatsapp: 'bg-green-100 text-green-800',
            sms: 'bg-yellow-100 text-yellow-800',
            promocional: 'bg-purple-100 text-purple-800',
            informativa: 'bg-gray-100 text-gray-800',
        };
        return classes[tipo] || 'bg-gray-100 text-gray-800';
    }

    async function excluirCampanhas(abrirModal = false) {
        if (campanhasSelecionadas.value.length === 0) {
            showNotifications.value.showWarningNotification('Nenhuma campanha selecionada para exclusão.');
            return;
        }
        if (abrirModal) {
            modalExcluir.value = true;
            return;
        }
        try {
            loading.value.show();
            const campanhas = campanhasSelecionadas.value;
            const exclusoes = [];
            const erros = [];

            for await (const campanha of campanhas) {
                const dados = {
                    cd_campanha: campanha.cd_campanha,
                    cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
                };
                const result = await CampanhasServices.excluir(dados);
                if (result.statuscode != 200) {
                    erros.push(campanha);
                    continue;
                }
                exclusoes.push(campanha);
            }

            if (erros.length > 0) {
                showNotifications.value.showErrorNotification(
                    `Não foi possível excluir as campanhas: ${erros.map((c) => c.ds_campanha).join(', ')}`
                );
            } else {
                showNotifications.value.showSuccessNotification(
                    campanhasSelecionadas.value.length + ' campanha(s) excluída(s) com sucesso!'
                );
                await carregarListaCampanhas();
            }
        } catch (error) {
            showNotifications.value.showErrorNotification('Erro ao excluir campanhas: ' + error.message);
        } finally {
            campanhasSelecionadas.value = [];
            checkAll.value = false;
            modalExcluir.value = false;
            loading.value.hide(500);
        }
    }

    async function carregarListaCampanhas() {
        listaCampanhas.value = [];
        campanhasSelecionadas.value = [];
        checkAll.value = false;
        await nextTick();
        loading.value.show();
        try {
            const filtrosConfigurados = {
                ...filtros.value,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            };

            // Remove campos vazios
            for (const key in filtrosConfigurados) {
                if (filtrosConfigurados[key] === '') {
                    delete filtrosConfigurados[key];
                }
            }

            const response = await CampanhasServices.listar(filtrosConfigurados);
            // console.log('🚀 ~ listaCampanhas.vue:526 ~ carregarListaCampanhas ~ response:', response);
            listaCampanhas.value =
                response.data?.map((campanha) => {
                    return {
                        ...campanha,
                        checked: false, // Adiciona a propriedade checked para o checkbox
                    };
                }) || [];
        } catch (error) {
            showNotifications.value.showErrorNotification('Erro ao carregar campanhas: ' + error.message);
        } finally {
            loading.value.hide(500);
        }
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text);
        showNotifications.value.showSuccessNotification('URL copiada!');
    }

    onMounted(async () => {
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);
        } else {
            showNotifications.value.showWarningNotification('Estabelecimento não liberado!');
        }
        await carregarListaCampanhas();
    });
</script>

<style scoped>
    .custom-table {
        border-collapse: separate !important;
        border-spacing: 0 0 !important;
    }
    .custom-table th,
    .custom-table td {
        padding: 12px;
        text-align: left;
    }
    .custom-table {
        border-bottom: none !important;
    }

    ::v-deep(.ts-dropdown) {
        z-index: 10000 !important;
    }
    ::v-deep(.multiselect-clear) {
        display: none !important;
        width: 0 !important;
    }
    ::v-deep(.multiselect-single-label) {
        padding-right: 28px !important;
    }
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-input) {
        cursor: pointer !important;
    }
</style>
