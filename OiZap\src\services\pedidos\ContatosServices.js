import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';
//const host = `${hosts.api}/admin/usuario`;
const hostApi = hosts.apiOiZap + '/contatos';

class ClientesServices {
    async incluir(req) {
        try {
            const response = await callApi('post', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async alterar(req) {
        try {
            const response = await callApi('put', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async remover(req) {
        try {
            const response = await callApi('delete', `${hostApi}/v1`, undefined, req);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    async listar(req) {
        try {
            const response = await callApi('get', `${hostApi}/v1`, req, undefined);

            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    }
}

export default new ClientesServices();
