require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';

export class MotivosAtendimentoDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['motivos_atendimento'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      // console.log('🚀 ~ MotivosAtendimentoDB.ts:16 ~ incluir ~ req.body:', req.body);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select * from motivos_atendimento where 1=1`;
      if (req.query.cd_estabelecimento) {
        xSQL += ` and cd_estabelecimento = ${req.query.cd_estabelecimento}`;
      }
      if (req.query.ds_motivo) {
        xSQL += ` and ds_motivo like '%${req.query.ds_motivo}%'`;
      }
      if (req.query.in_ativo !== undefined) {
        xSQL += ` and in_ativo = ${req.query.in_ativo}`;
      }
      xSQL += ` order by ds_motivo`;

      return await new PostgreSQLServices().query(xSQL);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['motivos_atendimento'],
        chaves: { cd_motivo: req.body.cd_motivo, cd_estabelecimento: req.body.cd_estabelecimento },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async excluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['motivos_atendimento'],
        chaves: { cd_motivo: req.body.cd_motivo, cd_estabelecimento: req.body.cd_estabelecimento },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
