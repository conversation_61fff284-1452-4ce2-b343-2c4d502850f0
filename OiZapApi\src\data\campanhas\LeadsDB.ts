import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';

export class LeadsDB {
  static async criarLead(data: any) {
    const opDb: OperationObject = {
      operacao: 'insert',
      tabelas: ['leads'],
      dados: data,
      retorno: '*',
    };
    return await new PostgreSQLServices().executar(opDb);
  }

  static async listarLeads(data: any) {
    const opDb: OperationObject = {
      operacao: 'select',
      tabelas: ['leads'],
      chaves: data,
      retorno: '*',
    };

    return await new PostgreSQLServices().executar(opDb);
  }
}
