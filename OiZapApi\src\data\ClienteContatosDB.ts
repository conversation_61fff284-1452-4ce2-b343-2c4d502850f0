require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';

const logger = Logger.getLogger();
export class ClienteContatosDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['cliente_contatos'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      const resp = await new PostgreSQLServices().executar(opDb);
      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['cliente_contatos'],
        chaves: {
          cd_cliente: req.body.cd_cliente,
          cd_contato: req.body.cd_contato,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `select * from cliente_contatos where  cd_estabelecimento = ${req.query.cd_estabelecimento} `;

      if (req.query.cd_cliente) {
        sql += ` and cd_cliente = ${req.query.cd_cliente} `;
      }

      if (req.query.cd_contato) {
        sql += ` and cd_contato = ${req.query.cd_contato} `;
      }

      // console.log('🚀 ~ ContatosDB.ts:69 ~ listar ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['cliente_contatos'],
        chaves: {
          cd_cliente: req.body.cd_cliente,
          cd_contato: req.body.cd_contato,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
