import { PerfilModel } from '../../models/perfis/PerfilModel';

export class PerfilDB {
  private model = new PerfilModel();

  listarPerfis() {
    return this.model.listarPerfis();
  }
  criarPerfil(data: any) {
    return this.model.criarPerfil(data);
  }
  atualizarPerfil(cd_perfil: number, data: any) {
    return this.model.atualizarPerfil(cd_perfil, data);
  }
  deletarPerfil(cd_perfil: number) {
    return this.model.deletarPerfil(cd_perfil);
  }
} 