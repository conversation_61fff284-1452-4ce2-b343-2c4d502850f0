require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';
import { WhatsServices } from '../services/WhatsServices';
const logger = Logger.getLogger();
export class MessagesDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['messages'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      // console.log(req.body);
      const resp = await new PostgreSQLServices().executar(opDb);
      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarUltimaMensagemContato(req: Request): Promise<IRetorno> {
    try {
      // Definir limite e offset com valores padrão caso não sejam passados na query string
      const limit = parseInt(req.query.limit as string) || 20; // Valor padrão de 20 se não for passado
      const offset = parseInt(req.query.offset as string) || 0; // Começa do primeiro registro (página 1)
      const urlNotFound = process.env.URL_PADRAO_IMAGEM_NOTFOUND;

      let sql = `select --l.*
       l.instance,l.nome,mensagem,message_timestamp,l.in_delete
       ,l.telefone,coalesce(url_profile_picture,'${urlNotFound}') url_profile_picture,hash,l.cd_atendimento,idunico,l.cd_estabelecimento
     -- ,to_char(l.message_timestamp,'HH24:mi') horario 
      ,CASE
        WHEN DATE(message_timestamp) = DATE(NOW()) THEN TO_CHAR(message_timestamp, 'HH24:MI') -- Hoje: mostra apenas o horário
        WHEN DATE(message_timestamp) = DATE(NOW() - INTERVAL '1 day') THEN 'Ontem' -- Ontem: mostra "Ontem"
        WHEN DATE(message_timestamp) >= DATE(NOW() - INTERVAL '7 days') THEN get_dia_semana_portugues(message_timestamp) -- Últimos 7 dias: mostra o dia da semana em português
        ELSE TO_CHAR(message_timestamp, 'DD/MM/YYYY') -- Mais de 7 dias: mostra a data completa
    END AS horario
,a.ds_hash,a.tp_status,a.tp_situacao,a.tp_etapachat,a.in_stop_bot
,(select count(*) from messages m where m.instance=l.instance and m.telefone=l.telefone
 and m.date_read is null and m.from_me = false and m.cd_estabelecimento = l.cd_estabelecimento) qt_messages_notread
,c.cd_cliente,i.nome nameinstance,ei.nr_hash,c.ds_obscliente
,(select json_agg(pedidos) from (select cd_pedido,tp_situacao,dt_importado from pedidos p1 where p1.cd_atendimento =l.cd_atendimento and p1.cd_estabelecimento = l.cd_estabelecimento) pedidos) pedidos
,c.ds_nome,c.ds_endereco,c.nr_endereco ,c.ds_bairro ,c.ds_complemento 
,c.ds_cidade ,c.ds_uf ,c.ds_pais,c.ds_estado,a.in_acessoulink 
,(select jsonb_agg(pedidos) from (select cd_pedido,tp_situacao,p1.vl_total
  from pedidos p1 where p1.cd_atendimento =l.cd_atendimento and p1.tp_situacao = 'Em Pedido' and p1.cd_estabelecimento = l.cd_estabelecimento) pedidos) pedido_aberto
, (case when (a.tp_status in ('Em Atendimento','Em Pedido', 'Em Pagamento') and (EXTRACT(EPOCH FROM (NOW() - message_timestamp)) / 60) > 10) then 10
        when (a.tp_status in ('Em Atendimento','Em Pedido', 'Em Pagamento') and (EXTRACT(EPOCH FROM (NOW() - message_timestamp)) / 60) > 5) then 5 else 0 end) AS minutos_atendimento  
,(select max(dt_importado) dt_importado from pedidos p1 where p1.cd_estabelecimento = l.cd_estabelecimento and dt_importado is not null)  dt_ultimaimportacao
,a.cd_departamento,d.ds_departamento,a.cd_atendente,au.ds_nome nm_atendente,a.ds_comentario
,a.cd_campanha,ca.ds_campanha,ca.tp_campanha
from last_message l 
left join atendimentos a on a.cd_atendimento = l.cd_atendimento and a.cd_estabelecimento = l.cd_estabelecimento
left join departamentos d on d.cd_departamento = a.cd_departamento and d.cd_estabelecimento = a.cd_estabelecimento
left join adm_usuarios au on au.cd_usuario = a.cd_atendente 
left join instances i on i.nameinstance = a.instance
left join estabelecimento_instancias ei on ei.id_instancia = i.id
left join clientes c on c.cd_cliente = a.cd_cliente and c.cd_estabelecimento = ei.cd_estabelecimento
left join campanhas ca on ca.cd_campanha = a.cd_campanha and ca.cd_estabelecimento = a.cd_estabelecimento
where l.instance in (${req.query.instance}) `;
      //and cast(message_timestamp as date) >= current_date-15

      /*
      if (req.query.tp_situacao == 'Em Atendimento') {
        sql += ` and a.tp_status not in ('Pedido Cancelado','Pedido Entregue','Atendimento Cancelado','Atendimento Finalizado') `;
      } else {
        sql += ` and a.tp_status in ('Pedido Entregue','Atendimento Finalizado','Pedido Cancelado','Atendimento Cancelado') `;
      }
        */
      if (req.query.nm_contato != undefined && req.query.nm_contato != '') {
        sql += `and upper(l.nome) like '%${req.query.nm_contato}%'`;
      }
      if (req.query.tp_situacao != undefined && req.query.tp_situacao != '') {
        sql += `and (a.tp_status in (${req.query.tp_situacao})  or a.tp_situacao  in (${req.query.tp_situacao})  or a.tp_etapachat  in (${req.query.tp_situacao})  )`;
        sql += ` and a.tp_status not in ('Atendimento Cancelado') `;
      }
      if (req.query.tp_status != undefined && req.query.tp_status != '') {
        sql += `and ( a.tp_status in (${req.query.tp_status})  or a.tp_etapachat  in (${req.query.tp_status})  )  `;
        sql += ` and a.tp_status not in ('Atendimento Cancelado') `;
      }

      if (req.query.departamentos != undefined && req.query.departamentos != '') {
        sql += ` and a.cd_departamento in (${req.query.departamentos}) `;
      }

      if (req.query.atendentes != undefined && req.query.atendentes != '') {
        sql += ` and a.cd_atendente in (${req.query.atendentes}) `;
      }

      if (req.query.in_mensagens_naolidas == 'true') {
        sql += ` and a.cd_atendimento in (select mm.cd_atendimento from messages mm where mm.instance in (${req.query.instance})
          and mm.date_read is null and mm.from_me = false) `;
      }
      //sql += ` order by l.instance,l.message_timestamp desc`;
      // Adicionar a ordenação e a paginação
      sql += `
      ORDER BY l.instance, l.message_timestamp DESC
      LIMIT ${limit}
      OFFSET ${offset};
    `;
      logger.debug('listarUltimaMensagemContato > sql \n' + sql);
      //console.log('🚀 ~ MessagesDB.ts:105 ~ listarUltimaMensagemContato ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);
      //  console.log('result:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaQtMensagensNaoLidas(req: Request): Promise<IRetorno> {
    try {
      // ,sum((case when (tp_status = 'Em Atendimento' and qt_messages_notread > 0 ) then 1 else 0 end)) qt_ematendimento
      // ,sum((case when (tp_status <> 'Em Atendimento' and qt_messages_notread > 0 ) then 1 else 0 end)) qt_finalizado

      //,(case when a1.tp_status in ('Em Atendimento', 'Em Pedido', 'Em Pagamento') then 'Em Atendimento' else 'Finalizados' end) tp_status
      let sql = `select instance
                ,sum((case when (tp_status = 'Em Atendimento') then qt_messages_notread else 0 end)) qt_ematendimento
                ,sum((case when (tp_status = 'Em Andamento') then qt_messages_notread else 0 end)) qt_emandamento
                ,sum((case when (tp_status = 'Finalizados') then qt_messages_notread else 0 end)) qt_finalizado
          from (
          select l1.instance,l1.telefone,l1.cd_estabelecimento
          ,(case when a1.tp_status in ('Em Atendimento', 'Em Pedido', 'Em Pagamento','Falar com Atendente','Pedido Realizado') then 'Em Atendimento'
                  when a1.tp_status in ('Pedido Confirmado','Pedido Pronto','Saiu para Entrega') then 'Em Andamento' else 'Finalizados' end) tp_status
          ,(select count(*) from messages m1 where m1.instance=l1.instance and m1.telefone=l1.telefone
          and m1.date_read is null and m1.from_me = false and m1.cd_estabelecimento = l1.cd_estabelecimento) qt_messages_notread
          from last_message l1
          left join atendimentos a1 on a1.cd_atendimento = l1.cd_atendimento and a1.cd_estabelecimento = l1.cd_estabelecimento
          where l1.instance in (${req.query.instance})
          ) as z group by instance `;
      // --and cast(l1.message_timestamp as date) >= current_date-3

      //logger.debug('listarUltimaMensagemContato > sql \n' + sql);
      const result = await new PostgreSQLServices().query(sql);
      //  console.log('result:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaQtAtendimentosNaoLidos(req: Request): Promise<IRetorno> {
    try {
      let sql = `select sum((case when (tp_status = 'Em Atendimento') then qt else 0 end)+qt_falaratendente) qt_ematendimento
,sum((case when (tp_status = 'Em Andamento') then qt else 0 end)) qt_emandamento
,sum((case when (tp_status_crm = 'Fila') then qt else 0 end)+qt_falaratendente) qt_fila_crm
,sum((case when (tp_status_crm = 'Em Andamento') then qt else 0 end)) qt_emandamento_crm
,sum((case when (tp_status_crm = 'Finalizados') then qt else 0 end)) qt_finalizados_crm
from (
      select *,1 qt from (    
                  select l1.instance,l1.telefone,l1.cd_estabelecimento,a1.tp_etapachat
                  ,(case  when a1.tp_status in ('Em Atendimento', 'Em Pedido', 'Em Pagamento','Falar com Atendente','Pedido Realizado') then 'Em Atendimento'
                          when a1.tp_status in ('Pedido Confirmado','Pedido Pronto','Saiu para Entrega') then 'Em Andamento' else 'Finalizados' end) tp_status
                   ,(case when a1.tp_etapachat in ('Falar com Atendente') then 1
                           else 0 end) qt_falaratendente
                     ,(case  when a1.tp_status in ('Fila','Transferido') then 'Fila'                        
                          when a1.tp_status in ('Em Atendimento','Em Pedido','Em Pagamento','Falar com Atendente') then 'Em Andamento' else 'Finalizados' end) tp_status_crm        
                  ,(select count(*) from messages m1 where m1.instance=l1.instance and m1.telefone=l1.telefone
                  and m1.date_read is null and m1.from_me = false and m1.cd_estabelecimento = l1.cd_estabelecimento) qt_messages_notread
                  from last_message l1
                  left join atendimentos a1 on a1.cd_atendimento = l1.cd_atendimento and a1.cd_estabelecimento = l1.cd_estabelecimento
                  where l1.instance in (${req.query.instance}) and a1.tp_status  not in ('Atendimento Finalizado')                  
      ) as z1 where z1.qt_messages_notread > 0
 ) as z `;
      // --and cast(l1.message_timestamp as date) >= current_date-3

      //logger.debug('listaQtAtendimentosNaoLidos > sql \n' + sql);
      const result = await new PostgreSQLServices().query(sql);
      //  console.log('result:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listMessages(req: Request): Promise<IRetorno> {
    try {
      const URL_ARQUIVOS = process.env.URL_ARQUIVOS;

      let telefone: string = '';
      telefone = req.query.telefone as string;
      let telefoneSemDDD = telefone.slice(2);
      let DDD = telefone.substr(0, 2);
      //console.log('telefoneSemDDD at line 13 in data/ClientesDB.ts:', telefoneSemDDD);
      let telefoneSem9DDD = telefoneSemDDD.slice(1);
      let telefoneSem9 = DDD + telefoneSem9DDD;
      //console.log('telefoneSem9 at line 15 in data/ClientesDB.ts:', telefoneSem9);

      //sql += `and (nr_telefonezap  = '${req.query.nr_telefonezap}' or nr_telefonezap  = '${telefoneSemDDD}')`;

      if (req.query.readMessages) {
        req.body = {
          instance: req.query.instance,
          telefone: telefone,
          telefoneSemDDD: telefoneSemDDD,
          telefoneSem9: telefoneSem9,
          telefoneSem9DDD: telefoneSem9DDD,
        };
        await this.readMessages(req);
      }

      let sql = `select m.id,m.from_me,coalesce(m.mensagem,'') mensagem,m.message_timestamp,m.message_id,m.type_message
      ,m.mimetype,null ds_base64,m.file_name
      ,m.url_audio,m.address,m.idunico,m.quotedmessage,m.type_message_quoted,m.mimetype_quoted,m.jpeg_thumbnail_quoted,m.file_name_quoted
      ,m.cd_estabelecimento,concat('${URL_ARQUIVOS}',m.url_midia) url_midiacompleta
      ,concat('${URL_ARQUIVOS}',m.url_midia_quoted) url_midia_quotedcompleta
      ,to_char(m.message_timestamp,'HH24:mi') horario
      ,(case when (m.date_read is not null) then 'Lido'
        when (m.date_received is not null) then 'Recebido'
        when (m.date_sent is not null) then 'Enviado'
        else null end) status_message,m.cd_atendimento
        ,mensagem_edited,in_mensagem_edited,message_id_edited
        ,mensagem_delete,in_mensagem_delete,message_id_delete,m.instance
        ,m.cd_atendente,au.ds_nome nm_atendente,m.cd_departamento,d.ds_departamento,m.ds_comentario,m.tp_status_atendimento
        ,m.cd_motivo,ma.ds_motivo,m.ds_resumo,m.ds_comentario
      from messages m 
      left join adm_usuarios au on au.cd_usuario = m.cd_atendente
      left join departamentos d on d.cd_departamento = m.cd_departamento and d.cd_estabelecimento = m.cd_estabelecimento
      left join motivos_atendimento ma on ma.cd_motivo = m.cd_motivo and ma.cd_estabelecimento = m.cd_estabelecimento
       where m.instance in ('${req.query.instance}')       
       and (m.telefone  = '${telefone}' or m.telefone  = '${telefoneSemDDD}' or m.telefone  = '${telefoneSem9}' or m.telefone  = '${telefoneSem9}' or m.telefone  = '${telefoneSem9DDD}')  `;

      if (req.query.cd_atendimento) {
        sql += ` and m.cd_atendimento in (${req.query.cd_atendimento})`;
      } else {
        sql += ` and cast(m.message_timestamp as date) >= current_date-5 `;
      }
      sql += ` order by m.message_timestamp`;
      //--and a.tp_situacao in ('Em Atendimento')
      // and m.telefone ='${req.query.telefone}'

      //left join atendimentos a on a.cd_atendimento = m.cd_atendimento
      //--and a.tp_situacao in ('Em Atendimento')

      //logger.info('sql at line 42 in data/MessagesDB.ts:' + sql);
      // logger.debug('MessagesDB.ts > listMessages : ' + sql);
      const result = await new PostgreSQLServices().query(sql);
      //logger.debug('MessagesDB.ts > listMessages > result: ' + JSON.stringify(result));
      //console.log('result:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async readMessages(req: Request): Promise<IRetorno> {
    try {
      let sql = `update messages set date_read = current_timestamp 
      where instance ='${req.body.instance}' 
      and (telefone  = '${req.body.telefone}' or telefone  = '${req.body.telefoneSemDDD}' or telefone  = '${req.body.telefoneSem9}' or telefone  = '${req.body.telefoneSem9}' or telefone  = '${req.body.telefoneSem9DDD}')  
       and date_read is null and from_me = false`;
      //and telefone ='${req.body.telefone}'
      //logger.debug('readMessages > sql: ' + sql);
      // logger.info('sql at line 154 in data/MessagesDB.ts:' + sql);
      const result = await new PostgreSQLServices().query(sql);
      // logger.debug('readMessages > result:' + JSON.stringify(result));
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async reenviarMensagens(req: Request): Promise<IRetorno> {
    try {
      // console.log('reenviarMensagens > req.body:', req.body);
      const ids = req.body.ids;

      let sql = `select instance,ds_json from messages  where id in (${ids.join(',')}) `;
      let result = await new PostgreSQLServices().query(sql);
      if (result.statuscode == 200) {
        for (const element of result.data) {
          req.body = {
            serverName: 'oizap',
            instanceName: element.instance,
            queueType: 'oizap.sendmessage',
            message: JSON.parse(element.ds_json),
          };

          WhatsServices.publishToQueue(req);
        }
      }

      sql = `update messages set tp_status = 'Fila', ds_log = null, dt_enviado_rabbitmq = current_timestamp
      where id in (${ids.join(',')}) `;
      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      result = await new PostgreSQLServices().query(sql);
      // console.log('result at line 246 in data/MessagesDB.ts:', result);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarRelatorioMensagens(req: Request): Promise<IRetorno> {
    try {
      // let sql = `select DISTINCT  type_message from messages m where 1=1 `;
      let sql = `select m.id,m.cd_estabelecimento,coalesce(i.nome,m.instance) instance ,m.telefone,m.mensagem
      ,m.message_timestamp,m.date_sent,m.date_received,m.date_read,m.tp_status,m.ds_log,m.mensagem
      from messages m 
      left join instances i on i.nameinstance = m."instance" 
      where 1=1 `;

      if (req.query.cd_estabelecimento) {
        sql += ` and m.cd_estabelecimento in (${req.query.cd_estabelecimento})`;
      }
      if (req.query.dt_enviado_inicial && req.query.dt_enviado_final) {
        sql += ` and m.message_timestamp::date between '${req.query.dt_enviado_inicial}' and '${req.query.dt_enviado_final}'`;
      }
      if (req.query.dt_recebido_inicial && req.query.dt_recebido_final) {
        sql += ` and m.date_received::date between '${req.query.dt_recebido_inicial}' and '${req.query.dt_recebido_final}'`;
      }
      if (req.query.dt_lido_inicial && req.query.dt_lido_final) {
        sql += ` and m.date_read::date between '${req.query.dt_lido_inicial}' and '${req.query.dt_lido_final}'`;
      }

      if (req.query.instance) {
        sql += ` and m.instance like ('%${req.query.instance}%')`;
      }
      if (req.query.tp_status) {
        if (req.query.tp_status == 'Enviado') {
          sql += ` and (m.tp_status = 'Lido' or m.tp_status = 'Recebido')`;
        } else if (req.query.tp_status == 'Não Enviado') {
          sql += ` and m.tp_status != 'Lido' and m.tp_status != 'Recebido'`;
        } else if (req.query.tp_status == 'Erro') {
          sql += ` and m.tp_status = 'Erro'`;
        } else {
          sql += ` and m.tp_status = '${req.query.tp_status}'`;
        }
      }
      if (req.query.telefone) {
        sql += ` and m.telefone like ('%${req.query.telefone}%')`;
      }
      if (req.query.from_me) {
        // SIM ou NAO
        sql += ` and m.from_me = ${req.query.from_me == 'SIM' ? 'true' : 'false'}`;
      }

      sql += ` order by m.message_timestamp desc`;

      // if (req.query.order) {
      //   sql += ` ${req.query.order}`;
      // }

      // if (req.query.limit && req.query.page) {
      //   const offsetPage = (parseInt(req.query.page as string) - 1) * parseInt(req.query.limit as string);
      //   sql += ` limit ${req.query.limit} offset ${offsetPage}`;
      // }

      // console.log('sql at line 272 in data/MessagesDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaMensagensHora(req: Request): Promise<IRetorno> {
    try {
      let sql = `select * from (select m.cd_estabelecimento,e.nm_estabelecimento,to_char(m.message_timestamp,'HH24') hora,count(*) qt_total
from messages m, estabelecimento e 
where e.cd_estabelecimento = m.cd_estabelecimento  `;

      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and m.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.dt_inicial != undefined) {
        sql += `and cast(m.message_timestamp as date) between ('${req.query.dt_inicial}') and ('${req.query.dt_final}') `;
      }

      sql += ` group by  m.cd_estabelecimento,e.nm_estabelecimento,to_char(m.message_timestamp,'HH24')
 ) as z order by qt_total desc `;

      const result = await new PostgreSQLServices().query(sql);
      // console.log('result at line 237 in data/MessagesDB.ts:', result);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async contarRelatorioMensagens(req: Request): Promise<IRetorno> {
    try {
      let sql = `select 
                  count(*) filter (where m.tp_status = 'Lido' or m.tp_status = 'Recebido') as total_enviado,
                  count(*) filter (where m.tp_status = 'Fila') as total_nao_enviado,
                  count(*) filter (where m.message_id is null and m.remote_jid is null and m.date_sent is null) as total_erro
                from messages m where 1=1 `;

      if (req.query.cd_estabelecimento) {
        sql += ` and m.cd_estabelecimento in (${req.query.cd_estabelecimento})`;
      }
      if (req.query.instance) {
        sql += ` and m.instance in ('${req.query.instance}')`;
      }
      if (req.query.dt_enviado_inicial && req.query.dt_enviado_final) {
        sql += ` and m.message_timestamp::date between '${req.query.dt_enviado_inicial}' and '${req.query.dt_enviado_final}'`;
      }
      if (req.query.dt_recebido_inicial && req.query.dt_recebido_final) {
        sql += ` and m.date_received::date between '${req.query.dt_recebido_inicial}' and '${req.query.dt_recebido_final}'`;
      }
      if (req.query.dt_lido_inicial && req.query.dt_lido_final) {
        sql += ` and m.date_read::date between '${req.query.dt_lido_inicial}' and '${req.query.dt_lido_final}'`;
      }

      if (req.query.instance) {
        sql += ` and m.instance like ('%${req.query.instance}%')`;
      }
      // if (req.query.tp_status) {
      //   if (req.query.tp_status == 'Enviado') {
      //     sql += ` and (m.tp_status = 'Lido' or m.tp_status = 'Recebido')`;
      //   } else if (req.query.tp_status == 'Não Enviado') {
      //     sql += ` and m.tp_status != 'Lido' and m.tp_status != 'Recebido'`;
      //   } else {
      //     sql += ` and m.tp_status = '${req.query.tp_status}'`;
      //   }
      // }
      if (req.query.telefone) {
        sql += ` and m.telefone like ('%${req.query.telefone}%')`;
      }
      if (req.query.from_me) {
        // SIM ou NAO
        sql += ` and m.from_me = ${req.query.from_me == 'SIM' ? 'true' : 'false'}`;
      }

      // console.log('sql at line 384 in data/MessagesDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async contarMensagensEnviadas(req: Request): Promise<IRetorno> {
    try {
      let sql = `select 
                    count(*) filter (where m.from_me = true) as enviadas,
                    count(*) filter (where m.from_me != true) as recebidas,
                    count(*) filter (where m.type_message = 'audioMessage') as audio,
                    count(*) filter (where m.type_message = 'contactMessage') as contato,
                    count(*) filter (where m.type_message = 'documentMessage') as documento,
                    count(*) filter (where m.type_message = 'editedMessage') as editado,
                    count(*) filter (where m.type_message = 'imageMessage') as imagem,
                    count(*) filter (where m.type_message = 'listMessage') as lista,
                    count(*) filter (where m.type_message = 'locationMessage') as localizacao,
                    count(*) filter (where m.type_message = 'ptvMessage') as ptv,
                    count(*) filter (where m.type_message = 'reactionMessage') as reacao,
                    count(*) filter (where m.type_message = 'stickerMessage') as sticker,
                    count(*) filter (where m.type_message = 'templateMessage') as template,
                    count(*) filter (where m.type_message = 'textMessage') as texto,
                    count(*) filter (where m.type_message = 'unknown') as desconhecido,
                    count(*) filter (where m.type_message = 'videoMessage') as video
                  from messages m where 1=1 `;

      if (req.query.cd_estabelecimento) {
        sql += ` and m.cd_estabelecimento in (${req.query.cd_estabelecimento})`;
      }
      if (req.query.instance) {
        sql += ` and m.instance in ('${req.query.instance}')`;
      }
      if (req.query.dt_enviado_inicial && req.query.dt_enviado_final) {
        sql += ` and m.message_timestamp::date between '${req.query.dt_enviado_inicial}' and '${req.query.dt_enviado_final}'`;
      }

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async contarMensagensPorPeriodo(req: Request): Promise<IRetorno> {
    try {
      let sql = `select 
                    date(m.message_timestamp) as dia,
                    count(*) as total_mensagens,
                    count(*) filter (where m.from_me = true) as total_enviadas,
                    count(*) filter (where m.from_me != true) as total_recebidas
                  from messages m 
                  where m.message_timestamp >= CURRENT_DATE - INTERVAL '${req.query.periodo} day'
                  and m.from_me = true `;

      if (req.query.cd_estabelecimento) {
        sql += ` and m.cd_estabelecimento in (${req.query.cd_estabelecimento})`;
      }
      if (req.query.instance) {
        sql += ` and m.instance in ('${req.query.instance}')`;
      }

      sql += `group by dia order by dia asc`;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaMensagensData(req: Request): Promise<IRetorno> {
    try {
      let sql = `select * from (select m.cd_estabelecimento,e.nm_estabelecimento,cast(m.message_timestamp as date) data,count(*) qt_total
from messages m, estabelecimento e 
where e.cd_estabelecimento = m.cd_estabelecimento  `;

      if (req.query.cd_estabelecimento != undefined) {
        sql += ` and m.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.dt_inicial != undefined) {
        sql += `and cast(m.message_timestamp as date) between ('${req.query.dt_inicial}') and ('${req.query.dt_final}') `;
      }

      sql += ` group by  m.cd_estabelecimento,e.nm_estabelecimento,cast(m.message_timestamp as date) 
    ) as z order by qt_total  desc `;

      // console.log('sql at line 262 in data/MessagesDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      // logger.debug('result at line 262 in data/MessagesDB.ts:' + JSON.stringify(result));

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async incluiUltimMensagem(req: Request): Promise<IRetorno> {
    try {
      const messageData = req.body;
      let sql = ` INSERT INTO last_message (in_delete,message,event,instance, from_me, mensagem, message_id, remote_jid`;

      if (messageData.cd_atendimento) {
        sql += ',cd_atendimento';
        sql += ',hash';
      }

      if (messageData.message_timestamp) {
        sql += ',message_timestamp';
      }
      if (messageData.url_profile_picture) {
        sql += ',url_profile_picture';
      }
      if (messageData.profile_picture_base64) {
        sql += ',profile_picture_base64';
      }
      if (messageData.idunico) {
        sql += ',idunico';
      }
      if (messageData.cd_estabelecimento) {
        sql += ',cd_estabelecimento';
      }
      if (messageData.telefone) {
        sql += ',telefone';
      }
      let nome = messageData.origem == 'bot' ? messageData.nm_cliente : messageData.nome;
      if (nome == undefined) {
        nome = messageData.nome;
      }
      if (nome == '' || nome == undefined) {
        nome = messageData.nome;
      }
      if (nome != undefined && nome != '') {
        sql += ',nome';
      }

      let mensagem = messageData.mensagem?.substring(0, 200);
      if (mensagem != undefined) mensagem = mensagem.replace(/'/g, '');

      sql += `) VALUES (false,'${mensagem}', '${messageData.event}', '${messageData.instance}'
    , ${messageData.from_me},'${mensagem}', '${messageData.message_id}', '${messageData.remote_jid} '`;

      if (messageData.cd_atendimento) {
        sql += `,'${messageData.cd_atendimento}'`;
        sql += `,'${messageData.ds_hash}'`;
      }
      if (messageData.message_timestamp) {
        sql += `,'${messageData.message_timestamp}' `;
      }
      if (messageData.url_profile_picture) {
        sql += `,'${messageData.url_profile_picture}'`;
      }
      if (messageData.profile_picture_base64) {
        sql += `,'${messageData.profile_picture_base64}'`;
      }
      if (messageData.idunico) {
        sql += `,'${messageData.idunico}'`;
      }
      if (messageData.cd_estabelecimento) {
        sql += `,${messageData.cd_estabelecimento}`;
      }
      if (messageData.telefone) {
        if (messageData?.remote_jid) {
          let remote_jid = messageData?.remote_jid.replace('@s.whatsapp.net', '');
          sql += `,'${remote_jid.trim()}'`;
        } else {
          sql += `,'${messageData.telefone}'`;
        }
      }

      if (nome != undefined && nome != '') {
        sql += `,'${nome.replace(/'/g, '')}'`;
      }

      sql += `) ON CONFLICT (instance,telefone) DO UPDATE SET in_delete=false,
  from_me=${messageData.from_me},mensagem='${messageData.mensagem?.substring(0, 200)}' `;
      if (messageData.message_id) {
        sql += `,message_id='${messageData.message_id}' `;
      }
      if (messageData.message_timestamp) {
        sql += `,message_timestamp='${messageData.message_timestamp}' `;
      }

      if (messageData.url_profile_picture) {
        sql += `,url_profile_picture='${messageData.url_profile_picture}' `;
      }
      if (messageData.profile_picture_base64) {
        sql += `,profile_picture_base64='${messageData.profile_picture_base64}' `;
      }
      if (messageData.idunico) {
        sql += `,idunico='${messageData.idunico}'`;
      }
      if (messageData.cd_estabelecimento) {
        sql += `,cd_estabelecimento='${messageData.cd_estabelecimento}'`;
      }

      if (messageData.cd_atendimento) {
        sql += `,cd_atendimento='${messageData.cd_atendimento}'`;
        sql += `,hash='${messageData.ds_hash}'`;
      }
      if (nome != undefined && nome != '') {
        sql += `,nome='${nome.replace(/'/g, '')}' `;
      }

      // console.log('🚀 ~ MessagesDB.ts:619 ~ incluiUltimMensagem ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);
      //console.log('🚀 ~ MessagesDB.ts:620 ~ incluiUltimMensagem ~ result:', result);
      // logger.debug('result at line 262 in data/MessagesDB.ts:' + JSON.stringify(result));

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
