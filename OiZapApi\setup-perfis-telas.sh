#!/bin/bash

# Script para configurar perfis, telas e associações no OiZap
# Execute este script após o backend estar rodando

echo "🚀 Configurando perfis, telas e associações no OiZap..."

# URL base da API
API_URL="http://localhost:3100"

echo "📋 1. Criando perfis..."

# Criar perfil Administrador
echo "   - Criando perfil Administrador..."
ADMIN_RESPONSE=$(curl -s -X POST "$API_URL/perfis" \
  -H "Content-Type: application/json" \
  -d '{"nm_perfil": "Administrador"}')
echo "   Resposta: $ADMIN_RESPONSE"

# Criar perfil Atendente
echo "   - Criando perfil Atendente..."
ATENDENTE_RESPONSE=$(curl -s -X POST "$API_URL/perfis" \
  -H "Content-Type: application/json" \
  -d '{"nm_perfil": "Atendente"}')
echo "   Resposta: $ATENDENTE_RESPONSE"

# Criar perfil Gerente
echo "   - Criando perfil Gerente..."
GERENTE_RESPONSE=$(curl -s -X POST "$API_URL/perfis" \
  -H "Content-Type: application/json" \
  -d '{"nm_perfil": "Gerente"}')
echo "   Resposta: $GERENTE_RESPONSE"

echo "📋 2. Criando telas administrativas..."

# Criar tela de cadastro de perfil
echo "   - Criando tela Cadastro de Perfil..."
TELA_PERFIL_RESPONSE=$(curl -s -X POST "$API_URL/telas" \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Cadastro de Perfil", "ds_rota": "/cadPerfil"}')
echo "   Resposta: $TELA_PERFIL_RESPONSE"

# Criar tela de cadastro de tela
echo "   - Criando tela Cadastro de Tela..."
TELA_TELA_RESPONSE=$(curl -s -X POST "$API_URL/telas" \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Cadastro de Tela", "ds_rota": "/cadTela"}')
echo "   Resposta: $TELA_TELA_RESPONSE"

# Criar tela de associação perfis x telas
echo "   - Criando tela Associação Perfis x Telas..."
TELA_ASSOCIACAO_RESPONSE=$(curl -s -X POST "$API_URL/telas" \
  -H "Content-Type: application/json" \
  -d '{"nm_tela": "Associação Perfis x Telas", "ds_rota": "/cadPerfisTelas"}')
echo "   Resposta: $TELA_ASSOCIACAO_RESPONSE"

echo "📋 3. Listando perfis criados..."
PERFIS=$(curl -s "$API_URL/perfis")
echo "   Perfis: $PERFIS"

echo "📋 4. Listando telas criadas..."
TELAS=$(curl -s "$API_URL/telas")
echo "   Telas: $TELAS"

echo "📋 5. Associando usuário 87 ao perfil Administrador..."
# Assumindo que o perfil Administrador tem cd_perfil = 1
USUARIO_PERFIL_RESPONSE=$(curl -s -X POST "$API_URL/usuarios/87/perfis" \
  -H "Content-Type: application/json" \
  -d '{"cd_perfil": 1}')
echo "   Resposta: $USUARIO_PERFIL_RESPONSE"

echo "📋 6. Associando telas administrativas ao perfil Administrador..."
# Assumindo que as telas têm cd_tela = 1, 2, 3
PERFIL_TELAS_RESPONSE=$(curl -s -X POST "$API_URL/perfis/associar" \
  -H "Content-Type: application/json" \
  -d '{"cd_perfil": 1, "telas": [1, 2, 3]}')
echo "   Resposta: $PERFIL_TELAS_RESPONSE"

echo "✅ Configuração concluída!"
echo ""
echo "📝 Resumo do que foi criado:"
echo "   - 3 perfis: Administrador, Atendente, Gerente"
echo "   - 3 telas administrativas: /cadPerfil, /cadTela, /cadPerfisTelas"
echo "   - Usuário 87 associado ao perfil Administrador"
echo "   - Telas administrativas associadas ao perfil Administrador"
echo ""
echo "🔗 Agora você pode acessar:"
echo "   - http://localhost:8080/cadPerfil"
echo "   - http://localhost:8080/cadTela"
echo "   - http://localhost:8080/cadPerfisTelas" 