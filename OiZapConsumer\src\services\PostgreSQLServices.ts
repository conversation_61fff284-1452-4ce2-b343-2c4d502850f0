import path from 'path';
import { Pool, QueryResult } from 'pg';
import { IRetorno, conflito, dadosNaoEncontrados, erroInterno, sucesso } from '../interfaces/IRetorno';

import dotenv, { config } from 'dotenv';
import Logger from './Logger';
const logger = Logger.getLogger();
config();
// Carrega o .env da raiz do projeto OiZapConsumer
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

let DATABASE: string;
let PASS: string;
let PORT: string;
let HOST: string;
if (process.env.AMBIENTE == 'PROD') {
  DATABASE = process.env.DATABASE || '';
  PASS = process.env.PASSWORD_DATABASE || '';
  PORT = process.env.PORT_DATABASE || '';
  HOST = process.env.HOST_DATABASE || '';
} else if (process.env.AMBIENTE == 'CRM') {
  DATABASE = process.env.DATABASE_CRM || '';
  PASS = process.env.PASSWORD_DATABASE || '';
  PORT = process.env.PORT_DATABASE || '';
  HOST = process.env.HOST_DATABASE || '';
} else {
  DATABASE = process.env.DATABASE_SANDBOX || '';
  PASS = process.env.PASSWORD_DATABASEDEV || '';
  PORT = process.env.PORT_DATABASEDEV || '';
  HOST = process.env.HOST_DATABASEDEV || '';
}

// console.log('process.env.AMBIENTE at line 19 in services/PostgreSQLServices.ts:', process.env.AMBIENTE);
// console.log('DATABASE:', DATABASE);
// console.log('PASS:', PASS);
// console.log('PORT:', PORT);
// console.log('HOST:', HOST);
// console.log('USER_DATABASE:', process.env.USER_DATABASE);

// Criar pool de conexões global
const pool = new Pool({
  host: HOST,
  user: process.env.USER_DATABASE || '',
  password: PASS,
  database: DATABASE,
  port: Number(PORT) || 5432,
  max: 20, // aumentado número máximo de conexões
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
  query_timeout: 10000,
  statement_timeout: 10000,
  keepAlive: true, // mantém conexão ativa
  allowExitOnIdle: false,
});

type OperationType = 'insert' | 'update' | 'delete' | 'select';
export interface OperationObject {
  operacao: OperationType;
  tabelas?: string[];
  dados?: Record<string, any>;
  chaves?: Record<string, any>;
  retorno?: string;
  relacionamento?: string[];
  ordem?: string[];
}
function isDate(value: string): boolean {
  const dateRegex: RegExp = /^\d{4}-\d{2}-\d{2}$/;
  return dateRegex.test(value);
}
export class PostgreSQLServices {
  async executar(operacaoObj: OperationObject): Promise<IRetorno> {
    const client = await pool.connect();
    try {
      let sql: string = '';
      let message: string = '';
      let values: any[] = [];

      if (operacaoObj && operacaoObj.tabelas && operacaoObj.operacao === 'insert') {
        const dados = operacaoObj.dados;

        if (dados && Array.isArray(operacaoObj.dados)) {
          const campos: string = Object.keys(dados[0])
            .filter((key) => dados[0][key] !== undefined)
            .join(', ');
          const placeholdersArray: string[] = dados.map(
            (_: string, index: number) =>
              `(${Object.keys(dados[0])
                .map((key) => `$${index * Object.keys(dados[0]).length + Object.keys(dados[0]).indexOf(key) + 1}`)
                .join(', ')})`,
          );
          const placeholders: string = placeholdersArray.join(', ');
          sql = `INSERT INTO ${operacaoObj.tabelas[0]} (${campos}) VALUES ${placeholders} RETURNING ${operacaoObj.retorno};`;
          values = dados.flatMap((obj: object) =>
            Object.values(obj).filter((value) => value !== undefined && typeof value !== 'function'),
          );
          message = 'Dados inseridos com sucesso!';
        } else if (dados) {
          const campos: string = Object.keys(dados)
            .filter((key) => dados[key] !== undefined)
            .join(', ');
          const placeholders: string = Object.keys(dados)
            .filter((key) => dados[key] !== undefined)
            .map((key, index) => `$${index + 1}`)
            .join(', ');
          sql = `INSERT INTO ${operacaoObj.tabelas[0]} (${campos}) VALUES (${placeholders}) RETURNING ${operacaoObj.retorno};`;
          values = Object.values(dados).filter((value) => value !== undefined && typeof value !== 'function');
          message = 'Dados inseridos com sucesso!';
        }
      } else if (operacaoObj.dados && operacaoObj.chaves && operacaoObj.tabelas && operacaoObj.operacao === 'update') {
        const setClause = Object.entries(operacaoObj.dados)
          .map(([key, value], index) => {
            // Verifica se o valor não é undefined antes de incluí-lo na cláusula
            if (value !== undefined) {
              values.push(value);
              return `${key} = $${values.length}`;
            }
            return null; // Retorna null para excluir esse campo da cláusula
          })
          .filter((clause) => clause !== null) // Filtra para remover os valores nulos
          .join(', ');

        const whereKeys = Object.keys(operacaoObj.chaves);
        const whereClause = whereKeys
          .map((key, index) => {
            // Verifica se o valor não é undefined antes de incluí-lo na cláusula
            if (operacaoObj.chaves && operacaoObj.chaves[key] !== undefined) {
              return `${key} = $${values.length + index + 1}`;
            }
            return null; // Retorna null para excluir esse campo da cláusula
          })
          .filter((clause) => clause !== null) // Filtra para remover os valores nulos
          .join(' AND ');

        sql = `UPDATE ${operacaoObj.tabelas[0]} SET ${setClause} WHERE ${whereClause} RETURNING ${operacaoObj.retorno};`;

        // Adicione os valores correspondentes ao array
        values = values.concat(Object.values(operacaoObj.chaves).filter((value) => value !== undefined));

        message = 'Dados atualizados com sucesso!';
      } else if (operacaoObj.tabelas && operacaoObj.chaves && operacaoObj.operacao === 'delete') {
        const whereKeys = Object.keys(operacaoObj.chaves);
        const whereClause = whereKeys
          .map((key, index) => {
            // Verifica se o valor não é undefined antes de incluí-lo na cláusula
            if (operacaoObj.chaves && operacaoObj.chaves[key] !== undefined) {
              // Ajusta o índice do parâmetro
              return `${key} = $${index + 1}`;
            }
            return null; // Retorna null para excluir esse campo da cláusula
          })
          .filter((clause) => clause !== null) // Filtra para remover os valores nulos
          .join(' AND ');

        sql = `DELETE FROM ${operacaoObj.tabelas[0]} WHERE ${whereClause}`;

        // Adicione os valores correspondentes ao array
        values = Object.values(operacaoObj.chaves).filter((value) => value !== undefined);
        message = 'Dados removidos com sucesso!';
      } else if (operacaoObj.operacao === 'select' && operacaoObj.tabelas) {
        delete operacaoObj.chaves?.SISTEMA;
        delete operacaoObj.chaves?.token;

        sql = `SELECT * FROM ${operacaoObj.tabelas[0]}`;

        if (operacaoObj.relacionamento) {
          for (let i = 1; i < operacaoObj.tabelas.length; i++) {
            if (operacaoObj.relacionamento[i - 1] && operacaoObj.relacionamento[i - 1].trim() !== '') {
              sql += ` INNER JOIN ${operacaoObj.tabelas[i]} ON ${operacaoObj.tabelas[0]}.${
                operacaoObj.relacionamento[i - 1]
              } = ${operacaoObj.tabelas[i]}.${operacaoObj.relacionamento[i - 1]}`;
            }
          }
        }

        const whereClauses = [];
        for (const key in operacaoObj.chaves) {
          const value = operacaoObj.chaves[key];
          let clause;

          if (value == 'true' || value == 'false') {
            clause = `${operacaoObj.tabelas[0]}.${key} = $${values.length + 1}`;
            values.push(value);
            whereClauses.push(clause);
          } else if (value && typeof value === 'string') {
            //verifica se é inteiro, pois na query vem como string
            if (/^\d+$/.test(value)) {
              clause = `${operacaoObj.tabelas[0]}.${key} = $${values.length + 1}`;
              values.push(value);
              whereClauses.push(clause);
            } else if (value.trim() !== '') {
              //para datas '2024-04-10'
              if (isDate(value)) {
                clause = `${operacaoObj.tabelas[0]}.${key} = $${values.length + 1}`;
                values.push(value);
                whereClauses.push(clause);
              } else {
                clause = `${operacaoObj.tabelas[0]}.${key} LIKE $${values.length + 1}`;
                values.push(`%${value}%`);
                whereClauses.push(clause);
              }
            }
          } else if (value && typeof value === 'number') {
            clause = `${operacaoObj.tabelas[0]}.${key} = $${values.length + 1}`;
            values.push(value);
            whereClauses.push(clause);
          }
        }

        if (whereClauses.length > 0) {
          sql += ` WHERE ${whereClauses.join(' AND ')}`;
        }

        if (operacaoObj.ordem) {
          for (let i = 0; i < operacaoObj.ordem.length; i++) {
            sql += ` ORDER BY ${operacaoObj.tabelas[0]}.${operacaoObj.ordem[i]}`;
          }
        }
      } else {
        throw new Error('Operação inválida');
      }

      //console.log('sql at line 198 in services/PostgreSQLServices.ts:', sql, values);
      const respDados: QueryResult = await client.query(sql, values);
      //console.log('respDados at line 200 in services/PostgreSQLServices.ts:', respDados);
      if (respDados.rowCount && respDados.rowCount > 0) {
        return sucesso(respDados.rows, message);
      } else {
        return dadosNaoEncontrados();
      }
    } catch (error: any) {
      //console.log('error:', error);
      if (error && error.message && error.message.includes('duplicate key value')) {
        return conflito(error);
      } else {
        return erroInterno(error);
      }
    } finally {
      client.release(); // Libera a conexão de volta ao pool
    }
  }
  async query(sql: string): Promise<IRetorno> {
    const client = await pool.connect();

    try {
      // Conectar usando um pool de conexões em vez de abrir e fechar a conexão a cada chamada
      // await this.client.connect();
      // console.log('sql:', sql);
      const respDados: QueryResult = await client.query(sql);

      // console.log('respDados:', respDados);

      if (respDados.rowCount && respDados.rowCount > 0) {
        return sucesso(respDados.rows);
      } else {
        return dadosNaoEncontrados();
      }

      //return result;
    } catch (error: any) {
      // console.log('error:', error);
      if (error && error.message && error.message.includes('duplicate key value')) {
        return conflito(error);
      } else {
        return erroInterno(error);
      }
    } finally {
      client.release(); // Libera a conexão de volta ao pool
    }
  }

  async queryRollback(sqls: string[]): Promise<IRetorno> {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      for (const sql of sqls) {
        await client.query(sql);
      }
      await client.query('COMMIT');
      return sucesso([], 'Transação executada com sucesso');
    } catch (error: any) {
      await client.query('ROLLBACK');
      return erroInterno(error);
    } finally {
      client.release();
    }
  }
}

// Capturar eventos de término da API e fechar o pool de conexões
process.on('SIGINT', async () => {
  await pool.end();
  console.log('Pool de conexões encerrado.');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await pool.end();
  console.log('Pool de conexões encerrado.');
  process.exit(0);
});

// Adicionar listeners de eventos no pool
pool.on('error', (err, client) => {
  logger.error('Unexpected error on idle client', err);
  process.exit(-1);
});

pool.on('connect', () => {
  logger.debug('New client connected to PostgreSQL');
});

pool.on('acquire', () => {
  // logger.debug('Client aquired from pool');
});

pool.on('remove', () => {
  // logger.debug('Client removed from pool');
});
