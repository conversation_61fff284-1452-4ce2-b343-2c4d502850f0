<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <div class="">
        <div class="intro-y flex flex-row items-center mt-1.5 sm:mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">API</h2>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-4 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <!-- <LinkIcon class="w-6 h-6 text-secondary" /> -->
                        <h2 class="text-lg font-semibold">EndPoints</h2>
                    </div>
                    <p class="text-md text-slate-600 dark:text-slate-400">
                        Utilize os endpoints para interagir com sua instância.
                    </p>
                </div>
                <div class="cursos-pointer flex items-center w-full sm:w-fit gap-2">
                    <!-- <label class="form-label mb-0">: </label> -->
                    <<PERSON>Select
                        size="small"
                        class="w-full min-w-[180px] !cursor-pointer"
                        @update:modelValue="selecionaInstancia"
                        v-model="instanciaSelecionada"
                        :options="{ allowEmptyOption: true }"
                    >
                        <option value="">Instâncias</option>
                        <option
                            v-for="item in listaInstancias"
                            :key="item.nameinstance"
                            v-bind:value="item.nameinstance"
                        >
                            {{ item.nome }}
                            {{ item.status_connection == 'Conectado' ? '(conectado)' : '(desconectado)' }}
                        </option>
                    </TomSelect>
                </div>
            </div>
            <div class="flex justify-between items-center py-4">
                <AccordionGroup class="w-full" :selectedIndex="-1">
                    <AccordionItem
                        v-for="endpoint in endpointsList"
                        :key="endpoint.route"
                        class="w-full border border-slate-200 rounded-xl mb-4 h-min overflow-hidden"
                    >
                        <Accordion
                            class="px-4 sm:px-6 hover:bg-slate-100 shadow-md bg-white dark:bg-darkmode-600 flex justify-between items-center font-medium h-fit"
                        >
                            <div
                                class="flex flex-col sm:flex-row justify-start items-start sm:items-center font-medium"
                            >
                                {{ endpoint.label }}
                                <span class="opacity-50 flex justify-start items-center gap-0.5 font-normal">
                                    <DotIcon class="hidden sm:block" />
                                    {{ endpoint.metodo }} {{ endpoint.rota }}
                                </span>
                            </div>
                            <ChevronDownIcon class="w-4 h-4" />
                        </Accordion>
                        <AccordionPanel
                            class="py-4 p-2 sm:p-4 grid grid-cols-12 sm:gap-8 shadow-inner bg-gray-50 dark:bg-darkmode-800"
                        >
                            <div class="col-span-12 lg:col-span-7 flex flex-col">
                                <h4 class="font-medium">Token</h4>
                                <form class="flex flex-col gap-2" @submit.prevent="testarEndpoint(endpoint)">
                                    <div class="w-full relative cursor-pointer">
                                        <input
                                            :type="!showToken ? 'password' : 'text'"
                                            class="form-control placeholder:whitespace-nowrap text-ellipsis placeholder:mr-12"
                                            :value="instanciaSelecionada"
                                            :placeholder="
                                                true
                                                    ? 'Selecione uma instância para testar o endpoint'
                                                    : 'Instância não conectada. Não é possível testar os endpoints.'
                                            "
                                            disabled
                                        />
                                        <EyeIcon
                                            class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                            v-if="showToken"
                                            @click="showToken = !showToken"
                                        />
                                        <EyeOffIcon
                                            class="absolute right-10 top-0 bottom-0 my-auto w-4 h-4"
                                            v-else
                                            @click="showToken = !showToken"
                                        />
                                        <CopyIcon
                                            class="absolute right-2 top-0 bottom-0 my-auto w-4 h-4"
                                            @click="copyID()"
                                            :class="{ copied: IDCopiado == true }"
                                        />
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <input
                                            ref="telefoneRef"
                                            placeholder="Adicione um numero de telefone. Ex: 5511999999999"
                                            type="text"
                                            class="form-control placeholder:whitespace-nowrap text-ellipsis"
                                            v-model="formDataTeste.nr_telefone"
                                            required
                                        />
                                        <Tippy content="Testar">
                                            <button class="btn btn-sm btn-outline-primary border-none shadow-md">
                                                <ZapIcon />
                                            </button>
                                        </Tippy>
                                    </div>
                                </form>
                                <div class="mt-4 p-1">
                                    <p class="pb-6">{{ endpoint.desc }}</p>
                                    <h3 class="font-medium mt-1">Corpo da Requisição</h3>
                                    <div
                                        v-if="!!Object.keys(endpoitsChaves).find((chave) => chave === endpoint.rota)"
                                        v-for="chave in endpoitsChaves[endpoint.rota]"
                                        :key="chave[0]"
                                    >
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-bold text-primary font-[consolas]">
                                                {{ chave[0] }}
                                            </h3>
                                            <span
                                                class="rounded-md flex justify-center items-center right-10 bg-primary text-white text-xs font-normal px-1 py-0.5 active:border-none"
                                            >
                                                {{ chave[1] }}
                                            </span>
                                        </div>
                                        <p>{{ chave[2] }}</p>
                                        <div class="my-4 w-full border border-b-slate-200" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-12 lg:col-span-5 flex flex-col gap-4">
                                <CodeBlock
                                    label="Requisição"
                                    shadow="md"
                                    lang="powershell"
                                    :editable="true"
                                    :hideComments="true"
                                    :code="obterCodigo(endpoint.rota + '_req')"
                                    :originalCode="obterCodigo(endpoint.rota + '_req', true)"
                                    :showEditButton="true"
                                    :editPlaceholder="[
                                        '*(apiHost)* => Máscara de API HOST.',
                                        '*(token)* => Máscara de TOKEN.',
                                        'Rota => A rota deve ser exatamente a mesma do accordion.',
                                        `data: '...' => Dados do corpo da requisição serão listados da documentação.`,
                                        `// ... => Comentarios ao lado das chaves do corpo da requisição serão listados como descrição da chave.`,
                                        `Estrutura do comentário => // tipo - 'Descrição da chave'`,
                                    ]"
                                    @edited="
                                        (code) => {
                                            salvarExemploApiDocs(code, endpoint.rota + '_req');
                                        }
                                    "
                                />
                                <CodeBlock
                                    label="Resposta"
                                    shadow="md"
                                    lang="json"
                                    :editable="true"
                                    :showEditButton="true"
                                    :code="obterCodigo(endpoint.rota + '_res')"
                                    :originalCode="obterCodigo(endpoint.rota + '_res', true)"
                                    :editPlaceholder="[
                                        '*(apiHost)* => Máscara de API HOST.',
                                        '*(token)* => Máscara de TOKEN.',
                                    ]"
                                    @edited="
                                        (code) => {
                                            salvarExemploApiDocs(code, endpoint.rota + '_res');
                                        }
                                    "
                                />
                            </div>
                        </AccordionPanel>
                    </AccordionItem>
                </AccordionGroup>
            </div>
        </div>
    </div>

    <!-- Modal  resposta teste endpoint-->
    <Modal :show="modalConfirmarEnvioTeste" @hidden="modalConfirmarEnvioTeste = false">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center">
            <div class="p-3 w-min bg-success/30 rounded-full flex justify-center items-center">
                <CheckIcon class="text-success w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-xl mt-2 font-medium">Pronto!</div>
            <div class="text-slate-500 mt-2">
                <div class="flex justify-content align-items">
                    <label> Requisição teste foi enviada! </label>
                </div>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" class="btn btn-success w-24 text-white" @click="modalConfirmarEnvioTeste = false">
                Ok
            </button>
        </ModalFooter>
    </Modal>
    <!-- END: resposta teste endpoint -->
</template>

<script setup>
    import { onBeforeMount, onMounted, reactive, ref } from 'vue';
    import { endpointsList } from './endpointsList';
    import notifications from '@/components/show-notifications/Main.vue';
    import CodeBlock from '@/components/code-block/Main.vue';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import endpointTeste from '@/utils/enpointsTeste';
    import ApiDocsServices from '@/services/administracao/ApiDocsServices';
    import { extrairDescricao } from '../../utils/enpointsTeste';

    const showNotifications = ref();
    const loading = ref();
    const showToken = ref(false);
    const listaInstancias = ref([]);
    const listaApiExemplos = ref([]);
    const endpoitsChaves = ref({});

    let modalConfirmarEnvioTeste = ref(false);
    const instanciaSelecionada = ref('');
    const formData = reactive({
        nr_hash: '',
    });
    const formDataTeste = reactive({
        nr_telefone: '',
    });

    function selecionaInstancia(value) {
        const instancia = listaInstancias.value.find((instancia) => instancia.nameinstance === value);
        if (instancia?.status_connection !== 'Conectado') {
            setTimeout(() => {
                instanciaSelecionada.value = '';
                formData.nr_hash = '';
            }, 100);
            showNotifications.value.showWarningNotification(
                'Instância não conectada. Não é possível testar os endpoints.'
            );
            return;
        }
        formData.nr_hash = instancia.nr_hash;
    }

    async function carregaInstancias() {
        loading.value.show();
        try {
            const codUsuario = await localStorage.getItem('codusuario');
            const estatabelecimentos = await localStorage.getItem('estabelecimentos');
            const estabelecimentosLiberado = JSON.parse(estatabelecimentos);

            let filtros = {
                cd_estabelecimento: estabelecimentosLiberado[0].cd_estabelecimento,
                cd_usuario: parseInt(codUsuario),
                in_verificastatus: true,
            };

            listaInstancias.value = [];

            let result = await InstanciaServices.listar(filtros);
            //console.log('result at line 113:', result);
            // console.log(result.data);

            if (result.statuscode == 200) {
                listaInstancias.value = result.data;
                if (listaInstancias.value.length == 0) {
                    showNotifications.value.showWarningNotification(
                        'Nenhuma instância encontrada, adicione uma instância para testar os endpoints!'
                    );
                    listaInstancias.value = [];
                    formData.nr_hash = '';
                }
                const instanciaConectada = listaInstancias.value.find(
                    (instancia) => instancia.status_connection == 'Conectado'
                );
                if (instanciaConectada) {
                    instanciaSelecionada.value = instanciaConectada.nameinstance;
                    formData.nr_hash = instanciaConectada.nr_hash;
                } else {
                    instanciaSelecionada.value = '';
                    formData.nr_hash = '';
                    showNotifications.value.showWarningNotification('Nenhuma instância conectada encontrada!');
                }
            }
            if (result.statuscode == 404) {
                showNotifications.value.showWarningNotification(
                    'Nenhuma instância encontrada, adicione uma instância para testar os endpoints!'
                );
                listaInstancias.value = [];
                formData.nr_hash = '';
            }
        } catch (error) {
            console.log('error at line 122:', error);
            showNotifications.value.showErrorNotification('Erro ao carregar instâncias!');
        } finally {
            loading.value.hide();
        }
    }

    async function testarEndpoint(endpoint) {
        try {
            if (!formData.nr_hash) {
                return showNotifications.value.showWarningNotification(
                    'Selecione uma instância para testar o endpoint!'
                );
            }
            if (!formDataTeste.nr_telefone) {
                return showNotifications.value.showWarningNotification(
                    'Informe um número de telefone para testar o endpoint!'
                );
            }
            loading.value.show();
            const result = await endpointTeste.testar(
                endpoint.metodo,
                endpoint.host + endpoint.rota,
                undefined,
                {
                    telefone: formDataTeste.nr_telefone,
                    instance: instanciaSelecionada.value,
                    mensagem: 'Teste de requisição de endpoint! 🚀',
                },
                formData.nr_hash
            );

            showNotifications.value.showSuccessNotification('Teste realizado com sucesso!');
            if (result.status == 200) {
                //modalConfirmarEnvioTeste.value = true;
            } else {
                showNotifications.value.showErrorNotification(result.message);
            }
        } catch (err) {
            console.error('Erro ao testar endpoint: ', err.message);
            showNotifications.value.showErrorNotification(err.message);
        } finally {
            loading.value.hide();
        }
    }

    function obterCodigo(ref, codigoOriginal) {
        const exemplo = listaApiExemplos.value?.find((ex) => ex.nm_retorno == ref)?.ds_exemplo;
        const apiHost = import.meta.env.VITE_API_HOST;

        if (!!apiHost && exemplo && !codigoOriginal) {
            if (showToken.value) {
                return exemplo.replaceAll('*(token)*', instanciaSelecionada.value).replaceAll('*(apiHost)*', apiHost);
            } else {
                return exemplo.replaceAll('*(apiHost)*', apiHost).replaceAll('*(token)*', '***');
            }
        }
        return !!exemplo ? exemplo : '';
    }

    function mudarExemplo(code, ref) {
        if (!listaApiExemplos.value.find((ex) => ex.nm_retorno === ref)) {
            listaApiExemplos.value.push({
                tp_retorno: 'WebHooks',
                nm_retorno: ref,
                ds_exemplo: code,
            });
        }
        listaApiExemplos.value.find((ex) => ex.nm_retorno === ref).ds_exemplo = code;
    }

    async function salvarExemploApiDocs(code, ref) {
        try {
            loading.value.show();
            const dadosExemplo = {
                tp_retorno: 'Api',
                nm_retorno: ref,
                ds_exemplo: code,
            };
            const result = await ApiDocsServices.alterarExemplo(dadosExemplo);
            if (result.statuscode == 200) {
                showNotifications.value.showSuccessNotification('Exemplo salvo com sucesso!');
                mudarExemplo(code, ref);
                return;
            } else if (result.statuscode == 404) {
                const result = await ApiDocsServices.incluirExemplo(dadosExemplo);
                if (result.statuscode != 200) {
                    showNotifications.value.showWarningNotification(result.message);
                } else {
                    showNotifications.value.showSuccessNotification('Exemplo salvo com sucesso!');
                    mudarExemplo(code, ref);
                    return;
                }
            } else {
                showNotifications.value.showErrorNotification(result.message);
            }
        } catch (error) {
            console.log('Erro ao salvar exemplo: ' + error.message);
        } finally {
            loading.value.hide();
        }
    }

    function gerarCamposDataDoc(code) {
        // entender como montar os dados para o backend
    }

    async function listarApiDocsExemplos() {
        
        const result = await ApiDocsServices.listarExemplos({ tp_retorno: 'Api' });
        console.log(result);
        if (result.statuscode == 200) {
            listaApiExemplos.value = result.data;
            endpoitsChaves.value = extrairDescricao(result.data);
        } else if (result.statuscode == 404) {
            showNotifications.value.showWarningNotification(result.message);
        } else {
            showNotifications.value.showErrorNotification(result.message);
        }
    }

    onMounted(async () => {
        await carregaInstancias();
        await listarApiDocsExemplos();
    });
</script>

<style scoped>
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-dropdown) {
        border-radius: 1.5rem !important;
        margin-top: 2px !important;
        overflow: hidden !important;
        padding: 0.4rem 0 !important;
    }
    ::v-deep(.option) {
        margin: 0.4rem 0 !important;
    }
    ::v-deep(.tom-select .ts-input) {
        cursor: pointer !important;
        border-radius: 15em !important;
    }
    ::v-deep(.dropdown-item-disabled) {
        opacity: 0.4 !important;
        pointer-events: none !important;
    }
</style>
