import { Knex } from 'knex';
import { safeCreateIndex } from '../../scripts/indexUtils';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('Iniciando criação dos índices na tabela cliente_contatos');

    await safeCreateIndex(knex, 'cliente_contatos', 'cd_cliente', 'cliente_contatos_cd_cliente_idx');
    console.log('Índice cd_cliente criado com sucesso na tabela cliente_contatos');

    await safeCreateIndex(knex, 'cliente_contatos', 'cd_contato', 'cliente_contatos_cd_contato_idx');
    console.log('Índice cd_contato criado com sucesso na tabela cliente_contatos');

    await safeCreateIndex(knex, 'cliente_contatos', 'cd_estabelecimento', 'cliente_contatos_cd_estabelecimento_idx');
    console.log('Índice cd_estabelecimento criado com sucesso na tabela cliente_contatos');
    return;
  } catch (error) {
    console.error('Erro ao criar indice cd_estabelecimento:', error);
  }
}

export async function down(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  try {
    await trx.schema.alterTable('cliente_contatos_cd_cliente_idx', (table) => {
      table.dropIndex([], 'cliente_contatos_cd_contato_idx');
      table.dropIndex([], 'cliente_contatos_cd_estabelecimento_idx');
    });
    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}
