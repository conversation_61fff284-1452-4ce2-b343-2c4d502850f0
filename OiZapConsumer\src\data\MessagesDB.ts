import dotenv from 'dotenv';
import { IMessage } from '../interfaces/IMessage';
import { IMessageEdited } from '../interfaces/IMessageEdited';
import { IRetorno, erroInterno, generico, sucesso } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';
import { AtendimentosDB } from './AtendimentosDB';
const logger = Logger.getLogger();

dotenv.config();

export class MessagesDB {
  static async incluiMessages(messageData: IMessage, messageContent: any): Promise<IRetorno> {
    // console.log('🚀 ~ MessagesDB.ts:14 ~ incluiMessages ~ messageData:', messageData);
    // console.log('🚀 ~ MessagesDB.ts:14 ~ incluiMessages ~ messageContent:', messageContent);
    //console.log('messageData at line 31 in data/MessagesDB.ts:', messageData);
    try {
      //caso o atendimento esteja vazio, consultamos novamente pois a mensagem pode estar vindo
      //externamente
      let atendimento;
      if (messageData.cd_atendimento == undefined) {
        /*const respAtendimento = await this.getAtendimento({
          instance: messageData.instance as string,
          telefone: messageData.telefone || '',
        });*/
        let telefone = messageData.telefone;
        //ROBINHO - 04/01/2025 13:05
        //Comentando a funcionalidade de incluir o 55 de telefone que não tem
        //Pois temos numeros fora do BR e atualmente a api está retornando corretamente o 55
        // if (!telefone?.startsWith('55')) {
        //   telefone = '55' + telefone;
        // }
        const respAtendimento = await AtendimentosDB.listarAtendimentos({
          //instance: messageData.instance as string,
          cd_estabelecimento: messageData.cd_estabelecimento,
          telefone: telefone || '',
          instance: messageData.instance,
        });

        //console.log('respAtendimento at line 47 in data/MessagesDB.ts:', respAtendimento);
        if (respAtendimento.statuscode == 200) {
          atendimento = respAtendimento.data[0];
          messageData.cd_atendimento = atendimento.cd_atendimento;
          messageData.ds_hash = atendimento.ds_hash;
          messageData.in_stop_bot = atendimento.in_stop_bot;
          messageData.tp_status_atendimento = atendimento.tp_status;
        } else {
          messageData.cd_atendimento = undefined;
          messageData.ds_hash = undefined;
          messageData.in_stop_bot = undefined;
          messageData.tp_status_atendimento = undefined;
        }
      }

      if (messageData.type_message == 'listMessage') {
        messageData.mensagem = messageData.mensagem + '\n \n' + messageData.opcoes;
      }
      if (messageData.type_message == 'locationMessage') {
        messageData.mensagem = messageData.address;
      }

      let sql = `INSERT INTO messages (instance,telefone`;
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      let sqlValues = `) VALUES ('${messageData.instance}','${telefone}'`;

      if (messageData.mensagem) {
        sql += ', mensagem';
        //,message
        //sqlValues += `,'${messageData.mensagem?.replace(/'/g, '')}'`;
        sqlValues += `,'${messageData.mensagem?.replace(/'/g, '')}'`;
      }
      if (messageData.message_id) {
        sql += ',message_id';
        sqlValues += `,'${messageData.message_id}'`;
      }
      if (messageData.remote_jid) {
        sql += ',remote_jid';
        sqlValues += `,'${messageData.remote_jid}'`;
      }
      if (messageData.sender_lid) {
        sql += ',sender_lid';
        sqlValues += `,'${messageData.sender_lid}'`;
      }
      if (messageData.nome) {
        sql += ',nome';
        sqlValues += `,'${messageData.nome.replace(/'/g, '')}'`;
      }
      if (messageData.from_me != undefined) {
        sql += ',from_me';
        sqlValues += `,${messageData.from_me}`;
      }
      if (messageData.event) {
        sql += ',event';
        sqlValues += `,'${messageData.event}'`;
      }
      if (messageData.message_timestamp) {
        sql += ',message_timestamp';
        sqlValues += `,'${messageData.message_timestamp}'`;
      }
      if (messageData.cd_estabelecimento) {
        sql += ',cd_estabelecimento';
        sqlValues += `,'${messageData.cd_estabelecimento}'`;
      }

      if (messageData.cd_atendimento) {
        sql += ',cd_atendimento';
        sqlValues += `,'${messageData.cd_atendimento}'`;
        sql += ',hash';
        sqlValues += `,'${messageData.ds_hash}'`;
      }
      if (messageData.url_profile_picture) {
        sql += ',url_profile_picture';
        sqlValues += `,'${messageData.url_profile_picture}'`;
      }
      if (messageData.profile_picture_base64) {
        sql += ',profile_picture_base64';
        sqlValues += `,'${messageData.profile_picture_base64}'`;
      }
      if (messageData.type_message) {
        sql += ',type_message';
        sqlValues += `,'${messageData.type_message}'`;
      }
      if (messageData.mimetype) {
        sql += ',mimetype';
        sqlValues += `,'${messageData.mimetype}'`;
      }
      if (messageData.file_sha256) {
        sql += ',file_sha256';
        sqlValues += `,'${messageData.file_sha256}'`;
      }
      if (messageData.file_length) {
        sql += ',file_length';
        sqlValues += `,'${messageData.file_length}'`;
      }
      if (messageData.nr_height) {
        sql += ',nr_height';
        sqlValues += `,'${messageData.nr_height}'`;
      }
      if (messageData.nr_width) {
        sql += ',nr_width';
        sqlValues += `,'${messageData.nr_width}'`;
      }
      if (messageData.media_key) {
        sql += ',media_key';
        sqlValues += `,'${messageData.media_key}'`;
      }
      if (messageData.jpeg_thumbnail) {
        sql += ',jpeg_thumbnail';
        sqlValues += `,'${messageData.jpeg_thumbnail}'`;
      }
      // if (messageData.url_image) {
      //   sql += ',url_image';
      //   sqlValues += `,'${messageData.url_image}'`;
      // }
      // if (messageData.ds_base64) {
      //   sql += ',ds_base64';
      //   sqlValues += `,'${messageData.ds_base64}'`;
      // }
      if (messageData.file_name) {
        sql += ',file_name';
        sqlValues += `,'${messageData.file_name}'`;
      }
      // if (messageData.url_audio) {
      //   sql += ',url_audio';
      //   sqlValues += `,'${messageData.url_audio}'`;
      // }
      if (messageData.url_midia) {
        //console.log('messageData.url_midia at line 164 in data/MessagesDB.ts:', messageData.url_midia);
        sql += ',url_midia';
        sqlValues += `,'${messageData.url_midia}'`;
      }
      if (messageData.latitude) {
        sql += ',degreesLatitude';
        sqlValues += `,'${messageData.latitude}'`;
      }
      if (messageData.longitude) {
        sql += ',degreesLongitude';
        sqlValues += `,'${messageData.longitude}'`;
      }
      if (messageData.estabelecimento) {
        sql += ',name';
        sqlValues += `,'${messageData.estabelecimento}'`;
      }
      if (messageData.address) {
        sql += ',address';
        sqlValues += `,'${messageData.address}'`;
      }
      if (messageData.idunico) {
        sql += ',idunico';
        sqlValues += `,'${messageData.idunico}'`;
      }
      if (messageData.title) {
        sql += ',title';
        sqlValues += `,'${messageData.title}'`;
      }
      if (messageData.description) {
        sql += ',description';
        sqlValues += `,'${messageData.description}'`;
      }
      if (messageData.buttonText) {
        sql += ',buttonText';
        sqlValues += `,'${messageData.buttonText}'`;
      }
      if (messageData.listType) {
        sql += ',listType';
        sqlValues += `,'${messageData.listType}'`;
      }
      if (messageData.in_rabbitmq) {
        sql += ',dt_enviado_rabbitmq';
        sqlValues += `,current_timestamp`;
      }
      if (messageData.quotedmessage) {
        sql += ',quotedmessage';
        sqlValues += `,'${messageData.quotedmessage.replace(/'/g, '')}'`;
      }

      if (messageData.type_message_quoted) {
        sql += ',type_message_quoted';
        sqlValues += `,'${messageData.type_message_quoted}'`;
      }

      if (messageData.message_id_quoted) {
        sql += ',message_id_quoted';
        sqlValues += `,'${messageData.message_id_quoted}'`;
      }

      if (messageData.mimetype_quoted) {
        sql += ',mimetype_quoted';
        sqlValues += `,'${messageData.mimetype_quoted}'`;
      }
      if (messageData.file_sha256_quoted) {
        sql += ',file_sha256_quoted';
        sqlValues += `,'${messageData.file_sha256_quoted}'`;
      }
      if (messageData.file_length_quoted) {
        sql += ',file_length_quoted';
        sqlValues += `,'${messageData.file_length_quoted}'`;
      }
      if (messageData.nr_height_quoted) {
        sql += ',nr_height_quoted';
        sqlValues += `,'${messageData.nr_height_quoted}'`;
      }
      if (messageData.nr_width_quoted) {
        sql += ',nr_width_quoted';
        sqlValues += `,'${messageData.nr_width_quoted}'`;
      }
      if (messageData.media_key_quoted) {
        sql += ',media_key_quoted';
        sqlValues += `,'${messageData.media_key_quoted}'`;
      }
      if (messageData.jpeg_thumbnail_quoted) {
        sql += ',jpeg_thumbnail_quoted';
        sqlValues += `,'${messageData.jpeg_thumbnail_quoted}'`;
      }
      if (messageData.url_midia_quoted) {
        sql += ',url_midia_quoted';
        sqlValues += `,'${messageData.url_midia_quoted}'`;
      }
      // if (messageData.ds_base64_quoted) {
      //   sql += ',ds_base64_quoted';
      //   sqlValues += `,'${messageData.ds_base64_quoted}'`;
      // }
      if (messageData.file_name_quoted) {
        sql += ',file_name_quoted';
        sqlValues += `,'${messageData.file_name_quoted}'`;
      }
      if (messageContent) {
        sql += ',ds_json';
        sqlValues += `,'${JSON.stringify(messageContent).replace(/'/g, '')}'`;
      }

      if (messageData.in_enviointerno) {
        sql += ',in_enviointerno';
        sqlValues += `,${messageData.in_enviointerno}`;
      }
      if (messageData.date_sent) {
        sql += ',date_sent';
        sqlValues += `,'${messageData.date_sent}'`;
      }

      if (messageData.tp_status) {
        sql += ',tp_status';
        sqlValues += `,'${messageData.tp_status}'`;
      }

      if (messageData.cd_atendente) {
        if (messageData.tp_status_crm == 'Iniciado') {
          sql += ',cd_atendente,tp_status_atendimento';
          sqlValues += `,${messageData.cd_atendente},'Iniciado'`;
        }
      }

      sql += sqlValues + ') returning instance,nome,telefone,mensagem,id,url_profile_picture,profile_picture_base64;';
      // console.log('🚀 ~ MessagesDB.ts:294 ~ incluiMessages ~ sql:', sql);

      //logger.debug('sql at line 254 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      // logger.debug('respMessage at line 255 in data/MessagesDB.ts:' + JSON.stringify(respMessage));
      //console.log('');
      if (respMessage.statuscode != 200) {
        logger.info('sql at line 270 in data/MessagesDB.ts:' + sql);
        logger.info('respMessage at line 271 in data/MessagesDB.ts:' + JSON.stringify(respMessage));
        return respMessage;
      }
      // console.log('respMessage at line 149 in data/MessagesDB.ts:', respMessage);

      sql = ` INSERT INTO last_message (in_delete,message,event,instance, from_me, mensagem, message_id, remote_jid, sender_lid`;

      if (messageData.cd_atendimento) {
        sql += ',cd_atendimento';
        sql += ',hash';
      }

      if (messageData.message_timestamp) {
        sql += ',message_timestamp';
      }
      if (messageData.url_profile_picture) {
        sql += ',url_profile_picture';
      }
      if (messageData.profile_picture_base64) {
        sql += ',profile_picture_base64';
      }
      if (messageData.idunico) {
        sql += ',idunico';
      }
      if (messageData.cd_estabelecimento) {
        sql += ',cd_estabelecimento';
      }
      if (messageData.telefone) {
        sql += ',telefone';
      }
      let nome = messageData.origem == 'bot' ? messageData.nm_cliente : messageData.nome;
      if (nome == undefined) {
        nome = messageData.nome;
      }
      if (nome == '' || nome == undefined) {
        nome = messageData.nome;
      }
      if (nome != undefined && nome != '') {
        sql += ',nome';
      }

      let mensagem = messageData.mensagem?.substring(0, 200);
      if (mensagem != undefined) mensagem = mensagem.replace(/'/g, '');

      sql += `) VALUES (false,'${mensagem}', '${messageData.event}', '${messageData.instance}'
        , ${messageData.from_me},'${mensagem}', '${messageData.message_id}', '${messageData.remote_jid}', '${messageData.sender_lid}'`;

      if (messageData.cd_atendimento) {
        sql += `,'${messageData.cd_atendimento}'`;
        sql += `,'${messageData.ds_hash}'`;
      }
      if (messageData.message_timestamp) {
        sql += `,'${messageData.message_timestamp}' `;
      }
      if (messageData.url_profile_picture) {
        sql += `,'${messageData.url_profile_picture}'`;
      }
      if (messageData.profile_picture_base64) {
        sql += `,'${messageData.profile_picture_base64}'`;
      }
      if (messageData.idunico) {
        sql += `,'${messageData.idunico}'`;
      }
      if (messageData.cd_estabelecimento) {
        sql += `,${messageData.cd_estabelecimento}`;
      }
      if (messageData.telefone) {
        if (messageData?.remote_jid) {
          let remote_jid = messageData?.remote_jid.replace('@s.whatsapp.net', '');
          sql += `,'${remote_jid.trim()}'`;
        } else {
          sql += `,'${messageData.telefone}'`;
        }
      }

      if (nome != undefined && nome != '') {
        sql += `,'${nome.replace(/'/g, '')}'`;
      }

      sql += `) ON CONFLICT (instance,telefone) DO UPDATE SET in_delete=false,
      from_me=${messageData.from_me},mensagem='${messageData.mensagem?.substring(0, 200)}' `;
      if (messageData.message_id) {
        sql += `,message_id='${messageData.message_id}' `;
      }
      if (messageData.message_timestamp) {
        sql += `,message_timestamp='${messageData.message_timestamp}' `;
      }

      if (messageData.url_profile_picture) {
        sql += `,url_profile_picture='${messageData.url_profile_picture}' `;
      }
      if (messageData.profile_picture_base64) {
        sql += `,profile_picture_base64='${messageData.profile_picture_base64}' `;
      }
      if (messageData.idunico) {
        sql += `,idunico='${messageData.idunico}'`;
      }
      if (messageData.cd_estabelecimento) {
        sql += `,cd_estabelecimento='${messageData.cd_estabelecimento}'`;
      }

      if (messageData.cd_atendimento) {
        sql += `,cd_atendimento='${messageData.cd_atendimento}'`;
        sql += `,hash='${messageData.ds_hash}'`;
      }
      if (nome != undefined && nome != '') {
        sql += `,nome='${nome.replace(/'/g, '')}' `;
      }

      //console.log('sql at line 290 in data/MessagesDB.ts:', sql);
      const respLastMessage = await new PostgreSQLServices().query(sql);
      // console.log('respLastMessage at line 385 in data/MessagesDB.ts:', respLastMessage);
      //console.log('');
      if (respLastMessage.statuscode != 200) {
        logger.info('sql at line 381 in data/MessagesDB.ts:', sql);
        logger.info('respMessage at line 381 in data/MessagesDB.ts:', respLastMessage);
      }

      respMessage.data[0].cd_atendimento = atendimento?.cd_atendimento;
      respMessage.data[0].tp_status_atendimento = atendimento?.tp_status;
      respMessage.data[0].ds_hash = atendimento?.ds_hash;
      respMessage.data[0].in_stop_bot = atendimento?.in_stop_bot;

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
  static async salvaLogMessages(messageData: IMessage): Promise<IRetorno> {
    try {
      // console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }
      let sql = `update messages set ds_log='${messageData.ds_log}'
       ,ds_json='${JSON.stringify(messageData.ds_json).replace(/'/g, '')}'
       ,tp_status='Erro'
       where idunico = '${messageData.idunico}' and instance = '${messageData.instance}'
      and telefone = '${telefone}'`;

      // console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      // console.log('respMessage at line 319 in data/MessagesDB.ts:', respMessage);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error salvaLogMessages:', error);
      return erroInterno(error);
    }
  }
  static async atualizaRetornoWhatsMessages(messageData: IMessage): Promise<IRetorno> {
    try {
      // console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);

      let messageid = messageData.message_id == undefined ? messageData.keyId : messageData.message_id;

      let sql = `update messages set message_id='${messageid}'
      ,remote_jid= '${messageData.remote_jid}'
      ,from_me=${messageData.from_me},message_timestamp='${messageData.message_timestamp}'
      ,date_sent='${messageData.date_sent}' `;

      if (messageData.mimetype) {
        sql += `,mimetype='${messageData.mimetype}'`;
      }
      if (messageData.file_sha256) {
        sql += `,file_sha256='${messageData.file_sha256}'`;
      }

      if (messageData.file_length) {
        sql += `,file_length='${messageData.file_length}'`;
      }
      if (messageData.nr_height) {
        sql += `,nr_height='${messageData.nr_height}'`;
      }
      if (messageData.nr_width) {
        sql += `,nr_width='${messageData.nr_width}'`;
      }
      if (messageData.media_key) {
        sql += `,media_key='${messageData.media_key}'`;
      }
      if (messageData.jpeg_thumbnail) {
        sql += `,jpeg_thumbnail='${messageData.jpeg_thumbnail}'`;
      }
      // if (messageData.url_image) {
      //   sql += `,url_image='${messageData.url_image}'`;
      // }
      // if (messageData.ds_base64) {
      //   sql += `,ds_base64='${messageData.ds_base64}'`;
      // }
      if (messageData.file_name) {
        sql += `,file_name='${messageData.file_name}'`;
      }
      // if (messageData.url_audio) {
      //   sql += `,url_audio='${messageData.url_audio}'`;
      // }
      if (messageData.url_midia) {
        sql += `,url_midia='${messageData.url_midia}'`;
      }
      if (messageData.latitude) {
        sql += `,degreesLatitude='${messageData.latitude}'`;
      }
      if (messageData.longitude) {
        sql += `,degreesLongitude='${messageData.longitude}'`;
      }

      if (messageData.estabelecimento) {
        sql += `,name='${messageData.estabelecimento}'`;
      }

      if (messageData.address) {
        sql += `,address='${messageData.address}'`;
      }

      if (messageData.title) {
        sql += `,title='${messageData.title}'`;
      }
      if (messageData.description) {
        sql += `,description='${messageData.description}'`;
      }
      if (messageData.buttonText) {
        sql += `,buttonText='${messageData.buttonText}'`;
      }
      if (messageData.listType) {
        sql += `,listType='${messageData.listType}'`;
      }
      if (messageData.in_rabbitmq) {
        sql += `,dt_processado_rabbitmq=current_timestamp`;
      }
      if (messageData.quotedmessage) {
        sql += `,quotedmessage='${messageData.quotedmessage}'`;
      }

      if (messageData.type_message_quoted) {
        sql += `,type_message_quoted='${messageData.type_message_quoted}'`;
      }

      if (messageData.message_id_quoted) {
        sql += `,message_id_quoted='${messageData.message_id_quoted}'`;
      }

      if (messageData.mimetype_quoted) {
        sql += `,mimetype_quoted='${messageData.mimetype_quoted}'`;
      }
      if (messageData.file_sha256_quoted) {
        sql += `,file_sha256_quoted='${messageData.file_sha256_quoted}'`;
      }
      if (messageData.file_length_quoted) {
        sql += `,file_length_quoted='${messageData.file_length_quoted}'`;
      }
      if (messageData.nr_height_quoted) {
        sql += `,nr_height_quoted='${messageData.nr_height_quoted}'`;
      }
      if (messageData.nr_width_quoted) {
        sql += `,nr_width_quoted='${messageData.nr_width_quoted}'`;
      }
      if (messageData.media_key_quoted) {
        sql += `,media_key_quoted='${messageData.media_key_quoted}'`;
      }
      if (messageData.jpeg_thumbnail_quoted) {
        sql += `,jpeg_thumbnail_quoted='${messageData.jpeg_thumbnail_quoted}'`;
      }
      if (messageData.url_midia_quoted) {
        sql += `,url_midia_quoted='${messageData.url_midia_quoted}'`;
      }
      // if (messageData.ds_base64_quoted) {
      //   sql += `,ds_base64_quoted='${messageData.ds_base64_quoted}'`;
      // }
      if (messageData.file_name_quoted) {
        sql += `,file_name_quoted='${messageData.file_name_quoted}'`;
      }

      if (messageData.tp_status) {
        sql += `,tp_status='${messageData.tp_status}'`;
      }

      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      sql += ` where idunico = '${messageData.idunico}' and instance = '${messageData.instance}'
      and telefone = '${telefone}'`;

      // console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      //console.log('respMessage at line 319 in data/MessagesDB.ts:', respMessage);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }

  static async updateMessage(messageData: IMessage): Promise<IRetorno> {
    try {
      let sqlSet = ` id=id, tp_status = '${messageData.tp_status}' `;

      //indica que foi enviado
      if (messageData.event == 'send.message') {
        sqlSet += `,date_sent = '${messageData.date_time}'`;
      }

      let messageid;

      if (messageData.status == 'PENDING') {
        sqlSet += `,date_sent = '${messageData.date_time}'`;
      }
      if (messageData.status == 'DELIVERY_ACK') {
        sqlSet += `,date_received = '${messageData.date_time}'`;
      }
      if (messageData.status == 'READ') {
        sqlSet += `,date_read = '${messageData.date_time}'`;
      }

      if (messageData.event == 'messages.edited') {
        sqlSet += `,date_sent = '${messageData.date_time}'`;
      }

      // if (messageData.mensagem) {
      //   sql += ',message, mensagem';
      //   sqlValues += `,'${messageData.mensagem?.replace(/'/g, '')}'`;
      //   sqlValues += `,'${messageData.mensagem?.replace(/'/g, '')}'`;
      // }

      messageid = messageData.message_id == undefined ? messageData.keyId : messageData.message_id;

      if (messageid == undefined) {
        return { statuscode: 200, message: '' };
      }

      sqlSet += ` where message_id= '${messageid}'`;

      const sql = 'update messages SET ' + sqlSet;

      //console.log('sql at line 559 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      // console.log('respMessage at line 555 in data/MessagesDB.ts:', respMessage);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }

  static async deleteMessage(messageData: IMessageEdited): Promise<IRetorno> {
    try {
      let sqlSet = `in_mensagem_delete = true`;
      sqlSet += `,message_id_delete = '${messageData.message_id}'`;
      sqlSet += ` where message_id= '${messageData.message_id}' returning telefone`;

      let sql = 'update messages SET ' + sqlSet;

      //console.log('sql at line 559 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      // console.log('respMessage at line 555 in data/MessagesDB.ts:', respMessage);

      if (respMessage.statuscode == 200) {
        sql = `update last_message set in_delete = true where instance = '${messageData.instance}' and telefone='${respMessage.data[0].telefone}'`;
        //const respLastMessage =
        await new PostgreSQLServices().query(sql);
        //console.log('respLastMessage at line 633 in data/MessagesDB.ts:', respLastMessage);
      }

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
  static async editedMessage(messageData: IMessageEdited): Promise<IRetorno> {
    try {
      const message = messageData.mensagem?.replace(/'/g, '');
      let sqlSet = `in_mensagem_edited = true`;
      sqlSet += `,mensagem_edited=mensagem,message='${message}',mensagem='${message}'`;
      sqlSet += `,message_id_edited = '${messageData.message_id}'`;
      sqlSet += ` where message_id= '${messageData.message_id}' returning telefone`;

      let sql = 'update messages SET ' + sqlSet;

      //console.log('sql at line 559 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      // console.log('respMessage at line 555 in data/MessagesDB.ts:', respMessage);

      if (respMessage.statuscode == 200) {
        sql = `update last_message set message = '${message}', in_delete = false where instance = '${messageData.instance}' and telefone='${respMessage.data[0].telefone}'`;
        //const respLastMessage =
        await new PostgreSQLServices().query(sql);
        //console.log('respLastMessage at line 633 in data/MessagesDB.ts:', respLastMessage);
      }

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
  static async updateLastMessage(data: any): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['last_message'],
        chaves: { instance: data.instance, telefone: data.telefone },
        dados: data,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async statusMessage(messageData: IMessage): Promise<IRetorno> {
    try {
      //console.log('dadosSend.data[0] at line 173 in models/Messages.js:', messageData);
      if (messageData.message_id == undefined && messageData.dadosSend != undefined) {
        messageData.message_id = messageData.dadosSend.data[0]?.message_id;
      }

      if (messageData.message_id == undefined && messageData.idunico == undefined) {
        // console.log('messageData at line 299 in data/MessagesDB.ts:', messageData);
        // console.log('message_id e idunico = undefined');
        return generico(messageData);
      }

      let sqlSet = ' id=id ';
      if (messageData.sent) {
        sqlSet += `, date_sentclient = current_timestamp`;
      }
      if (messageData.received) {
        sqlSet += `, date_receivedclient = current_timestamp`;
      }
      if (messageData.read) {
        sqlSet += `, date_readclient = current_timestamp`;
      }
      if (messageData.message_id) {
        sqlSet += ` where message_id= '${messageData.message_id}' `;
      } else if (messageData.idunico) {
        sqlSet += ` where idunico= '${messageData.idunico}' `;
      }

      const sql = 'update messages SET ' + sqlSet;

      //console.log('Messages > statusMessage', sql);

      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      //console.log('respMessage at line 599 in data/MessagesDB.ts:', respMessage);

      return sucesso(respMessage.data);

      //return sucesso([]);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }

  static async listMessages(messageData: IMessage): Promise<IRetorno> {
    try {
      //console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      let sql = `select *,to_char(message_timestamp,'HH24:mi') horario
       ,(case when (date_readclient is not null) then 'Lido'
        when (date_receivedclient is not null) then 'Recebido'
        when (date_sentclient is not null) then 'Enviado'
        else null end) status_message
      from messages where instance ='${messageData.instance}'
       and telefone ='${telefone}' 
       and cast(message_timestamp as date) >= current_date-3
       order by id`;

      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
  static async consultaMensagem(messageData: IMessage): Promise<IRetorno> {
    try {
      //console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      let sql = `select telefone,message_id,message_timestamp,instance
      from messages where instance ='${messageData.instance}'
       and telefone ='${telefone}' 
       and cast(message_timestamp as date) >= current_date-1
       and message_id ='${messageData.message_id}'`;

      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);

      return respMessage;
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
  static async getUrlMidiaMessageQuoted(messageData: IMessage): Promise<IRetorno> {
    try {
      //console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }
      let sql = `select url_midia,file_name,type_message
      from messages where instance ='${messageData.instance}'
       and telefone ='${telefone}'
       and message_id ='${messageData.message_id}' `;

      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);
      //console.log('respMessage at line 540 in data/MessagesDB.ts:', respMessage);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }

  static async getNewNumber(messageData: IMessage): Promise<IRetorno> {
    try {
      // console.log('messageData at line 216 in data/MessagesDB.ts:', messageData);
      let telefone = messageData.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }
      let sql = `select count(*) qt from last_message 
      where instance = '${messageData.instance}' and telefone = '${telefone}' `;

      //console.log('sql at line 225 in data/MessagesDB.ts:', sql);
      const respMessage = await new PostgreSQLServices().query(sql);

      return sucesso(respMessage.data);
    } catch (error) {
      console.error('Error handling and storing message:', error);
      return erroInterno(error);
    }
  }
}

export default MessagesDB;
