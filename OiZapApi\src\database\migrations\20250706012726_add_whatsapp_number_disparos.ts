import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('disparos_pixel migration started');
    let hasColumn = await knex.schema.hasColumn('disparos_pixel', 'whatsapp_number');

    if (!hasColumn) {
      await knex.schema.alterTable('disparos_pixel', (table) => {
        table.integer('whatsapp_number');
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('disparos_pixel', (table) => {
      table.dropColumn('whatsapp_number');
    });
  } catch (error) {
    throw error;
  }
}
