import { config } from 'dotenv';
import { EstabelecimentosDB } from '../../../data/EstabelecimentosDB';
import { DisparosPixelDB } from '../../../data/DisparosPixelDB';
import { IRetorno, sucesso } from '../../../interfaces/IRetorno';
import { MessagesModel } from '../../../models/MessagesModel';
import MessagesRabbitMQ from '../../../models/MessagesRabbitMQ';
import { PushWebHook } from '../../../services/PushWebHook';
import { BotServices } from '../../BotServices';
import Logger from '../../Logger';
config();
const logger = Logger.getLogger();

export class handlerMessagesUpSert {
  static async messagesUpSet(messageContent: any): Promise<IRetorno> {
    try {
      logger.info('MessagesUpSertConsumer > messageContent: ' + JSON.stringify(messageContent));
      if (
        messageContent.data.key.remoteJid.includes('status@broadcast') ||
        messageContent.data.key.remoteJid.includes('@g.us') ||
        messageContent.data.message?.call ||
        messageContent.data.messageType == 'editedMessage'
      ) {
        return sucesso({}, 'nack');
      }

      const messageRabbitMQ = new MessagesRabbitMQ(messageContent);
      // logger.debug('MessagesUpSertConsumer > messageRabbitMQ: ' + JSON.stringify(messageRabbitMQ));
      const processedData = await messageRabbitMQ.messageUpSert();
      //console.log('🚀 ~ handlerMessagesUpSert.ts:28 ~ messagesUpSet ~ processedData:', processedData);
      logger.debug('MessagesUpSertConsumer > processedData :' + JSON.stringify(processedData));

      const respModulosAtivos = await EstabelecimentosDB.modulosAtivos({
        nameinstance: messageContent.instance,
      });

      const inAPI = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_api : false;
      const inChat = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_chat_pedidos : false;
      const inCrm = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_crm : false;

      if (inCrm == true) {


        const { messagen, telefone } = processedData.data[0];
        const match = messagen.match(/ID: (\d+)/);
        const campanha_id = match ? match[1] : null;
        await DisparosPixelDB.criarLead({
          cd_disparo: campanha_id ? parseInt(campanha_id) : null,
          numero: telefone,
          mensagem: messagen,
          data_mensagem: new Date(),
        });
      }

      //Quando foi API, nao vai salvar a mensagem no banco, apenas envia o webhook
      if (inAPI == true && inChat == false) {
        const retorno = {
          webhookType: 'receive',
          event: 'receive',
          ...processedData.data[0],
        };

        await PushWebHook.enviarWebhookComRetry(messageContent.instance, retorno, 3);
      }

      const respConsultaMessage = await new MessagesModel().consultaMensagem(processedData.data[0]);
      //se achar a mesma mensagem com o message_id não continua
      if (respConsultaMessage.statuscode === 200) {
        return sucesso({}, 'nack');
      }

      //salva a mensagem no banco
      const respMessage = await new MessagesModel().incluiMessages(processedData, messageContent);
      logger.debug('MessagesUpSertConsumer > incluiMessages > respMessage :' + JSON.stringify(respMessage));

      if (respMessage && respMessage.statuscode === 200) {
        const dataArray = respMessage.data as any[];
        let dataMensagem: any;
        if (dataArray.length > 0) {
          dataMensagem = dataArray[0];
          // processedData.data[0].atendimento = dataMensagem.atendimento;
        }
        //logger.debug('dataMensagem :', dataMensagem);
        logger.debug('MessagesUpSertConsumer > dataMensagem :' + JSON.stringify(dataMensagem));

        const respInt = await new BotServices().interpetra(dataMensagem);
        // logger.debug('respInt at line 41 in Evolution/handlerMessagesUpSert.ts:' + JSON.stringify(respInt));

        if (respInt.data.length > 0) {
          const data = respInt.data[0];
          if (data?.tp_etapachat != undefined || data?.tp_etapachat != null) {
            dataMensagem.tp_etapachat = data?.tp_etapachat;
            dataMensagem.in_stop_bot = data?.in_stop_bot;
          }
        }
        const messageRabbitMQ = {
          statuscode: 200,
          message: 'Sucesso',
          event: dataMensagem.event,
          instance: dataMensagem.instance,
          data: [dataMensagem],
        };

        // logger.debug(
        //   'messageRabbitMQ at line 58 in Evolution/handlerMessagesUpSert.ts:' + JSON.stringify(messageRabbitMQ),
        // );
        return sucesso(messageRabbitMQ, 'ack');
        //console.log('respBot :', respBot);

        //console.log('dataMensagem at line 103 in rabbitmq/MessagesUpSertConsumer.ts:', dataMensagem);
        //const respBot = await this.botHandler.fluxoAtendimento(dataMensagem);
      } else {
        logger.error('MessagesUpSertConsumer > incluiMessages > respMessage - reject :' + JSON.stringify(respMessage));
        return sucesso(respMessage, 'reject');
      }
    } catch (error: any) {
      logger.error('error at line 50 in Evolution/handlerMessagesUpSert.ts:' + JSON.stringify(error));
      return sucesso(error, 'reject');
    }
  }
}
