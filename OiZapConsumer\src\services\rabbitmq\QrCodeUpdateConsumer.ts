import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection, ConsumeMessage } from 'amqplib';

export default class QrCodeUpdateConsumer {
  private queueName: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private io: any;
  private CONTINUE: boolean = false;

  constructor(queueName: string, io: any) {
    this.queueName = queueName;
    this.io = io;
  }

  async connect() {
    try {
      let room = this.queueName.replace('.qrcode.updated', '');
      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();
      await this.channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });

      this.channel.consume(this.queueName, async (message: ConsumeMessage | null) => {
        if (!this.CONTINUE) {
          return;
        }

        if (message !== null) {
          const messageContent = JSON.parse(message.content.toString());

          const data = {
            event: messageContent.event,
            nameinstance: messageContent.instance,
            message: messageContent.data.statusCode === 500 ? messageContent.data.message : 'qrcode.update',
            code: messageContent.data?.qrcode?.code,
            base64: messageContent.data?.qrcode?.base64,
          };

          this.io.to(room).emit('qrcode.updated', data);

          this.channel!.ack(message);
        }
      });
    } catch (error) {
      console.error('Error connecting to RabbitMQ-QrCodeUpdate:', error);
    }
  }

  close() {
    if (this.connection) {
      this.connection.close();
    }
  }
}
