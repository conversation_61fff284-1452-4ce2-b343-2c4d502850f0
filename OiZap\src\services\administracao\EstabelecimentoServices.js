import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';
const hostApi = hosts.apiOiZap + '/estabelecimento';

const EstabelecimentoServices = {
    /*async executa(req) {
    //console.log('EstabelecimentoServices > executa', body);
    try {
      const response = await callApi('post', hosts.api + '/estabelecimento/executar/v1', undefined, req);
      return response;
    } catch (error) {
      console.error(error);
      return null;
    }
  },*/
    async incluir(req) {
        try {
            const response = await callApi('post', hostApi + '/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async criarApiDedicada(req) {
        try {
            const response = await callApi('post', hostApi + '/criarApiDedicada/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async alterar(req) {
        try {
            const response = await callApi('put', hostApi + '/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listar(req) {
        try {
            const response = await callApi('get', hostApi + '/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async limpaDados(req) {
        try {
            const response = await callApi('delete', hostApi + '/limpaDados/v1', undefined, req);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listarEstabelecimentoExterno(req) {
        try {
            const response = await callApi('get', hostApi + '/listarEstabelecimentoExterno/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listarAtualizacaoTabelasPDV(req) {
        try {
            const response = await callApi('get', hosts.apiOiZap + '/listarAtualizacaoTabelasPDV/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaEstabelecimentoDirect(req) {
        try {
            const response = await callApi('get', hostApi + '/listaEstabelecimentoDirect/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async listaInstanciasPorEstabelecimento(req) {
        try {
            const response = await callApi('get', hostApi + '/listaInstanciasPorEstabelecimento/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
    async inAtualizaTabelasPDV(req) {
        try {
            const response = await callApi('get', hosts.apiOiZap + '/inAtualizaTabelasPDV/v1', req, undefined);
            return response;
        } catch (error) {
            console.error(error);
            return null;
        }
    },
};
export default EstabelecimentoServices;
