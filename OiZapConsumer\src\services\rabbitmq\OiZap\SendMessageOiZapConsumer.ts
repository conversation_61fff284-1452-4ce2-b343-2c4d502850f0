import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { Message } from 'amqplib';
import { WhatsAppService } from '../../WhatsAppService';
//import { MessagesModel } from '../../models/MessagesModel';
import MessagesDB from '../../../data/MessagesDB';
import { IMessage } from '../../../interfaces/IMessage';
//import { BotServices } from '../BotServices';
import Logger from '../../Logger';
import RabbitMQConnectionOiZap from './RabbitMQConnectionOiZap';
const logger = Logger.getLogger();

export default class SendMessageOiZapConsumer {
  private queueName: string;
  private io: any;
  //private connection: Connection | null = null;
  //private channel: Channel | null = null;
  private CONTINUE = false;
  //private CONTINUE = true; // Variável para controle do consumo contínuo
  private MAX_ATTEMPTS = 10; // Máximo de 10 tentativas

  constructor(queueName: string, io: any) {
    this.queueName = queueName;
    this.io = io;
  }

  // private async ensureQueueExists(channel: Channel, queueName: string): Promise<void> {
  //   try {
  //     await channel.assertQueue(queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });
  //   } catch (error) {
  //     console.error('Error ensuring queue exists:', error);
  //   }
  // }

  public async connect(): Promise<void> {
    try {
      let room = this.queueName.replace('.oizap.sendmessage', '');
      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      logger.debug('SendMessageOiZapConsumer > room: ' + room + ' > INSTANCE_DEV: ' + this.CONTINUE);

      // let HOST_RABBITMQ: string = process.env.HOST_RABBITMQ_OIZAP as string;
      //logger.debug('SendMessageOiZapConsumer > HOST_RABBITMQ: ' + HOST_RABBITMQ);

      /*PROCESSO DE CONEXÃO PARA CADA CLASSE*/
      // this.connection = await amqp.connect(HOST_RABBITMQ);
      // channel = await this.connection.createChannel();
      //await channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });
      //await this.ensureQueueExists(channel, this.queueName);

      const channel = await RabbitMQConnectionOiZap.getInstance().connect();
      await channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });
      channel.prefetch(1);

      channel.consume(this.queueName, async (message: Message | null) => {
        if (!this.CONTINUE) return;

        if (message !== null) {
          const messageContent = JSON.parse(message.content.toString());
          logger.debug('SendMessageOiZapConsumer > messageContent: ' + JSON.stringify(messageContent));
          let attemptCount = messageContent.attempts || 0;
          //const queue = this.queueName;
          //console.log('.oizap.sendmessage');

          const respState = await WhatsAppService.connectionState(messageContent.instance);
          logger.debug('SendMessageOiZapConsumer > connectionState: ' + JSON.stringify(respState));

          if (respState.statuscode === 200 && respState.data.status_connection !== 'Conectado') {
            await new Promise((resolve) => setTimeout(resolve, 60000)); // Wait for 1 minuto
            channel.nack(message, false, true); // Reenfileira a mensagem
            return;
          }

          if (messageContent.type == 'Message') {
            //console.log('queue at line 52 in rabbitmq/SendMessageOiZapConsumer.ts:', queue);

            //console.log('messageContent at line 86 in rabbitmq/SendMessageOiZapConsumer.ts:', messageContent);
            logger.debug(`SendMessageOiZapConsumer > messageContent : ${JSON.stringify(messageContent)}`);
            const respWhats = await WhatsAppService.sendMessage(messageContent);
            //console.log('respWhats :', respWhats);
            //console.log('respWhats at line 90 in rabbitmq/SendMessageOiZapConsumer.ts:', respWhats);
            logger.debug(`SendMessageOiZapConsumer > sendMessage : ${JSON.stringify(respWhats)}`);
            if (respWhats.statuscode == 200) {
              if (channel) {
                channel.ack(message);
              }
              //console.log(`Message sent to queue ${this.queueName}.`);

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          } else if (messageContent.type == 'Image') {
            const respWhats = await WhatsAppService.sendImage(messageContent);
            //console.log('respWhats at line 91 in rabbitmq/SendMessageOiZapConsumer.ts:', respWhats);

            if (respWhats.statuscode == 200) {
              if (channel) channel.ack(message);

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                url_midia: respWhats.data[0].url_midia,
                ds_base64: respWhats.data[0].ds_base64,
                file_length: respWhats.data[0].file_length,
                file_sha256: respWhats.data[0].file_sha256,
                media_key: respWhats.data[0].media_key,
                nr_height: respWhats.data[0].nr_height,
                nr_width: respWhats.data[0].nr_width,
                mimetype: respWhats.data[0].mimetype,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);
              // console.log('resp at line 118 in rabbitmq/SendMessageOiZapConsumer.ts:', resp);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              //console.log('emitEvent at line 125 in rabbitmq/SendMessageOiZapConsumer.ts:', emitEvent);
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          } else if (messageContent.type == 'Document') {
            //console.log('messageContent at line 211 in rabbitmq/SendMessageOiZapConsumer.ts:', messageContent);
            const respWhats = await WhatsAppService.sendDocument(messageContent);
            //console.log('respWhats at line 91 in rabbitmq/SendMessageOiZapConsumer.ts:', respWhats);

            if (respWhats.statuscode == 200) {
              if (channel) {
                channel.ack(message);
              }

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                url_midia: respWhats.data[0].url_midia,
                ds_base64: respWhats.data[0].ds_base64,
                file_length: respWhats.data[0].file_length,
                file_sha256: respWhats.data[0].file_sha256,
                file_name: respWhats.data[0].file_name,
                media_key: respWhats.data[0].media_key,
                nr_height: respWhats.data[0].nr_height,
                nr_width: respWhats.data[0].nr_width,
                mimetype: respWhats.data[0].mimetype,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);
              // console.log('resp at line 118 in rabbitmq/SendMessageOiZapConsumer.ts:', resp);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              //console.log('emitEvent at line 125 in rabbitmq/SendMessageOiZapConsumer.ts:', emitEvent);
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          } else if (messageContent.type == 'Audio') {
            console.log('messageContent at line 211 in rabbitmq/SendMessageOiZapConsumer.ts:', messageContent);
            const respWhats = await WhatsAppService.sendAudio(messageContent);
            console.log('respWhats at line 91 in rabbitmq/SendMessageOiZapConsumer.ts:', respWhats);

            if (respWhats.statuscode == 200) {
              if (channel) {
                channel.ack(message);
              }

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                url_midia: respWhats.data[0].url_midia,
                ds_base64: respWhats.data[0].ds_base64,
                file_length: respWhats.data[0].file_length,
                file_sha256: respWhats.data[0].file_sha256,
                file_name: respWhats.data[0].file_name,
                media_key: respWhats.data[0].media_key,
                nr_height: respWhats.data[0].nr_height,
                nr_width: respWhats.data[0].nr_width,
                mimetype: respWhats.data[0].mimetype,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);
              // console.log('resp at line 118 in rabbitmq/SendMessageOiZapConsumer.ts:', resp);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              //console.log('emitEvent at line 125 in rabbitmq/SendMessageOiZapConsumer.ts:', emitEvent);
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          } else if (messageContent.type == 'List') {
            const respWhats = await WhatsAppService.sendList(messageContent);

            if (respWhats.statuscode == 403) {
              if (channel) {
                channel.ack(message);
              }
            } else if (respWhats.statuscode == 200) {
              if (channel) {
                channel.ack(message);
              }

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                title: respWhats.data[0].title,
                description: respWhats.data[0].description,
                buttonText: respWhats.data[0].buttonText,
                listType: respWhats.data[0].listType,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);
              // console.log('resp at line 118 in rabbitmq/SendMessageOiZapConsumer.ts:', resp);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              //console.log('emitEvent at line 125 in rabbitmq/SendMessageOiZapConsumer.ts:', emitEvent);
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          } else if (messageContent.type == 'Location') {
            const respWhats = await WhatsAppService.sendLocation(messageContent);

            if (respWhats.statuscode == 200) {
              if (channel) {
                channel.ack(message);
              }

              const dataMes: IMessage = {
                idunico: messageContent.idunico,
                from_me: true,
                instance: messageContent.instance,
                telefone: messageContent.telefone,
                message_id: respWhats.data[0].message_id,
                remote_jid: respWhats.data[0].remote_jid,
                message_timestamp: respWhats.data[0].timestamp,
                date_sent: respWhats.data[0].timestamp,
                horario: respWhats.data[0].time,
                estabelecimento: respWhats.data[0].estabelecimento,
                address: respWhats.data[0].address,
                latitude: respWhats.data[0].latitude,
                longitude: respWhats.data[0].longitude,
                in_rabbitmq: true,
              };
              //console.log('dataMes at line 73 in rabbitmq/SendMessageOiZapConsumer.ts:', dataMes);
              const resp = await MessagesDB.atualizaRetornoWhatsMessages(dataMes);
              // console.log('resp at line 118 in rabbitmq/SendMessageOiZapConsumer.ts:', resp);

              let emitEvent = respWhats.data[0];
              emitEvent.idunico = messageContent.idunico;
              emitEvent.ds_evento = messageContent.ds_evento;
              emitEvent.status_pedido = messageContent.status_pedido;
              emitEvent.pedidos = messageContent?.pedidos;
              emitEvent.ds_foto = messageContent?.ds_foto;
              emitEvent.profile_picture_base64 = messageContent?.profile_picture_base64;
              //console.log('emitEvent at line 125 in rabbitmq/SendMessageOiZapConsumer.ts:', emitEvent);
              this.io.in(room).emit('send.message.oizap.update', emitEvent);
            } else {
              attemptCount++; // Incrementa o número de tentativas
              messageContent.attempts = attemptCount; // Atualiza o contador na mensagem

              if (attemptCount >= this.MAX_ATTEMPTS) {
                // Se exceder o número de tentativas, grava o erro e remove da fila
                let log: any = respWhats?.errors ? respWhats?.errors : respWhats.message;
                const dataMes: IMessage = {
                  idunico: messageContent.idunico,
                  instance: messageContent.instance,
                  telefone: messageContent.telefone,
                  ds_log: log || '',
                };
                await MessagesDB.salvaLogMessages(dataMes);
                if (channel) channel.ack(message); // Remove da fila
              } else {
                // Se ainda não atingiu o limite de tentativas, recoloca na fila
                //if (channel) channel.nack(message, false, true);
                // Reenvia a mensagem atualizada com o valor de attempts incrementado
                messageContent.attempts = attemptCount;
                if (channel) {
                  channel.sendToQueue(this.queueName, Buffer.from(JSON.stringify(messageContent)), {
                    persistent: true,
                  });
                  channel.ack(message); // Remove a original
                }
              }
            }
          }
        }
      });
    } catch (error) {
      console.error('Error connecting to RabbitMQ-SendMessageOiZapConsumer:' + JSON.stringify(error));
      logger.info('Reconnecting in 5 seconds...');
      await new Promise((resolve) => setTimeout(resolve, 5000));
      this.connect(); // Tentativa de reconectar
    }
  }
}
