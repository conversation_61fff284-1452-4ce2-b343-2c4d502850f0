import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    let hasColumn = await knex.schema.hasColumn('messages', 'cd_departamento');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.integer('cd_departamento').comment('Indica o departamento responsável pela mensagem');
      });
    }

    hasColumn = await knex.schema.hasColumn('messages', 'cd_atendente');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.integer('cd_atendente').comment('Indica o atendente responsável pela mensagem');
      });
    }

    hasColumn = await knex.schema.hasColumn('messages', 'ds_comentario');

    if (!hasColumn) {
      await knex.schema.alterTable('messages', (table) => {
        table.string('ds_comentario').comment('Indica o comentário da mensagem');
      });
    }
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('messages', (table) => {
      table.dropColumn('cd_departamento');
      table.dropColumn('cd_atendente');
      table.dropColumn('ds_comentario');
    });
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
