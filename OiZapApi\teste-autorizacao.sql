-- ========================================
-- Teste: Sistema de Autorização
-- Data: 2025-01-09
-- Descrição: Script para testar o sistema de autorização com diferentes perfis
-- ========================================

-- 1. Limpar dados de teste anteriores
DELETE FROM usuarios_perfis WHERE cd_usuario IN (87, 88, 89);
DELETE FROM perfis_telas WHERE cd_perfil IN (1, 2, 3);
DELETE FROM telas WHERE ds_rota IN ('/cadPerfil', '/cadTela', '/cadPerfisTelas', '/statusAutorizacao');
DELETE FROM perfis WHERE nm_perfil IN ('Administrador', 'Atendente', 'Gerente');

-- 2. <PERSON><PERSON>r perfis de teste
INSERT INTO perfis (nm_perfil, created_at) 
VALUES 
  ('Administrador', CURRENT_TIMESTAMP),
  ('Atendente', CURRENT_TIMESTAMP),
  ('Gerente', CURRENT_TIMESTAMP);

-- 3. Criar telas de teste
INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES 
  ('Cadastro de Perfil', '/cadPerfil', CURRENT_TIMESTAMP),
  ('Cadastro de Tela', '/cadTela', CURRENT_TIMESTAMP),
  ('Associação Perfis x Telas', '/cadPerfisTelas', CURRENT_TIMESTAMP),
  ('Status Autorização', '/statusAutorizacao', CURRENT_TIMESTAMP),
  ('Dashboard', '/dashboard', CURRENT_TIMESTAMP),
  ('Home', '/home', CURRENT_TIMESTAMP),
  ('Chat', '/chat', CURRENT_TIMESTAMP),
  ('Atendimento', '/atendimento', CURRENT_TIMESTAMP),
  ('Clientes', '/listaClientes', CURRENT_TIMESTAMP),
  ('Contatos', '/listaContatos', CURRENT_TIMESTAMP),
  ('Mensagens', '/listaMensagem', CURRENT_TIMESTAMP),
  ('Fluxos', '/listaFluxos', CURRENT_TIMESTAMP),
  ('Configurações', '/integracao', CURRENT_TIMESTAMP),
  ('Usuários', '/listaUsuarios', CURRENT_TIMESTAMP),
  ('Cadastro de Usuário', '/cadUsuario', CURRENT_TIMESTAMP),
  ('Estabelecimentos', '/listaEstabelecimento', CURRENT_TIMESTAMP),
  ('Cadastro de Estabelecimento', '/cadEstabelecimento', CURRENT_TIMESTAMP);

-- 4. Associar usuários aos perfis
INSERT INTO usuarios_perfis (cd_usuario, cd_perfil, created_at, updated_at)
VALUES 
  (87, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Usuário 87 = Administrador
  (88, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Usuário 88 = Atendente
  (89, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP); -- Usuário 89 = Gerente

-- 5. Associar telas ao perfil Administrador (todas as permissões)
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
SELECT 1, cd_tela, true, true, true, true, CURRENT_TIMESTAMP
FROM telas;

-- 6. Associar telas ao perfil Atendente (apenas visualização e operações básicas)
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
SELECT 2, cd_tela, 
  CASE 
    WHEN ds_rota IN ('/cadPerfil', '/cadTela', '/cadPerfisTelas', '/statusAutorizacao', '/listaUsuarios', '/cadUsuario', '/listaEstabelecimento', '/cadEstabelecimento') 
    THEN false 
    ELSE true 
  END,
  CASE 
    WHEN ds_rota IN ('/chat', '/atendimento', '/listaMensagem', '/listaFluxos') 
    THEN true 
    ELSE false 
  END,
  CASE 
    WHEN ds_rota IN ('/chat', '/atendimento', '/listaMensagem', '/listaFluxos') 
    THEN true 
    ELSE false 
  END,
  false,
  CURRENT_TIMESTAMP
FROM telas;

-- 7. Associar telas ao perfil Gerente (visualização e algumas operações)
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
SELECT 3, cd_tela, 
  CASE 
    WHEN ds_rota IN ('/cadPerfil', '/cadTela', '/cadPerfisTelas', '/statusAutorizacao') 
    THEN false 
    ELSE true 
  END,
  CASE 
    WHEN ds_rota IN ('/chat', '/atendimento', '/listaMensagem', '/listaFluxos', '/listaClientes', '/listaContatos', '/integracao') 
    THEN true 
    ELSE false 
  END,
  CASE 
    WHEN ds_rota IN ('/chat', '/atendimento', '/listaMensagem', '/listaFluxos', '/listaClientes', '/listaContatos', '/integracao') 
    THEN true 
    ELSE false 
  END,
  false,
  CURRENT_TIMESTAMP
FROM telas;

-- 8. Verificações finais
SELECT '=== PERFIS CRIADOS ===' as info;
SELECT cd_perfil, nm_perfil FROM perfis ORDER BY cd_perfil;

SELECT '=== TELAS CRIADAS ===' as info;
SELECT cd_tela, nm_tela, ds_rota FROM telas ORDER BY cd_tela;

SELECT '=== ASSOCIAÇÕES USUÁRIO-PERFIL ===' as info;
SELECT up.cd_usuario, p.nm_perfil, up.cd_perfil 
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
ORDER BY up.cd_usuario;

SELECT '=== PERMISSÕES ADMINISTRADOR ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM perfis_telas pt
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE pt.cd_perfil = 1
ORDER BY t.nm_tela;

SELECT '=== PERMISSÕES ATENDENTE ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM perfis_telas pt
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE pt.cd_perfil = 2
ORDER BY t.nm_tela;

SELECT '=== PERMISSÕES GERENTE ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM perfis_telas pt
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE pt.cd_perfil = 3
ORDER BY t.nm_tela;

-- 9. Teste de consulta para usuário específico
SELECT '=== TESTE: TELAS DO USUÁRIO 87 (ADMIN) ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 87 AND pt.in_visualizar = true
ORDER BY t.nm_tela;

SELECT '=== TESTE: TELAS DO USUÁRIO 88 (ATENDENTE) ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 88 AND pt.in_visualizar = true
ORDER BY t.nm_tela;

SELECT '=== TESTE: TELAS DO USUÁRIO 89 (GERENTE) ===' as info;
SELECT t.nm_tela, t.ds_rota, pt.in_visualizar, pt.in_inserir, pt.in_alterar, pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 89 AND pt.in_visualizar = true
ORDER BY t.nm_tela; 