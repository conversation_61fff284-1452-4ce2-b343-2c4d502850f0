import { Knex } from 'knex';

export async function indexExists(knex: Knex, tableName: string, indexName: string): Promise<boolean> {
  const result = await knex.raw(
    `
    SELECT COUNT(*) as count
    FROM pg_indexes 
    WHERE indexname = ? 
      AND tablename = ?
  `,
    [indexName, tableName],
  );

  return parseInt(result.rows[0].count) > 0;
}

export async function safeCreateIndex(
  knex: Knex,
  tableName: string,
  columnName: string,
  indexName: string,
): Promise<void> {
  const exists = await indexExists(knex, tableName, indexName);

  if (!exists) {
    await knex.schema.alterTable(tableName, (table) => {
      table.index(columnName, indexName);
    });
    console.log(`Índice ${indexName} criado com sucesso`);
  } else {
    console.log(`Índice ${indexName} já existe, pulando criação`);
  }
}
