-- Migration: add_estabelecimento_motivo_atendimento
-- Created: 2025-06-22T01:00:29.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna cd_estabelecimento na tabela motivos_atendimento
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'motivos_atendimento' 
        AND column_name = 'cd_estabelecimento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE motivos_atendimento ADD COLUMN cd_estabelecimento INTEGER;
        COMMENT ON COLUMN motivos_atendimento.cd_estabelecimento IS 'Código do estabelecimento relacionado ao motivo';
        RAISE NOTICE 'Coluna cd_estabelecimento adicionada à tabela motivos_atendimento';
    ELSE
        RAISE NOTICE 'Coluna cd_estabelecimento já existe na tabela motivos_atendimento';
    END IF;
END $$; 