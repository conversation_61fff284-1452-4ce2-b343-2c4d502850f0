<template>
    <!-- Header <PERSON> <PERSON><PERSON> de módu<PERSON> -->
    <div class="p-4">
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <Building2Icon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Cadastro Simples</h3>
                        <p class="text-emerald-100 text-sm">Tela de cadastro simples.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Formulário -->
        <div class="bg-white dark:bg-slate-800 rounded-b-xl border-t-0">
            <div class="p-6">
                <form @submit.prevent="save">
                    <div class="grid grid-cols-12 gap-4 mb-6">
                        <!-- Campo 1 -->
                        <div class="col-span-12 md:col-span-8">
                            <label
                                for="ds_campo1"
                                class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
                            >
                                Campo 1
                                <span class="text-red-500">*</span>
                            </label>
                            <BaseInput
                                :disabled="loading"
                                autofocus
                                id="ds_campo1"
                                v-model.trim="formData.ds_campo1"
                                type="text"
                                name="ds_campo1"
                                placeholder="Digite o campo 1"
                                class="w-full"
                            />
                        </div>

                        <!-- Campo 2 -->
                        <div class="col-span-12 md:col-span-4">
                            <label
                                for="ds_campo2"
                                class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
                            >
                                Campo 2
                                <span class="text-red-500">*</span>
                            </label>
                            <BaseInput
                                :disabled="loading"
                                id="ds_campo2"
                                v-model.trim="formData.ds_campo2"
                                type="text"
                                name="ds_campo2"
                                placeholder="Digite o campo 2"
                                class="w-full"
                            />
                        </div>

                        <!-- Status Ativo/Inativo -->
                        <div class="col-span-12">
                            <div class="flex items-center space-x-3">
                                <div class="form-check form-switch">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        :value="formData.in_ativo"
                                        :checked="formData.in_ativo"
                                        @change="onChangeAtivo($event)"
                                        :disabled="loading"
                                    />
                                </div>
                                <label class="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    {{ formData.in_ativo ? 'Ativo' : 'Inativo' }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Campos obrigatórios -->
                    <div class="mb-6">
                        <cite class="text-xs text-slate-500 dark:text-slate-400">
                            <span class="text-red-500">*</span> Campos obrigatórios
                        </cite>
                    </div>

                    <!-- Botões de ação -->
                    <div
                        class="flex justify-end items-center gap-3 pt-4 border-t border-slate-200 dark:border-slate-600"
                    >
                        <button
                            type="submit"
                            class="btn btn-success-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                            :disabled="loading"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        >
                            <SaveIcon class="w-4 h-4 mr-1.5" v-if="!loading" />
                            <div
                                v-if="loading"
                                class="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1.5"
                            ></div>
                            <span>{{ loading ? 'Salvando...' : 'Salvar' }}</span>
                        </button>

                        <button
                            type="button"
                            class="btn btn-warning-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                            @click="limparFormulario"
                            :disabled="loading"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        >
                            <RotateCcwIcon class="w-4 h-4 mr-1.5" />
                            <span>Limpar</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Filtros e Listagem -->
        <div class="mt-2">
            <!-- Filtros -->
            <div class="bg-white dark:bg-slate-800 rounded-lg p-4 mb-4">
                <div class="flex flex-wrap gap-3 items-end">
                    <div class="flex-1 min-w-48">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Pesquisar
                        </label>
                        <BaseInput
                            v-model="filter.value"
                            type="text"
                            placeholder="Digite para filtrar..."
                            @input="onFilter"
                        />
                    </div>
                    <button
                        type="button"
                        class="btn btn-warning-soft btn-sm flex items-center px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                        @click="onResetFilter"
                    >
                        <RotateCcwIcon class="w-4 h-4 mr-1" />
                        Limpar
                    </button>
                </div>

                <div class="p-2">
                    <div class="overflow-x-auto">
                        <div id="tabulator" class="table-report table-report--tabulator"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, reactive, onMounted } from 'vue';
    import { useRouter } from 'vue-router';
    import { createIcons, icons } from 'lucide';
    import { TabulatorFull as Tabulator } from 'tabulator-tables';
    import dom from '@left4code/tw-starter/dist/js/dom';
    import cadSimplesServices from '@/services/administracao/cadSimplesServices';
    import { useToast } from '@/global-components/toastify/useToast';

    // Icons
    import { Building2Icon, SaveIcon, RotateCcwIcon } from 'lucide-vue-next';

    const router = useRouter();
    const toast = useToast();

    // Refs e Reactive
    const usuarios = ref([]);
    const tabulator = ref();
    const loading = ref(false);

    const filter = reactive({
        field: 'ds_campo1',
        type: 'like',
        value: '',
    });

    const formData = reactive({
        cd_campo: null,
        ds_campo1: '',
        ds_campo2: '',
        in_ativo: true,
    });

    // Colunas da tabela
    const columns = [
        {
            title: 'Código',
            minWidth: 80,
            responsive: 0,
            field: 'cd_campo',
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
        },
        {
            title: 'Campo 1',
            minWidth: 200,
            responsive: 0,
            field: 'ds_campo1',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Campo 2',
            minWidth: 150,
            field: 'ds_campo2',
            hozAlign: 'left',
            headerHozAlign: 'left',
            vertAlign: 'middle',
        },
        {
            title: 'Status',
            minWidth: 100,
            field: 'in_ativo',
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                const isActive = cell.getData().in_ativo;
                return `<div class="flex items-center justify-center">
                    <span class="px-2 py-1 text-xs rounded-full ${
                        isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }">
                        ${isActive ? 'Ativo' : 'Inativo'}
                    </span>
                </div>`;
            },
        },
        {
            title: 'Ações',
            minWidth: 120,
            responsive: 1,
            hozAlign: 'center',
            headerHozAlign: 'center',
            vertAlign: 'middle',
            formatter(cell) {
                const a = dom(`
                    <div class="flex justify-center items-center gap-2">
                        <button class="btn btn-sm btn-outline-primary flex items-center edit-btn">
                            <i data-lucide="edit-2" class="w-3 h-3 mr-1"></i>
                            Editar
                        </button>
                        <button class="btn btn-sm btn-outline-danger flex items-center delete-btn">
                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                            Excluir
                        </button>
                    </div>
                `);

                // Evento de editar
                dom(a)
                    .find('.edit-btn')
                    .on('click', function () {
                        editar(cell.getData());
                    });

                // Evento de excluir
                dom(a)
                    .find('.delete-btn')
                    .on('click', function () {
                        excluirRegistro(cell.getData());
                    });

                return a[0];
            },
        },
    ];

    // Métodos
    function onChangeAtivo(event) {
        formData.in_ativo = event.target.checked;
    }

    function limparFormulario() {
        formData.cd_campo = null;
        formData.ds_campo1 = '';
        formData.ds_campo2 = '';
        formData.in_ativo = true;
    }

    async function save() {
        if (!validarFormulario()) return;

        loading.value = true;

        try {
            let response;

            if (formData.cd_campo) {
                // Edição
                response = await cadSimplesServices.alterar(formData);
                if (response) {
                    toast.success('Registro alterado com sucesso!');
                }
            } else {
                // Inclusão
                response = await cadSimplesServices.incluir(formData);
                if (response) {
                    toast.success('Registro incluído com sucesso!');
                }
            }

            if (response) {
                limparFormulario();
                await listaDados();
            } else {
                toast.error('Erro ao salvar registro');
            }
        } catch (error) {
            toast.error('Erro ao salvar: ' + error.message);
        } finally {
            loading.value = false;
        }
    }

    function validarFormulario() {
        if (!formData.ds_campo1.trim()) {
            toast.error('Campo 1 é obrigatório');
            return false;
        }
        if (!formData.ds_campo2.trim()) {
            toast.error('Campo 2 é obrigatório');
            return false;
        }
        return true;
    }

    function editar(registro) {
        formData.cd_campo = registro.cd_campo;
        formData.ds_campo1 = registro.ds_campo1;
        formData.ds_campo2 = registro.ds_campo2;
        formData.in_ativo = registro.in_ativo;

        // Scroll para o topo do formulário
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    async function excluirRegistro(registro) {
        try {
            toast.confirm(
                {
                    title: 'Confirmar a exclusão',
                    description: `Tem certeza que deseja excluir o registro "${registro.ds_campo1}"?`,
                    confirmText: 'Sim',
                    cancelText: 'Não',
                    confirmColor: 'success',
                    cancelColor: 'warning',
                },
                () => {
                    excluir(registro);
                },
                () => {
                    return;
                }
            );
        } catch (error) {
            toast.error('Erro ao excluir registro: ' + error.message);
        }
    }

    async function excluir(registro) {
        try {
            const response = await MotivosAtendimentoServices.remover({ cd_campo: registro.cd_campo });

            console.log('🚀 ~ cadMotivosAtendimento.vue:353 ~ excluir ~ response:', response);
            if (response.statuscode == 200) {
                toast.success('Registro excluído com sucesso!');
                await listaDados();
            } else {
                toast.error('Erro ao excluir registro');
            }
        } catch (error) {
            toast.error('Erro ao excluir: ' + error.message);
        }
    }

    function onFilter() {
        if (tabulator.value) {
            tabulator.value.setFilter(filter.field, filter.type, filter.value);
        }
    }

    function onResetFilter() {
        filter.field = 'ds_campo1';
        filter.type = 'like';
        filter.value = '';
        onFilter();
    }

    async function listaDados() {
        try {
            usuarios.value = [];
            if (tabulator.value) {
                tabulator.value.destroy();
            }
            const data = await cadSimplesServices.listar({
                field: filter.field,
                type: filter.type,
                value: filter.value,
            });

            if (data.statuscode == 200) {
                usuarios.value = data.data;
                initTabulator();
            }
        } catch (error) {
            toast.error('Erro ao carregar dados: ' + error.message);
        }
    }

    function initTabulator() {
        if (tabulator.value) {
            tabulator.value.destroy();
        }

        tabulator.value = new Tabulator('#tabulator', {
            data: usuarios.value,
            layout: 'fitColumns',
            responsiveLayout: 'collapse',
            placeholder: 'Nenhum registro encontrado',
            columns: columns,
            pagination: 'local',
            paginationSize: 10,
            paginationSizeSelector: [5, 10, 20, 50],
        });

        tabulator.value.on('renderComplete', function () {
            createIcons({
                icons,
                'stroke-width': 1.5,
                nameAttr: 'data-lucide',
            });
        });
    }

    const reInitOnResizeWindow = () => {
        window.addEventListener('resize', () => {
            if (tabulator.value) {
                tabulator.value.redraw();
                createIcons({
                    icons,
                    'stroke-width': 1.5,
                    nameAttr: 'data-lucide',
                });
            }
        });
    };

    onMounted(async () => {
        reInitOnResizeWindow();
        await listaDados();
    });
</script>
