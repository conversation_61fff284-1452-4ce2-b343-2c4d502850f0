<template>
    <div
        class="flex flex-col divide-y divide-gray-100 dark:divide-darkmode-400"
        :class="{
            'text-xs': true,
            'h-full': chatsPedidosRealizados.length > 0,
            'h-full': chatsNaoPedidos.length > 0,
            'h-max': chatsPedidosRealizados.length === 0 && chatsNaoPedidos.length === 0,
        }"
    >
        <!-- Lista de Conversas Normais -->
        <div
            class="chat__chat-list overflow-y-auto"
            :style="chatsPedidosRealizados.length > 0 ? 'flex: 1; max-height: 50%;' : 'flex: 1; height: 100%;'"
            @scroll="$emit('scroll', $event)"
            v-show="chatsNaoPedidos.length > 0"
        >
            <ChatListItem
                v-for="(chat, index) in chatsNaoPedidos"
                :key="'chat-' + index"
                :data-chat-index="index"
                :chat="chat"
                :index="index"
                :is-active="chatAtivo === chat"
                :get-color-for-status="getColorForStatus"
                :modulosOiZap="modulosOiZap"
                @chat-selected="onChatSelected"
                @transferir="onTransferir"
                :qtInstancias="qtInstancias"
            />
        </div>

        <!-- Seção de Pedidos Realizados -->
        <div v-if="chatsPedidosRealizados.length > 0" class="flex-1 flex flex-col" style="max-height: 50%">
            <!-- Header dos Pedidos -->
            <div
                class="px-2 py-1 bg-gray-50 dark:bg-darkmode-600 border-b border-gray-100 dark:border-darkmode-400 flex-shrink-0"
            >
                <div class="flex items-center justify-between">
                    <h4 class="text-xs font-medium text-gray-700 dark:text-slate-300">Pedidos Realizados</h4>
                    <span
                        :class="{
                            'text-xs': true,
                            'text-red-600 dark:text-red-400 font-medium': passouDoisMinutos,
                            'text-gray-500 dark:text-slate-400': !passouDoisMinutos,
                        }"
                    >
                        Última sincronização ás
                        {{ converters.date('DD/MM HH:MM:SS', maiorDtUltimaImportacao) }}
                    </span>
                </div>
            </div>

            <!-- Lista de Pedidos com Scroll -->
            <div class="chat__chat-list-pedidos overflow-y-auto flex-1">
                <ChatListItem
                    v-for="(chat, index) in chatsPedidosRealizados"
                    :key="'pedido-' + index"
                    :chat="chat"
                    :index="index"
                    :is-active="chatAtivo === chat"
                    :get-color-for-status="getColorForStatus"
                    :modulosOiZap="modulosOiZap"
                    @chat-selected="onChatSelected"
                    :qtInstancias="qtInstancias"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
    import ChatListItem from './ChatListItem.vue';
    import converters from '@/utils/converters';

    // Props
    const props = defineProps({
        chatsNaoPedidos: {
            type: Array,
            required: true,
        },
        chatsPedidosRealizados: {
            type: Array,
            required: true,
        },
        chatAtivo: {
            type: Object,
            default: null,
        },
        getColorForStatus: {
            type: Function,
            required: true,
        },
        passouDoisMinutos: {
            type: Boolean,
            default: false,
        },
        maiorDtUltimaImportacao: {
            type: [String, Date],
            default: null,
        },
        modulosOiZap: {
            type: Object,
            default: () => ({}),
        },
        qtInstancias: {
            type: Number,
            default: 0,
        },
    });

    // Emits
    const emit = defineEmits(['chat-selected', 'scroll', 'transferir']);

    // Methods
    const onChatSelected = (chat, index) => {
        emit('chat-selected', chat, index);
    };
    const onTransferir = (chat, index) => {
        emit('transferir', chat, index);
    };
</script>
