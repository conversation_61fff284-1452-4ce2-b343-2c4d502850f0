//const PostgreSQL = require('../services/PostgreSQL');
import axios from 'axios';
import { Request } from 'express';
import { ContatosDB } from '../data/ContatosDB';
import { IRetorno, erroInterno, naoAutorizado, parametrosInvalidos, sucesso } from '../interfaces/IRetorno';
export class ContatosModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      return await ContatosDB.incluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterar(req: Request): Promise<IRetorno> {
    try {
      return await ContatosDB.alterar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listar(req: Request): Promise<IRetorno> {
    try {
      return await ContatosDB.listar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async remover(req: Request): Promise<IRetorno> {
    try {
      return await ContatosDB.remover(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
