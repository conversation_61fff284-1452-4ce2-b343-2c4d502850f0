import bcrypt from 'bcryptjs';
import { config } from 'dotenv';
import { Request } from 'express';
import jwt from 'jsonwebtoken';
import { EstabelecimentoModulosDB } from '../../data/estabelecimento/EstabelecimentoModulosDB';
import { UsuarioDB } from '../../data/usuarios/UsuarioDB';
import { UsuariosEstabelecimentosDB } from '../../data/usuarios/UsuarioEstabelecimentosDB';
import { IRetorno, erroInterno, naoAutorizado, sucesso } from '../../interfaces/IRetorno';
import { Funcoes } from '../../services/Funcoes';
config();

interface IUsuario {
  cd_usuario: number;
  ds_nome: string;
  ds_login: string;
  tp_privilegio: string;
  ds_privilegio: string;
  ds_status: string;
  cd_profissional: number;
  tp_status: string;
  instance: string;
  nr_hashunica: string;
  in_visualizaalerta: string;
  token: string;
  expires_in: Date;
  estabelecimento: string[];
  acessos: string[];
  in_aplicadesconto: boolean;
  in_usuarioadmin: boolean;
  modulos: string[];
}

export class UsuarioModel {
  async incluirUsuario(req: Request): Promise<IRetorno> {
    const cd_estabelecimento = req.body.cd_estabelecimento;
    try {
      delete req.body.cd_estabelecimento;
      const departamentos = req.body.cd_departamento;
      delete req.body.cd_departamento;
      const nr_hashunica = Funcoes.gerarHash(req.body.ds_email, 25);
      req.body.nr_hashunica = nr_hashunica;
      const ds_senha = await Funcoes.hashPassword(req.body.ds_senha);
      req.body.ds_senha = ds_senha;
      const respUsuario = await UsuarioDB.incluirUsuario(req);

      const cd_usuario = respUsuario.data[0].cd_usuario;

      if (departamentos != undefined) {
        req.body = {
          cd_usuario: req.body.cd_usuario,
          cd_estabelecimento: cd_estabelecimento,
        };

        await UsuarioDB.removerUsuarioDepartamentos(req);

        // Converter para array se for string
        let arrayDepartamentos;
        if (typeof departamentos === 'string') {
          // Remove aspas simples e divide por vírgula
          arrayDepartamentos = departamentos.split(',').map((dept) => dept.replace(/'/g, '').trim());
        } else if (Array.isArray(departamentos)) {
          arrayDepartamentos = departamentos;
        } else {
          // Se for um único valor
          arrayDepartamentos = [departamentos];
        }

        for (const element of arrayDepartamentos) {
          req.body = {
            cd_usuario: req.body.cd_usuario,
            cd_departamento: element,
            cd_estabelecimento: cd_estabelecimento,
          };

          await UsuarioDB.incluirUsuarioDepartamento(req);
        }
      }

      //const nrHash = cd_usuario + cd_estabelecimento;
      //const nr_hash = Funcoes.gerarHash(nrHash, 25);
      req.body = {
        cd_usuario: cd_usuario,
        cd_estabelecimento: cd_estabelecimento,
        nr_hash: nr_hashunica,
      };

      await UsuariosEstabelecimentosDB.incluirUsuariosEstabelecimentos(req);

      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterarUsuario(req: Request): Promise<IRetorno> {
    try {
      const body = req.body;

      if (req.body.ds_senha != undefined) {
        const ds_senha = await Funcoes.hashPassword(req.body.ds_senha);
        req.body.ds_senha = ds_senha;
      }
      const departamentos = req.body.cd_departamento;
      delete req.body.cd_departamento;

      if (departamentos != undefined) {
        req.body = {
          cd_usuario: body.cd_usuario,
          cd_estabelecimento: body.cd_estabelecimento,
        };

        await UsuarioDB.removerUsuarioDepartamentos(req);

        // Converter para array se for string
        let arrayDepartamentos;
        if (typeof departamentos === 'string') {
          // Remove aspas simples e divide por vírgula
          arrayDepartamentos = departamentos.split(',').map((dept) => dept.replace(/'/g, '').trim());
        } else if (Array.isArray(departamentos)) {
          arrayDepartamentos = departamentos;
        } else {
          // Se for um único valor
          arrayDepartamentos = [departamentos];
        }

        for (const element of arrayDepartamentos) {
          req.body = {
            cd_usuario: body.cd_usuario,
            cd_departamento: element,
            cd_estabelecimento: body.cd_estabelecimento,
          };

          await UsuarioDB.incluirUsuarioDepartamento(req);
        }
      }
      delete req.body.cd_estabelecimento;
      delete req.body.cd_departamento;
      const resp = await UsuarioDB.alterarUsuario(req);
      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alteraSenha(req: Request): Promise<IRetorno> {
    try {
      // console.log('req.body at line 78 in usuarios/UsuarioModel.ts:', req.body);
      let dsSenha = await Funcoes.hashPassword(req.body.ds_senhanova);

      req.body = {
        cd_usuario: req.body.cd_usuario,
        ds_senha: dsSenha,
      };

      return await UsuarioDB.alterarUsuario(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarUsuario(req: Request): Promise<IRetorno> {
    try {
      return await UsuarioDB.listarUsuario(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listaUsuarios(req: Request): Promise<IRetorno> {
    try {
      const respUsuario = await UsuarioDB.listaUsuarios(req);

      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaUsuariosDepartamentos(req: Request): Promise<IRetorno> {
    try {
      return await UsuarioDB.listaUsuariosDepartamentos(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaUsuariosPorDepartamento(req: Request): Promise<IRetorno> {
    try {
      const respUsuario = await UsuarioDB.listaUsuariosPorDepartamento(req);

      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaUsuariosTodos(req: Request): Promise<IRetorno> {
    try {
      const respUsuario = await UsuarioDB.listaUsuariosTodos(req);

      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async login(req: Request): Promise<IRetorno> {
    try {
      const result = await UsuarioDB.login(req);

      //console.log('result:', result);

      if (result.statuscode != 200) {
        return result;
      }

      const dataUsuario = result.data[0];

      const currentDate = new Date();
      const expireSeconds = parseInt(process.env.EXPIRESECONDS_TOKEN || '0');
      const expirationDate = new Date(currentDate.getTime() + expireSeconds * 1000);

      let senhaOk;

      //VERIFICAR PORQUE NA COMPARAÇÃO DA SENHA ESTÁ DANDO SENHA INCORRETA

      if (req.body.ds_senha != undefined) {
        // Aguarda a comparação das senhas
        senhaOk = await new Promise<boolean>((resolve, reject) => {
          bcrypt.compare(req.body.ds_senha, dataUsuario.ds_senha, function (err, res) {
            if (err) {
              reject(err);
            } else {
              senhaOk = true;

              resolve(res);
            }
          });
        });
      } else {
        senhaOk = true;
      }

      if (senhaOk) {
        req.query.cd_usuario = dataUsuario.cd_usuario;
        // console.log('🚀 ~ UsuarioModel.ts:151 ~ login ~ req.query:', req.query);
        const respEstabelecimentos = await UsuariosEstabelecimentosDB.listarUsuariosEstabelecimentos(req);
        // console.log('🚀 ~ UsuarioModel.ts:208 ~ login ~ respEstabelecimentos:', respEstabelecimentos);
        // console.log('🚀 ~ UsuarioModel.ts:154 ~ login ~ respEstabelecimentos:', respEstabelecimentos);
        const dataEstabelecimento = respEstabelecimentos.data;
        let acessos: any = { data: [] };
        let modulos: any = { data: [] };
        let instancias: any;
        if (respEstabelecimentos.statuscode == 200) {
          req.query.cd_estabelecimento = dataEstabelecimento[0].cd_estabelecimento;
          req.query.tp_situacao = 'Ativo';
          //const respAcessos = await UsuarioAcessosDB.listarUsuarioAcessos(req);
          //acessos.data = respAcessos.data;
          //instancias = await UsuarioInstanciaDB.listarUsuarioInstancia(req);
          //  console.log('🚀 ~ UsuarioModel.ts:166 ~ login ~ req:', req.query);
          const respModulos = await EstabelecimentoModulosDB.listarEstabelecimentoModulos(req);
          // console.log('respModulos at line 163 in usuarios/UsuarioModel.ts:', respModulos);
          modulos.data = respModulos.data;
        }
        // console.log('respEstabelecimentoModulo:', respEstabelecimentoModulo);
        // Cria o token JWT
        const token = jwt.sign(
          {
            cd_usuario: dataUsuario.cd_usuario,
            ds_nome: dataUsuario.ds_nome,
            ds_login: dataUsuario.ds_login,
            tp_privilegio: dataUsuario.tp_privilegio,
            expires_in: expirationDate,
          },
          process.env.TOKEN || '',
          {
            expiresIn: expireSeconds,
          },
        );

        const usuario: IUsuario = {
          cd_usuario: dataUsuario.cd_usuario,
          ds_nome: dataUsuario.ds_nome,
          ds_login: dataUsuario.ds_login,
          tp_privilegio: dataUsuario.tp_privilegio,
          ds_privilegio: dataUsuario.ds_privilegio,
          ds_status: dataUsuario.ds_status,
          cd_profissional: dataUsuario.cd_profissional,
          tp_status: dataUsuario.tp_status,
          instance: dataUsuario.instance,
          nr_hashunica: dataUsuario.nr_hashunica,
          in_visualizaalerta: dataUsuario.in_visualizaalerta,
          in_aplicadesconto: dataUsuario.in_aplicadesconto,
          in_usuarioadmin: dataUsuario.in_usuarioadmin,
          token: token,
          expires_in: expirationDate,
          estabelecimento: dataEstabelecimento,
          acessos: acessos.data,
          modulos: modulos.data,
          //instancias: instancias.data,
        };

        return sucesso([usuario]);
      } else {
        return naoAutorizado('Senha incorreta');
      }
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async removerUsuario(req: Request): Promise<IRetorno> {
    try {
      return await UsuarioDB.removerUsuario(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
