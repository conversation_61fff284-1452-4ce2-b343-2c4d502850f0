require('dotenv').config();
const { Client } = require('pg');

const environment = process.env.NODE_ENV || 'DEV';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

async function cleanMigration() {
  const config = getConnectionConfig(environment);
  const client = new Client(config);

  try {
    await client.connect();
    console.log(`🧹 Limpando migration problemática do banco ${environment}...`);

    // Verificar se a tabela de migrations existe
    const checkTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'pgmigrations'
      );
    `);

    if (checkTable.rows[0].exists) {
      // Remover a migration problemática
      const result = await client.query(`
        DELETE FROM pgmigrations 
        WHERE name = '001_create_table_perfis'
      `);

      console.log(`✅ Migration problemática removida! Linhas afetadas: ${result.rowCount}`);
    } else {
      console.log('📄 Tabela pgmigrations não existe ainda');
    }

    // Verificar migrations restantes
    const migrations = await client.query(`
      SELECT name FROM pgmigrations ORDER BY run_on
    `);

    if (migrations.rows.length > 0) {
      console.log('📋 Migrations restantes:');
      migrations.rows.forEach((row) => {
        console.log(`  - ${row.name}`);
      });
    } else {
      console.log('📋 Nenhuma migration registrada');
    }
  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await client.end();
  }
}

cleanMigration();
