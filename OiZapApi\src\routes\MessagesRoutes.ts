import { Router } from 'express';
import { MessagesController } from '../controllers/MessagesControllers';
import authApi from '../middleware/authApi';

export const MessagesRoutes = Router();

/* rotas */
const rotaPadrao = '/messages';

MessagesRoutes.get(
  rotaPadrao + '/listarUltimaMensagemContato/v1',
  authApi,
  MessagesController.listarUltimaMensagemContato,
);
MessagesRoutes.post(rotaPadrao + '/incluir/v1', authApi, MessagesController.incluir);
MessagesRoutes.get(rotaPadrao + '/listMessages/v1', authApi, MessagesController.listMessages);
MessagesRoutes.get(rotaPadrao + '/listarRelatorioMensagens/v1', authApi, MessagesController.listarRelatorioMensagens);
MessagesRoutes.post(rotaPadrao + '/readMessages/v1', authApi, MessagesController.readMessages);
MessagesRoutes.post(rotaPadrao + '/reenviarMensagens/v1', authApi, MessagesController.reenviarMensagens);
MessagesRoutes.get(rotaPadrao + '/lista-mensagens-data/v1', authApi, MessagesController.listaMensagensData);
MessagesRoutes.get(rotaPadrao + '/lista-mensagens-hora/v1', authApi, MessagesController.listaMensagensHora);
/* end rotas */
