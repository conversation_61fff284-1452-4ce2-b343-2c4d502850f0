@echo off
REM Script para configurar apenas o perfil Administrador no OiZap
REM Execute este script após o backend estar rodando

echo 🚀 Configurando perfil Administrador no OiZap...

REM URL base da API
set API_URL=http://localhost:3100

echo 📋 1. Criando perfil Administrador...

REM Criar perfil Administrador
echo    - Criando perfil Administrador...
curl -s -X POST "%API_URL%/perfis" -H "Content-Type: application/json" -d "{\"nm_perfil\": \"Administrador\"}"
echo.

echo 📋 2. Criando telas administrativas...

REM Criar tela de cadastro de perfil
echo    - Criando tela Cadastro de Perfil...
curl -s -X POST "%API_URL%/telas" -H "Content-Type: application/json" -d "{\"nm_tela\": \"Cadastro de Perfil\", \"ds_rota\": \"/cadPerfil\"}"
echo.

REM Criar tela de cadastro de tela
echo    - Criando tela Cadastro de Tela...
curl -s -X POST "%API_URL%/telas" -H "Content-Type: application/json" -d "{\"nm_tela\": \"Cadastro de Tela\", \"ds_rota\": \"/cadTela\"}"
echo.

REM Criar tela de associação perfis x telas
echo    - Criando tela Associação Perfis x Telas...
curl -s -X POST "%API_URL%/telas" -H "Content-Type: application/json" -d "{\"nm_tela\": \"Associação Perfis x Telas\", \"ds_rota\": \"/cadPerfisTelas\"}"
echo.

echo 📋 3. Listando perfil criado...
curl -s "%API_URL%/perfis"
echo.

echo 📋 4. Listando telas criadas...
curl -s "%API_URL%/telas"
echo.

echo 📋 5. Associando usuário 87 ao perfil Administrador...
REM Assumindo que o perfil Administrador tem cd_perfil = 1
curl -s -X POST "%API_URL%/usuarios/87/perfis" -H "Content-Type: application/json" -d "{\"cd_perfil\": 1}"
echo.

echo 📋 6. Associando telas administrativas ao perfil Administrador...
REM Assumindo que as telas têm cd_tela = 1, 2, 3
curl -s -X POST "%API_URL%/perfis/associar" -H "Content-Type: application/json" -d "{\"cd_perfil\": 1, \"telas\": [1, 2, 3]}"
echo.

echo ✅ Configuração do Administrador concluída!
echo.
echo 📝 Resumo do que foi criado:
echo    - 1 perfil: Administrador
echo    - 3 telas administrativas: /cadPerfil, /cadTela, /cadPerfisTelas
echo    - Usuário 87 associado ao perfil Administrador
echo    - Telas administrativas associadas ao perfil Administrador
echo.
echo 🔗 Agora você pode acessar:
echo    - http://localhost:8080/cadPerfil
echo    - http://localhost:8080/cadTela
echo    - http://localhost:8080/cadPerfisTelas

pause 