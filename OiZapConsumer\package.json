{"name": "OiZapConsumer", "version": "2.0.2", "description": "", "main": "server.js", "scripts": {"start": "nodemon", "start:dev": "nodemon --watch src/**/*.ts --exec ts-node src/server.ts", "watch": "tsc -w", "copy-files": "npx copyfiles package.json dist ; npx copyfiles downloads/**/* dist", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "node utils/enviarServidor.js", "localhost": "node utils/enviarServidor.js localhost", "oizap": "node utils/enviarServidor.js oizap", "sandbox": "node utils/enviarServidor.js sandbox", "devzap": "node utils/enviarServidor.js devzap", "crm": "node utils/enviarServidor.js crm", "commit": "node utils/commit.js", "version": "node utils/version.js", "tsc": "tsc"}, "devDependencies": {"@types/amqplib": "^0.10.4", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pg": "^8.11.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "copyfiles": "^2.4.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.1", "inquirer": "^8.2.4", "nodemon": "^3.1.0", "prettier": "^3.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.15.7", "typescript": "^5.5.2"}, "dependencies": {"@google-cloud/speech": "^6.0.2", "@types/fluent-ffmpeg": "^2.1.24", "@types/knex": "^0.16.1", "@whiskeysockets/baileys": "^6.5.0", "amqplib": "^0.10.8", "axios": "^1.5.1", "body-parser": "^1.20.2", "colorette": "^2.0.20", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "fs": "^0.0.1-security", "fs-extra": "^11.2.0", "http": "^0.0.1-security", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mongodb": "^6.1.0", "morgan": "^1.10.0", "natural": "^6.7.2", "nodemon": "^3.0.1", "normalize": "^0.3.1", "openai": "^4.10.0", "os": "^0.1.2", "path": "^0.12.7", "pg": "^8.11.3", "readline-sync": "^1.4.10", "redis": "^4.6.14", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "sonic-boom": "^4.1.0", "ssh2": "^1.16.0", "ssh2-sftp-client": "^10.0.3", "winston": "^3.14.2"}, "author": "", "license": "ISC"}