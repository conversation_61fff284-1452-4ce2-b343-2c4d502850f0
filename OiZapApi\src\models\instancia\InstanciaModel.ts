//const PostgreSQL = require('../services/PostgreSQL');
import { Request } from 'express';
import { EstabelecimentoInstanciasDB } from '../../data/estabelecimento/EstabelecimentoInstanciasDB';
import { InstanciaDB } from '../../data/instancia/InstanciaDB';
import { IRetorno, conflito, erroInterno, sucesso } from '../../interfaces/IRetorno';
//import { InstanciaModulosDB } from '../../data/instancia/InstanciaModulosDB';
import { WhatsServices } from '../../services/WhatsServices';
//import { InstanciaModulosModel } from './InstanciaModulosModel';
import { UsuarioInstanciaDB } from '../../data/usuarios/UsuarioInstanciasDB';
//import { FluxosDB } from '../../data/fluxos/FluxosDB';
import { EstabelecimentoDB } from '../../data/estabelecimento/EstabelecimentoDB';
import { MessagesDB } from '../../data/MessagesDB';
import { Funcoes } from '../../services/Funcoes';
import Logger from '../../services/Logger';
const logger = Logger.getLogger();

interface IInstancia {
  id_instancia: number;
  nameinstance: string;
  nome: string;
  telefone: string;
  status: string;
  status_connection: string;
  in_stop_bot: boolean;
  dt_start_stop_bot: Date;
  nr_hash: string;
  modulos: Array<string>;
}

export class InstanciaModel {
  async incluirInstancia(req: Request): Promise<IRetorno> {
    try {
      let { cd_estabelecimento, modulos, ...body } = req.body;
      const nome = req.body.nome;
      req.query = {
        nome: nome,
        cd_estabelecimento: cd_estabelecimento,
      };

      let respInstancia = await InstanciaDB.listarInstancia(req);
      // console.log('respInstancia at line 31:', respInstancia);

      if (respInstancia.statuscode == 200) {
        return conflito(respInstancia.message, 'Já existe instância cadastrada com esse nome!');
      }

      let nrHash = req.body.nome + cd_estabelecimento;
      let nr_hashunica = Funcoes.gerarHash(nrHash, 25);
      req.body = body;
      req.body.nameinstance = nr_hashunica;
      req.body.in_stop_bot = true;

      /*BEGIN: Cria a Instancia*/
      respInstancia = await InstanciaDB.incluirInstancia(req);
      //console.log('respInstancia at line 29:', respInstancia);
      if (respInstancia.statuscode != 200) {
        return respInstancia;
      }
      /*END: Cria a Instancia*/

      /*BEGIN: Relaciona a Instancia ao Estabelecimento*/
      nrHash = nr_hashunica;
      nr_hashunica = Funcoes.gerarHash(nrHash, 25);
      req.body = {
        id_instancia: respInstancia.data[0].id,
        cd_estabelecimento: cd_estabelecimento,
        nr_hash: nr_hashunica,
      };

      const respEstIns = await EstabelecimentoInstanciasDB.incluirEstabelecimentoInstancias(req);
      //console.log('respEstIns at line 44:', respEstIns);

      if (respEstIns.statuscode != 200) {
        return respEstIns;
      }
      /*END: Relaciona a Instancia ao Estabelecimento*/

      /*BEGIN: Salva os modulos contratados*/
      /*
      if (modulos.length > 0) {
        let reqBody: any = [];
        modulos.forEach((modulo: any) => {
          reqBody.push({
            id_instancia: respInstancia.data[0].id,
            cd_modulo: modulo.cd_modulo,
            tp_situacao: 'Contratado',
            vl_modulo: modulo.vl_modulo,
            in_contratado: true,
          });
        });

        req.body = reqBody;

        const respModulosContratados = await InstanciaModulosDB.incluirInstanciaModulos(req);
        //console.log('respModulosContratados at line 60:', respModulosContratados);

        if (respModulosContratados.statuscode != 200) {
          return respModulosContratados;
        }

        modulos = respModulosContratados.data;
      }
      */
      /*END: Salva os modulos contratados*/
      /*
      req.body = {
        instance: respInstancia.data[0].nameinstance,
      };

      const resultFluxo = await FluxosDB.replicaFluxo(req);

      if (resultFluxo.statuscode != 200) {
        return erroInterno(resultFluxo);
      }
      */
      const instancia: IInstancia = {
        id_instancia: respInstancia.data[0].id,
        nameinstance: respInstancia.data[0].nameinstance, //hash principal
        nome: respInstancia.data[0].nome,
        telefone: respInstancia.data[0].telefone,
        status: respInstancia.data[0].status,
        status_connection: respInstancia.data[0].status_connection,
        in_stop_bot: respInstancia.data[0].in_stop_bot,
        dt_start_stop_bot: respInstancia.data[0].dt_start_stop_bot,
        nr_hash: respEstIns.data[0].nr_hash, //hash secundário - TOKEN
        modulos: modulos,
      };

      //console.log('instancia at line 88:', instancia);

      return sucesso([instancia]);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterarInstancia(req: Request): Promise<IRetorno> {
    try {
      return await InstanciaDB.alterarInstancia(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarInstancia(req: Request): Promise<IRetorno> {
    try {
      const in_lista_instancia_usuario: any = req.query.in_lista_instancia_usuario;

      if (in_lista_instancia_usuario == 'true') {
        let query = req.query;
        req.query = {
          cd_estabelecimento: query.cd_estabelecimento,
          cd_usuario: query.cd_usuario,
        };
        // console.log('🚀 ~ InstanciaModel.ts:154 ~ listarInstancia ~ req.query:', req.query);
        const respUsuInstancia = await UsuarioInstanciaDB.listarUsuarioInstancia(req);
        // console.log('🚀 ~ InstanciaModel.ts:155 ~ listarInstancia ~ respUsuInstancia:', respUsuInstancia);

        let instancias: string[] = [];
        if (respUsuInstancia.statuscode == 200) {
          respUsuInstancia.data.forEach((inst: any) => {
            instancias.push(inst.id_instancia);
          });

          query.id_instancia = instancias;
        }

        req.query = query;
      }

      let respInstancias = await InstanciaDB.listarInstancia(req);
      // console.log('respInstancias at line 161 in models/InstanciaModel.ts:', respInstancias);
      const in_verificastatus: any = req.query.in_verificastatus;

      // for (const instancia of respInstancias.data) {
      //   if (in_verificastatus == 'true') {
      //     if (instancia.nameinstance != '') {
      //       req.query = { instance: instancia.nameinstance, nr_hash: instancia.nr_hash };
      //       console.log('req.query at line 169 in models/InstanciaModel.ts:', req.query);
      //       const respState = await WhatsServices.connectionState(req);
      //       console.log('respState at line 169 in models/InstanciaModel.ts:', respState);
      //       instancia.status_connection =
      //         respState.statuscode === 200 ? respState.data.status_connection : 'Desconectado';
      //       instancia.status = respState.statuscode === 500 ? 'notFound' : respState.data.status;
      //     }
      //   } else {
      //     instancia.status_connection = 'Desconectado';
      //   }
      //   instancia.modulos = [];
      // }

      let modulosAtivos: any;
      if (respInstancias.statuscode == 200) {
        const respModulosAtivos = await EstabelecimentoDB.modulosAtivos({
          nameinstance: respInstancias.data[0].nameinstance,
        });
        // console.log('respModulosAtivos at line 192 in instancia/InstanciaModel.ts:', respModulosAtivos);

        modulosAtivos = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data : undefined;
      }

      await Promise.all(
        respInstancias.data.map(async (instancia: any) => {
          if (in_verificastatus == 'true') {
            if (instancia.nameinstance != '') {
              req.query = { instance: instancia.nameinstance, nr_hash: instancia.nr_hash };
              //console.log('req.query at line 169 in models/InstanciaModel.ts:', req.query);
              const respState = await WhatsServices.connectionState(req);
              // console.log('respState at line 169 in models/InstanciaModel.ts:', respState);
              instancia.status_connection =
                respState.statuscode === 200 ? respState.data.status_connection : 'Desconectado';
              instancia.status =
                respState.statuscode === 500
                  ? 'notFound'
                  : respState.statuscode === 401
                    ? 'notFound'
                    : respState.data.status;

              if (modulosAtivos?.in_api) {
                const today = new Date();
                const startDate = new Date(today);
                startDate.setHours(0, 0, 0, 0); // Start of today

                const formattedStartDate = startDate.toISOString().split('T')[0];

                req.query = {
                  instance: instancia.nameinstance,
                  dt_enviado_inicial: formattedStartDate,
                  dt_enviado_final: formattedStartDate,
                  from_me: 'SIM',
                };
                // console.log('req.query  at line 224 in instancia/InstanciaModel.ts:', req.query);
                const respContarMensagens = await MessagesDB.contarRelatorioMensagens(req);

                instancia.estatisticas =
                  respContarMensagens.statuscode == 200 ? respContarMensagens.data[0] : undefined;
              } else {
                instancia.estatisticas = {
                  total_enviado: 0,
                  total_nao_enviado: 0,
                  total_erro: 0,
                };
              }
            }
          } else {
            instancia.status_connection = 'Desconectado';
          }
          //req.query = { id_instancia: instancia.id_instancia };
          //const respInstMod = await new InstanciaModulosModel().listarInstanciaModulosContratados(req);
          instancia.modulos = []; //respInstMod.data;
        }),
      );

      // console.log('🚀 ~ InstanciaModel.ts:253 ~ listarInstancia ~ respInstancias:', respInstancias);
      return respInstancias;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async restartInstancia(req: Request): Promise<IRetorno> {
    try {
      let respInstancias = await InstanciaDB.listarInstancia(req);

      await Promise.all(
        respInstancias.data.map(async (instancia: any) => {
          if (instancia.nameinstance != '') {
            req.query = { instance: instancia.nameinstance, nr_hash: instancia.nr_hash };
            //console.log('req.query at line 169 in models/InstanciaModel.ts:', req.query);
            const respState = await WhatsServices.restartInstance(req);
            // console.log('respState at line 225 in instancia/InstanciaModel.ts:', respState);
          }
        }),
      );

      return respInstancias;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async restartInstanciaInterno(): Promise<IRetorno> {
    try {
      let respInstancias = await InstanciaDB.listarInstanciaInterno();

      const req: Request = {
        query: {},
        body: {},
        params: {}, // Pode ser necessário, dependendo do que as funções esperam
        headers: {}, // Pode ser necessário
        method: 'GET', // Pode ser necessário
      } as unknown as Request; // Cast temporário; ajuste conforme necessário

      await Promise.all(
        respInstancias.data.map(async (instancia: any) => {
          if (instancia.nameinstance != '') {
            req.query = { instance: instancia.nameinstance, nr_hash: instancia.nr_hash };
            //console.log('req.query at line 169 in models/InstanciaModel.ts:', req.query);
            const respState = await WhatsServices.restartInstance(req);
            //console.log('respState at line 225 in instancia/InstanciaModel.ts:', respState);
          }
        }),
      );

      return respInstancias;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async removerInstancia(req: Request): Promise<IRetorno> {
    try {
      const query = req.query;
      req.body = {
        instance: query.instance,
      };
      const respLogout = await WhatsServices.logoutInstance(req);
      logger.debug('respLogout at line 214 in instancia/InstanciaModel.ts:' + JSON.stringify(respLogout));

      const respDelete = await WhatsServices.deleteInstance(req);
      logger.debug('respDelete at line 214 in instancia/InstanciaModel.ts:' + JSON.stringify(respDelete));

      const respRemoveFilas = await WhatsServices.removeFilas(req);
      logger.debug('respRemoveFilas at line 214 in instancia/InstanciaModel.ts:' + JSON.stringify(respRemoveFilas));

      const respRemove = await InstanciaDB.removerInstancia(req);

      return respRemove;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarDadosDashboard(req: Request): Promise<IRetorno> {
    try {
      const respMensagensEnviadas = await MessagesDB.contarMensagensEnviadas(req);
      if (respMensagensEnviadas.statuscode != 200) {
        return respMensagensEnviadas;
      }

      const respMensagensPeriodo = await MessagesDB.contarMensagensPorPeriodo(req);
      if (respMensagensPeriodo.statuscode != 200) {
        return respMensagensPeriodo;
      }

      respMensagensEnviadas.data[0].mensagens_periodo = respMensagensPeriodo.data;
      return respMensagensEnviadas;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
