<template>
    <Transition name="notification-fade">
        <div
            v-show="novaAtualizacao"
            class="fixed top-0 left-0 right-0 w-full z-[99999] shadow-lg notification-overlay"
        >
            <div class="bg-gray-900 dark:bg-gray-950 border-b border-gray-800 dark:border-gray-700">
                <div class="mx-auto px-4 py-2 sm:px-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 text-emerald-400 dark:text-emerald-500 mr-3">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                        </div>

                        <p class="text-sm font-medium text-gray-100 dark:text-gray-200 mr-4">
                            Uma nova versão do
                            <span
                                class="bg-gradient-to-r from-emerald-400 to-green-400 bg-clip-text text-transparent font-semibold"
                            >
                                OiZap
                            </span>
                            está disponível!
                        </p>

                        <button
                            @click="atualizar()"
                            class="rounded-md bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 transition-all duration-150 px-3 py-1 text-xs font-medium text-white shadow-md focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:ring-offset-gray-900"
                        >
                            Atualizar agora
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </Transition>
</template>

<script setup>
    import { ref, onMounted } from 'vue';
    import hosts from '@/utils/hosts';
    import io from 'socket.io-client';
    import { useRouter } from 'vue-router';
    import VersaoServices from '@/services/administracao/VersaoServices';
    import { useRegisterSW } from 'virtual:pwa-register/vue';

    const { needRefresh, updateServiceWorker } = useRegisterSW();

    // Adicione no início do seu script setup
    const router = useRouter();

    let socket;
    let dsPass = ref('');
    let dsLogin = ref('');
    let lembrarMe = ref('');
    const novaAtualizacao = ref(false);
    const versaoAtual = ref(localStorage.getItem('versaoOiZap') || '0.0.0');

    if (import.meta.env.MODE === 'development') {
        socket = io.connect(hosts.webSocket, { transports: ['websocket'] });
    } else {
        socket = io.connect(hosts.hostConsumer, {
            rememberUpgrade: true,
            transports: ['websocket'],
            secure: true,
            enabledTransports: ['ws', 'wss'],
            rejectUnauthorized: false,
            path: hosts.webSocket,
        });
    }

    // if (import.meta.env.MODE === 'development') {
    //     socket = io.connect(hosts.webSocket, { transports: ['websocket'] });
    // } else {
    //     socket = io.connect(hosts.hostConsumer, {
    //         rememberUpgrade: true,
    //         transports: ['websocket'],
    //         secure: true,
    //         enabledTransports: ['ws', 'wss'],
    //         rejectUnauthorized: false,
    //         path: hosts.webSocket,
    //         reconnection: true, // Habilitar reconexão automática
    //         reconnectionAttempts: 5, // Tentativas máximas de reconexão
    //         reconnectionDelay: 2000, // Esperar 2 segundos antes de tentar reconectar
    //         reconnectionDelayMax: 5000, // Limite máximo de 5 segundos entre tentativas
    //         timeout: 20000, // Tempo limite de conexão em 20 segundos
    //         pingInterval: 10000, // 10s
    //         pingTimeout: 5000, // 5s
    //     });
    // }

    // Função para limpar cache
    async function limparCache() {
        // Salva todos os dados importantes primeiro
        const dadosImportantes = {
            versao: localStorage.getItem('versaoOiZap'),
            token: localStorage.getItem('token'),
            password: localStorage.getItem('password'),
            login: localStorage.getItem('login'),
            lembrarMe: localStorage.getItem('lembrarMe'),
        };

        // Atualiza as refs
        dsPass.value = dadosImportantes.password;
        dsLogin.value = dadosImportantes.login;
        lembrarMe.value = dadosImportantes.lembrarMe;

        // Limpa localStorage
        localStorage.clear();

        // Restaura todos os dados importantes
        Object.entries(dadosImportantes).forEach(([key, value]) => {
            if (value) localStorage.setItem(key, value);
        });

        // Limpa cache do navegador
        if ('caches' in window) {
            const caches = await window.caches.keys();
            await Promise.all(caches.map((cache) => window.caches.delete(cache)));
        }
    }

    async function atualizar() {
        try {
            await limparCache();
            if (needRefresh.value) {
                await updateServiceWorker();
            }
            window.location.href = '?v=' + Date.now();
        } catch (error) {
            console.error('Erro ao atualizar:', error);
            window.location.reload(true);
        }
        // try {
        //     await limparCache();
        //     window.location.href = '?v=' + Date.now();
        // } catch (error) {
        //     console.error('Erro ao atualizar:', error);
        //     window.location.reload(true);
        // }
    }
    onMounted(async () => {
        versaoAtual.value = localStorage.getItem('versaoOiZap') || '0.0.0';
        try {
            const ambiente =
                import.meta.env.MODE === 'production' ? 'OIZAP_OIZAP' : import.meta.env.MODE.toUpperCase() + '_OIZAP';
            const response = await VersaoServices.listar({ nm_versao: ambiente });
            const versaoServidor = response.data[0].ds_versao;

            // Se não existir versão no localStorage, apenas salva a versão atual
            if (!localStorage.getItem('versaoOiZap')) {
                localStorage.setItem('versaoOiZap', versaoServidor);
                versaoAtual.value = versaoServidor;
            }
            // Se existir versão no localStorage, compara para ver se precisa atualizar
            else {
                versaoAtual.value = localStorage.getItem('versaoOiZap');
                if (versaoAtual.value !== versaoServidor) {
                    console.log('Nova versão disponível:', versaoServidor);
                    versaoAtual.value = versaoServidor;
                    localStorage.setItem('versaoOiZap', versaoServidor);
                    novaAtualizacao.value = true;
                }
            }
        } catch (error) {
            // console.error('Erro ao verificar versão:', error);
        }

        // Escuta eventos de atualização
        socket.on('atualizacao.oizap', (message) => {
            const ambiente = import.meta.env.MODE === 'production' ? 'OIZAP' : import.meta.env.MODE.toUpperCase();

            // console.log('ambiente at line 141 in atualizacao/notificacao.vue:', ambiente);
            if (message.sistema.includes(ambiente)) {
                //  console.log('message at line 143 in atualizacao/notificacao.vue:', message);
                const novaVersao = message.versao;

                if (versaoAtual.value !== novaVersao) {
                    versaoAtual.value = novaVersao;
                    localStorage.setItem('versaoOiZap', novaVersao);
                    novaAtualizacao.value = true;
                }
            }
        });
    });
</script>

<style scoped>
    /* Animações */
    .notification-fade-enter-active,
    .notification-fade-leave-active {
        transition: all 0.3s ease;
    }

    .notification-fade-enter-from {
        opacity: 0;
        transform: translateY(-100%);
    }

    .notification-fade-leave-to {
        opacity: 0;
        transform: translateY(-100%);
    }

    /* Notificação sobreposta */
    .notification-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 99999; /* Z-index muito alto para ficar acima de tudo */
        pointer-events: auto; /* Permitir cliques */
    }

    /* Borda piscante */
    .notification-overlay::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #10b981, #22c55e, #10b981);
        animation: borderBlink 2s infinite;
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.8);
    }

    @keyframes borderBlink {
        0% {
            opacity: 0.3;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.3;
        }
    }
</style>
