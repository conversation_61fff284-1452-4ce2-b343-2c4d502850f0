<template>
    <!-- h-full  -->
    <div class="flex items-center justify-center bg-gray-50 dark:bg-darkmode-600" :style="[tamanho]">
        <div class="text-center p-8 max-w-md mx-auto">
            <!-- Logo -->
            <div class="mb-6">
                <img alt="OiZap" class="max-w-[400px] mx-auto opacity-80" src="@/assets/images/logo_oizap2.png" />
            </div>

            <!-- Mensagem Principal -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 dark:text-slate-300 mb-2">Bem-vindo ao OiZap Chat</h3>
                <p class="text-gray-500 dark:text-slate-400">
                    Clique em uma conversa ao lado para iniciar o atendimento
                </p>
            </div>

            <!-- Status da Conexão -->
            <div v-if="situacaoInstancias === 'parado' || listaInstancias.length === 0" class="space-y-4">
                <!-- Al<PERSON><PERSON> de Conexão -->
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex items-center justify-center mb-3">
                        <UnplugIcon class="w-5 h-5 text-red-500 mr-2" />
                        <span class="text-red-600 dark:text-red-400 font-medium text-sm">
                            {{
                                listaInstancias.length === 0
                                    ? 'Nenhuma instância encontrada'
                                    : 'WhatsApp desconectado do OiZap'
                            }}
                        </span>
                    </div>

                    <!-- Botão Conectar -->
                    <button
                        type="button"
                        @click="$emit('abrir-modal-conexao')"
                        :disabled="listaInstancias.length === 0"
                        class="w-full px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                        <PlugIcon class="w-4 h-4" />
                        <span>Conectar WhatsApp</span>
                    </button>
                </div>
            </div>

            <!-- Status Online -->
            <div
                v-else-if="situacaoInstancias === 'ok'"
                class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
            >
                <div class="flex items-center justify-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-green-600 dark:text-green-400 font-medium text-sm"> WhatsApp conectado </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { UnplugIcon, PlugIcon } from 'lucide-vue-next';

    defineProps({
        situacaoInstancias: {
            type: String,
            default: 'ok',
        },
        listaInstancias: {
            type: Array,
            default: () => [],
        },
        tamanho: {
            type: String,
        },
    });

    defineEmits(['abrir-modal-conexao']);
</script>
