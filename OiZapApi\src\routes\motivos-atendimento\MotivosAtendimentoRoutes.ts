import { Router } from 'express';
import { MotivosAtendimentoController } from '../../controllers/motivos-atendimento/MotivosAtendimentoController';
import authApi from '../../middleware/authApi';

export const MotivosAtendimentoRoutes = Router();

MotivosAtendimentoRoutes.post('/motivos-atendimento/v1', authApi, MotivosAtendimentoController.incluir);
MotivosAtendimentoRoutes.put('/motivos-atendimento/v1', authApi, MotivosAtendimentoController.alterar);
MotivosAtendimentoRoutes.get('/motivos-atendimento/v1', authApi, MotivosAtendimentoController.listar);
MotivosAtendimentoRoutes.delete('/motivos-atendimento/v1', authApi, MotivosAtendimentoController.excluir);
