<template>
    <notifications ref="showNotifications" />
    <ShowLoading ref="loading" />

    <div class="">
        <div class="intro-y flex flex-row items-center mt-1.5 mx-4 border-b border-slate-200/60">
            <h2 class="text-lg font-medium mr-auto py-2">Dashboard Geral</h2>
        </div>

        <!-- BEGIN: Filtros -->
        <div class="grid grid-cols-4 md:grid-cols-12 gap-4 mt-5 mx-2 md:m-8">
            <div class="intro-y col-span-4 md:col-span-12 flex flex-row justify-between items-center w-full">
                <div class="flex items-center gap-2">
                    <div class="items-center gap-2 hidden w-0 md:flex md:w-auto">
                        <button
                            v-for="(tempo, index) in listaPeriodosSelect"
                            v-show="index != 4"
                            :key="index"
                            type="button"
                            :class="`btn btn-sm  ${
                                periodoSelecionado === index ? 'btn-primary-soft' : 'btn-secondary-soft'
                            } rounded-full w-20 mr-2`"
                            @click="periodoSelecionado = index"
                        >
                            {{ tempo }}
                        </button>
                    </div>
                    <div class="md:hidden">
                        <Dropdown placement="bottom-end">
                            <DropdownToggle
                                tag="a"
                                :class="`btn btn-sm  btn-primary-soft  rounded-full w-20 mr-2 `"
                                href="javascript:;"
                            >
                                {{ listaPeriodosSelect[periodoSelecionado] }}
                            </DropdownToggle>
                            <DropdownMenu class="w-32">
                                <DropdownContent>
                                    <DropdownItem
                                        v-for="(tempo, index) in listaPeriodosSelect"
                                        v-show="index != 4"
                                        :key="index"
                                        @click="periodoSelecionado = index"
                                    >
                                        {{ tempo }}
                                    </DropdownItem>
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                    <Tippy
                        as="div"
                        class="cursor-pointer"
                        content="Selecionar período personalizado"
                        key="periodo_custom"
                    >
                        <Dropdown placement="bottom-end">
                            <DropdownToggle
                                tag="a"
                                :class="`btn btn-sm  ${
                                    periodoSelecionado == 4 ? 'btn-primary-soft' : 'btn-secondary-soft'
                                } rounded-full `"
                                href="javascript:;"
                            >
                                <CalendarSearchIcon class="w-4 h-4 mr-1" />
                            </DropdownToggle>
                            <DropdownMenu class="w-max">
                                <DropdownContent class="flex items-end gap-2">
                                    <div class="w-64 p-2">
                                        <label for="filtro3" class="form-label mb-0">Período</label>
                                        <div class="relative w-full">
                                            <Litepicker
                                                class="form-control"
                                                v-model="periodoPersonalizado"
                                                @update:modelValue="selecionaPeriodoPersonalizado"
                                                :options="{
                                                    lang: 'pt-BR',
                                                    footer: false,
                                                    autoApply: true,
                                                    singleMode: false,
                                                    numberOfColumns: 2,
                                                    numberOfMonths: 2,
                                                    showWeekNumbers: false,
                                                    dropdowns: {
                                                        minYear: 2022,
                                                        maxYear: new Date().getFullYear(),
                                                        months: true,
                                                        years: true,
                                                    },
                                                    tooltipText: {
                                                        one: 'dia',
                                                        other: 'dias',
                                                    },
                                                    format: 'DD/MM/YYYY',
                                                }"
                                            />
                                        </div>
                                    </div>
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown>
                    </Tippy>
                </div>
            </div>
        </div>
        <!-- END: Filtros -->

        <!-- BEGIN: Indicadores -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-5 mx-2 md:m-8">
            <div class="box p-4 text-center">
                <h3 class="text-lg font-semibold">Total de Mensagens</h3>
                <p class="text-2xl font-bold">{{ converters.formataQuantidade(totalMensagens) }}</p>
            </div>
            <div class="box p-4 text-center">
                <h3 class="text-lg font-semibold">Total de Pedidos</h3>
                <p class="text-2xl font-bold">{{ converters.formataQuantidade(totalPedidos) }}</p>
            </div>
            <div class="box p-4 text-center">
                <h3 class="text-lg font-semibold">Total de Atendimentos</h3>
                <p class="text-2xl font-bold">{{ converters.formataQuantidade(totalAtendimentos) }}</p>
            </div>
        </div>
        <!-- END: Indicadores -->

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Pedidos por data</h2>
                    </div>
                </div>
            </div>
            <div class="chart-container relative z-10">
                <LineChart :data="lineValoresPedidos" :dataAxis="lineLabelsPedidos" :Legend="lineLegendasPedidos" />
            </div>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Pedidos por hora</h2>
                    </div>
                </div>
            </div>
            <div class="mt-1 border-b broder-slate-200 w-full">
                <div class="chart-container relative z-10">
                    <LineChart
                        :data="barValoresPedidosHora"
                        :dataAxis="barLabelsPedidosHora"
                        :Legend="barLegendasPedidosHora"
                    />
                </div>
            </div>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Atendimentos por data</h2>
                    </div>
                </div>
            </div>
            <div class="mt-1 border-b broder-slate-200 w-full">
                <div class="chart-container relative z-10">
                    <LineChart
                        :data="lineValoresAtendimentos"
                        :dataAxis="lineLabelsAtendimentos"
                        :Legend="lineLegendasAtendimentos"
                    />
                </div>
            </div>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Atendimentos por hora</h2>
                    </div>
                </div>
            </div>
            <div class="mt-1 border-b broder-slate-200 w-full">
                <div class="chart-container relative z-10">
                    <LineChart
                        :data="barValoresAtendimentosHora"
                        :dataAxis="barLabelsAtendimentosHora"
                        :Legend="barLegendasAtendimentosHora"
                    />
                </div>
            </div>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Mensagens por data</h2>
                    </div>
                </div>
            </div>
            <div class="mt-1 border-b broder-slate-200 w-full">
                <div class="chart-container relative z-10">
                    <LineChart
                        :data="lineValoresMensagens"
                        :dataAxis="lineLabelsMensagens"
                        :Legend="lineLegendasMensagens"
                    />
                </div>
            </div>
        </div>

        <div class="box intro-y sm:m-4 p-4 px-4 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-start sm:justify-between lg:items-center mb-1 gap-y-2">
                <div>
                    <div class="flex items-center gap-1.5 w-max">
                        <h2 class="text-lg font-semibold">Mensagens por hora</h2>
                    </div>
                </div>
            </div>
            <div class="mt-1 border-b broder-slate-200 w-full">
                <div class="chart-container relative z-10">
                    <LineChart
                        :data="barValoresMensagensHora"
                        :dataAxis="barLabelsMensagensHora"
                        :Legend="barLegendasMensagensHora"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { ref, onMounted, watch } from 'vue';

    import notifications from '@/components/show-notifications/Main.vue';

    import MessagesServices from '@/services/chat/MessagesServices';
    import AtendimentoServices from '@/services/chat/AtendimentoServices';
    import PedidosServices from '@/services/pedidos/PedidosServices';

    import dayjs from 'dayjs';

    import LineChart from '@/components/charts/LineChart.vue';

    import converters from '@/utils/converters';

    const loading = ref();
    const showNotifications = ref();
    const periodoPersonalizado = ref('');

    const periodoSelecionado = ref(0);
    const listaPeriodosSelect = ref(['Hoje', 'Ontem', 'Semana', 'Mês', 'Busca']);
    const listaPeriodos = ref([
        [new Date(), new Date(), 0],
        [dayjs(new Date()).subtract(1, 'day'), dayjs(new Date()).subtract(1, 'day'), 1],
        [dayjs(new Date()).subtract(7, 'day'), dayjs(new Date()), 6],
        [dayjs(new Date()).subtract(1, 'month'), dayjs(new Date()), 30],
        ['', '', 0],
    ]);

    // Indicadores
    const totalMensagens = ref(0);
    const totalPedidos = ref(0);
    const totalAtendimentos = ref(0);

    // Gráficos de Mensagens
    const lineValoresMensagens = ref([]);
    const lineLabelsMensagens = ref([]);
    const lineLegendasMensagens = ref([]);
    const barValoresMensagensHora = ref([]);
    const barLabelsMensagensHora = ref([]);
    const barLegendasMensagensHora = ref([]);

    // Gráficos de Pedidos
    const lineValoresPedidos = ref([]);
    const lineLabelsPedidos = ref([]);
    const lineLegendasPedidos = ref([]);
    const barValoresPedidosHora = ref([]);
    const barLabelsPedidosHora = ref([]);
    const barLegendasPedidosHora = ref([]);

    // Gráficos de Atendimentos
    const lineValoresAtendimentos = ref([]);
    const lineLabelsAtendimentos = ref([]);
    const lineLegendasAtendimentos = ref([]);
    const barValoresAtendimentosHora = ref([]);
    const barLabelsAtendimentosHora = ref([]);
    const barLegendasAtendimentosHora = ref([]);

    function selecionaPeriodoPersonalizado(value) {
        if (dayjs(listaPeriodos.value[0][0]).format('DD/MM/YYYY') == value.split(' - ')[0]) {
            //return console.log('igual');
        }

        const [inicio, fim] = value.split(' - ');

        listaPeriodos.value[4][0] = converters.date('YYYY-MM-DD', inicio);
        listaPeriodos.value[4][1] = converters.date('YYYY-MM-DD', fim);
        listaPeriodos.value[4][2] = contarDiasPeriodo(inicio, fim);
        periodoSelecionado.value = 4;
        console.log('periodo4', listaPeriodos.value[4]);

        carregaDadosDashboard();
    }

    function contarDiasPeriodo(inicio, fim) {
        const dataInicio = dayjs(converters.date('YYYY-MM-DD', inicio));
        const dataFim = dayjs(converters.date('YYYY-MM-DD', fim));
        const dias = dataFim.diff(dataInicio, 'day') + 1;

        return dias;
    }
    function montarLineChart(data, valores, labels, legendas) {
        // Limpar os arrays antes de preenchê-los
        valores.value = [];
        labels.value = [];
        legendas.value = [];

        // Extrair e ordenar as datas
        const datasOrdenadas = Array.from(
            new Set(
                data.map((item) => item.data) // Extrair as datas
            )
        ).sort((a, b) => new Date(a) - new Date(b)); // Ordenar as datas

        // Atualizar labels com as datas ordenadas
        labels.splice(0, labels.length, ...datasOrdenadas.map((data) => dayjs(data).format('DD/MM')));

        // Agrupar os dados por estabelecimento
        const agrupadosPorEstabelecimento = data.reduce((acc, item) => {
            if (!acc[item.nm_estabelecimento]) {
                acc[item.nm_estabelecimento] = [];
            }
            acc[item.nm_estabelecimento].push(item);
            return acc;
        }, {});

        // Preencher os valores para cada estabelecimento
        Object.keys(agrupadosPorEstabelecimento).forEach((estabelecimento) => {
            const dadosEstabelecimento = agrupadosPorEstabelecimento[estabelecimento];

            // Adicionar legenda
            if (!legendas.includes(estabelecimento)) {
                legendas.push(estabelecimento);
            }

            // Criar um mapa de data -> valor para o estabelecimento
            const dataMap = new Map();
            dadosEstabelecimento.forEach((item) => {
                dataMap.set(item.data, Number(item.qt_total));
            });

            // Preencher os valores com base nas datas ordenadas
            const valoresPreenchidos = datasOrdenadas.map((data) => dataMap.get(data) || 0);

            // Adicionar os valores preenchidos ao gráfico
            valores.push({
                name: estabelecimento,
                type: 'line',
                data: valoresPreenchidos,
                smooth: true, // Desativar suavização
            });
        });

        // Ordenar as séries com base na ordem das legendas
        const seriesOrdenadas = legendas.map((legenda) => {
            return valores.find((serie) => serie.name === legenda);
        });

        // Atualizar o gráfico com as séries ordenadas
        valores.splice(0, valores.length, ...seriesOrdenadas);
    }

    // function montarBarChart(data, valores, labels) {
    //     valores.push(...data.map((item) => Number(item.qt_total)));
    //     labels.push(...data.map((item) => `${item.hora}:00`));
    // }

    function montarLineChartHora(data, valores, labels, legendas) {
        // Limpar os arrays antes de preenchê-los
        valores.value = [];
        labels.value = [];
        legendas.value = [];

        // Extrair e ordenar as horas
        const horasOrdenadas = Array.from(
            new Set(
                data.map((item) => item.hora) // Extrair as horas
            )
        ).sort((a, b) => Number(a) - Number(b)); // Ordenar as horas numericamente

        // Atualizar labels com as horas ordenadas
        labels.splice(0, labels.length, ...horasOrdenadas.map((hora) => `${hora}:00`));

        // Agrupar os dados por estabelecimento
        const agrupadosPorEstabelecimento = data.reduce((acc, item) => {
            if (!acc[item.nm_estabelecimento]) {
                acc[item.nm_estabelecimento] = [];
            }
            acc[item.nm_estabelecimento].push(item);
            return acc;
        }, {});

        // Preencher os valores para cada estabelecimento
        Object.keys(agrupadosPorEstabelecimento).forEach((estabelecimento) => {
            const dadosEstabelecimento = agrupadosPorEstabelecimento[estabelecimento];

            // Adicionar legenda
            if (!legendas.includes(estabelecimento)) {
                legendas.push(estabelecimento);
            }

            // Criar um mapa de hora -> valor para o estabelecimento
            const horaMap = new Map();
            dadosEstabelecimento.forEach((item) => {
                horaMap.set(item.hora, Number(item.qt_total));
            });

            // Preencher os valores com base nas horas ordenadas
            const valoresPreenchidos = horasOrdenadas.map((hora) => horaMap.get(hora) || 0);

            // Adicionar os valores preenchidos ao gráfico
            valores.push({
                name: estabelecimento,
                type: 'line',
                data: valoresPreenchidos,
                smooth: true, // Desativar suavização
            });
        });

        // Ordenar as séries com base na ordem das legendas
        const seriesOrdenadas = legendas.map((legenda) => {
            return valores.find((serie) => serie.name === legenda);
        });

        // Atualizar o gráfico com as séries ordenadas
        valores.splice(0, valores.length, ...seriesOrdenadas);
    }

    async function carregaDadosDashboard() {
        loading.value.show();

        const periodo = listaPeriodos.value[periodoSelecionado.value].map((item) => dayjs(item).format('YYYY-MM-DD'));

        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        let estabelecimentosLiberado = undefined;
        if (estatabelecimentos) {
            const estabLib = JSON.parse(estatabelecimentos);

            estabelecimentosLiberado = estabLib[0].cd_estabelecimento;
        }

        const filtros = {
            cd_estabelecimento: estabelecimentosLiberado,
            dt_inicial: periodo[0],
            dt_final: periodo[1],
        };

        try {
            // Gráficos de Mensagens
            lineValoresMensagens.value = [];
            lineLabelsMensagens.value = [];
            lineLegendasMensagens.value = [];
            barValoresMensagensHora.value = [];
            barLabelsMensagensHora.value = [];
            barLegendasMensagensHora.value = [];

            // Gráficos de Pedidos
            lineValoresPedidos.value = [];
            lineLabelsPedidos.value = [];
            lineLegendasPedidos.value = [];
            barValoresPedidosHora.value = [];
            barLabelsPedidosHora.value = [];
            barLegendasPedidosHora.value = [];

            // Gráficos de Atendimentos
            lineValoresAtendimentos.value = [];
            lineLabelsAtendimentos.value = [];
            lineLegendasAtendimentos.value = [];
            barValoresAtendimentosHora.value = [];
            barLabelsAtendimentosHora.value = [];
            barLegendasAtendimentosHora.value = [];
            // Mensagens
            const mensagensData = await MessagesServices.listaMensagensData(filtros);

            const mensagensHora = await MessagesServices.listaMensagensHora(filtros);
            if (mensagensData.statuscode === 200) {
                montarLineChart(
                    mensagensData.data,
                    lineValoresMensagens.value,
                    lineLabelsMensagens.value,
                    lineLegendasMensagens.value
                );
                totalMensagens.value = mensagensData.data.reduce((sum, item) => sum + Number(item.qt_total), 0);
            }
            if (mensagensHora.statuscode === 200) {
                montarLineChartHora(
                    mensagensHora.data,
                    barValoresMensagensHora.value,
                    barLabelsMensagensHora.value,
                    barLegendasMensagensHora.value
                );
            }

            // Pedidos
            const pedidosData = await PedidosServices.listaPedidosData(filtros);
            const pedidosHora = await PedidosServices.listaPedidosHora(filtros);
            if (pedidosData.statuscode === 200) {
                montarLineChart(
                    pedidosData.data,
                    lineValoresPedidos.value,
                    lineLabelsPedidos.value,
                    lineLegendasPedidos.value
                );
                totalPedidos.value = pedidosData.data.reduce((sum, item) => sum + Number(item.qt_total), 0);
            }
            if (pedidosHora.statuscode === 200) {
                montarLineChartHora(
                    pedidosHora.data,
                    barValoresPedidosHora.value,
                    barLabelsPedidosHora.value,
                    barLegendasPedidosHora.value
                );
            }

            // Atendimentos
            const atendimentosData = await AtendimentoServices.listaAtendimentoData(filtros);
            const atendimentosHora = await AtendimentoServices.listaAtendimentoHora(filtros);
            if (atendimentosData.statuscode === 200) {
                montarLineChart(
                    atendimentosData.data,
                    lineValoresAtendimentos.value,
                    lineLabelsAtendimentos.value,
                    lineLegendasAtendimentos.value
                );
                totalAtendimentos.value = atendimentosData.data.reduce((sum, item) => sum + Number(item.qt_total), 0);
            }
            if (atendimentosHora.statuscode === 200) {
                montarLineChartHora(
                    atendimentosHora.data,
                    barValoresAtendimentosHora.value,
                    barLabelsAtendimentosHora.value,
                    barLegendasAtendimentosHora.value
                );
            }
        } catch (error) {
            // console.log('error at line 359 in dashboard/dashGeral.vue:', error);
            showNotifications.value.showWarningNotification('Erro ao carregar os dados do dashboard.');
        } finally {
            loading.value.hide();
        }
    }

    onMounted(async () => {
        await carregaDadosDashboard();
    });

    watch(periodoSelecionado, async () => {
        await carregaDadosDashboard();
    });
</script>

<style scoped>
    .chart {
        height: 250px;
    }
    .chart-container {
        position: relative;
        z-index: 1; /* Ajustar para evitar interferências */
        overflow: visible; /* Garantir que o tooltip não seja cortado */
    }
    .echarts-tooltip {
        position: absolute !important;
        z-index: 99999 !important; /* Forçar o tooltip a ficar no topo */
        pointer-events: auto !important;
    }
    ::v-deep(.dropdown-input-wrap) {
        display: none !important;
    }
    ::v-deep(.ts-dropdown) {
        border-radius: 1.5rem !important;
        margin-top: 2px !important;
        overflow: hidden !important;
        padding: 0.4rem 0 !important;
    }
    ::v-deep(.option) {
        margin: 0.4rem 0 !important;
    }
    ::v-deep(.tom-select .ts-input) {
        cursor: pointer !important;
    }
    ::v-deep(.dropdown-item-disabled) {
        opacity: 0.4 !important;
        pointer-events: none !important;
    }
</style>
