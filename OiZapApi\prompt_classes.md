Olá! Quero que você atue como um especialista em TypeScript e Node.js para me ajudar a gerar código para minha API.

Vou fornecer um conjunto de arquivos de exemplo que definem a arquitetura para um CRUD da tabela departamentos. Seu objetivo é usar essa arquitetura como um molde para criar os arquivos CRUD para novas tabelas que eu apresentar a partir de uma migration Knex.

A arquitetura é composta por 5 passos/arquivos principais para cada entidade:

Controller (...Controller.ts): Responsável por receber as requisições HTTP (req, res), validar os parâmetros obrigatórios do req.body e req.query, e chamar o método correspondente no Model. Ele manipula os status de resposta HTTP (sucesso e erro).
Model (...Model.ts): Atua como uma camada intermediária. Pode conter regras de negócio e é responsável por chamar a camada de acesso a dados (DB). No método de inclusão, ele captura o código do usuário (cd_usucad) a partir de um token e o adiciona ao req.body.
DB (...DB.ts): É a camada de acesso direto ao banco de dados. Usa um serviço (PostgreSQLServices) para executar operações. Para insert, update e delete, ele monta um OperationObject. Para listagem (listar), ele constrói uma query SQL manualmente, permitindo filtros.
Routes (...Routes.ts): Define os endpoints da API usando o express.Router, mapeando os verbos HTTP para os métodos do Controller e aplicando middlewares de autenticação (authApi).
Agregador de Rotas (routerHandler.ts): Este é o arquivo central que importa todas as rotas da aplicação (como DepartamentosRoutes) e as registra no roteador principal do Express.

Contexto e Exemplos de Código (Arquitetura para departamentos)
Aqui estão os arquivos de exemplo para a entidade departamentos. Use-os como base para qualquer nova entidade.

1. O Controller: DepartamentosController.ts
   TypeScript

import {
BAD_REQUEST,
INTERNAL_SERVER_ERROR,
parametrosInvalidos,
erroInterno,
sucesso,
} from '../../interfaces/IRetorno';
import { Request, Response } from 'express';
import { DepartamentosModel } from '../../models/departamentos/DepartamentosModel';

export class DepartamentosController {
static async incluir(req: Request, res: Response): Promise<Response> {
try {
const errors: string[] = [];
if (!req.body.cd_estabelecimento) {
errors.push('O campo "cd_estabelecimento" é obrigatório.');
}
if (!req.body.ds_departamento) {
errors.push('O campo "ds_departamento" é obrigatório.');
}
if (typeof req.body.in_ativo !== 'boolean') {
errors.push('O campo "in_ativo" deve ser um booleano.');
}
if (errors.length > 0) {
return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
}
const result = await new DepartamentosModel().incluir(req);
return res.status(result.statuscode).send(result);
} catch (error: any) {
return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
}
}
static async listar(req: Request, res: Response): Promise<Response> {
try {
const result = await new DepartamentosModel().listar(req);
return res.status(result.statuscode).send(result);
} catch (error: any) {
return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
}
}
static async alterar(req: Request, res: Response): Promise<Response> {
try {
const errors: string[] = [];
if (!req.body.cd_departamento) {
errors.push('O campo "cd_departamento" é obrigatório.');
}
if (!req.body.cd_estabelecimento) {
errors.push('O campo "cd_estabelecimento" é obrigatório.');
}
if (errors.length > 0) {
return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
}
const result = await new DepartamentosModel().alterar(req);
return res.status(result.statuscode).send(result);
} catch (error: any) {
return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
}
}
static async excluir(req: Request, res: Response): Promise<Response> {
try {
const errors: string[] = [];
if (!req.body.cd_estabelecimento) {
errors.push('O campo "cd_estabelecimento" é obrigatório.');
}
if (!req.body.cd_departamento) {
errors.push('O campo "cd_departamento" é obrigatório.');
}
if (errors.length > 0) {
return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
}
const result = await new DepartamentosModel().excluir(req);
return res.status(result.statuscode).send(result);
} catch (error: any) {
return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
}
}
} 2. O Model: DepartamentosModel.ts
TypeScript

import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { DepartamentosDB } from '../../data/departamentos/DepartamentosDB';
import { IToken } from '@/interfaces/IToken';

export class DepartamentosModel {
async incluir(req: Request): Promise<IRetorno> {
try {
const token = req.query.token as unknown as IToken;
req.body.cd_usucad = token?.cd_usuario || 0;
return await DepartamentosDB.incluir(req);
} catch (error: any) {
return erroInterno(error);
}
}
async listar(req: Request): Promise<IRetorno> {
try {
return await DepartamentosDB.listar(req);
} catch (error: any) {
return erroInterno(error);
}
}
async alterar(req: Request): Promise<IRetorno> {
try {
return await DepartamentosDB.alterar(req);
} catch (error: any) {
return erroInterno(error);
}
}
async excluir(req: Request): Promise<IRetorno> {
try {
return await DepartamentosDB.excluir(req);
} catch (error: any) {
return erroInterno(error);
}
}
} 3. A Camada DB: DepartamentosDB.ts
TypeScript

require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';

export class DepartamentosDB {
static async incluir(req: Request): Promise<IRetorno> {
try {
const opDb: OperationObject = {
operacao: 'insert',
tabelas: ['departamentos'],
chaves: undefined,
dados: req.body,
retorno: '_',
};
return await new PostgreSQLServices().executar(opDb);
} catch (error: any) {
return erroInterno(error);
}
}
static async listar(req: Request): Promise<IRetorno> {
try {
let xSQL = `select _ from departamentos where 1=1`;
      if (req.query.ds_departamento) {
        xSQL += ` and ds*departamento like '%${req.query.ds_departamento}%'`;
      }
      xSQL += ` order by ds_departamento`;
return await new PostgreSQLServices().query(xSQL);
} catch (error: any) {
return erroInterno(error);
}
}
static async alterar(req: Request): Promise<IRetorno> {
try {
const opDb: OperationObject = {
operacao: 'update',
tabelas: ['departamentos'],
chaves: { cd_departamento: req.body.cd_departamento },
dados: req.body,
retorno: '*',
};
return await new PostgreSQLServices().executar(opDb);
} catch (error: any) {
return erroInterno(error);
}
}
static async excluir(req: Request): Promise<IRetorno> {
try {
const opDb: OperationObject = {
operacao: 'delete',
tabelas: ['departamentos'],
chaves: { cd*departamento: req.body.cd_departamento },
retorno: '*',
};
return await new PostgreSQLServices().executar(opDb);
} catch (error: any) {
return erroInterno(error);
}
}
} 4. As Rotas: DepartamentosRoutes.ts
TypeScript

import { Router } from 'express';
import authorizationApi from '../../middleware/authorizationApi';
import authApi from '../../middleware/authApi';
import { DepartamentosController } from '../../controllers/departamentos/DepartamentosController';
export const DepartamentosRoutes = Router();

DepartamentosRoutes.post('/departamentos/v1', authApi, DepartamentosController.incluir);
DepartamentosRoutes.put('/departamentos/v1', authApi, DepartamentosController.alterar);
DepartamentosRoutes.get('/departamentos/v1', authApi, DepartamentosController.listar);
DepartamentosRoutes.delete('/departamentos/v1', authApi, DepartamentosController.excluir);
Minha Tarefa Atual
Agora, com base em todo o contexto e nos exemplos acima, quero que você crie os 4 arquivos (Controller, Model, DB, Routes) para a tabela definida na migration anexada
junto ao contexto.

Por favor, gere o código para os seguintes arquivos, adaptando os nomes das variáveis, campos e rotas:

MotivosAtendimentoController.ts
MotivosAtendimentoModel.ts
MotivosAtendimentoDB.ts
MotivosAtendimentoRoutes.ts
E, finalmente, mostre como o arquivo src/routes/routerHandler.ts deve ser modificado para importar e registrar as novas MotivosAtendimentoRoutes, adicionando a importação em ordem alfabética e incluindo a variável na matriz rotas.
Preste atenção especial em adaptar os campos de validação no Controller (ds_motivo, etc.) e as chaves primárias nos métodos alterar e excluir (cd_motivo). Mantenha a lógica de adicionar cd_usucad no Model.
