import { Request, Response, NextFunction } from 'express';
import { PostgreSQLServices } from '../services/PostgreSQLServices';

export async function authorizationTela(req: Request, res: Response, next: NextFunction) {
  try {
    // Pega o cd_usuario do payload do token (injetado pelo authApi em req.query.token)
    let usuarioPayload: any = req.query.token;
    if (typeof usuarioPayload === 'string') {
      try { usuarioPayload = JSON.parse(usuarioPayload); } catch {}
    }
    const cd_usuario = usuarioPayload && usuarioPayload.cd_usuario;
    if (!cd_usuario) return res.status(401).json({ error: 'Usuário não autenticado' });

    // Pegue a rota acessada (ex: /admin, /dashboard)
    const rota = req.baseUrl + (req.route?.path || '');

    // Consulta se o usuário tem permissão para essa rota
    const sql = `
      SELECT 1
      FROM usuarios_perfis up
      JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
      JOIN telas t ON t.cd_tela = pt.cd_tela
      WHERE up.cd_usuario = ${cd_usuario}
        AND t.ds_rota = '${rota}'
        AND pt.in_visualizar = true
      LIMIT 1
    `;
    const result = await new PostgreSQLServices().query(sql);

    if (!result.data || result.data.length === 0) {
      return res.status(403).json({ error: 'Acesso não permitido para esta tela' });
    }

    next();
  } catch (error) {
    return res.status(500).json({ error: 'Erro na verificação de permissão' });
  }
} 