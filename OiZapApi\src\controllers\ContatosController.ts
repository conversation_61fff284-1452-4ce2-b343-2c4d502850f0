import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../interfaces/IRetorno';
import { ContatosModel } from '../models/ContatosModel';

export class ContatosController {
  static async incluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.push_name === undefined || req.body.push_name.trim() === '') {
        errors.push('O campo "push_name" é obrigatório.');
      }
      if (req.body.telefone === undefined || req.body.telefone.trim() === '') {
        errors.push('O campo "telefone" é obrigatório.');
      }
      if (req.body.id === undefined || req.body.id.trim() === '') {
        errors.push('O campo "id" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      //console.log('errors at line 19 in controllers/ClientesController.ts:', errors);

      const result = await new ContatosModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      //console.log('req.body at line 32 in controllers/ClientesController.ts:', req.body);
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.cd_contato === undefined) {
        errors.push('O campo "cd_contato" é obrigatório.');
      }
      // if (req.body.ds_nome === undefined || req.body.ds_nome.trim() === '') {
      //   errors.push('O campo "ds_nome" é obrigatório.');
      // }
      // if (req.body.nr_telefonezap === undefined || req.body.nr_telefonezap.trim() === '') {
      //   errors.push('O campo "nr_telefonezap" é obrigatório.');
      // }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new ContatosModel().alterar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listar(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new ContatosModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async remover(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.cd_contato == undefined) {
        errors.push('O campo "cd_contato" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new ContatosModel().remover(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
