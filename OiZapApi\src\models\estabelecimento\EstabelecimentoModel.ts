//const PostgreSQL = require('../services/PostgreSQL');
import { Request } from 'express';
import { EstabelecimentoDB } from '../../data/estabelecimento/EstabelecimentoDB';
import { UsuarioDB } from '../../data/usuarios/UsuarioDB';
import { UsuariosEstabelecimentosDB } from '../../data/usuarios/UsuarioEstabelecimentosDB';
import { IRetorno, conflito, erroInterno, sucesso } from '../../interfaces/IRetorno';
//import { FluxosDB } from '../../data/fluxos/FluxosDB';
import EmailService, { TemplateType } from '../../services/EmailService';
//import moment from 'moment';
import { AtualizacaoTabelasPDVDB } from '../../data/AtualizacaoTabelasPDVDB';
import { EstabelecimentoInstanciasDB } from '../../data/estabelecimento/EstabelecimentoInstanciasDB';
import { EstabelecimentoModulosDB } from '../../data/estabelecimento/EstabelecimentoModulosDB';
import { FluxosDB } from '../../data/fluxos/FluxosDB';
import { ModulosDB } from '../../data/ModulosDB';
import { ParametrosDB } from '../../data/parametros/ParametrosDB';
import { AdicionaisDB } from '../../data/produtos/AdicionaisDB';
import { AdicionalAgrupadoDB } from '../../data/produtos/AdicionalAgrupadoDB';
import { GruposDB } from '../../data/produtos/GruposDB';
import { ProdutosDB } from '../../data/produtos/ProdutosDB';
import { SubGruposDB } from '../../data/produtos/SubGruposDB';
import { TamanhosDB } from '../../data/produtos/TamanhosDB';
import { DockerApiEvolutionServices } from '../../services/DockerApiEvolutionServices';
import { Funcoes } from '../../services/Funcoes';
import Logger from '../../services/Logger';
import { WhatsServices } from '../../services/WhatsServices';
const logger = Logger.getLogger();

interface IEstabelecimento {
  // id: string;
  cd_estabelecimento: number;
  nm_estabelecimento: string;
  nr_hash: string;
  nr_cnpj?: string;
  ds_email: string;
  ds_hostentregadochef?: string;
  ds_endereco?: string;
  vl_entrega?: number;
  in_apidedicada?: boolean;
}

export class EstabelecimentoModel {
  async incluirEstabelecimentoIntegracao(req: Request): Promise<IRetorno> {
    try {
      const {
        ds_email,
        nm_estabelecimento,
        nr_cnpj,
        ds_hostentregadochef,
        ds_endereco,
        vl_entrega,
        in_apidedicada,
        tp_calculopizza,
      } = req.body;

      if (!Funcoes.isValidEmail(ds_email)) {
        return conflito('', 'E-mail inválido!');
      }
      // Verificar se o usuário já existe
      req.query = { ds_email };
      let resultUsuario = await UsuarioDB.listarUsuario(req);
      // logger.debug('incluirEstabelecimentoIntegracao > resultUsuario' + JSON.stringify(resultUsuario));
      let nr_hashunica: string;

      //caso não existe usuario, cria um novo
      if (resultUsuario.statuscode == 404) {
        nr_hashunica = Funcoes.gerarHash(ds_email, 25);
        //const instance = nr_hashunica.substring(0, 10);
        const ds_senha = await Funcoes.hashPassword(ds_email);

        req.body = {
          ds_nome: nm_estabelecimento,
          ds_login: ds_email,
          ds_senha,
          tp_privilegio: 'A',
          tp_status: 'A',
          ds_email,
          nr_hashunica: nr_hashunica,
        };

        resultUsuario = await UsuarioDB.incluirUsuario(req);
        if (resultUsuario.statuscode != 200) {
          return erroInterno(resultUsuario);
        }
      } else {
        return conflito('', 'Existe estabelecimento cadastrado com esse E-mail!');
        //nr_hashunica = resultUsuario.data[0].nr_hash;
      }

      const dataUsuario = resultUsuario.data[0];

      req.query = { nr_cnpj };
      // Verificar se o estabelecimento já existe
      let resultEstabelecimento = await EstabelecimentoDB.listarEstabelecimento(req);

      //caso não existe estabelecimento e usuario, cria um novo
      if (resultEstabelecimento.statuscode == 404) {
        req.body = {
          nm_estabelecimento,
          nr_cnpj,
          ds_hostentregadochef,
          ds_endereco,
          cd_usuario: dataUsuario.cd_usuario,
          nr_hash: nr_hashunica,
          vl_entrega,
          in_apidedicada,
          in_habilitaproxy: true,
        };

        resultEstabelecimento = await EstabelecimentoDB.incluirEstabelecimento(req);

        if (resultEstabelecimento.statuscode != 200) {
          req.body = { cd_usuario: dataUsuario.cd_usuario };
          resultUsuario = await UsuarioDB.removerUsuario(req);

          return erroInterno(resultEstabelecimento);
        }
      }

      const dataEstabelecimento = resultEstabelecimento.data[0];

      req.body = {
        cd_usuario: dataUsuario.cd_usuario,
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      //logger.warn('HABILITAR O REPLICA FLUXO');
      let resultFluxo = await FluxosDB.replicaFluxo(req);
      if (resultFluxo.statuscode != 200) {
        return erroInterno(resultFluxo);
      }

      req.query = {
        cd_usuario: dataUsuario.cd_usuario,
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      let respUsuEst = await UsuariosEstabelecimentosDB.listarUsuariosEstabelecimentos(req);
      if (respUsuEst.statuscode == 404) {
        const nrHash = dataUsuario.cd_usuario + dataEstabelecimento.cd_estabelecimento;
        const nr_hashunica = Funcoes.gerarHash(nrHash, 25);
        req.body = {
          cd_usuario: dataUsuario.cd_usuario,
          cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
          nr_hash: nr_hashunica,
        };

        respUsuEst = await UsuariosEstabelecimentosDB.incluirUsuariosEstabelecimentos(req);
      }

      req.query = {
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      let respEstMod = await EstabelecimentoModulosDB.listarEstabelecimentoModulos(req);
      if (respEstMod.statuscode == 404) {
        req.query = {
          nm_modulo: 'ChatBot/Pedidos',
        };
        const respModulos = await ModulosDB.listarModulos(req);

        if (respModulos.statuscode == 200) {
          const dataModulo = respModulos.data[0];
          req.body = {
            cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
            cd_modulo: dataModulo.cd_modulo,
            tp_situacao: 'Ativo',
            vl_modulo: dataModulo.vl_modulo,
            in_contratado: true,
            qt_instancias: 1,
            qt_usuarios: 3,
          };

          respEstMod = await EstabelecimentoModulosDB.incluirEstabelecimentoModulos(req);
          //console.log('respUsuEst at line 174 in estabelecimento/EstabelecimentoModel.ts:', respEstMod);
        }
      }

      req.query = {
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      let respParametros = await ParametrosDB.listarParametros(req);
      if (respParametros.statuscode == 404) {
        req.body = {
          cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
          in_permite_item_zerado: false,
          in_aplicadesconto: true,
          tp_calculopizza,
        };

        respParametros = await ParametrosDB.incluirParametros(req);
      }

      // console.log('respUsuEst:', respUsuEst);

      const estabelecimento: IEstabelecimento = {
        // id: dataUsuario.nr_hashunica,
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
        nm_estabelecimento: dataEstabelecimento.nm_estabelecimento,
        nr_hash: dataEstabelecimento.nr_hash,
        //nr_hash: respUsuEst.data[0].nr_hash,
        nr_cnpj: dataEstabelecimento.nr_cnpj,
        ds_email: dataUsuario.ds_email,
        ds_hostentregadochef: dataEstabelecimento.ds_hostentregadochef,
        ds_endereco: dataEstabelecimento.ds_endereco,
        vl_entrega: dataEstabelecimento.vl_entrega,
        in_apidedicada: false, //dataEstabelecimento.in_apidedicada,
      };

      // const codigoAleatorio: string = Funcoes.gerarCodigoAleatorio(6);

      // const dataVencimento = await moment().add(15, 'minutes');

      // const dataVencimentoFormatada = dataVencimento.format('YYYY-MM-DD HH:mm:ss');

      req.body = {
        cd_usuario: dataUsuario.cd_usuario,
        //nr_codigo: codigoAleatorio,
        //dt_vencimentocodigo: dataVencimentoFormatada,
        tp_status: 'R',
      };

      const respUsuario = await UsuarioDB.alterarUsuario(req);

      if (respUsuario.statuscode != 200) {
        return respUsuario;
      }

      // if (in_apidedicada) {
      //   const respCriaApi = await DockerApiEvolutionServices.runDockerCompose(dataEstabelecimento.nm_estabelecimento);
      //   //logger.info('incluirEstabelecimentoIntegracao > runDockerCompose :' + JSON.stringify(respCriaApi));
      //   if (respCriaApi.statuscode == 200) {
      //     const dataApi = respCriaApi.data[0];
      //     req.body = {
      //       cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      //       id_host_cloudflare: dataApi.id_host_cloudflare,
      //       ds_hostapi: 'https://' + dataApi.ds_hostapi,
      //       id_stack: dataApi.id_stack,
      //       nm_stack: dataApi.nm_stack,
      //     };

      //     resultEstabelecimento = await EstabelecimentoDB.alterarEstabelecimento(req);
      //   }
      // }

      const dadosEmail = {
        name: nm_estabelecimento,
        destinatario: ds_email,
        assunto: 'Seja Bem-vindo ao Oi Zap!',
        codigo: '', // codigoAleatorio,
      };
      const respEmail = await EmailService.enviarEmail(TemplateType.BEM_VINDO, dadosEmail);

      if (respEmail.statuscode === 200) {
        return sucesso([estabelecimento]);
      } else {
        return erroInterno(respEmail);
      }
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async incluirEstabelecimentoCadastro(req: Request): Promise<IRetorno> {
    try {
      const { ds_email, nm_estabelecimento, nm_modulo } = req.body;

      if (!Funcoes.isValidEmail(ds_email)) {
        return conflito('', 'E-mail inválido!');
      }
      // Verificar se o usuário já existe
      req.query = { ds_email };
      let resultUsuario = await UsuarioDB.listarUsuario(req);
      logger.debug('incluirEstabelecimentoIntegracao > resultUsuario' + JSON.stringify(resultUsuario));
      let nr_hashunica: string;

      //caso não existe usuario, cria um novo
      if (resultUsuario.statuscode == 404) {
        nr_hashunica = Funcoes.gerarHash(ds_email, 25);
        //const instance = nr_hashunica.substring(0, 10);
        const ds_senha = await Funcoes.hashPassword(ds_email);

        req.body = {
          ds_nome: nm_estabelecimento,
          ds_login: ds_email,
          ds_senha,
          tp_privilegio: 'A',
          tp_status: 'A',
          ds_email,
          nr_hashunica: nr_hashunica,
        };

        resultUsuario = await UsuarioDB.incluirUsuario(req);
        if (resultUsuario.statuscode != 200) {
          return erroInterno(resultUsuario);
        }
      } else {
        return conflito('', 'Existe estabelecimento cadastrado com esse E-mail!');
        //nr_hashunica = resultUsuario.data[0].nr_hash;
      }

      const dataUsuario = resultUsuario.data[0];

      req.query = { ds_email };
      // Verificar se o estabelecimento já existe
      let resultEstabelecimento = await EstabelecimentoDB.listarEstabelecimento(req);
      //console.log('resultEstabelecimento:', resultEstabelecimento);

      //caso não existe estabelecimento e usuario, cria um novo
      if (resultEstabelecimento.statuscode == 404) {
        req.body = {
          nm_estabelecimento,
          ds_email,
          cd_usuario: dataUsuario.cd_usuario,
          nr_hash: nr_hashunica,
        };

        resultEstabelecimento = await EstabelecimentoDB.incluirEstabelecimento(req);

        if (resultEstabelecimento.statuscode != 200) {
          req.body = { cd_usuario: dataUsuario.cd_usuario };
          resultUsuario = await UsuarioDB.removerUsuario(req);
          return erroInterno(resultEstabelecimento);
        }
      }

      const dataEstabelecimento = resultEstabelecimento.data[0];

      req.query = {
        cd_usuario: dataUsuario.cd_usuario,
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      let respUsuEst = await UsuariosEstabelecimentosDB.listarUsuariosEstabelecimentos(req);
      if (respUsuEst.statuscode == 404) {
        const nrHash = dataUsuario.cd_usuario + dataEstabelecimento.cd_estabelecimento;
        const nr_hashunica = Funcoes.gerarHash(nrHash, 25);
        req.body = {
          cd_usuario: dataUsuario.cd_usuario,
          cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
          nr_hash: nr_hashunica,
        };

        respUsuEst = await UsuariosEstabelecimentosDB.incluirUsuariosEstabelecimentos(req);
      }

      req.query = {
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      };

      let respEstMod = await EstabelecimentoModulosDB.listarEstabelecimentoModulos(req);
      if (respEstMod.statuscode == 404) {
        req.query = {
          nm_modulo: nm_modulo,
        };
        const respModulos = await ModulosDB.listarModulos(req);

        if (respModulos.statuscode == 200) {
          const dataModulo = respModulos.data[0];
          req.body = {
            cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
            cd_modulo: dataModulo.cd_modulo,
            tp_situacao: 'Ativo',
            vl_modulo: dataModulo.vl_modulo,
            in_contratado: true,
          };

          respEstMod = await EstabelecimentoModulosDB.incluirEstabelecimentoModulos(req);
          //console.log('respUsuEst at line 174 in estabelecimento/EstabelecimentoModel.ts:', respEstMod);
        }
      }

      // console.log('respUsuEst:', respUsuEst);

      const estabelecimento: IEstabelecimento = {
        // id: dataUsuario.nr_hashunica,
        cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
        nm_estabelecimento: dataEstabelecimento.nm_estabelecimento,
        nr_hash: dataEstabelecimento.nr_hash,
        ds_email: dataUsuario.ds_email,
      };

      // const codigoAleatorio: string = Funcoes.gerarCodigoAleatorio(6);

      // const dataVencimento = await moment().add(15, 'minutes');

      // const dataVencimentoFormatada = dataVencimento.format('YYYY-MM-DD HH:mm:ss');

      req.body = {
        cd_usuario: dataUsuario.cd_usuario,
        //nr_codigo: codigoAleatorio,
        //dt_vencimentocodigo: dataVencimentoFormatada,
        tp_status: 'R',
      };

      const respUsuario = await UsuarioDB.alterarUsuario(req);

      if (respUsuario.statuscode != 200) {
        return respUsuario;
      }

      // if (in_apidedicada) {
      //   const respCriaApi = await DockerApiEvolutionServices.runDockerCompose(dataEstabelecimento.nm_estabelecimento);
      //   //logger.info('incluirEstabelecimentoIntegracao > runDockerCompose :' + JSON.stringify(respCriaApi));
      //   if (respCriaApi.statuscode == 200) {
      //     const dataApi = respCriaApi.data[0];
      //     req.body = {
      //       cd_estabelecimento: dataEstabelecimento.cd_estabelecimento,
      //       id_host_cloudflare: dataApi.id_host_cloudflare,
      //       ds_hostapi: 'https://' + dataApi.ds_hostapi,
      //       id_stack: dataApi.id_stack,
      //       nm_stack: dataApi.nm_stack,
      //     };

      //     resultEstabelecimento = await EstabelecimentoDB.alterarEstabelecimento(req);
      //   }
      // }

      const dadosEmail = {
        name: nm_estabelecimento,
        destinatario: ds_email,
        assunto: 'Seja Bem-vindo ao Oi Zap!',
        codigo: '', // codigoAleatorio,
      };
      // console.log('dadosEmail at line 143 in estabelecimento/EstabelecimentoModel.ts:', dadosEmail);

      const respEmail = await EmailService.enviarEmail(TemplateType.BEM_VINDO, dadosEmail);

      if (respEmail.statuscode === 200) {
        return sucesso([estabelecimento]);
      } else {
        return erroInterno(respEmail);
      }
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async limpaDados(req: Request): Promise<IRetorno> {
    try {
      await AdicionaisDB.limpaBaseAdicionais(req);
      await AdicionalAgrupadoDB.limpaBaseAdicionalAgrupado(req);
      await GruposDB.limpaBaseGrupo(req);
      await ProdutosDB.limpaBaseProdutos(req);
      await SubGruposDB.limpaBaseSubGrupo(req);
      await TamanhosDB.limpaBaseTamanho(req);
      // await AtualizacaoTabelasPDVDB.limpaAtualizacaoTabelasPDV(req);
      // await ClientesDB.limpaClientes(req);

      req.body = {
        cd_estabelecimento: req.body.cd_estabelecimento,
        in_atualiza: true,
      };
      await AtualizacaoTabelasPDVDB.alterarAtualizacaoTabelasPDV(req);

      return sucesso('Base deletada com sucesso!');
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarEstabelecimentoIntegracao(req: Request): Promise<IRetorno> {
    try {
      // Verificar se o estabelecimento já existe
      let resultEstabelecimento = await EstabelecimentoDB.listarEstabelecimento(req);
      // console.log('resultEstabelecimento:', req.query);
      // console.log('resultEstabelecimento:', resultEstabelecimento);
      let listaEstabelecimento = [];
      for (const estab of resultEstabelecimento.data) {
        // Verificar se o usuário já existe
        req.query = { cd_usuario: estab.cd_usuario };
        let resultUsuario = await UsuarioDB.listarUsuario(req);

        // console.log('resultUsuario:', resultUsuario);

        req.query = {
          cd_usuario: resultUsuario.data[0]?.cd_usuario,
          cd_estabelecimento: estab.cd_estabelecimento,
        };
        // console.log('req.query:', req.query);
        let respUsuEst = await UsuariosEstabelecimentosDB.listarUsuariosEstabelecimentos(req);

        //console.log('respUsuEst:', respUsuEst);

        listaEstabelecimento.push({
          //id: resultUsuario.data[0]?.nr_hashunica,
          cd_estabelecimento: estab.cd_estabelecimento,
          nm_estabelecimento: estab.nm_estabelecimento,
          nr_hash: estab.nr_hash,
          //nr_hash: respUsuEst.data[0]?.nr_hash,
          nr_cnpj: estab.nr_cnpj,
          ds_email: resultUsuario.data[0]?.ds_email,
          ds_hostentregadochef: estab.ds_hostentregadochef,
          ds_endereco: estab.ds_endereco,
        });
      }

      return sucesso([listaEstabelecimento]);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async incluirEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      return await EstabelecimentoDB.incluirEstabelecimento(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async criarApiDedicada(req: Request): Promise<IRetorno> {
    try {
      const respCriaApi = await DockerApiEvolutionServices.runDockerCompose(req.body.nm_estabelecimento);
      if (respCriaApi.statuscode == 200) {
        const dataApi = respCriaApi.data[0];
        req.body = {
          cd_estabelecimento: req.body.cd_estabelecimento,
          id_host_cloudflare: dataApi.id_host_cloudflare,
          ds_hostapi: 'https://' + dataApi.ds_hostapi,
          id_stack: dataApi.id_stack,
          nm_stack: dataApi.nm_stack,
        };

        const resultEstabelecimento = await EstabelecimentoDB.alterarEstabelecimento(req);
        console.log(
          '🚀 ~ EstabelecimentoModel.ts:532 ~ criarApiDedicada ~ resultEstabelecimento:',
          resultEstabelecimento,
        );
        return resultEstabelecimento;
      }
      return respCriaApi;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async alterarEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      return await EstabelecimentoDB.alterarEstabelecimento(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      return await EstabelecimentoDB.listarEstabelecimento(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async removerEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      return await EstabelecimentoDB.removerEstabelecimento(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaEstabelecimentoDirect(req: Request): Promise<IRetorno> {
    return await EstabelecimentoDB.listaEstabelecimentosDirect(req);
  }
  async listaInstanciasPorEstabelecimento(req: Request): Promise<IRetorno> {
    try {
      let respInstanciasEst = await EstabelecimentoInstanciasDB.listaInstanciasPorEstabelecimento(req);

      await Promise.all(
        respInstanciasEst.data.map(async (est: any) => {
          est.status = undefined;
          est.status_connection = 'Desconectado';
          est.nr_telefone = undefined;
          est.nm_perfil = undefined;
          est.url_foto = undefined;
          est.qt_messages = undefined;
          est.qt_contact = undefined;
          est.qt_chat = undefined;
          est.in_apidedicada = est.in_apidedicada;
          if (est.nameinstance != null) {
            req.query = { instance: est.nameinstance, nr_hash: est.nr_hash };

            const respState = await WhatsServices.fetchInstances(req);
            est.status_connection = 'Desconectado';
            if (respState.statuscode === 200) {
              const data = respState.data;
              est.status = data.status;
              est.status_connection = data.status_connection;
              est.nr_telefone = data.nr_telefone;
              est.nm_perfil = data.nm_perfil;
              est.url_foto = data.url_foto;
              est.qt_messages = data.qt_messages;
              est.qt_contact = data.qt_contact;
              est.qt_chat = data.qt_chat;
            }
          }
        }),
      );

      return respInstanciasEst;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
