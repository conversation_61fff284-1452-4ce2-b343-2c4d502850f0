const axios = require('axios');

async function testarRotas() {
  console.log('🧪 Testando rotas da API...\n');

  const baseURL = 'http://localhost:3100/oizap';

  try {
    // Teste 1: Rota raiz
    console.log('1. Testando rota raiz...');
    const response1 = await axios.get(baseURL);
    console.log('✅ Rota raiz OK:', response1.data);
    console.log('');

    // Teste 2: Rota de perfis (GET)
    console.log('2. Testando rota de perfis (GET)...');
    try {
      const response2 = await axios.get(`${baseURL}/perfis`);
      console.log('✅ Rota perfis OK:', response2.data);
    } catch (error) {
      console.log('❌ Erro na rota perfis:', error.response?.data || error.message);
    }
    console.log('');

    // Teste 3: Rota de perfis (POST)
    console.log('3. Testando rota de perfis (POST)...');
    try {
      const response3 = await axios.post(`${baseURL}/perfis`, {
        nm_perfil: 'Teste Perfil'
      });
      console.log('✅ POST perfis OK:', response3.data);
    } catch (error) {
      console.log('❌ Erro no POST perfis:', error.response?.data || error.message);
    }
    console.log('');

    // Teste 4: Rota de telas (GET)
    console.log('4. Testando rota de telas (GET)...');
    try {
      const response4 = await axios.get(`${baseURL}/telas`);
      console.log('✅ Rota telas OK:', response4.data);
    } catch (error) {
      console.log('❌ Erro na rota telas:', error.response?.data || error.message);
    }
    console.log('');

    // Teste 5: Listar todas as rotas disponíveis
    console.log('5. Testando outras rotas...');
    const rotas = ['usuarios', 'estabelecimentos', 'produtos', 'pedidos'];
    
    for (const rota of rotas) {
      try {
        const response = await axios.get(`${baseURL}/${rota}`);
        console.log(`✅ Rota ${rota} OK`);
      } catch (error) {
        console.log(`❌ Rota ${rota} não encontrada`);
      }
    }

  } catch (error) {
    console.error('❌ Erro geral:', error.message);
  }
}

testarRotas(); 