//const PostgreSQL = require('../services/PostgreSQL');
import { Request } from 'express';
import { MessagesDB } from '../data/MessagesDB';
import { IRetorno, erroInterno, sucesso } from '../interfaces/IRetorno';
export class MessagesModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      const respMensagem = await MessagesDB.incluir(req);
      return respMensagem;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarUltimaMensagemContato(req: Request): Promise<IRetorno> {
    try {
      const resp = await MessagesDB.listarUltimaMensagemContato(req);
      const respNaoLidas = await MessagesDB.listaQtMensagensNaoLidas(req);
      const respAtendimentosNaoLidas = await MessagesDB.listaQtAtendimentosNaoLidos(req);

      if (resp.statuscode != 200) return resp;
      const data = [
        {
          chats: resp.data,
          naoLidas: respNaoLidas.data[0],
          atendimentoNaoLido: respAtendimentosNaoLidas.data[0],
        },
      ];

      // console.log('data at line 19 in models/MessagesModel.ts:', data);
      return sucesso(data);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listMessages(req: Request): Promise<IRetorno> {
    try {
      const respMensagem = await MessagesDB.listMessages(req);
      return respMensagem;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async readMessages(req: Request): Promise<IRetorno> {
    try {
      return await MessagesDB.readMessages(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async reenviarMensagens(req: Request): Promise<IRetorno> {
    try {
      return await MessagesDB.reenviarMensagens(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listarRelatorioMensagens(req: Request): Promise<IRetorno> {
    try {
      const contagem = await MessagesDB.contarRelatorioMensagens(req);
      if (contagem.statuscode != 200) return contagem;
      const lista = await MessagesDB.listarRelatorioMensagens(req);
      if (lista.statuscode != 200) return lista;
      const data = {
        ...contagem.data[0],
        lista: lista.data,
      };
      return sucesso(data);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaMensagensData(req: Request): Promise<IRetorno> {
    try {
      const respPedidos = await MessagesDB.listaMensagensData(req);

      return respPedidos;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  async listaMensagensHora(req: Request): Promise<IRetorno> {
    try {
      const respPedidos = await MessagesDB.listaMensagensHora(req);

      return respPedidos;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
