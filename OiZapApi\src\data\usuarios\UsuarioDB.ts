require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';
export class UsuarioDB {
  static async incluirUsuario(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['adm_usuarios'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async incluirUsuarioDepartamento(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['usuario_departamentos'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarUsuario(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['adm_usuarios'],
        chaves: { cd_usuario: req.body.cd_usuario },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async removerUsuarioDepartamentos(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['usuario_departamentos'],
        chaves: {
          cd_usuario: req.body.cd_usuario,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarUsuario(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'select',
        tabelas: ['adm_usuarios'],
        chaves: req.query,
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async login(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select * from v_adm_usuarios where 1=1`;
      if (req.body.ds_login != undefined) {
        xSQL += ` and UPPER(ds_login) = upper('${req.body.ds_login}')`;
      }
      if (req.body.iduser != undefined) {
        xSQL += ` and nr_hashunica = '${req.body.iduser}'`;
      }

      // console.log('xSQL:', xSQL);

      const respUsuario = await new PostgreSQLServices().query(xSQL);
      // console.log('respUsuario:', respUsuario);
      return respUsuario;
    } catch (error: any) {
      //console.log('error:', error);
      return erroInterno(error);
    }
  }

  static async listaUsuarios(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select u.cd_usuario,ds_nome,ds_login,tp_privilegio,ds_privilegio 
               ,ds_status,tp_status,(case when (tp_status='A') then true else false end) status,u.in_aplicadesconto,u.in_usuarioadmin
               from v_adm_usuarios u left join usuarios_estabelecimentos ue on ue.cd_usuario = u.cd_usuario
               where 1=1`;
      if (req.query.cd_estabelecimento != undefined) {
        xSQL += ` and ue.cd_estabelecimento = '${req.query.cd_estabelecimento}'`;
      }
      if (req.query.cd_usuario != undefined) {
        xSQL += ` and u.cd_usuario = '${req.query.cd_usuario}'`;
      }
      if (req.query.tp_privilegio != undefined && req.query.tp_privilegio != "'O'") {
        xSQL += ` and u.tp_privilegio in (${req.query.tp_privilegio})`;
      }
      if (req.query.ds_nome != undefined) {
        xSQL += ` and u.ds_nome like '%${req.query.ds_nome}%'`;
      }
      if (req.query.ds_login != undefined) {
        xSQL += ` and u.ds_login like '%${req.query.ds_login}%'`;
      }
      if (req.query.ds_privilegio != undefined) {
        xSQL += ` and u.ds_privilegio like '%${req.query.ds_privilegio}%'`;
      }

      xSQL += ` order by u.ds_nome `;

      const respUsuario = await new PostgreSQLServices().query(xSQL);
      // console.log('respUsuario:', respUsuario);
      return respUsuario;
    } catch (error: any) {
      //console.log('error:', error);
      return erroInterno(error);
    }
  }

  static async listaUsuariosTodos(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select au.*,e.nm_estabelecimento
              from adm_usuarios au
              left join usuarios_estabelecimentos ue on ue.cd_usuario=au.cd_usuario
              left join estabelecimento e on e.cd_estabelecimento=ue.cd_estabelecimento
              where 1=1`;

      xSQL += ` order by au.ds_nome `;

      const respUsuario = await new PostgreSQLServices().query(xSQL);
      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listaUsuariosPorDepartamento(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select a.cd_usuario,a.ds_nome 
from adm_usuarios a
left join usuario_departamentos ud on ud.cd_usuario = a.cd_usuario
where 1=1`;

      if (req.query.cd_departamento != undefined) {
        xSQL += ` and ud.cd_departamento = ${req.query.cd_departamento}`;
      }

      xSQL += ` order by a.ds_nome `;

      const respUsuario = await new PostgreSQLServices().query(xSQL);
      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listaUsuariosDepartamentos(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select ud.*,d.ds_departamento
from usuario_departamentos ud, departamentos d 
 where d.cd_departamento = ud.cd_departamento and ud.cd_usuario = ${req.query.cd_usuario}`;
      if (req.query.cd_departamento != undefined) {
        xSQL += ` and ud.cd_departamento = ${req.query.cd_departamento}`;
      }

      // console.log('🚀 ~ UsuarioDB.ts:186 ~ listaUsuariosDepartamentos ~ xSQL:', xSQL);
      const respUsuario = await new PostgreSQLServices().query(xSQL);
      return respUsuario;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async removerUsuario(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['adm_usuarios'],
        chaves: {
          cd_usuario: req.body.cd_usuario,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
