require('dotenv').config();

const environment = process.env.NODE_ENV || 'CRM';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
    SANDBOX: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
    PROD: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

const connectionConfig = getConnectionConfig(environment);

module.exports = {
  'migrations-dir': 'migrations',
  'migrations-table': 'pgmigrations',
  'database-url': `postgres://${connectionConfig.user}:${connectionConfig.password}@${connectionConfig.host}:${connectionConfig.port}/${connectionConfig.database}`,
  'migration-file-language': 'js',
  'create-schema': true,
  'check-order': true,
  'ignore-pattern': '\\..*',
};
