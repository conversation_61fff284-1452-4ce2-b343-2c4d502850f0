import { Request, Response } from 'express';
import { PerfilTelaDB } from '../../data/perfis/PerfilTelaDB';

export class PerfilTelaController {
  static async listarTelasPorPerfil(req: Request, res: Response) {
    try {
      const { cd_perfil } = req.params;
      const telas = await new PerfilTelaDB().listarTelasPorPerfil(Number(cd_perfil));
      return res.json(telas);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao listar telas do perfil' });
    }
  }
  static async associarTelaPerfil(req: Request, res: Response) {
    try {
      const associacao = await new PerfilTelaDB().associarTelaPerfil(req.body);
      return res.status(201).json(associacao);
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao associar tela ao perfil' });
    }
  }
  static async desassociarTelaPerfil(req: Request, res: Response) {
    try {
      const { cd_perfil, cd_tela } = req.params;
      await new PerfilTelaDB().desassociarTelaPerfil(Number(cd_perfil), Number(cd_tela));
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ error: 'Erro ao desassociar tela do perfil' });
    }
  }
} 