import { Router } from 'express';
import { TelaController } from '../../controllers/telas/TelaController';
import { authorizationTela } from '../../middleware/authorizationTela';

const router = Router();

router.get('/', authorizationTela, TelaController.listarTelas);
router.post('/', authorizationTela, TelaController.criarTela);
router.put('/:cd_tela', authorizationTela, TelaController.atualizarTela);
router.delete('/:cd_tela', authorizationTela, TelaController.deletarTela);

export default router; 