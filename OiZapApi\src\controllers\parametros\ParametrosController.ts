import {
  BAD_REQUEST,
  INTERNAL_SERVER_ERROR,
  parametrosInvalidos,
  erroInterno,
  sucesso,
} from '../../interfaces/IRetorno';
import { Request, Response } from 'express';
import { ParametrosModel } from '../../models/parametros/ParametrosModel';

export class ParametrosController {
  static async incluirParametros(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new ParametrosModel().incluirParametros(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterarParametros(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new ParametrosModel().alterarParametros(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarParametros(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new ParametrosModel().listarParametros(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async importaParametros(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      req.body.cd_estabelecimento = req.query.cd_estabelecimento;
      console.log('req.body at line 64 in parametros/ParametrosController.ts:', req.body);
      const result = await new ParametrosModel().alterarParametros(req);
      return res.status(result.statuscode).send(sucesso(result.message));
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
