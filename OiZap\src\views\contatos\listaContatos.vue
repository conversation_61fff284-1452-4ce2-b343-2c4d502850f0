<template>
    <ShowLoading ref="loading" />

    <div class="p-4">
        <!-- Header <PERSON> <PERSON><PERSON> de módulos -->

        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <BookAIcon class="w-6 h-6" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Contatos</h3>
                        <p class="text-blue-100 text-sm">Contatos cadastrados no sistema.</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="gavetaContato.estado = true"
                    >
                        <PlusIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Novo Contato</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="box intro-y p-4">
            <div class="flex flex-col justify-between items-center py-4">
                <!-- BEGIN: Filtros -->
                <div
                    class="w-full dark:border-darkmode-800 pb-4 flex flex-col md:flex-row justify-between items-center gap-2"
                >
                    <div class="grid grid-cols-12 gap-1 w-full">
                        <div class="col-span-4">
                            <div class="relative flex-1">
                                <BaseInput
                                    v-model="filtros.search"
                                    placeholder="Pesquisar por nome ou telefone..."
                                    @input="pesquisarFiltro()"
                                    iconLeft="SearchIcon"
                                />
                            </div>
                        </div>

                        <div
                            class="col-span-12 sm:col-span-6"
                            v-if="instanciaLiberada.length > 0 && instanciaLiberada.length == 0"
                        >
                            <div class="form-control col-span-2 -my-1.5">
                                <Multiselect
                                    :customHeight="33"
                                    v-model="instanciaSelecionada"
                                    placeholder="Selecione a Instância"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :createOption="false"
                                    :options="listaInstancias"
                                    @emitEvent="selecionaInstancia"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2 w-full md:w-fit">
                        <button
                            class="btn btn-secondary-soft border-none bg-transparent shadow-none intro-y"
                            @click="limparFiltros()"
                            v-if="
                                filtros.ds_uf ||
                                filtros.ds_nome ||
                                filtros.nr_telefonezap ||
                                filtros.ds_cidade ||
                                filtros.ds_estado ||
                                filtros.ds_pais ||
                                filtros.search
                            "
                        >
                            <XIcon class="w-5 h-5" style="stroke-width: 2.6" />
                        </button>
                        <!-- <TomSelect
                            v-model="filtros.ds_uf"
                            :options="{
                                allowEmptyOption: true,
                                create: true,
                            }"
                            size="small"
                            class="w-full !cursor-pointer"
                            @change="
                                () => {
                                    filtros.page = 1;
                                    carregarListaContatos();
                                }
                            "
                        >
                            <option value="" disabled>UF</option>
                            <option v-for="item in listaUF" :key="item" :value="item">
                                {{ item }}
                            </option>
                        </TomSelect> -->
                        <!-- <Dropdown
                            :show="estadoDropdownFiltros"
                            @hidden="estadoDropdownFiltros = false"
                            class="ml-auto"
                            placement="bottom-start"
                        >
                            <DropdownToggle
                                @click="estadoDropdownFiltros = true"
                                tag="button"
                                class="btn"
                                href="javascript:;"
                            >
                                <ListFilterIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                Filtros
                            </DropdownToggle>
                            <DropdownMenu class="min-w-[300px] max-w-[460px] w-max">
                                <DropdownContent>
                                    <div class="w-full flex flex-col items-center gap-2 p-2">
                                        <div class="flex justify-between items-center w-full">
                                            <h3 class="text-lg font-medium">Filtros</h3>
                                            <div class="flex items-center">
                                                <button
                                                    class="btn btn-secondary-soft border-none bg-transparent shadow-none text-xs"
                                                    @click="
                                                        limparFiltros();
                                                        estadoDropdownFiltros = false;
                                                    "
                                                >
                                                    Limpar
                                                </button>
                                                <button
                                                    class="btn btn-secondary-soft btn-sm border-none bg-transparent shadow-none"
                                                    @click="
                                                        // limparFiltros();
                                                        estadoDropdownFiltros = false
                                                    "
                                                >
                                                    <XIcon class="w-5 h-5" style="stroke-width: 2.6" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-6 w-full gap-4">
                                            <div class="form-control col-span-6">
                                                <label
                                                    for="validation-form-1"
                                                    class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                                                >
                                                    Cod. Cliente
                                                </label>
                                                <div class="relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Informe o Telefone"
                                                        v-model="filtros.cd_cliente"
                                                    />
                                                </div>
                                            </div>
                                            <div class="form-control col-span-6 gap-1">
                                                <label
                                                    for="validation-form-1"
                                                    class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                                                    >Endereço</label
                                                >
                                                <div class="relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Informe o Estabelecimento"
                                                        v-model="filtros.ds_endereco"
                                                    />
                                                </div>
                                            </div>
                                            <div class="form-control col-span-6 gap-1">
                                                <label
                                                    for="validation-form-1"
                                                    class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                                                    >Cidade</label
                                                >
                                                <div class="relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Informe a Cidade"
                                                        v-model="filtros.ds_cidade"
                                                    />
                                                </div>
                                            </div>
                                            <div class="form-control col-span-6 gap-1">
                                                <label
                                                    for="validation-form-1"
                                                    class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                                                    >Estado</label
                                                >
                                                <div class="relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Informe o Estado"
                                                        v-model="filtros.ds_estado"
                                                    />
                                                </div>
                                            </div>
                                            <div class="form-control col-span-6 gap-1">
                                                <label
                                                    for="validation-form-1"
                                                    class="form-label text-slate-500 text-xs w-20 mb-0 pb-0"
                                                    >País</label
                                                >
                                                <div class="relative w-full">
                                                    <input
                                                        type="text"
                                                        class="form-control col-span-1 block"
                                                        placeholder="Informe o País"
                                                        v-model="filtros.ds_pais"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <DropdownDivider class="" />
                                    <div class="w-full flex justify-end">
                                        <button
                                            class="btn btn-primary-soft border-none shadow-none"
                                            @click="
                                                carregarListaContatos();
                                                estadoDropdownFiltros = false;
                                            "
                                        >
                                            <CheckIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                            Aplicar Filtros
                                        </button>
                                    </div>
                                </DropdownContent>
                            </DropdownMenu>
                        </Dropdown> -->
                    </div>
                </div>
                <!-- END: Filtros -->
                <div class="box-content w-full relative sm:mt-2">
                    <div class="border border-slate-200/60 w-full rounded-t-xl overflow-x-auto">
                        <!-- BEGIN: Tabela -->
                        <div
                            class="absolute top-0 right-0 w-[calc(100%-50px)] h-[2.8rem] backdrop-blur-[5px] bg-gray-200/60 dark:bg-darkmode-800 rounded-tr-xl flex justify-between items-center z-50"
                            :class="{
                                'animate-fade-in-left': checkAll || contatosSelecionados.length > 0,
                                'animate-fade-out-left': !checkAll && contatosSelecionados.length < 1,
                            }"
                        >
                            <h3 class="text-lg font-medium text-slate-500 ml-4 opacity-0 md:opacity-0">Menu</h3>

                            <div class="flex items-center gap-2 mr-2">
                                <button
                                    class="btn btn-danger-soft bg-transparent border-none shadow-none"
                                    @click="excluirContatos(true)"
                                >
                                    <Trash2Icon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                    <span class="text-ellipsis whitespace-nowrap overflow-hidden">Excluir</span>
                                </button>
                            </div>
                        </div>
                        <table class="table custom-table intro-y rounded-xl">
                            <thead v-if="listaContatos.length < 1" class="bg-gray-200/60 dark:bg-darkmode-800">
                                <tr>
                                    <th class="">Lista vazia...</th>
                                </tr>
                            </thead>
                            <thead class="relative" v-else>
                                <tr>
                                    <th
                                        class="w-[20px] whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 rounded-tl-xl"
                                    >
                                        <!-- CHECKBOX -->
                                        <div class="form-check w-[20px]">
                                            <input
                                                type="checkbox"
                                                class="form-check-input"
                                                id="check-all"
                                                v-model="checkAll"
                                                @change="(e) => selcionarContatos(true, e.target.checked)"
                                            />
                                        </div>
                                        <!-- END CHECKBOX -->
                                    </th>
                                    <th
                                        class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 w-[60px]"
                                    >
                                        Foto
                                    </th>
                                    <th
                                        class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 w-[100px]"
                                    >
                                        Número
                                    </th>
                                    <th class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800">
                                        Nome
                                    </th>

                                    <th
                                        class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800"
                                        v-if="instanciaLiberada.length > 0 && instanciaLiberada.length == 0"
                                    >
                                        Instância
                                    </th>
                                    <th
                                        class="whitespace-nowrap text-slate-500 bg-gray-200/60 dark:bg-darkmode-800 rounded-tr-xl w-[140px]"
                                    >
                                        Ações
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="listaContatos.length < 1" class="text-center">
                                    <td colspan="5" class="py-4 text-slate-500">
                                        Nenhum contato encontrado com os filtros aplicados.
                                    </td>
                                </tr>
                                <tr
                                    v-else
                                    v-for="contato in listaContatos"
                                    :key="contato.id"
                                    class="even:bg-slate-100/30 odd:bg-white hover:bg-slate-200/50 transition-all duration-200"
                                >
                                    <td>
                                        <!-- CHECKBOX -->
                                        <div class="form-check w-[20px] h-full">
                                            <input
                                                type="checkbox"
                                                class="form-check-input"
                                                :id="'check-' + contato.id"
                                                v-model="contato.checked"
                                                @change="selcionarContatos(false)"
                                            />
                                            <label class="form-check-label" :for="'check-' + contato.id"></label>
                                        </div>
                                        <!-- END CHECKBOX -->
                                    </td>

                                    <td>
                                        <img
                                            v-if="contato.profile_picture_base64 || contato.url_profile_picture"
                                            :src="contato.profile_picture_base64 || contato.url_profile_picture"
                                            class="w-8 h-8 rounded-full mr-2 shadow-md"
                                            alt="Avatar"
                                        />
                                        <div
                                            v-else
                                            class="w-8 h-8 rounded-full bg-slate-200 shadow-md flex justify-center items-end border border-slate-300 dark:border-darkmode-800 dark:bg-darkmode-800"
                                        >
                                            <UserIcon
                                                class="w-6 h-6 text-slate-400 dark:text-slate-500"
                                                style="stroke-width: 2.4px"
                                            />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-slate-500 text-xs whitespace-nowrap">
                                            {{ contato.telefone }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-slate-500 text-xs whitespace-nowrap">
                                            {{ contato.push_name }}
                                        </div>
                                    </td>

                                    <td v-if="instanciaLiberada.length > 0 && instanciaLiberada.length == 0">
                                        <div class="text-slate-500 text-xs whitespace-nowrap">
                                            {{ contato.nameinstance }} ({{ contato.nr_telefoneinstancia }})
                                        </div>
                                    </td>
                                    <td>
                                        <button
                                            class="btn btn-sm btn-dark-soft bg-transparent border-none shadow-none text-slate-500 hover:text-slate-700"
                                            @click="acionarEdicao(contato)"
                                        >
                                            <PenBoxIcon class="w-4 h-4 mr-1" style="stroke-width: 2" />
                                            <span class="text-ellipsis whitespace-nowrap overflow-hidden">Editar</span>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- END: Tabela -->
                    </div>
                    <!-- BEGIN: Limit Offset -->
                    <div
                        class="w-full p-4 flex justify-between items-center border border-slate-200/60 border-t-0 rounded-b-xl"
                    >
                        <div class="">
                            <TomSelect
                                v-model="filtros.limit"
                                :options="{}"
                                size="small"
                                class="w-full !cursor-pointer"
                                @change="
                                    () => {
                                        carregarListaContatos();
                                    }
                                "
                            >
                                <option v-for="item in listaItensPorPagina" :key="item" :value="item">
                                    {{ item }}
                                </option>
                            </TomSelect>
                        </div>
                        <div class="flex justify-center items-center">
                            <button
                                class="btn btn-sm btn-secondary-soft border-none shadow-none"
                                @click="
                                    filtros.page = filtros.page - 1;
                                    carregarListaContatos();
                                "
                                :disabled="filtros.page <= 1"
                            >
                                <ChevronLeftIcon class="w-4 h-4" />
                            </button>
                            <div class="mx-2 text-sm text-slate-500">
                                {{ filtros.page }}
                            </div>
                            <button
                                class="btn btn-sm btn-secondary-soft border-none shadow-none"
                                @click="
                                    filtros.page = filtros.page + 1;
                                    carregarListaContatos();
                                "
                                :disabled="listaContatos.length < filtros.limit"
                            >
                                <ChevronRightIcon class="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                    <!-- END: Limit Offset -->
                    <cite class="text-xs text-slate-500 px-4 py-2 self-start">
                        <span>*</span> Selecione os contatos para editar ou excluir.
                    </cite>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal  confirma novo token-->
    <Modal :show="modalExcluir" @hidden="modalExcluir = false">
        <ModalBody class="px-4 py-8 text-center flex flex-col justify-center items-center gap-4">
            <div class="p-3 w-min bg-danger/30 rounded-full flex justify-center items-center mt-6">
                <AlertTriangleIcon class="text-danger w-7 h-7 pb-0.5" style="stroke-width: 2.5" />
            </div>
            <div class="text-2xl mt-2 font-medium">Excluir contatos?</div>
            <div class="text-slate-500 mt-2">
                <p class="text-sm">Você está prestes a excluir os contatos selecionados.</p>
                <p class="text-sm font-semibold">Esta ação não pode ser desfeita!</p>
                <p class="text-sm">{{ contatosSelecionados.length }} contatos(s) selecionado(s).</p>
                <p class="text-sm font-semibold">Deseja realmente excluir?</p>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-end items-center">
            <button type="button" @click="modalExcluir = false" class="btn btn-outline-secondary w-24 mr-2">Não</button>
            <button type="button" class="btn btn-danger w-24" @click="excluirContatos(false)">Sim</button>
        </ModalFooter>
    </Modal>
    <!-- END: confirma novo token -->

    <!-- Modal Gaveta de teste -->
    <gavetaCadContato
        :estado="gavetaContato.estado"
        :dados="gavetaContato.dados"
        @update:estado="
            (estado) => {
                gavetaContato.estado = estado;
                if (!estado) {
                    gavetaContato.dados = {};
                }
            }
        "
        @response:success="carregarListaContatos()"
    />
    <!-- END:Gaveta de teste  -->
</template>

<script setup>
    import { nextTick, onMounted, ref } from 'vue';
    import ContatosServices from '@/services/pedidos/ContatosServices';
    import gavetaCadContato from './gavetaCadContato.vue';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    import { debounce } from 'lodash';
    import { UserIcon } from 'lucide-vue-next';
    import { useToast } from '@/global-components/toastify/useToast';
    import UsuarioInstanciasServices from '@/services/administracao/UsuarioInstanciasServices';
    const toast = useToast();
    const codUsuario = ref();
    const loading = ref();
    const estadoDropdownFiltros = ref(false);
    const modalExcluir = ref(false);
    const gavetaContato = ref({
        estado: false,
        dados: {},
    });
    const checkAll = ref(false);
    const contatosSelecionados = ref([]);
    const estabelecimentosLiberado = ref([]);
    const listaContatos = ref([]);
    const listaUF = ref([
        '',
        'AC',
        'AL',
        'AM',
        'AP',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MG',
        'MS',
        'MT',
        'PA',
        'PB',
        'PE',
        'PI',
        'PR',
        'RJ',
        'RN',
        'RO',
        'RR',
        'RS',
        'SC',
        'SE',
        'SP',
        'TO',
    ]);
    const listaItensPorPagina = ref([10, 25, 50, 100]);
    const filtros = ref({
        cd_cliente: '',
        cd_clientepdv: '',
        ds_nome: '',
        nr_telefonezap: '',
        ds_cidade: '',
        ds_uf: '',
        ds_pais: '',
        ds_estado: '',
        limit: listaItensPorPagina.value[0],
        page: 1,
    });
    const listaInstancias = ref([]);
    const instanciaSelecionada = ref([]);
    const instanciaLiberada = ref([]);
    function limparFiltros() {
        filtros.value = {
            cd_cliente: '',
            cd_clientepdv: '',
            ds_nome: '',
            nr_telefonezap: '',
            ds_cidade: '',
            ds_uf: '',
            ds_pais: '',
            ds_estado: '',
            search: '',
            limit: listaItensPorPagina.value[0],
            page: 1,
            instance: '',
        };
        carregarListaContatos();
    }

    async function selcionarContatos(all, value) {
        if (all) {
            listaContatos.value.forEach((cliente) => {
                cliente.checked = value;
            });
            checkAll.value = value;
            contatosSelecionados.value = value ? listaContatos.value.filter((cliente) => cliente.checked) : [];
        } else {
            checkAll.value = listaContatos.value.every((cliente) => cliente.checked) ? true : false;
            contatosSelecionados.value = listaContatos.value.filter((cliente) => cliente.checked);
        }
    }

    function acionarEdicao(cliente) {
        gavetaContato.value.dados = {
            ...cliente,
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
        };
        gavetaContato.value.estado = true;
    }

    const selecionaInstancia = async (event) => {
        // console.log('🚀 ~ listaContatos.vue:585 ~ selecionaInstancia ~ event:', event);
        if (event.event == 'Select') {
            await carregarListaContatos();
        }
    };

    const pesquisarFiltro = debounce(async () => {
        filtros.value.page = 1;
        filtros.value.search = filtros.value.search?.trim();
        await carregarListaContatos();
    }, 500);

    async function excluirContatos(abrirModal = false) {
        console.log('TESTAR ESSE METODO');
        // if (contatosSelecionados.value.length === 0) {
        //     showNotifications.value.showWarningNotification('Nenhum cliente selecionado para exclusão.');
        //     return;
        // }
        // if (abrirModal) {
        //     modalExcluir.value = true;
        //     return;
        // }
        // try {
        //     loading.value.show();
        //     const contatos = contatosSelecionados.value;
        //     const dados = {
        //         cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
        //     };
        //     const exclusoes = [];
        //     const erros = [];
        //     for await (const cliente of contatos) {
        //         dados.id = cliente.id;
        //         const result = await ContatosServices.remover(dados);
        //         if (result.statuscode != 200) {
        //             erros.push(cliente);
        //             continue;
        //         }
        //         exclusoes.push(cliente);
        //     }
        //     if (erros.length > 0) {
        //         showNotifications.value.showErrorNotification(
        //             `Não foi possível excluir os contatos: ${erros.map((c) => c.ds_nome).join(', ')}`
        //         );
        //     } else {
        //         showNotifications.value.showSuccessNotification(
        //             contatosSelecionados.value.length + ' contatos excluídos com sucesso!'
        //         );
        //         await carregarListaContatos();
        //     }
        // } catch (error) {
        //     showNotifications.value.showErrorNotification(error);
        // } finally {
        //     contatosSelecionados.value = [];
        //     checkAll.value = false;
        //     modalExcluir.value = false;
        //     loading.value.hide(500);
        // }
    }

    async function carregarListaContatos() {
        listaContatos.value = [];
        contatosSelecionados.value = [];
        checkAll.value = false;
        await nextTick();
        loading.value.show();
        try {
            const filtrosConfigurados = {
                ...filtros.value,
                cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
                instance: instanciaSelecionada.value,
            };
            for (const key in filtrosConfigurados) {
                if (filtrosConfigurados[key] === '') {
                    delete filtrosConfigurados[key];
                }
            }

            const response = await ContatosServices.listar(filtrosConfigurados);
            listaContatos.value =
                response.data?.map((cliente) => {
                    return {
                        ...cliente,
                        checked: false, // Adiciona a propriedade checked para o checkbox
                    };
                }) || [];
        } catch (error) {
            showNotifications.value.showErrorNotification(error);
        } finally {
            loading.value.hide(500);
        }
    }

    async function carregaUsuarioInstancias() {
        //cdUsuario.value,dsLogin.value,dsUsuario.value
        const filtro = {
            cd_usuario: parseInt(codUsuario.value),
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
        };
        const respInst = await UsuarioInstanciasServices.listar(filtro);
        //  console.log('🚀 ~ listaContatos.vue:682 ~ carregaUsuarioInstancias ~ respInst:', respInst);

        if (respInst.statuscode === 500) {
            toast.showErrorNotification(respInst.message);
        } else if (respInst.statuscode === 200) {
            let inst = [];
            respInst.data.forEach((instancia) => {
                inst.push(instancia.nameinstance);
            });

            instanciaLiberada.value = inst;

            instanciaSelecionada.value = inst;
        }
    }

    async function carregaInstancias() {
        const filtros = {
            cd_estabelecimento:
                estabelecimentosLiberado.value.length > 0
                    ? estabelecimentosLiberado.value[0].cd_estabelecimento
                    : undefined,
            cd_usuario: parseInt(codUsuario.value),
            in_verificastatus: false,
        };

        listaInstancias.value = [];

        const result = await InstanciaServices.listar(filtros);

        if (result.statuscode == 200) {
            listaInstancias.value = result.data.map((instancia) => {
                return {
                    value: instancia.nameinstance,
                    label: instancia.nome + ' - ' + instancia.telefone,
                };
            });
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
    }

    onMounted(async () => {
        codUsuario.value = localStorage.getItem('codusuario');
        const estatabelecimentos = localStorage.getItem('estabelecimentos');

        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);
        } else {
            showNotifications.value.showWarningNotification('Estabelecimento não liberado!');
        }

        await carregaInstancias();
        await carregaUsuarioInstancias();

        await carregarListaContatos();

        // Enter para filtros
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && estadoDropdownFiltros.value) {
                event.preventDefault();
                carregarListaContatos();
            }
        });
    });
</script>

<style scoped>
    .custom-table {
        border-collapse: separate !important;
        border-spacing: 0 0 !important;
    }
    .custom-table th,
    .custom-table td {
        padding: 12px;
        text-align: left;
    }
    .custom-table {
        border-bottom: none !important;
    }

    ::v-deep(.ts-dropdown) {
        z-index: 10000 !important;
    }
</style>
