<template>
    <ShowLoading ref="loading" />

    <div class="box p-4">
        <!-- Header da seção de módulos -->
        <!-- Header da seção de módulos -->
        <div class="bg-gradient-to-br from-emerald-400 to-teal-600 rounded-t-xl p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                        <MessageCircleIcon
                            class="w-6 h-6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                        />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">Mensagens</h3>
                        <p class="text-blue-100 text-sm">Configuração do fluxo de mensagens</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button
                        class="btn btn-success-soft mr-2 shadow-sm hover:shadow-md transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:opacity-100"
                        @click="abreCadMensagem(undefined, false)"
                    >
                        <PlusIcon class="w-4 h-4 mr-1 text-white" style="stroke-width: 3" />
                        <span class="text-white whitespace-nowrap overflow-hidden">Nova Mensagem</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="col-span-12" style="height: 92%">
            <div class="intro-y box flex flex-col" style="height: 92%">
                <!-- <div class="grid grid-cols-12" v-show="inMensagemPadrao">
        <div class="col-span-2 ml-2">
          <div class="form-check mr-2 mt-2">
            <div class="form-check form-switch -mt-1.5">
              <input
                id="inAtiva"
                name="inAtiva"
                class="form-check-input"
                type="checkbox"
                :value="inMensagemPadrao"
                :checked="inMensagemPadrao"
                @change="onChangeMSGPadrao($event)"
              />
            </div>
            <label class="forceLeft form-label self-center ml-2">Mensagens Padrão</label>
          </div>
        </div>
        <div class="col-span-3 ml-2">
          <button class="btn btn-secondary-soft mr-1 mb-2 mt-1 h-6" @click="abreModalCopiarMensagens()">
            Copiar Mensagens
          </button>
        </div>
      </div> -->
                <div class="grid grid-cols-12 flex-1">
                    <div class="col-span-12 p-3 flex-1 flex flex-col h-full">
                        <TabGroup class="flex-1 flex flex-col">
                            <TabList
                                class="nav-pills w-60 border border-slate-300 dark:border-darkmode-300 border-dashed rounded-md p-1"
                                :class="{
                                    'bg-orange-400': inMensagemPadrao == true,
                                }"
                            >
                                <Tab
                                    class="w-full py-1.5 px-2"
                                    tag="button"
                                    @click="carregaFluxoAtendimentos('Fixa', inMensagemPadrao)"
                                    >Fixa</Tab
                                >
                                <Tab
                                    class="w-full py-1.5 px-2"
                                    tag="button"
                                    @click="carregaFluxoAtendimentos('Personalizada', inMensagemPadrao)"
                                    >Personalizada</Tab
                                >
                            </TabList>
                            <TabPanels class="mt-2 flex-1 overflow-auto">
                                <TabPanel class="h-full">
                                    <div class="table-container">
                                        <table class="custom-table table table-report">
                                            <thead>
                                                <tr>
                                                    <th class="w-1/2">Título</th>
                                                    <th class="w-1/4">Ativa</th>
                                                    <th class="w-1/4 text-center">Ação</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="mensagem in listaMensagens" :key="mensagem.nr_controle">
                                                    <td :class="{ 'ml-10': mensagem.qt_filhos != '0' }">
                                                        <span class="font-medium"
                                                            >{{ mensagem.ds_titulo }}
                                                            {{
                                                                mensagem.ds_teclaatalho != null
                                                                    ? '(' + mensagem.ds_teclaatalho + ')'
                                                                    : ''
                                                            }}</span
                                                        >
                                                        <div class="text-slate-500 text-xs mt-0.5">
                                                            {{ mensagem.ds_descritivo }}
                                                        </div>
                                                        <span
                                                            class="flex items-center mr-3"
                                                            v-show="mensagem.qt_filhos != '0'"
                                                        >
                                                            <label class="form-label text-slate-500 text-xs mt-1.5">
                                                                Tem(os) {{ mensagem.qt_filhos }} mensagem(ns)
                                                                relacionada(s)
                                                            </label>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="flex items-center cursor-pointer">
                                                            <span class="flex items-center mr-3">
                                                                <div class="form-check form-switch">
                                                                    <input
                                                                        class="form-check-input"
                                                                        type="checkbox"
                                                                        :value="mensagem.in_ativa"
                                                                        :checked="mensagem.in_ativa"
                                                                        @change="onChangeStatus(mensagem, $event)"
                                                                    />
                                                                </div>
                                                                <label class="form-label mt-1.5 ml-1">{{
                                                                    mensagem.in_ativa ? 'Ativada' : 'Desativada'
                                                                }}</label>
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div
                                                            class="flex items-center cursor-pointer"
                                                            @click="abreCadMensagem(mensagem, false)"
                                                        >
                                                            <span class="flex items-center mr-3">
                                                                <EyeIconIcon class="w-4 h-4 mr-1" />
                                                                Visualizar
                                                            </span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </TabPanel>
                            </TabPanels>
                        </TabGroup>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <cadMensagem
        ref="cadMensagemRef"
        @eventMensagemSalva="handleMensagemSalva"
        @eventMensagemVinculadaSalva="handleMensagemVinculadaSalva"
        @eventDesvinculaMensagem="handleDesvinculaMsg"
        @eventMensagemRemovida="handleMsgRemovida"
    />

    <cadCopiarMensagens ref="cadCopiarMensagensRef" />
</template>

<script setup>
    import { computed, onMounted, reactive, ref, toRefs } from 'vue';
    import { usePagina } from '@/stores/pagina';

    import { useRouter, useRoute } from 'vue-router';
    import cadMensagem from '@/views/mensagem/cadMensagem.vue';
    import FluxoAtendimentoServices from '@/services/fluxos/FluxoAtendimentoServices';
    import cadCopiarMensagens from '@/views/mensagem/cadCopiarMensagens.vue';
    // import logger from '../../utils/logger';
    import { useDarkModeStore } from '@/stores/dark-mode';
    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);
    import { useToast } from '@/global-components/toastify/useToast';
    const toast = useToast();

    let pagina = usePagina();
    const loading = ref();
    const router = useRouter();
    const cadMensagemRef = ref();
    const cadCopiarMensagensRef = ref();
    let codUsuario = ref();

    let estabelecimentosLiberado = ref();
    let listaMensagens = ref([]);
    let tpMensagem = ref();
    //let modalMensagem = ref(false);
    let inMensagemPadrao = ref(false);

    // const formDataMensagem = reactive({
    //   cd_mensagem: undefined,
    //   ds_titulo: undefined,
    //   ds_descritivo: undefined,
    //   ds_mensagem: undefined,
    // });

    // const organizeMessages = (messages) => {
    //   const messageMap = new Map();

    //   // Passo 1: Criar um mapa de mensagens pelo `nr_controle`
    //   messages.forEach((message) => {
    //     messageMap.set(message.nr_controle, { ...message, filhos: [] });
    //   });

    //   // Passo 2: Iterar pelas mensagens e agrupar as que têm `nr_fluxopai`
    //   messages.forEach((message) => {
    //     if (message.nr_fluxopai !== null) {
    //       const parentMessage = messageMap.get(message.nr_fluxopai);
    //       if (parentMessage) {
    //         parentMessage.filhos.push(messageMap.get(message.nr_controle));
    //       }
    //     }
    //   });

    //   // Passo 3: Filtrar as mensagens principais que não têm `nr_fluxopai`
    //   const organizedMessages = messages
    //     .filter((message) => message.nr_fluxopai === null)
    //     .map((message) => messageMap.get(message.nr_controle));

    //   return organizedMessages;
    // };

    async function carregaFluxoAtendimentos(tpMen, inPadrao) {
        tpMensagem.value = tpMen;
        loading.value.show();
        let filtros = {
            cd_estabelecimento: estabelecimentosLiberado.value[0].cd_estabelecimento,
            tp_mensagem: tpMen,
            in_fluxopadrao: inPadrao,
        };

        listaMensagens.value = [];

        let result = await FluxoAtendimentoServices.listarFluxoAtendimentoFilhos(filtros);
        // logger.debug('carregaFluxoAtendimentos > listarFluxoAtendimentoFilhos > result:', result);

        if (result.statuscode == 200) {
            //listaMensagens.value = organizeMessages(result.data);
            listaMensagens.value = result.data;
        } else if (result.statuscode != 404) {
            toast.showErrorNotification(result.message);
        }
        loading.value.hide();
    }

    const onChangeMSGPadrao = async (event) => {
        inMensagemPadrao.value = event.target.checked;
        await carregaFluxoAtendimentos(tpMensagem.value, inMensagemPadrao.value);
    };

    const onChangeStatus = async (mensagem, event) => {
        mensagem.in_ativa = event.target.checked;
        const findMensagem = listaMensagens.value.find((msg) => msg.nr_controle == mensagem.nr_controle);
        if (findMensagem) {
            findMensagem.in_ativa = mensagem.in_ativa;
        }
        const req = {
            nr_controle: mensagem.nr_controle,
            cd_estabelecimento: mensagem.cd_estabelecimento,
            in_ativa: mensagem.in_ativa,
        };
        await alterar(req);
    };

    async function alterar(msg) {
        loading.value.show();
        let filtros = {
            nr_controle: msg.nr_controle,
            cd_estabelecimento: msg.cd_estabelecimento,
            in_ativa: msg.in_ativa,
        };

        let result = await FluxoAtendimentoServices.alterar(filtros);

        if (result.statuscode == 200) {
            if (msg.in_ativa) {
                toast.showSuccessNotification('Mensagem Ativada com sucesso!');
            } else {
                toast.showWarningNotification('Mensagem Desativada com sucesso!');
            }
        } else if (result.statuscode == 404) {
            toast.showWarningNotification(result.message);
        } else {
            toast.showErrorNotification(result.message);
        }
        loading.value.hide();
    }

    const abreCadMensagem = (mensagem, open) => {
        let titulo = mensagem;
        if (mensagem != undefined) {
            if (tpMensagem.value == 'Fixa') {
                titulo = 'Mensagem Fixa';
            } else {
                titulo = 'Mensagem Personalizada';
            }
        }
        cadMensagemRef.value.abreModal(titulo, mensagem, !open, tpMensagem.value, inMensagemPadrao.value);
    };

    const handleMensagemSalva = async (mensagem) => {
        const findMensagem = listaMensagens.value.find((msg) => msg.nr_controle == mensagem.nr_controle);

        if (findMensagem) {
            for (const key in mensagem) {
                if (mensagem.hasOwnProperty(key)) {
                    findMensagem[key] = mensagem[key];
                }
            }
        } else {
            listaMensagens.value.push(mensagem);
        }
    };

    const handleMensagemVinculadaSalva = async (mensagem) => {
        const mensagemPai = listaMensagens.value.find((mens) => mens.nr_controle === mensagem.nr_contmensagem);

        if (mensagemPai) {
            let qtFilhos = parseInt(mensagemPai.qt_filhos) + 1;
            mensagemPai.qt_filhos = qtFilhos.toString();
        }
    };

    const handleMsgRemovida = async (mensagem) => {
        const findMensagem = listaMensagens.value.filter((mens) => mens.nr_controle != mensagem.nr_controle);

        listaMensagens.value = findMensagem;
    };

    const handleDesvinculaMsg = async (mensagem) => {
        const mensagemPai = listaMensagens.value.find((mens) => mens.nr_controle === mensagem.nr_contmensagem);
        if (mensagemPai) {
            let qtFilhos = parseInt(mensagemPai.qt_filhos) - 1;
            mensagemPai.qt_filhos = qtFilhos.toString();
        }
    };

    async function abreModalCopiarMensagens() {
        //cadCopiarMensagensRef.value.abreModal();
    }

    onMounted(async () => {
        const hostName = location.href;
        pagina.pagename = 'Lista de Mensagem';
        pagina.description = 'Mensagem';
        pagina.module = 'Mensagem';
        tpMensagem.value = 'Fixa';
        inMensagemPadrao.value = false;

        codUsuario.value = await localStorage.getItem('codusuario');
        if (hostName.includes('listaMensagemPadrao')) {
            estabelecimentosLiberado.value = [{ cd_estabelecimento: 0 }];
            inMensagemPadrao.value = true;
            await carregaFluxoAtendimentos(tpMensagem.value, inMensagemPadrao.value);
            return;
        }

        const estatabelecimentos = await localStorage.getItem('estabelecimentos');
        if (estatabelecimentos) {
            estabelecimentosLiberado.value = JSON.parse(estatabelecimentos);
            await carregaFluxoAtendimentos(tpMensagem.value, inMensagemPadrao.value);
        } else {
            toast.showWarningNotification('Estabelecimento não liberado!');
        }
    });
</script>

<style scoped>
    .forceLeft {
        margin-left: 10px;
    }
    .table-container {
        width: 100%;
        height: 69vh; /* Ajuste conforme necessário */
        overflow-y: auto;
        /* background-color: rgb(250, 250, 250);*/
    }

    .custom-table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed; /* Adicionado */
        font-size: 12px;
    }

    .custom-table thead {
        position: sticky;
        top: 0;
        /*  background: #f8fafc;*/ /* Ajuste conforme necessário */
        z-index: 1;
    }
    .custom-table th {
        padding: 0.3rem; /* Reduz o padding */
        line-height: 0.5; /* Ajusta a altura da linha */
    }

    .custom-table td {
        padding: 0.5rem; /* Reduz o padding */
        line-height: 1.1; /* Ajusta a altura da linha */
    }

    .custom-table th {
        text-align: left;
    }

    .custom-table tbody {
        max-height: calc(92vh - 42px); /* Ajuste conforme necessário */
        overflow-y: auto;
    }
</style>
