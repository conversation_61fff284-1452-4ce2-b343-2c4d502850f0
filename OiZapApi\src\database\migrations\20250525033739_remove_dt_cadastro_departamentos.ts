import { Knex } from 'knex';
import path from 'path';

export async function up(knex: Knex): Promise<void> {
  try {
    // Auto-limpeza antes de executar
    const migrationsPath = path.join(__dirname, '..');
    // await cleanOrphanMigrations(knex, migrationsPath);

    // Verifica se a coluna existe
    const hasColumn = await knex.schema.hasColumn('departamentos', 'dt_cadastro');

    if (hasColumn) {
      await knex.schema.alterTable('departamentos', (table) => {
        table.dropColumn('dt_cadastro');
      });
    }
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.alterTable('departamentos', (table) => {
      table.timestamp('dt_cadastro').nullable();
    });
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
