require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';

export class CampanhasDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['campanhas'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      // console.log('🚀 ~ CampanhasDB.ts:16 ~ incluir ~ opDb: OperationObject.req.body:', req.body);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async findByHash(hash: string) {
    const xSQL = `select * from campanhas where nr_hash = '${hash}' limit 1`;
    const result = await new PostgreSQLServices().query(xSQL);
    return result.data && result.data[0];
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select * from campanhas where 1=1`;
      if (req.query.ds_campanha) {
        xSQL += ` and ds_campanha like '%${req.query.ds_campanha}%'`;
      }
      if (req.query.tp_campanha) {
        xSQL += ` and tp_campanha like '%${req.query.tp_campanha}%'`;
      }
      if (req.query.in_ativo) {
        xSQL += ` and in_ativo = ${req.query.in_ativo}`;
      }
      if (req.query.cd_estabelecimento) {
        xSQL += ` and cd_estabelecimento = ${req.query.cd_estabelecimento}`;
      }
      xSQL += ` order by ds_campanha`;
      return await new PostgreSQLServices().query(xSQL);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['campanhas'],
        chaves: { cd_campanha: req.body.cd_campanha, cd_estabelecimento: req.body.cd_estabelecimento },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async excluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['campanhas'],
        chaves: { cd_campanha: req.body.cd_campanha, cd_estabelecimento: req.body.cd_estabelecimento },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
