const utils = require('./util');
require('dotenv').config();
const { Client } = require('pg');

async function start() {
  //Salva no SandBox
  const message = process.argv[2];
  let client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASEDEV,
    database: process.env.DATABASE_SANDBOX,
    password: process.env.PASSWORD_DATABASEDEV,
    port: process.env.PORT_DATABASEDEV,
  });
  client.connect();
  await client.query(
    `insert into commits (sistema,comentario,data) values ('OiZap Api','${message}',current_timestamp)`,
  );
  client.end();

  //Salva na crm
  client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASE,
    database: process.env.DATABASE_CRM,
    password: process.env.PASSWORD_DATABASE,
    port: process.env.PORT_DATABASE,
  });
  client.connect();
  await client.query(
    `insert into commits (sistema,comentario,data) values ('OiZap Api','${message}',current_timestamp)`,
  );
  client.end();

  //Salva na Produção
  client = new Client({
    user: process.env.USER_DATABASE,
    host: process.env.HOST_DATABASE,
    database: process.env.DATABASE,
    password: process.env.PASSWORD_DATABASE,
    port: process.env.PORT_DATABASE,
  });
  client.connect();
  await client.query(
    `insert into commits (sistema,comentario,data) values ('OiZap Api','${message}',current_timestamp)`,
  );
  client.end();

  const { data } = await utils.executar(`git rev-parse --abbrev-ref HEAD`);
  var resultGit = await utils.executar(`git c "${message}"`);
  resultGit = await utils.executar(`git push backup ${data[0]}`);

  if (resultGit.statuscode != 200) {
    console.log(resultGit.message);
    return;
  }

  console.log('Commit realizado com sucesso!');
}
start();
