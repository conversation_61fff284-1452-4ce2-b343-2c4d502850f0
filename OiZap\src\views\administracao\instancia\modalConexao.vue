<template>
    <ShowLoading ref="loading" class="!z-[100000]" />
    <notifications ref="showNotifications" />

    <!--09/06/2025 09:44 - <PERSON><PERSON>
        Foi alterado para não fazer a verificação de conexão quando o modal for fechado,
        para não remover a fila de conexão do RabbitMQ.
     @hidden="voltar(formData.status_connection === 'Conectado')" -->
    <Modal
        :show="modalState"
        @hidden="voltar(formData.status_connection === 'Conectado', false)"
        :key="chaveModal"
        size="modal-xl"
    >
        <ModalHeader
            class="flex items-center justify-between bg-gradient-to-r from-emerald-400 to-teal-600 text-white rounded-t-lg py-2"
        >
            <h2 class="font-medium text-base">Conectar WhatsApp</h2>
            <!-- @click="voltar(formData.status_connection === 'Conectado')" -->
            <button
                @hidden="voltar(formData.status_connection === 'Conectado', false)"
                class="btn text-white hover:bg-white/10 border-none shadow-none p-1"
                :disabled="exibirCancelandoQRCode"
            >
                <XIcon class="w-4 h-4" />
            </button>
        </ModalHeader>

        <ModalBody class="transition-all duration-300 ease-in-out flex flex-col lg:flex-row p-0 overflow-hidden">
            <div class="intro-y h-full w-full lg:w-[400px] flex flex-col bg-gray-50 dark:bg-slate-800/50 p-3 pt-4">
                <div class="flex flex-col items-center justify-center">
                    <div class="text-lg font-semibold mb-1">
                        {{
                            exibirLoadingQRCode
                                ? 'Aguarde, gerando QR code'
                                : inDesconectado || formData?.status_connection != 'Conectado'
                                ? 'Escaneie o QR Code'
                                : 'WhatsApp Conectado 😍'
                        }}
                    </div>

                    <div
                        class="flex items-center justify-center mb-2 py-0.5 px-4 rounded-full text-sm font-medium"
                        :class="{
                            'bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-gray-300': !formData?.id_instancia,
                            'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400':
                                formData?.status_connection == 'Conectado',
                            'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400':
                                formData?.status_connection == 'Conectando' || exibirLoadingQRCode,
                            'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400':
                                formData?.status_connection == 'Desconectado' && !exibirLoadingQRCode,
                        }"
                    >
                        <div
                            class="w-1.5 h-1.5 rounded-full mr-1.5"
                            :class="{
                                'bg-gray-400 dark:bg-gray-500': !formData?.id_instancia,
                                'bg-emerald-500 dark:bg-emerald-400': formData?.status_connection == 'Conectado',
                                'bg-amber-500 dark:bg-amber-400 animate-pulse':
                                    formData?.status_connection == 'Conectando' || exibirLoadingQRCode,
                                'bg-red-500 dark:bg-red-400':
                                    formData?.status_connection == 'Desconectado' && !exibirLoadingQRCode,
                            }"
                        ></div>
                        {{
                            exibirLoadingQRCode
                                ? 'Gerando QR code...'
                                : formData?.status_connection == 'Conectado'
                                ? 'Conectado'
                                : formData?.status_connection == 'Conectando'
                                ? 'Aguardando conexão...'
                                : 'Desconectado'
                        }}
                    </div>

                    <!-- Indicador de loading para geração do QR code -->
                    <div v-if="exibirLoadingQRCode" class="flex flex-col items-center justify-center py-4">
                        <LoadIcon icon="three-dots" class="w-10 h-10 mx-auto text-amber-500 dark:text-amber-400 mb-2" />
                        <div class="text-base font-medium text-gray-800 dark:text-gray-200">Gerando QR code...</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Aguarde um momento</div>
                    </div>

                    <CheckIcon
                        class="w-16 h-16 text-emerald-500 dark:text-emerald-400 mx-auto my-3"
                        style="stroke-width: 2"
                        v-if="!exibirLoadingQRCode && formData?.status_connection == 'Conectado'"
                    />

                    <!-- Substitua o CloudOffIcon por um indicador mais claro de cancelamento -->
                    <div v-if="exibirCancelandoQRCode" class="flex flex-col items-center justify-center py-4">
                        <LoadIcon icon="oval" class="w-10 h-10 mx-auto text-amber-500 dark:text-amber-400 mb-3" />
                        <div class="text-base font-medium text-gray-800 dark:text-gray-200">Cancelando conexão...</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Aguarde enquanto finalizamos o processo
                        </div>
                    </div>

                    <!-- Manter o CloudOffIcon apenas para o estado desconectado quando não está cancelando -->
                    <CloudOffIcon
                        class="w-16 h-16 mx-auto mt-2 mb-2"
                        :class="{
                            'text-red-500 dark:text-red-400': formData?.status_connection == 'Desconectado',
                            'text-gray-300 dark:text-gray-600': !formData?.id_instancia,
                        }"
                        style="stroke-width: 2"
                        v-if="
                            !exibirLoadingQRCode &&
                            formData?.status_connection != 'Conectado' &&
                            qrCode == '' &&
                            !timeOutQRCode &&
                            !exibirCancelandoQRCode
                        "
                    />

                    <!-- QR Code (reduzir margin) -->
                    <div
                        v-if="gerandoQRCode"
                        class="flex items-center justify-center p-2 rounded-xl shadow-lg mt-2 border border-slate-200 dark:border-slate-700 w-[50%] bg-white dark:bg-slate-900"
                    >
                        <img alt="qrcode" :src="qrCode" class="mx-auto w-full" />
                    </div>

                    <!-- Tela de expiração (reduzir padding) -->
                    <div v-if="timeOutQRCode" class="p-3 text-center">
                        <div class="rounded-full bg-amber-100 dark:bg-amber-900/30 p-3 w-20 h-20 mx-auto mb-3">
                            <XCircleIcon class="w-14 h-14 text-amber-500 dark:text-amber-400" style="stroke-width: 2" />
                        </div>
                        <div class="text-lg font-medium text-gray-800 dark:text-gray-200 mt-1">
                            Limite de QR code atingido!
                        </div>
                        <div class="text-gray-500 dark:text-gray-400 mt-1 text-sm">
                            Foram gerados 2 códigos QR sem sucesso.
                        </div>
                    </div>

                    <!-- Botões da tela de expiração (reduzir padding) -->
                    <div
                        v-if="timeOutQRCode"
                        class="flex flex-col sm:flex-row items-center justify-center p-2 gap-2 w-full"
                    >
                        <button
                            type="button"
                            @click="voltar(formData.status_connection === 'Conectado', true)"
                            class="btn btn-danger-soft border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 w-full sm:w-28"
                        >
                            Cancelar
                        </button>
                        <button
                            type="button"
                            class="btn bg-gradient-to-r from-emerald-400 to-teal-600 text-white w-full sm:w-[60%] hover:from-emerald-500 hover:to-teal-700 shadow-md"
                            @click="
                                resetarTentativas();
                                timeOutQRCode = false;
                                conectar();
                            "
                        >
                            <RefreshCcwIcon class="w-4 h-4 mr-2" />
                            Tentar novamente
                        </button>
                    </div>

                    <div class="w-full mt-3 flex flex-col items-center">
                        <div
                            v-if="contagemRegressivaQrCode > 0 && !exibirCancelandoQRCode && !timeOutQRCode"
                            class="mb-2 bg-gray-100 dark:bg-slate-700 rounded-full h-1.5 w-full max-w-[250px]"
                        >
                            <div
                                class="h-1.5 rounded-full bg-gradient-to-r from-emerald-400 to-teal-600"
                                :style="`width: ${(contagemRegressivaQrCode / 45) * 100}%`"
                            ></div>
                        </div>
                        <div
                            class="text-xs text-gray-500 dark:text-gray-400"
                            v-if="contagemRegressivaQrCode > 0 && !exibirCancelandoQRCode && !timeOutQRCode"
                        >
                            O QR Code expira em
                            <strong class="text-teal-600 dark:text-teal-400">{{ contagemRegressivaQrCode }}</strong>
                            segundos
                        </div>
                    </div>

                    <button
                        v-if="formData?.status_connection == 'Conectado' || (gerandoQRCode && !exibirCancelandoQRCode)"
                        class="btn mt-3 border border-gray-300 dark:border-slate-700 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 w-[80%] py-1"
                        @click="voltar('Desconectar', true)"
                    >
                        <XIcon class="w-4 h-4 mr-2" /> {{ gerandoQRCode ? 'Cancelar' : 'Desconectar' }}
                    </button>
                </div>
            </div>

            <div class="flex flex-col p-4 items-start justify-center h-full lg:flex-1">
                <h2
                    class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 w-full text-center border-b border-gray-200 dark:border-slate-700 pb-3"
                >
                    Como conectar seu WhatsApp
                </h2>

                <div class="space-y-4 w-full max-w-[500px] mx-auto">
                    <!-- Reduzir o tamanho dos círculos e diminuir espaçamento -->
                    <div class="flex items-start gap-3" v-for="(item, index) in passosWhatsapp" :key="index">
                        <div
                            class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full text-white shadow-lg bg-gradient-to-br from-emerald-400 to-teal-600 text-sm"
                        >
                            {{ item.id }}
                        </div>
                        <div>
                            <h3 class="font-medium text-sm text-gray-800 dark:text-gray-200">{{ item.title }}</h3>
                            <p class="text-gray-600 dark:text-gray-400 text-xs mt-0.5">{{ item.description }}</p>
                        </div>
                    </div>

                    <div
                        class="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-100 dark:border-emerald-800 rounded-lg p-3 mt-4"
                    >
                        <div class="flex items-start">
                            <InfoIcon class="w-4 h-4 text-emerald-500 dark:text-emerald-400 mr-2 mt-0.5" />
                            <div>
                                <h4 class="font-medium text-xs text-emerald-700 dark:text-emerald-400">
                                    Dica importante
                                </h4>
                                <p class="text-xs text-emerald-600 dark:text-emerald-300 mt-0.5">
                                    Mantenha seu celular conectado à internet durante o processo de vinculação. Caso o
                                    QR Code expire, você pode gerar um novo a qualquer momento.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ModalBody>
    </Modal>
</template>

<script setup>
    import { onMounted, reactive, ref, toRaw, watch, onUnmounted } from 'vue';
    // import { useRouter, useRoute } from 'vue-router';
    import notifications from '@/components/show-notifications/Main.vue';
    //import InstanciaModulosServices from '@/services/administracao/InstanciaModulosServices';
    //import EstabelecimentoInstancias from '@/services/administracao/EstabelecimentoInstanciasServices';
    import InstanciaServices from '@/services/chat/InstanciaServices';
    //import endpointTeste from '@/utils/enpointsTeste';
    import LoadIcon from '@/global-components/loading-icon/Main.vue';
    //import CodeBlock from '@/components/code-block/Main.vue';
    //import modalConexao from './modalConexao.vue';

    //import { usePagina } from '@/stores/pagina';
    // import hosts from '@/utils/hosts';

    //  import io from 'socket.io-client';

    import WhatsAppServices from '@/services/whatsapp/WhatsAppServices';
    import logger from '../../../utils/logger';
    //import { CloudOffIcon } from 'lucide-vue-next';
    // import { endpointsList } from '../../api/endpointsList';

    const showNotifications = ref(null);
    const loading = ref();
    const modalState = ref(false);
    const chaveModal = ref(0);

    const qrCode = ref('');
    const contagemRegressivaQrCode = ref(0);
    const exibirLoadingQRCode = ref(false);
    const gerandoQRCode = ref(false);
    const timeOutQRCode = ref(false);
    const exibirQRCode = ref(false);
    const inDesconectado = ref(false);
    const exibirLoading = ref(false);
    const exibirCancelandoQRCode = ref(false);
    const tentativasQrCode = ref(0);

    const limiteAtingido = ref(false);
    const emit = defineEmits(['conexao']);

    const props = defineProps({
        baseQRCode: {
            type: String,
        },
    });

    const passosWhatsapp = ref([
        {
            id: 1,
            title: 'Abra o WhatsApp no seu celular',
            description: 'Certifique-se de que você está usando a versão mais recente do aplicativo',
        },
        {
            id: 2,
            title: 'Acesse as configurações',
            description: 'Toque em Configurações > Dispositivos conectado',
        },
        {
            id: 3,
            title: 'Escaneie o QR Code',
            description: 'Clique em "Conectar dispositivo" e escaneie o QR Code exibido na tela',
        },
        {
            id: 4,
            title: 'Aguarde a conexão',
            description:
                'Aguarde a conexão ser estabelecida. Você verá uma mensagem de confirmação quando estiver conectado',
        },
    ]);
    const formData = reactive({
        id_instancia: null,
        in_stop_bot: null,
        nameinstance: null,
        nome: null,
        nr_hash: null,
        nr_telefone: null,
        status_connection: '',
        status: null,
    });

    function iniciarTimer() {
        // Não iniciar timer se já atingiu o limite
        if (limiteAtingido.value || tentativasQrCode.value >= 2) {
            // console.log('Não iniciando timer, limite já foi atingido');
            return;
        }

        // Se já existe um timer rodando, não crie outro
        if (window.qrCodeTimer) {
            // console.log('Timer já existe, não iniciando novo');
            return;
        }

        // Inicia com o tempo configurado
        contagemRegressivaQrCode.value = 45;
        // console.log('Iniciando novo timer de', contagemRegressivaQrCode.value, 'segundos');

        // Cria o intervalo
        window.qrCodeTimer = setInterval(() => {
            // Decrementa o contador
            if (contagemRegressivaQrCode.value > 0) {
                contagemRegressivaQrCode.value--;
                // Log menos frequente para não sobrecarregar o console
                // if (contagemRegressivaQrCode.value % 5 === 0) {
                //     console.log('Timer:', contagemRegressivaQrCode.value);
                // }
            }
            // Quando chegar a zero
            else {
                // console.log('Timer chegou a zero');
                // Limpa o intervalo para parar a contagem
                clearInterval(window.qrCodeTimer);
                window.qrCodeTimer = null;

                // Se já atingiu o limite de tentativas
                if (tentativasQrCode.value >= 2) {
                    // console.log('Timer expirou e limite atingido, mostrando tela de expiração');
                    // Mostra a tela de tempo expirado
                    timeOutQRCode.value = true;
                    qrCode.value = '';
                    exibirQRCode.value = false;
                    gerandoQRCode.value = false;
                    limiteAtingido.value = true;

                    // Executa logout para parar de receber eventos
                    logoutInstanciaAposLimite();
                } else {
                    // Caso contrário, tenta gerar um novo QR code
                    // console.log('Timer expirou, mas ainda não atingiu limite, gerando novo QR code');
                    conectar();
                }
            }
        }, 1000);
    }

    async function logoutInstanciaAposLimite() {
        // console.log('Executando logout forçado após limite atingido');

        // Garantir que todas as flags estejam corretamente configuradas
        timeOutQRCode.value = true;
        qrCode.value = '';
        exibirQRCode.value = false;
        gerandoQRCode.value = false;
        limiteAtingido.value = true;

        // Parar o timer se estiver rodando
        if (window.qrCodeTimer) {
            clearInterval(window.qrCodeTimer);
            window.qrCodeTimer = null;
        }

        // Executar logout e delete para limpar completamente
        try {
            const reqObj = {
                nr_hash: formData.nr_hash,
                instance: formData.nameinstance,
            };

            // Fazer logout
            const respClose = await WhatsAppServices.logoutInstance(reqObj);
            // console.log('logoutInstance após limite:', respClose);

            // Deletar a instância
            const respDelete = await WhatsAppServices.deleteInstance(reqObj);
            // console.log('deleteInstance após limite:', respDelete);

            // Emitir evento para o componente pai saber que houve timeout
            emit('conexao', { ...formData, status: 'timeout', status_connection: 'Desconectado' });

            // Mudar imediatamente o status para desconectado
            formData.status_connection = 'Desconectado';
            formData.status = 'Desconectado';
        } catch (error) {
            // console.error('Erro ao fazer logout após limite:', error);
        }
    }

    const verificaConnection = async () => {
        let tentativas = 0;
        let maxTentativas = 5;

        let retorno;

        while (tentativas < maxTentativas) {
            tentativas++;
            const respConect = await connectionState();
            // console.log('respConect at line 276 in instancia/modalConexao.vue:', respConect);

            if (respConect.data.status_connection != 'Conectado') {
                retorno = { statuscode: 404, tentativas };
                await new Promise((resolve) => setTimeout(resolve, 5000)); // Espera 5 segundos antes de tentar novamente
            } else {
                let filtros = {
                    instance: formData.nameinstance,
                    nr_hash: formData.nr_hash,
                };

                const respInstance = await WhatsAppServices.fetchInstances(filtros);
                logger.debug('connection > fetchInstances > respInstance', respInstance);
                if (respInstance.statuscode == 200) {
                    contagemRegressivaQrCode.value = 0;
                    let telefone = respInstance.data.nr_telefone; //respInstance.data[0].owner;
                    if (telefone == undefined) {
                        formData.nr_telefone = null;
                    } else {
                        //telefone = telefone.replace('@s.whatsapp.net', '').replace('55', '');
                        formData.nr_telefone = telefone;

                        let req = {
                            telefone: telefone,
                            // instance: formData.nameinstance,
                            id: formData.id_instancia,
                        };

                        let result = await InstanciaServices.alterar(req);
                        //  console.log('carregaInstancia > update', result);
                    }
                }

                retorno = { statuscode: 200, tentativas };
                break;
            }
        }

        tentativas = 100000;

        return retorno;
    };

    async function cancelaQRCode() {
        timeOutQRCode.value = false;
        qrCode.value = '';
        timeOutQRCode.value = false;
        exibirLoading.value = false;
        exibirLoadingQRCode.value = false;
        exibirQRCode.value = false;
        gerandoQRCode.value = false;
        inDesconectado.value = false;
        //stopInterval();

        const reqObj = {
            nr_hash: formData.nr_hash,
            instance: formData.nameinstance,
        };
        let respClose = await WhatsAppServices.logoutInstance(reqObj);
        logger.debug('logoutInstance', respClose);

        respClose = await WhatsAppServices.deleteInstance(reqObj);
        logger.debug('deleteInstance', respClose);
    }

    async function connectionState() {
        let filtros = {
            //instance: formData.nr_telefone,
            instance: formData.nameinstance,
            nr_hash: formData.nr_hash,
        };

        // logger.debug('connectionState > connectInstance > filtros', filtros);
        const respConn = await WhatsAppServices.connectionState(filtros);

        if (respConn.data?.status == 'connecting') iniciarTimer();
        //logger.debug('connectionState > connectInstance > respConn', respConn);

        if (respConn.statuscode == 200) {
            //formData.status_connection = trataStatus(respConn.data.state);
            formData.status_connection = respConn.data.status_connection;

            if (formData.status_connection == 'Conectado') {
                exibirQRCode.value = false;
                gerandoQRCode.value = false;
                exibirLoadingQRCode.value = false;
                timeOutQRCode.value = false;
                qrCode.value = '';
                //stopInterval();
            }

            let dataInstance = respConn.data;
            let resp = {
                statuscode: respConn.statuscode,
                message: respConn.message,
                data: dataInstance,
            };

            await connection(resp);
        }

        return respConn;
    }

    async function connection(connect) {
        // console.log('connectionState', connect);
        //if (connect?.code == 'ERR_NETWORK') {
        //clearInterval(intervalId.value);
        //} else if (connect.statuscode != 200) {
        //clearInterval(intervalId.value);
        // } else if (connect.data.status_connection != 'Conectado') {
        //clearInterval(intervalId.value);
        // } else {
        //clearInterval(intervalId.value);
        if (formData.nr_telefone == null || formData.nr_telefone == undefined) {
            let filtros = {
                instance: formData.nameinstance,
                nr_hash: formData.nr_hash,
            };

            //logger.debug('connection > fetchInstances > filtros', filtros);
            const respInstance = await WhatsAppServices.fetchInstances(filtros);
            //logger.debug('connection > fetchInstances > respInstance', respInstance);
            if (respInstance.statuscode == 200) {
                let telefone = respInstance.data.nr_telefone; //respInstance.data[0].owner;
                if (telefone == undefined) {
                    formData.nr_telefone = null;
                } else {
                    //telefone = telefone.replace('@s.whatsapp.net', '').replace('55', '');
                    formData.nr_telefone = telefone;
                }

                let req = {
                    telefone: telefone,
                    // instance: formData.nameinstance,
                    id: formData.id_instancia,
                };

                let result = await InstanciaServices.alterar(req);
                // console.log('carregaInstancia > update', result);
            }
        }

        if (connect.data.status_connection == 'Conectado') {
            conectado();
        }
    }

    async function conectado() {
        // Atualiza os estados
        contagemRegressivaQrCode.value = 0;
        formData.status_connection = 'Conectado';
        exibirQRCode.value = false;
        gerandoQRCode.value = false;
        exibirLoadingQRCode.value = false;
        timeOutQRCode.value = false;
        qrCode.value = '';

        // Pequena pausa para mostrar o ícone de sucesso brevemente antes de fechar
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Fecha o modal automaticamente após conectado com sucesso
        fechar();
    }
    async function connectInstance(req) {
        // console.log('req at line 765 in instancia/viewInstancia.vue:', req);
        // logger.debug('connectInstance > connectInstance > req', req);
        const respInst = await WhatsAppServices.connectInstance(req);
        // logger.debug('connectInstance > connectInstance > respInst', respInst);
        qrCode.value = respInst.data.base64;
        exibirLoadingQRCode.value = false;
        gerandoQRCode.value = true;
        timeOutQRCode.value = false;
        exibirLoading.value = false;
    }

    async function conectar() {
        // Se já atingiu o máximo de tentativas, não tenta mais (REDUZIDO PARA 2)
        if ((tentativasQrCode.value >= 2 && !timeOutQRCode.value) || limiteAtingido.value) {
            timeOutQRCode.value = true;
            qrCode.value = '';
            exibirQRCode.value = false;
            gerandoQRCode.value = false;

            // Se ainda não fez logout, faz agora
            if (!limiteAtingido.value) {
                limiteAtingido.value = true;
                logoutInstanciaAposLimite();
            }
            return;
        }

        // Resto da função permanece igual
        inDesconectado.value = false;
        const reqObj = {
            instance: formData.nameinstance,
            nr_hash: formData.nr_hash,
        };

        if (formData.status_connection != 'Conectado') {
            qrCode.value = '';
            //instancia.value = false;
            //chat.value = true;
            gerandoQRCode.value = false;
            exibirLoadingQRCode.value = true;
            exibirQRCode.value = true;
            // modaLogar.value = true;

            let filtros = {
                instance: formData.nameinstance,
                nr_hash: formData.nr_hash,
            };

            //  logger.debug('connection > fetchInstances > filtros', filtros);
            const respInstance = await WhatsAppServices.fetchInstances(filtros);
            // logger.debug('connection > fetchInstances > respInstance', respInstance);
            if (respInstance.statuscode != 200) {
                const respConn = await WhatsAppServices.createInstanceRabbitMQ(reqObj);
                // console.log('respConn', respConn);
                if (respConn.statuscode != 200 && respConn.errors != 'Instância já existe') {
                    showNotifications.value.showErrorNotification(respConn.message);
                    return;
                }

                qrCode.value = respConn.data.base64;

                exibirLoadingQRCode.value = false;
                gerandoQRCode.value = true;
                timeOutQRCode.value = false;
                exibirLoading.value = false;

                verificaConnection();
                return;
            } else {
                let telefone = respInstance.data.nr_telefone; //respInstance.data[0].owner;
                if (telefone == undefined) {
                    formData.nr_telefone = null;
                } else {
                    //telefone = telefone.replace('@s.whatsapp.net', '').replace('55', '');
                    formData.nr_telefone = telefone;

                    let req = {
                        telefone: telefone,
                        // instance: formData.nameinstance,
                        id: formData.id_instancia,
                    };

                    let result = await InstanciaServices.alterar(req);
                    // console.log('carregaInstancia > update', result);
                }
            }

            const respState = await WhatsAppServices.connectionState(filtros);
            //logger.info('previewModalLogar > connectionState', respState);

            if (respState.statuscode != 200) {
                const respConn = await WhatsAppServices.createInstanceRabbitMQ(reqObj);
                // console.log('respConn', respConn);
                if (respConn.statuscode != 200 && respConn.errors != 'Instância já existe') {
                    showNotifications.value.showErrorNotification(respConn.message);
                    return;
                }

                qrCode.value = respConn.data.base64;

                exibirLoadingQRCode.value = false;
                gerandoQRCode.value = true;
                timeOutQRCode.value = false;
                exibirLoading.value = false;

                verificaConnection();
            } else if (respState.data.status_connection != 'Conectado') {
                // logger.debug('conectar > connectInstance > reqObj', reqObj);
                const respConn = await WhatsAppServices.connectInstance(reqObj);
                // logger.debug('conectar > connectInstance > respConn', respConn);
                if (respConn.statuscode != 200) {
                    showNotifications.value.showErrorNotification(respConn.message);
                    return;
                }

                qrCode.value = respConn.data.base64;

                exibirLoadingQRCode.value = false;
                gerandoQRCode.value = true;
                timeOutQRCode.value = false;
                exibirLoading.value = false;

                verificaConnection();
            } else {
                await conectado();
            }
        } else {
            // Quando o status for 'Conectado', em vez de chamar conectado() diretamente,
            // vamos verificar o status de conexão e deixar a função connection() lidar com isso

            // Verificar o status atual da conexão
            let filtros = {
                instance: formData.nameinstance,
                nr_hash: formData.nr_hash,
            };

            const respState = await WhatsAppServices.connectionState(filtros);
            if (respState.statuscode == 200) {
                // Passar o resultado para a função connection
                // que irá verificar o status e chamar conectado() se necessário
                await connection(respState);
            }
        }
    }
    async function voltar(status_connection, logoutInstance) {
        // Parar o timer imediatamente ao clicar em Cancelar
        if (window.qrCodeTimer) {
            clearInterval(window.qrCodeTimer);
            window.qrCodeTimer = null;
        }

        // Remover a visualização do contador
        contagemRegressivaQrCode.value = 0;

        resetarTentativas();
        if (status_connection) return fechar();

        exibirCancelandoQRCode.value = true;
        inDesconectado.value = true;

        // Limpar estados do QR code
        exibirQRCode.value = false;
        gerandoQRCode.value = false;
        exibirLoadingQRCode.value = false;
        timeOutQRCode.value = false;
        qrCode.value = '';

        try {
            // Realizar o logout da instância
            if (logoutInstance) {
                const reqObj = {
                    nr_hash: formData.nr_hash,
                    instance: formData.nameinstance,
                };
                let respClose = await WhatsAppServices.logoutInstance(reqObj);
                //logger.debug('logoutInstance', respClose);

                respClose = await WhatsAppServices.deleteInstance(reqObj);
                //  logger.debug('deleteInstance', respClose);
            }

            // Atualizar status
            formData.status_connection = 'Desconectado';
            formData.status = 'Desconectado';

            // Pequeno atraso para mostrar a mensagem de cancelando antes de fechar
            await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (error) {
            console.error('Erro ao desconectar:', error);
        } finally {
            exibirCancelandoQRCode.value = false;
            fechar();
        }
    }

    function abrir(dados) {
        formData.id_instancia = dados.id_instancia;
        formData.in_stop_bot = dados.in_stop_bot;
        formData.nameinstance = dados.nameinstance;
        formData.nome = dados.nome;
        formData.nr_hash = dados.nr_hash;
        formData.nr_telefone = dados.nr_telefone;

        // Define como 'Conectando' em vez de usar o status atual
        // Isso garante que mostremos o estado correto enquanto aguardamos o QR code
        formData.status_connection = 'Conectando';

        // Ativar o estado de loading do QR code imediatamente
        exibirLoadingQRCode.value = true;

        modalState.value = true;
    }

    // function atualizaQrCode(dados) {
    //     console.log('dados at line 642 in instancia/modalConexao.vue:', dados);
    //     qrCode.value = dados.base64;
    //     console.log('qrCode.value at line 694 in instancia/modalConexao.vue:', qrCode.value);
    // }

    function fechar() {
        // formData.status_connection = null;
        if (modalState.value == false) return;

        modalState.value = false;
        emit('conexao', formData);
    }

    defineExpose({
        abrir,
        fechar,
        // atualizaQrCode,
    });

    watch(
        () => modalState.value,
        async () => {
            if (modalState.value) {
                // console.log('abriu');
                await conectar();
            }
        },
        { immediate: true }
    );

    watch(
        () => props.baseQRCode,
        (newValue) => {
            // Verifica se já atingimos o limite antes de qualquer processamento
            if (limiteAtingido.value || tentativasQrCode.value >= 2) {
                //console.log('IGNORANDO QR code - limite já foi atingido');
                // Força logout para interromper o ciclo
                if (!timeOutQRCode.value) {
                    timeOutQRCode.value = true;
                    qrCode.value = '';
                    exibirQRCode.value = false;
                    gerandoQRCode.value = false;
                    limiteAtingido.value = true;
                    logoutInstanciaAposLimite();
                }
                return;
            }

            if (newValue) {
                qrCode.value = newValue;
                exibirLoadingQRCode.value = false;
                exibirCancelandoQRCode.value = false;
                exibirQRCode.value = true;
                gerandoQRCode.value = true;

                // Incrementa o contador de tentativas
                tentativasQrCode.value++;
                // console.log('TENTATIVAS QRCODE', tentativasQrCode.value);

                // Verifica se esta tentativa atingiu o limite
                if (tentativasQrCode.value >= 2) {
                    // console.log('Limite atingido após incremento, parando o processo');
                    timeOutQRCode.value = true;
                    limiteAtingido.value = true;
                    logoutInstanciaAposLimite();
                    return; // Não reinicia o timer
                }

                // Iniciar timer apenas se não existir um já em execução
                if (!window.qrCodeTimer) {
                    iniciarTimer();
                }

                formData.status_connection = 'Conectando';
            }
        }
    );

    function resetarTentativas() {
        tentativasQrCode.value = 0;
        limiteAtingido.value = false;
        if (window.qrCodeTimer) {
            clearInterval(window.qrCodeTimer);
            window.qrCodeTimer = null;
        }
        timeOutQRCode.value = false;
        contagemRegressivaQrCode.value = 0;
    }

    onUnmounted(() => {
        if (window.qrCodeTimer) {
            clearInterval(window.qrCodeTimer);
            window.qrCodeTimer = null;
        }
    });
</script>
