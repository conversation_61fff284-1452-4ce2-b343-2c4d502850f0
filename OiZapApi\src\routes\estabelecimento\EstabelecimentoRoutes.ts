import { Router } from 'express';
import { EstabelecimentoController } from '../../controllers/estabelecimento/EstabelecimentoController';
import auth from '../../middleware/authApi'; // auth caso queira autenticar
import authorizationApi from '../../middleware/authorizationApi';

export const EstabelecimentoRoutes = Router();

/* rotas */
EstabelecimentoRoutes.post('/estabelecimento/v1', auth, EstabelecimentoController.incluirEstabelecimento);
EstabelecimentoRoutes.post(
  '/estabelecimentoIntegracao/v1',
  authorizationApi,
  EstabelecimentoController.incluirEstabelecimentoIntegracao,
);
EstabelecimentoRoutes.post('/estabelecimento/criarApiDedicada/v1', auth, EstabelecimentoController.criarApiDedicada);
EstabelecimentoRoutes.post(
  '/estabelecimentoCadastro/v1',
  authorizationApi,
  EstabelecimentoController.incluirEstabelecimentoCadastro,
);
EstabelecimentoRoutes.delete('/estabelecimento/limpaDados/v1', auth, EstabelecimentoController.limpaDados);
EstabelecimentoRoutes.put('/estabelecimento/v1', auth, EstabelecimentoController.alterarEstabelecimento);
EstabelecimentoRoutes.get('/estabelecimento/v1', auth, EstabelecimentoController.listarEstabelecimento);
EstabelecimentoRoutes.get(
  '/estabelecimento/listaInstanciasPorEstabelecimento/v1',
  auth,
  EstabelecimentoController.listaInstanciasPorEstabelecimento,
);
EstabelecimentoRoutes.get(
  '/estabelecimento/listarEstabelecimentoIntegracao/v1',
  authorizationApi,
  EstabelecimentoController.listarEstabelecimentoIntegracao,
);
EstabelecimentoRoutes.get(
  '/estabelecimento/listaEstabelecimentoDirect/v1',
  auth,
  EstabelecimentoController.listaEstabelecimentoDirect,
);
//EstabelecimentoRoutes.delete('/estabelecimento/v1', auth, EstabelecimentoController.removerEstabelecimento);

/* end rotas */
