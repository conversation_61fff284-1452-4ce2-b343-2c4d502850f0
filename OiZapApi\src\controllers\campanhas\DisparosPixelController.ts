import { Request, Response } from 'express';
import { erroInterno, INTERNAL_SERVER_ERROR } from '../../interfaces/IRetorno';
import { DisparosPixelModel } from '../../models/campanhas/DisparosPixelModel';

export class DisparosPixelController {
  static async criarDisparo(req: Request, res: Response) {
    try {
      const result = await new DisparosPixelModel().criarDisparo(req);
      return res.status(result.statuscode || 201).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  // static async capturarClique(req: Request, res: Response) {
  //   try {
  //     await new DisparosPixelModel().capturarClique(req, res);
  //   } catch (error: any) {
  //     return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
  //   }
  // }

  // static async webhookWhatsapp(req: Request, res: Response) {
  //   try {
  //     await new DisparosPixelModel().webhookWhatsapp(req, res);
  //   } catch (error: any) {
  //     return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
  //   }
  // }

  static async listarDisparos(req: Request, res: Response) {
    try {
      const result = await new DisparosPixelModel().listarDisparos(req);
      return res.status(result.statuscode || 200).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
