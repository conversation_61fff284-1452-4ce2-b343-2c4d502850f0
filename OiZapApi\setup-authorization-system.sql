-- ========================================
-- Setup: Sistema de Autorização Completo
-- Data: 2025-01-09
-- Descrição: Configuração completa do sistema de perfis, telas e autorização
-- ========================================

-- 1. <PERSON><PERSON><PERSON> perfi<PERSON> básicos
INSERT INTO perfis (nm_perfil, created_at) 
VALUES 
  ('Administrador', CURRENT_TIMESTAMP),
  ('Atendente', CURRENT_TIMESTAMP),
  ('Gerente', CURRENT_TIMESTAMP)
ON CONFLICT (nm_perfil) DO NOTHING;

-- 2. Criar telas administrativas
INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES 
  ('Cadastro de Perfil', '/cadPerfil', CURRENT_TIMESTAMP),
  ('Cadastro de Tela', '/cadTela', CURRENT_TIMESTAMP),
  ('Associação Perfis x Telas', '/cadPerfisTelas', CURRENT_TIMESTAMP),
  ('Lista de Perfis', '/listaPerfis', CURRENT_TIMESTAMP),
  ('Lista de Telas', '/listaTelas', CURRENT_TIMESTAMP),
  ('Dashboard', '/dashboard', CURRENT_TIMESTAMP),
  ('Home', '/home', CURRENT_TIMESTAMP),
  ('Chat', '/chat', CURRENT_TIMESTAMP),
  ('Atendimento', '/atendimento', CURRENT_TIMESTAMP),
  ('Clientes', '/listaClientes', CURRENT_TIMESTAMP),
  ('Contatos', '/listaContatos', CURRENT_TIMESTAMP),
  ('Mensagens', '/listaMensagem', CURRENT_TIMESTAMP),
  ('Fluxos', '/listaFluxos', CURRENT_TIMESTAMP),
  ('Configurações', '/integracao', CURRENT_TIMESTAMP)
ON CONFLICT (ds_rota) DO NOTHING;

-- 3. Associar usuário 87 ao perfil Administrador
-- Primeiro, vamos pegar o cd_perfil do Administrador
DO $$
DECLARE
    admin_perfil_id INTEGER;
    usuario_id INTEGER := 87;
BEGIN
    -- Pegar o ID do perfil Administrador
    SELECT cd_perfil INTO admin_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Administrador' 
    LIMIT 1;
    
    -- Associar usuário ao perfil (se não existir)
    IF admin_perfil_id IS NOT NULL THEN
        INSERT INTO usuarios_perfis (cd_usuario, cd_perfil, created_at)
        VALUES (usuario_id, admin_perfil_id, CURRENT_TIMESTAMP)
        ON CONFLICT (cd_usuario, cd_perfil) DO NOTHING;
        
        RAISE NOTICE 'Usuário % associado ao perfil Administrador (ID: %)', usuario_id, admin_perfil_id;
    ELSE
        RAISE NOTICE 'Perfil Administrador não encontrado!';
    END IF;
END $$;

-- 4. Associar todas as telas ao perfil Administrador com todas as permissões
DO $$
DECLARE
    admin_perfil_id INTEGER;
    tela_record RECORD;
BEGIN
    -- Pegar o ID do perfil Administrador
    SELECT cd_perfil INTO admin_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Administrador' 
    LIMIT 1;
    
    -- Associar todas as telas ao perfil Administrador
    IF admin_perfil_id IS NOT NULL THEN
        FOR tela_record IN SELECT cd_tela FROM telas LOOP
            INSERT INTO perfis_telas (
                cd_perfil, 
                cd_tela, 
                in_visualizar, 
                in_inserir, 
                in_alterar, 
                in_excluir, 
                created_at
            )
            VALUES (
                admin_perfil_id, 
                tela_record.cd_tela, 
                true,  -- visualizar
                true,  -- inserir
                true,  -- alterar
                true,  -- excluir
                CURRENT_TIMESTAMP
            )
            ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
                in_visualizar = EXCLUDED.in_visualizar,
                in_inserir = EXCLUDED.in_inserir,
                in_alterar = EXCLUDED.in_alterar,
                in_excluir = EXCLUDED.in_excluir,
                updated_at = CURRENT_TIMESTAMP;
        END LOOP;
        
        RAISE NOTICE 'Todas as telas associadas ao perfil Administrador (ID: %)', admin_perfil_id;
    ELSE
        RAISE NOTICE 'Perfil Administrador não encontrado!';
    END IF;
END $$;

-- 5. Associar algumas telas ao perfil Atendente (permissões limitadas)
DO $$
DECLARE
    atendente_perfil_id INTEGER;
BEGIN
    -- Pegar o ID do perfil Atendente
    SELECT cd_perfil INTO atendente_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Atendente' 
    LIMIT 1;
    
    -- Associar telas básicas ao perfil Atendente
    IF atendente_perfil_id IS NOT NULL THEN
        -- Dashboard e Home (apenas visualizar)
        INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
        SELECT atendente_perfil_id, cd_tela, true, false, false, false, CURRENT_TIMESTAMP
        FROM telas 
        WHERE ds_rota IN ('/dashboard', '/home')
        ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir,
            updated_at = CURRENT_TIMESTAMP;
        
        -- Chat e Atendimento (visualizar e inserir)
        INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
        SELECT atendente_perfil_id, cd_tela, true, true, true, false, CURRENT_TIMESTAMP
        FROM telas 
        WHERE ds_rota IN ('/chat', '/atendimento')
        ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir,
            updated_at = CURRENT_TIMESTAMP;
        
        -- Clientes e Contatos (apenas visualizar)
        INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
        SELECT atendente_perfil_id, cd_tela, true, false, false, false, CURRENT_TIMESTAMP
        FROM telas 
        WHERE ds_rota IN ('/listaClientes', '/listaContatos')
        ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir,
            updated_at = CURRENT_TIMESTAMP;
        
        RAISE NOTICE 'Telas básicas associadas ao perfil Atendente (ID: %)', atendente_perfil_id;
    ELSE
        RAISE NOTICE 'Perfil Atendente não encontrado!';
    END IF;
END $$;

-- 6. Associar algumas telas ao perfil Gerente (permissões intermediárias)
DO $$
DECLARE
    gerente_perfil_id INTEGER;
BEGIN
    -- Pegar o ID do perfil Gerente
    SELECT cd_perfil INTO gerente_perfil_id 
    FROM perfis 
    WHERE nm_perfil = 'Gerente' 
    LIMIT 1;
    
    -- Associar telas ao perfil Gerente
    IF gerente_perfil_id IS NOT NULL THEN
        -- Todas as telas básicas (visualizar e inserir)
        INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
        SELECT gerente_perfil_id, cd_tela, true, true, true, false, CURRENT_TIMESTAMP
        FROM telas 
        WHERE ds_rota IN ('/dashboard', '/home', '/chat', '/atendimento', '/listaClientes', '/listaContatos', '/listaMensagem', '/listaFluxos')
        ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir,
            updated_at = CURRENT_TIMESTAMP;
        
        -- Configurações (apenas visualizar)
        INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
        SELECT gerente_perfil_id, cd_tela, true, false, false, false, CURRENT_TIMESTAMP
        FROM telas 
        WHERE ds_rota IN ('/integracao')
        ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir,
            updated_at = CURRENT_TIMESTAMP;
        
        RAISE NOTICE 'Telas associadas ao perfil Gerente (ID: %)', gerente_perfil_id;
    ELSE
        RAISE NOTICE 'Perfil Gerente não encontrado!';
    END IF;
END $$;

-- 7. Verificações finais
SELECT 
    'PERFIS CRIADOS:' as info,
    nm_perfil,
    cd_perfil
FROM perfis
ORDER BY cd_perfil;

SELECT 
    'TELAS CRIADAS:' as info,
    nm_tela,
    ds_rota,
    cd_tela
FROM telas
ORDER BY cd_tela;

SELECT 
    'ASSOCIAÇÕES USUÁRIO-PERFIL:' as info,
    up.cd_usuario,
    p.nm_perfil,
    up.cd_perfil
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
ORDER BY up.cd_usuario, up.cd_perfil;

SELECT 
    'ASSOCIAÇÕES PERFIL-TELA:' as info,
    p.nm_perfil,
    t.nm_tela,
    pt.in_visualizar,
    pt.in_inserir,
    pt.in_alterar,
    pt.in_excluir
FROM perfis_telas pt
JOIN perfis p ON p.cd_perfil = pt.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
ORDER BY p.nm_perfil, t.nm_tela;

-- 8. Teste de permissão para usuário 87
SELECT 
    'TESTE DE PERMISSÃO - USUÁRIO 87:' as info,
    p.nm_perfil,
    t.nm_tela,
    t.ds_rota,
    pt.in_visualizar,
    pt.in_inserir,
    pt.in_alterar,
    pt.in_excluir
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 87
ORDER BY t.nm_tela; 