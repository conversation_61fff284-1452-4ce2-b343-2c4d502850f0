import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection } from 'amqplib';
import axios from 'axios';

export default class RemoveQueues {
  private queuePrefix: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;

  constructor(queuePrefix: string) {
    this.queuePrefix = queuePrefix;
  }

  async connect() {
    try {
      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();

      const queuesToDelete = await this.getQueuesWithPrefix(this.queuePrefix);

      for (const queueName of queuesToDelete) {
        await this.deleteQueue(queueName);
      }

      await this.channel.close();
      await this.connection.close();
      return queuesToDelete;
    } catch (error) {
      console.error('Erro ao processar as filas:', error);
      return [];
    }
  }

  async getQueuesWithPrefix(prefix: string): Promise<string[]> {
    // console.log('prefix at line 53 in rabbitmq/RemoveQueue.ts:', prefix);
    try {
      // console.log('process.env.API_RABBITMQ at line 131 in rabbitmq/RabbitMQServices.ts:', process.env.API_RABBITMQ);
      // console.log(
      //   ' process.env.USER_RABBITMQ  at line 134 in rabbitmq/RabbitMQServices.ts:',
      //   process.env.USER_RABBITMQ,
      // );
      // console.log('process.env.PASS_RABBITMQ at line 136 in rabbitmq/RabbitMQServices.ts:', process.env.PASS_RABBITMQ);
      // const response = await axios.get(process.env.API_RABBITMQ as string, {
      //   auth: {
      //     username: process.env.USER_RABBITMQ as string,
      //     password: process.env.PASS_RABBITMQ as string,
      //   },
      // });

      let HOST_RABBITMQ: string = '';

      if (prefix == 'oizap') {
        HOST_RABBITMQ = process.env.API_RABBITMQOIZAP as string;
      } else {
        if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
          HOST_RABBITMQ = process.env.API_RABBITMQ as string;
        } else {
          HOST_RABBITMQ = process.env.API_RABBITMQSANDBOX as string;
        }
      }
      const apiUrl = `${HOST_RABBITMQ}/default`;

      const response = await axios.get(apiUrl, {
        auth: {
          username: process.env.USER_RABBITMQ as string,
          password: process.env.PASS_RABBITMQ as string,
        },
      });
      const queues = response.data;

      return queues
        .map((queue: { name: string }) => queue.name)
        .filter((queueName: string) => queueName.startsWith(prefix));
    } catch (error) {
      console.error('error at line 101 in rabbitmq/RemoveQueue.ts:', error);

      return [];
    }
  }

  async deleteQueue(queueName: string) {
    try {
      await this.channel!.deleteQueue(queueName);
    } catch (error) {
      console.error(`Erro ao remover a fila ${queueName}:`, error);
    }
  }
}
