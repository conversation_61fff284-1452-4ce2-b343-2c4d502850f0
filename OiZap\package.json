{"name": "<PERSON><PERSON>", "version": "2.1.54", "scripts": {"dev": "vite", "build": "vite build", "enviaroizap": "vite build --mode production", "enviarsandbox": "vite build --mode sandbox", "enviardev": "vite build --mode dev", "enviarcrm": "vite build --mode crm", "deploy": "node ./services/enviarServidor.js", "oizap": "node ./services/enviarServidor.js oizap", "sandbox": "node ./services/enviarServidor.js sandbox", "devzap": "node ./services/enviarServidor.js dev", "crm": "node ./services/enviarServidor.js crm", "commit": "node ./services/commit.js", "version": "node ./services/version.js", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-balloon": "^29.1.0", "@ckeditor/ckeditor5-build-balloon-block": "^29.1.0", "@ckeditor/ckeditor5-build-classic": "^29.1.0", "@ckeditor/ckeditor5-build-decoupled-document": "^29.1.0", "@ckeditor/ckeditor5-build-inline": "^29.1.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/vue-fontawesome": "^3.0.3", "@fullcalendar/core": "^5.5.1", "@fullcalendar/daygrid": "^5.5.0", "@fullcalendar/interaction": "^5.5.0", "@fullcalendar/list": "^5.5.0", "@fullcalendar/timegrid": "^5.5.1", "@googlemaps/js-api-loader": "^1.11.4", "@googlemaps/markerclustererplus": "^1.1.0", "@highlightjs/vue-plugin": "^2.1.0", "@left4code/tw-starter": "^3.1.35", "@popperjs/core": "^2.7.0", "@vue-flow/additional-components": "^1.3.3", "@vue-flow/background": "^1.2.0", "@vue-flow/controls": "^1.1.0", "@vue-flow/core": "^1.26.0", "@vue-flow/minimap": "^1.2.0", "@vue-flow/node-toolbar": "^1.1.0", "@vueform/multiselect": "^2.6.6", "@vuelidate/core": "^2.0.0-alpha.12", "@vuelidate/validators": "^2.0.0-alpha.11", "axios": "^1.6.8", "chalk": "^4.1.2", "chart.js": "^3.7.1", "crypto-js": "^4.2.0", "date-fns": "2.30.0", "date-fns-tz": "^2.0.0", "dayjs": "^1.10.4", "dotenv": "^16.4.5", "dropzone": "^5.7.6", "echarts": "^5.6.0", "fs-extra": "^11.2.0", "highlight.js": "^11.11.1", "install": "^0.13.0", "js-beautify": "^1.13.5", "litepicker": "^2.0.11", "lucide": "^0.460.0", "lucide-vue-next": "^0.460.0", "node-ssh": "^13.1.0", "npm": "^10.2.5", "pg": "^8.13.1", "pinia": "^2.0.9", "pinia-plugin-persistedstate": "^3.2.0", "save": "^2.9.0", "simplebar": "^5.3.6", "socket.io-client": "^4.7.2", "tabulator-tables": "^6.2.0", "tiny-slider": "^2.9.3", "tippy.js": "^6.2.7", "toastify-js": "^1.9.3", "tom-select": "^1.7.5", "uuid": "^9.0.1", "v-money3": "^3.24.1", "vue": "^3.3.7", "vue-3-slider-component": "^1.0.1", "vue-echarts": "^7.0.3", "vue-iframes": "^0.0.20", "vue-router": "4", "vue3-emoji-picker": "^1.1.8", "vue3-google-map": "^0.21.0", "vue3-timepicker": "^1.0.0-beta.2", "vue3-toastify": "^0.2.8", "vuedraggable": "^4.1.0", "xlsx": "^0.16.9", "zoom-vanilla.js": "^2.0.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@types/tabulator-tables": "^5.5.3", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "10.4.6", "inquirer": "^8.2.4", "lodash": "^4.17.21", "postcss": "^8.4.5", "postcss-advanced-variables": "^3.0.1", "postcss-import": "^14.0.2", "tailwindcss": "^3.3.5", "vite": "^4.4.6", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^1.0.0"}}