import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { downloadMediaMessage } from '@whiskeysockets/baileys';
import axios from 'axios';
import fs from 'fs';
import os from 'os';

import {
  IDocumentWhats,
  IImageWhats,
  IListWhats,
  ILocationWhats,
  IMessageWhats,
  IProfileWhats,
} from '../interfaces/IRespMessageWhats';
import { IRetorno, erroApiWhats, erroInterno, sucesso } from '../interfaces/IRetorno';
import {
  ISendAudio,
  ISendImage,
  ISendList,
  ISendLocation,
  ISendMessage,
  ISendProfile,
} from '../interfaces/ISendMessageWhats';
import { Funcoes } from './Funcoes';
import Logger from './Logger';
const logger = Logger.getLogger();

let HOST_APIWHATS: string | undefined;
if (process.env.AMBIENTE === 'PROD') {
  HOST_APIWHATS = process.env.HOST_APIWHATS;
} else if (process.env.AMBIENTE === 'CRM') {
  HOST_APIWHATS = process.env.HOST_APIWHATS;
} else {
  HOST_APIWHATS = process.env.HOST_APIWHATS_SANDBOX;
}

// async function saveBase64ToFile(base64Data: string, pathMida: any, fileName: string): Promise<void> {
//   const bufferData = Buffer.from(base64Data, 'base64');
//   /*let pathAudio: string | undefined;

//   if (os.platform() === 'win32') {
//     pathAudio = process.env.PATH_AUDIOSWINDOWS;
//   } else if (os.platform() === 'linux') {
//     if (process.env.AMBIENTE === 'PROD') {
//       pathAudio = process.env.PATH_AUDIOS;
//     } else {
//       pathAudio = process.env.PATH_AUDIOS_SANDBOX;
//     }
//   } else {
//     console.log('Estou em um sistema operacional diferente.');
//   }*/

//   if (pathMida) {
//     const filePath = path.join(pathMida, fileName);

//     // Extraia o diretório do caminho do arquivo completo
//     const dir = path.dirname(filePath);

//     try {
//       // Verifique e crie a pasta, se necessário
//       if (!fs.existsSync(dir)) {
//         fs.mkdirSync(dir, { recursive: true });
//         //  console.log(`Diretório criado: ${dir}`);
//       } else {
//         //console.log(`Diretório já existe: ${dir}`);
//       }

//       // Escreva o arquivo
//       fs.writeFileSync(filePath, bufferData);
//       //console.log(`Arquivo salvo em ${filePath}`);
//     } catch (error) {
//       console.error('Erro ao criar diretório ou escrever arquivo:', error);
//     }
//   }
// }

// const getMediaBase64 = async (message: any): Promise<string> => {
//   try {
//     const buffer = await downloadMediaMessage(message, 'buffer', {});
//     const base64Data = buffer.toString('base64');
//     return base64Data;
//   } catch (error: any) {
//     console.log('error at line 89 in services/EvolutionApiService.ts:', error);
//     throw error;
//   }
// };

const getMediaBase64 = async (message: any): Promise<string> => {
  try {
    // Verifica se a mensagem é válida
    if (!message || !message.message) {
      throw new Error('Mensagem inválida ou vazia');
    }

    logger.debug('Iniciando download da mídia...');

    const buffer = await downloadMediaMessage(message, 'buffer', {});

    if (!buffer) {
      throw new Error('Não foi possível baixar a mídia');
    }

    const base64Data = buffer.toString('base64');
    return base64Data;
  } catch (error: any) {
    logger.error(`Erro ao processar mídia: ${error.message}`);
    if (error.message.includes('crypto')) {
      logger.error('Erro de criptografia - verifique se o módulo crypto está disponível');
    }
    throw error;
  }
};

// Para o erro do ffmpeg, ajuste a função saveBase64ToFile:
async function saveBase64ToFile(base64Data: string, pathMida: any, fileName: string): Promise<void> {
  try {
    // Verifica se pathMida é válido
    if (!pathMida || typeof pathMida !== 'string') {
      throw new Error('Caminho inválido para salvar o arquivo');
    }

    const bufferData = Buffer.from(base64Data, 'base64');
    const filePath = path.join(pathMida, fileName);
    const dir = path.dirname(filePath);

    // Cria o diretório se não existir
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.debug(`Diretório criado: ${dir}`);
    }

    // Verifica se o diretório é gravável
    try {
      await fs.promises.access(dir, fs.constants.W_OK);
    } catch (error) {
      throw new Error(`Diretório não tem permissão de escrita: ${dir}`);
    }

    // Salva o arquivo
    await fs.promises.writeFile(filePath, bufferData);
    logger.debug(`Arquivo salvo com sucesso em: ${filePath}`);
  } catch (error: any) {
    logger.error(`Erro ao salvar arquivo: ${error.message}`);
    throw error;
  }
}

interface ReqObj {
  instance: string;
  id?: string;
  telefone?: string;
  timer?: number;
  mensagem?: string;
  nm_estabelecimento?: string;
  ds_endereco?: string;
  nr_latitude?: number;
  nr_longitude?: number;
  url_midia?: string;
  url_midiacompleta?: string;
  mediatype?: string;
  fileName?: string;
  caption?: string;
  url_document?: string;
  media?: string;
}

export class EvolutionApiService {
  static async sendText(reqObj: ISendMessage): Promise<IRetorno> {
    try {
      // console.log('reqObj.time:', reqObj.timer);
      const data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer,
          presence: 'composing',
          linkPreview: reqObj.linkpreview,
        },
        textMessage: {
          text: reqObj.mensagem,
        },
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/message/sendText/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };
      // console.log('sendText > config:', config);

      const response = await axios.request(config);

      if (response.status === 201) {
        //console.log('response.data at line 268 in services/EvolutionApiService.ts:', response.data);
        const resp: IMessageWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
        };
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função sendText:', error.response.data);
      //return { statuscode: 500, message: 'Erro na função sendText', data: error.response.data?.response?.message };
      return erroApiWhats(error);
    }
  }

  static async sendImage(reqObj: ISendImage): Promise<IRetorno> {
    try {
      //console.log('reqObj.time:', reqObj.timer);
      const data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer,
          presence: 'composing',
        },
        /*imageMessage: {
          url: reqObj.url,
          caption: reqObj.mensagem,
        },*/
        mediaMessage: {
          mediatype: 'image',
          caption: reqObj.mensagem,
          media: reqObj.url_midiacompleta,
        },
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/message/sendMedia/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      const response = await axios.request(config);
      /*
      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
*/
      if (response.status === 201) {
        //console.log('response.data at line 268 in services/EvolutionApiService.ts:', response.data);
        const resp: IImageWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          url_midia: reqObj.url_midia,
          url_midiacompleta: reqObj.url_midiacompleta,
          file_length: response.data.message.imageMessage.fileLength,
          file_sha256: response.data.message.imageMessage.fileSha256,
          media_key: response.data.message.imageMessage.mediaKey,
          nr_height: response.data.message.imageMessage.height,
          nr_width: response.data.message.imageMessage.width,
          mimetype: response.data.message.imageMessage.mimetype,
        };
        //console.log('resp at line 348 in services/EvolutionApiService.ts:', resp);
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      //console.error('Erro na função sendImage:', error.response.data);
      //return { statuscode: 500, message: 'Erro na função sendImage', data: error.response.data };
      return erroApiWhats(error);
      //return erroInterno(error, 'Erro na função sendImage');
    }
  }

  static async sendList(reqObj: ISendList): Promise<IRetorno> {
    try {
      const data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer,
          presence: 'composing',
        },
        listMessage: {
          title: reqObj.title,
          description: reqObj.description,
          buttonText: reqObj.buttonText,
          footerText: reqObj.footerText,
          sections: reqObj.sections,
        },
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/message/sendList/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      // console.log('config at line 226 in services/EvolutionApiService.ts:', config);
      const response = await axios.request(config);

      //console.log('response.data at line 228 in services/EvolutionApiService.ts:', response.data);
      if (response.status === 201) {
        const resp: IListWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          title: response.data.message.listMessage.title,
          description: response.data.message.listMessage.description,
          buttonText: response.data.message.listMessage.buttonText,
          listType: response.data.message.listMessage.listType,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
        };
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função sendList:', error.response.data);
      //return { statuscode: 500, message: 'Erro na função sendText', data: error.response.data?.response?.message };
      return erroApiWhats(error);
    }
  }

  static async findChats(reqObj: ReqObj): Promise<IRetorno> {
    try {
      // carrega os contatos
      const respFindContacts = await this.findContacts(reqObj);

      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/findChats/${reqObj.instance}`,
        headers: {
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
      };

      const response = await axios.request(config);
      const chats = response.data;
      const contacts = respFindContacts.data;

      if (response.status === 200) {
        // Percorra o segundo array
        chats.forEach((objChats: any) => {
          // Encontre o objeto correspondente no primeiro array com base no campo "id"
          const findContact = contacts.find((objContacts: any) => objContacts.id === objChats.id);

          // Se um objeto correspondente for encontrado, adicione o campo "profilePictureUrl" ao segundo objeto
          if (findContact) {
            objChats.pushName = findContact.pushName;
            objChats.profilePictureUrl = findContact.profilePictureUrl;
          }
        });

        return { statuscode: 200, message: 'Sucesso', data: chats };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função findChats:', error.response.data);
      return { statuscode: 500, message: 'Erro na função findChats', data: error.response.data };
    }
  }

  static async findContacts(reqObj: ReqObj): Promise<IRetorno> {
    try {
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/findContacts/${reqObj.instance}`,
        headers: {
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data: JSON.stringify({
          where: reqObj.id ? { id: reqObj.id } : {},
        }),
      };

      const response = await axios.request(config);

      if (response.status === 200) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função findContacts:', error.response.data);
      return { statuscode: 500, message: 'Erro na função findContacts', data: error.response.data };
    }
  }

  static async fetchProfilePictureUrl(reqObj: ReqObj): Promise<IRetorno> {
    try {
      const data = JSON.stringify({
        number: reqObj.telefone,
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/fetchProfilePictureUrl/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      const response = await axios.request(config);

      console.log(' response.data at line 366 in services/EvolutionApiService.ts:', response.data);
      if (response.status === 200) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função fetchProfilePictureUrl:', error.response.data);
      return { statuscode: 500, message: 'Erro na função fetchProfilePictureUrl', data: error.response.data };
    }
  }

  static async fetchProfile(reqObj: ISendProfile): Promise<IRetorno> {
    try {
      const data = JSON.stringify({
        number: reqObj.telefone,
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/fetchProfile/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      const response = await axios.request(config);
      //console.log('response.data at line 396 in services/EvolutionApiService.ts:', response.status, response.data);

      if (response.status === 200) {
        const resp: IProfileWhats = {
          telefone: reqObj.telefone,
          name: response.data.name,
          numberExists: response.data.numberExists,
          picture: response.data.picture,
          status: response.data.status,
          isBusiness: response.data.isBusiness,
        };
        return sucesso([resp]);

        // return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      //console.error('Erro na função fetchProfile:', error.response.data);
      return { statuscode: 500, message: 'Erro na função fetchProfile', data: error.response.data };
    }
  }

  static async findMessages(reqObj: ReqObj): Promise<IRetorno> {
    try {
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/findMessages/${reqObj.instance}`,
        headers: {
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data: JSON.stringify({
          where: {
            key: reqObj.id ? { remoteJid: reqObj.id } : {},
          },
        }),
      };

      const response = await axios.request(config);

      if (response.status === 200) {
        const dataMessage = response.data;
        for (let i = dataMessage.length - 1; i >= 0; i--) {
          if (dataMessage[i].key.remoteJid === 'status@broadcast') {
            dataMessage.splice(i, 1);
          }
        }
        return { statuscode: 200, message: 'Sucesso', data: dataMessage };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      // console.error('Erro na função findMessages:', error.response.data);
      return { statuscode: 500, message: 'Erro na função findMessages', data: error.response.data };
    }
  }

  static async sendLocation(reqObj: ISendLocation): Promise<IRetorno> {
    try {
      let latitude: number = Number(reqObj.latitude);
      let longitude: number = Number(reqObj.longitude);
      let data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer,
          presence: 'composing',
        },
        locationMessage: {
          name: reqObj.estabelecimento,
          address: reqObj.address,
          latitude: latitude,
          longitude: longitude,
        },
      });

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/message/sendLocation/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //console.log('sendLocation > reqObj', reqObj);
      // console.log('sendLocation > data', data);
      // console.error('sendLocation > config', config);

      const response = await axios.request(config);
      // console.log('sendLocation', response.data);

      if (response.status === 201) {
        const resp: ILocationWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          estabelecimento: reqObj.estabelecimento,
          address: reqObj.address,
          latitude: reqObj.latitude,
          longitude: reqObj.longitude,
        };

        return sucesso([resp]);

        //return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      //console.error('Erro na função sendLocation:', error.response.data);
      return erroInterno(error, 'Erro na função sendLocation');
      //return { statuscode: 500, message: 'Erro na função sendLocation', data: error.response.data };
    }
  }

  static async sendDocument(reqObj: ReqObj): Promise<IRetorno> {
    try {
      let data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
          presence: 'composing',
        },
        mediaMessage: {
          mediatype: 'document',
          fileName: reqObj.fileName,
          caption: reqObj.caption,
          media: reqObj.url_midiacompleta,
        },
      });

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/message/sendMedia/' + reqObj.instance,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //  console.log('sendDocument > reqObj', reqObj);
      //console.log('sendDocument > data', data);
      //console.error('sendDocument > config', config);

      const response = await axios.request(config);
      // console.error('sendDocument', response.data);

      if (response.status === 201) {
        //console.log('response.data at line 268 in services/EvolutionApiService.ts:', response.data);
        const resp: IDocumentWhats = {
          telefone: reqObj.telefone || '',
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          url_midia: reqObj.media || '',
          url_midiacompleta: reqObj.url_midiacompleta || '',
          file_length: response.data.message.documentMessage.fileLength,
          file_sha256: response.data.message.documentMessage.fileSha256,
          file_name: response.data.message.documentMessage.fileName,
          media_key: response.data.message.documentMessage.mediaKey,
          nr_height: response.data.message.documentMessage.height,
          nr_width: response.data.message.documentMessage.width,
          mimetype: response.data.message.documentMessage.mimetype,
        };

        //console.log('resp at line 348 in services/EvolutionApiService.ts:', resp);
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
      /*
      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }*/
    } catch (error) {
      return erroApiWhats(error);
      //return erroInterno(error, 'Erro na função sendDocument');
      //return { statuscode: 500, message: 'Erro na função sendDocument', data: error.response.data };
    }
  }

  static async sendAudio(reqObj: ISendAudio): Promise<IRetorno> {
    try {
      let data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
          presence: 'recording',
          encoding: true,
        },
        audioMessage: {
          audio: reqObj.url_midia,
        },
      });

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/message/sendWhatsAppAudio/' + reqObj.instance,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //  console.log('sendDocument > reqObj', reqObj);
      //  console.log('sendDocument > data', data);
      //  console.error('sendDocument > config', config);

      const response = await axios.request(config);
      // console.error('sendDocument', response.data);

      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função sendDocument');
    }
  }

  //retorna o Status da Conexão
  static async connectionState(instance: string): Promise<IRetorno> {
    // console.log('connectionState', instance);
    try {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/connectionState/' + instance,
        headers: {
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      //console.log(config);
      const response = await axios.request(config);
      //console.log(response);

      //console.log('response.data at line 496:', response.data);

      if (response.status === 200) {
        const data = {
          nameinstance: response.data.instance.instanceName,
          status: response.data.instance.state,
          status_connection: Funcoes.trataStatus(response.data.instance.state),
        };
        // console.log('data at line 522:', response.data.instance.state, data);
        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        //throw new Error('Erro na requisição da API.');
        return erroInterno(response.data, 'Erro na função connectionState');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função connectionState');
      //console.error('Erro na função connectionState:', message ? message : error.response.data);
    }
  }

  static async downloadAudio(id: any, outputPath: any, instance: any) {
    console.log('downloadAudio - FAZENDO O DOWNLOAD ID ' + id, outputPath);
    try {
      const data = JSON.stringify({
        message: {
          key: {
            id: id,
          },
        },
        convertToMp4: false,
      });

      console.log('downloadAudio - data', data);

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${HOST_APIWHATS}/chat/getBase64FromMediaMessage/${instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: `${process.env.APIKEY_WHATS}`,
        },
        data: data,
      };

      console.log('downloadAudio - config', config);

      const response = await axios.request(config);
      console.log('downloadAudio - response', response);
      const audio64 = response.data.base64;
      console.log('downloadAudio - audio64', audio64);

      // Use await para esperar a escrita do arquivo ser concluída
      await fs.promises.writeFile(outputPath, Buffer.from(audio64, 'base64'));

      // Agora você pode prosseguir com o próximo passo após o salvamento do arquivo
      console.log('Download e salvamento do arquivo concluídos com sucesso.');
      return { statuscode: 200, message: 'OK', data: [] };
    } catch (error) {
      //console.log('Erro ao baixar e salvar o arquivo de áudio:', error.response.data)
      return { statuscode: 500, message: error, data: [] };
    }
  }

  static async donwloadMedia(reqObj: any, type: any): Promise<any> {
    const data = reqObj;
    console.log('data at line 739 in services/EvolutionApiService.ts:', data);

    try {
      const message = data.data;
      let fileName;
      const instance = reqObj.instance;

      const currentTimeStamp = new Date().getTime(); // Obtém um carimbo de data/hora exclusivo

      let pathMida;
      let urlMidia;

      if (os.platform() === 'win32') {
        pathMida = process.env.PATH_MIDIA_WINDOWS + instance + '\\' + type + '\\';
        urlMidia = process.env.URL_MIDIA_WINDOWS + instance + '\\' + type + '\\';
      } else if (os.platform() === 'linux') {
        pathMida =
          process.env.AMBIENTE == 'SANDBOX'
            ? process.env.PATH_MIDIA_SANDBOX + instance + '/' + type + '/'
            : process.env.PATH_MIDIA + instance + '/' + type + '/';
        urlMidia =
          process.env.AMBIENTE == 'SANDBOX'
            ? process.env.URL_MIDIA_SANDBOX + instance + '/' + type + '/'
            : process.env.URL_MIDIA + instance + '/' + type + '/';
      } else {
        console.log('Estou em um sistema operacional diferente.');
      }

      //if ((message && message.message.audioMessage) || message.message?.documentMessage) {
      if (type == 'audioMessage') {
        /* if (message.message?.documentMessage) {
          if (message.message?.documentMessage.mimetype.includes('audio')) {
            fileName = `${currentTimeStamp}_${message.message?.documentMessage.fileSha256}.oga`; // Use uma extensão adequada, como .oga
            path = 'audio';
          } else {
            fileName = `audio_${currentTimeStamp}.oga`;
            path = 'audio';
          }
        } else {
         */

        fileName = `audio_${currentTimeStamp}.oga`; // Use uma extensão adequada, como .oga
        urlMidia = pathMida;
        console.log('fileName at line 781 in services/EvolutionApiService.ts:', fileName);
        console.log('pathMida at line 782 in services/EvolutionApiService.ts:', pathMida);
        //}
        // } else if (message.message.imageMessage) {
      } else if (type == 'imageMessage') {
        //const fileName = message.message.audioMessage.fileName;
        //console.log(fileName);
        fileName = `imagem_${currentTimeStamp}.jpeg`;
        urlMidia += fileName;
        console.log('urlMidia at line 774 in services/EvolutionApiService.ts:', urlMidia);

        //console.log(pdfBase64);
      } else {
        return { statuscode: 200, message: 'Mensagem sem mídia', data: [] };
      }

      const pdfBase64 = await getMediaBase64(message);
      await saveBase64ToFile(pdfBase64, pathMida, fileName);
      return {
        statuscode: 200,
        message: 'Arquivo salvo com sucesso',
        data: [{ url_midia: urlMidia, file_name: fileName }],
      };
    } catch (error) {
      return erroInterno(error, 'Erro na função donwloadMedia');
      //console.error('Erro ao processar a requisição:', error.message);
      //return { statuscode: 500, message: error.message, data: [] };
    }
  }

  static async createInstanceRabbitMQ(instance: string): Promise<IRetorno> {
    try {
      let data = JSON.stringify({
        instanceName: instance,
        token: instance,
        qrcode: true,
        rabbitmq_enabled: true,
        rabbitmq_events: [
          'QRCODE_UPDATED',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'MESSAGES_DELETE',
          'SEND_MESSAGE',
          'CONNECTION_UPDATE',
          'PRESENCE_UPDATE',
        ],
      });

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/create',
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      const response = await axios.request(config);
      //console.log('createInstanceRabbitMQ', config, response.data);

      if (response.status === 201) {
        const data = {
          nameinstance: response.data.instanceName,
          status: response.data.status,
          status_connection: Funcoes.trataStatus(response.data.status),
          code: response.data.qrcode.code,
          base64: response.data.qrcode.base64,
          count: response.data.qrcode.count,
        };

        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroApiWhats(error);
    }
  }

  static async deleteInstance(instance: string): Promise<IRetorno> {
    try {
      let config = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/delete/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      // console.log(config);

      const response = await axios.request(config);

      //console.log('response', 'status= ' + response?.status, response?.data);

      if (response.status === 200) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função deleteInstance');
    }
  }

  static async logoutInstance(instance: string): Promise<IRetorno> {
    try {
      let config = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/logout/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      const response = await axios.request(config);

      if (response.status == 200) {
        return {
          statuscode: 200,
          message: 'Sucesso',
          data: [
            {
              status: 'Desconectado',
              message: 'Instancia desconectada',
            },
          ],
        };
      } else {
        console.log('logoutInstance', response);
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função logoutInstance');
    }
  }

  static async restartInstance(instance: string): Promise<IRetorno> {
    try {
      let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/restart/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      const response = await axios.request(config);

      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função restartInstance');
    }
  }

  static async connectInstance(instance: string): Promise<IRetorno> {
    try {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/connect/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };
      const response = await axios.request(config);
      //console.log('response', 'status= ' + response?.status, response);
      if (response.status === 200) {
        const data = {
          nameinstance: instance,
          code: response.data.code,
          base64: response.data.base64,
          count: response.data.count,
        };
        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função connectInstance');
    }
  }

  static async fetchInstances(instance: string): Promise<IRetorno> {
    console.log('Fetching instances', instance);
    try {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: HOST_APIWHATS + '/instance/fetchInstances?instanceName=' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };
      //console.log(config);
      const response = await axios.request(config);
      // console.log('response at line 830:', response.data);

      if (response.status === 200) {
        let nrTelefone = response.data.instance?.owner;

        if (nrTelefone != undefined) {
          nrTelefone = response.data.instance?.owner.replace('@s.whatsapp.net', '').replace('55', '');
        }

        const data = {
          nameinstance: response.data.instance.instanceName,
          status: response.data.instance.status,
          status_connection: Funcoes.trataStatus(response.data.instance.status),
          nr_telefone: nrTelefone,
        };
        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função fetchInstances');
    }
  }
}
