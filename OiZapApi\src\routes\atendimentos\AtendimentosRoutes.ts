import { Router } from 'express';

import { AtendimentosController } from '../../controllers/atendimentos/AtendimentosController';
import authApi from '../../middleware/authApi';
import authorizationOnline from '../../middleware/authorizationOnline';
export const AtendimentosRoutes = Router();

AtendimentosRoutes.post('/atendimentos/v1', authApi, AtendimentosController.incluirAtendimentos);
AtendimentosRoutes.post('/atendimentos/stop/v1', authApi, AtendimentosController.stopAtendimentos);
AtendimentosRoutes.post('/atendimentos/novo/v1', authApi, AtendimentosController.novoAtendimento);
AtendimentosRoutes.post('/atendimentos/encerrar/v1', authApi, AtendimentosController.encerraAtendimento);
AtendimentosRoutes.post('/atendimentos/iniciar/v1', authApi, AtendimentosController.iniciarAtendimento);
AtendimentosRoutes.post('/atendimentos/transferir/v1', authApi, AtendimentosController.transferirAtendimento);
AtendimentosRoutes.post('/atendimentos/atendido/v1', authApi, AtendimentosController.atendido);
AtendimentosRoutes.put('/atendimentos/v1', authApi, AtendimentosController.alterarAtendimentos);
AtendimentosRoutes.post('/atendimentos/link/status/v1', authorizationOnline, AtendimentosController.statusAtendimentos);
AtendimentosRoutes.get('/atendimentos/v1', authApi, AtendimentosController.listarAtendimentos);
AtendimentosRoutes.get(
  '/atendimentos/listaAtendimentosHistorico/v1',
  authApi,
  AtendimentosController.listaAtendimentosHistorico,
);
AtendimentosRoutes.get(
  '/atendimentos/listarAtendimentosFluxo/v1',
  authApi,
  AtendimentosController.listarAtendimentosFluxo,
);
AtendimentosRoutes.delete('/atendimentos/v1', authApi, AtendimentosController.removerAtendimentos);
AtendimentosRoutes.get(
  '/atendimentos/lista-atendimentos-data/v1',
  authApi,
  AtendimentosController.listaAtendimentoData,
);
AtendimentosRoutes.get(
  '/atendimentos/lista-atendimentos-hora/v1',
  authApi,
  AtendimentosController.listaAtendimentoHora,
);
AtendimentosRoutes.get(
  '/atendimentos/lista-atendimentos-por-telefone/v1',
  authApi,
  AtendimentosController.listarAtendimentoPorTelefone,
);
