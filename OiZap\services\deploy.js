require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs-extra'); // fs-extra já está sendo importado

class deploy {
    async start(server) {
        let client;

        if (server == 'sandbox') {
            client = new Client({
                user: process.env.USER_DATABASE,
                host: process.env.HOST_DATABASEDEV,
                database: process.env.DATABASE_SANDBOX,
                password: process.env.PASSWORD_DATABASEDEV,
                port: process.env.PORT_DATABASEDEV,
            });
            client.connect();
            await client.query(
                `update commits set data_deploy = current_timestamp where sistema = 'OiZap' and data_deploy is null `
            );
            client.end();
        } else if (server == 'oizap') {
            //Salva na Produção
            client = new Client({
                user: process.env.USER_DATABASE,
                host: process.env.HOST_DATABASE,
                database: process.env.DATABASE,
                password: process.env.PASSWORD_DATABASE,
                port: process.env.PORT_DATABASE,
            });
            client.connect();
            await client.query(
                `update commits set data_deploy = current_timestamp where sistema = 'OiZap' and  data_deploy is null `
            );
            client.end();
        } else if (server == 'crm') {
            //Salva na Produção
            client = new Client({
                user: process.env.USER_DATABASE,
                host: process.env.HOST_DATABASE,
                database: process.env.DATABASE_CRM,
                password: process.env.PASSWORD_DATABASE,
                port: process.env.PORT_DATABASE,
            });
            client.connect();
            await client.query(
                `update commits set data_deploy = current_timestamp where sistema = 'OiZap' and  data_deploy is null `
            );
            client.end();
        }

        console.log('Commit realizado com sucesso!');
    }
}

module.exports = new deploy();
