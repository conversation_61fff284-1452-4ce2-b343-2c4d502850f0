-- Migration: add_departamento_atendimento_historico
-- Created: 2025-06-02T03:28:47.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar colunas na tabela atendimento_historicos
-- ========================================

-- Adicionar cd_departamento
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'atendimento_historicos' 
        AND column_name = 'cd_departamento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE atendimento_historicos ADD COLUMN cd_departamento INTEGER;
        COMMENT ON COLUMN atendimento_historicos.cd_departamento IS 'Indica o departamento relacionado ao atendimento';
        RAISE NOTICE 'Coluna cd_departamento adicionada à tabela atendimento_historicos';
    ELSE
        RAISE NOTICE 'Coluna cd_departamento já existe na tabela atendimento_historicos';
    END IF;
END $$;

-- Adicionar cd_atendente
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'atendimento_historicos' 
        AND column_name = 'cd_atendente'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE atendimento_historicos ADD COLUMN cd_atendente INTEGER;
        COMMENT ON COLUMN atendimento_historicos.cd_atendente IS 'Indica o atendente responsável pelo atendimento';
        RAISE NOTICE 'Coluna cd_atendente adicionada à tabela atendimento_historicos';
    ELSE
        RAISE NOTICE 'Coluna cd_atendente já existe na tabela atendimento_historicos';
    END IF;
END $$;

-- Adicionar ds_comentario
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'atendimento_historicos' 
        AND column_name = 'ds_comentario'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE atendimento_historicos ADD COLUMN ds_comentario TEXT;
        COMMENT ON COLUMN atendimento_historicos.ds_comentario IS 'Indica um comentário sobre o atendimento';
        RAISE NOTICE 'Coluna ds_comentario adicionada à tabela atendimento_historicos';
    ELSE
        RAISE NOTICE 'Coluna ds_comentario já existe na tabela atendimento_historicos';
    END IF;
END $$; 