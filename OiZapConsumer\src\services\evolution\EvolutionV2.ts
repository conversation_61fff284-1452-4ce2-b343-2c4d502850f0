import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import { downloadMediaMessage } from '@whiskeysockets/baileys';
import axios from 'axios';
import fs from 'fs';
import os from 'os';
import { EstabelecimentoInstanciasDB } from '../../data/EstabelecimentoInstanciasDB';
import {
  IDocumentWhats,
  IImageWhats,
  IListWhats,
  ILocationWhats,
  IMessageWhats,
  IProfileWhats,
  IRestartInstance,
} from '../../interfaces/IRespMessageWhats';
import { IRetorno, erroApiWhats, erroInterno, sucesso } from '../../interfaces/IRetorno';
import {
  ISendAudio,
  ISendButton,
  ISendImage,
  ISendList,
  ISendLocation,
  ISendMessage,
  ISendProfile,
} from '../../interfaces/ISendMessageWhats';
import { Funcoes } from '../Funcoes';
import Logger from '../Logger';
const logger = Logger.getLogger();

let HOST_APIWHATS: string | undefined;
let VERSAO_EVOLUTION = process.env.VERSAO_EVOLUTION;
if (process.env.AMBIENTE === 'PROD' || process.env.AMBIENTE === 'CRM') {
  if (VERSAO_EVOLUTION === 'V1') {
    HOST_APIWHATS = process.env.HOST_APIWHATS;
  } else {
    HOST_APIWHATS = process.env.HOST_APIWHATS_V2;
  }
} else {
  if (VERSAO_EVOLUTION === 'V1') {
    HOST_APIWHATS = process.env.HOST_APIWHATS_SANDBOX;
  } else {
    HOST_APIWHATS = process.env.HOST_APIWHATS_SANDBOX_V2;
  }
}

// async function saveBase64ToFile(base64Data: string, pathMida: any, fileName: string): Promise<void> {
//   const bufferData = Buffer.from(base64Data, 'base64');
//   /*let pathAudio: string | undefined;

//   if (os.platform() === 'win32') {
//     pathAudio = process.env.PATH_AUDIOSWINDOWS;
//   } else if (os.platform() === 'linux') {
//     if (process.env.AMBIENTE === 'PROD') {
//       pathAudio = process.env.PATH_AUDIOS;
//     } else {
//       pathAudio = process.env.PATH_AUDIOS_SANDBOX;
//     }
//   } else {
//     console.log('Estou em um sistema operacional diferente.');
//   }*/

//   if (pathMida) {
//     const filePath = path.join(pathMida, fileName);

//     // Extraia o diretório do caminho do arquivo completo
//     const dir = path.dirname(filePath);

//     try {
//       // Verifique e crie a pasta, se necessário
//       if (!fs.existsSync(dir)) {
//         fs.mkdirSync(dir, { recursive: true });
//         //  console.log(`Diretório criado: ${dir}`);
//       } else {
//         //console.log(`Diretório já existe: ${dir}`);
//       }

//       // Escreva o arquivo
//       fs.writeFileSync(filePath, bufferData);
//       //console.log(`Arquivo salvo em ${filePath}`);
//     } catch (error) {
//       console.error('Erro ao criar diretório ou escrever arquivo:', error);
//     }
//   }
// }

async function saveBase64ToFile(base64Data: string, pathMida: any, fileName: string): Promise<void> {
  const bufferData = Buffer.from(base64Data, 'base64');
  const uint8ArrayData = new Uint8Array(bufferData); // Conversão de Buffer para Uint8Array

  if (pathMida) {
    const filePath = path.join(pathMida, fileName);

    // Extraia o diretório do caminho do arquivo completo
    const dir = path.dirname(filePath);

    try {
      // Verifique e crie a pasta, se necessário
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        //  console.log(`Diretório criado: ${dir}`);
      } else {
        // console.log(`Diretório já existe: ${dir}`);
      }

      // Escreva o arquivo usando Uint8Array
      fs.writeFileSync(filePath, uint8ArrayData);
      // console.log(`Arquivo salvo em ${filePath}`);
    } catch (error) {
      console.error('Erro ao criar diretório ou escrever arquivo:', error);
    }
  }
}

const getMediaBase64 = async (message: any): Promise<string> => {
  try {
    const buffer = await downloadMediaMessage(message, 'buffer', {});
    const base64Data = buffer.toString('base64');
    return base64Data;
  } catch (error: any) {
    console.error('error at line 130 in evolution/EvolutionV2.ts:', error);

    throw error;
  }
};

async function convertPdfUrlToBase64(url: string): Promise<IRetorno> {
  try {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
      },
    });

    const base64 = Buffer.from(response.data, 'binary').toString('base64');
    return sucesso([{ base64: base64 }]);
  } catch (error) {
    return erroInterno(error);
  }
}

interface ReqObj {
  instance: string;
  id?: string;
  telefone?: string;
  timer?: number;
  mensagem?: string;
  nm_estabelecimento?: string;
  ds_endereco?: string;
  nr_latitude?: number;
  nr_longitude?: number;
  url_midia?: string;
  url_midiacompleta?: string;
  ds_base64?: string;
  mediatype?: string;
  fileName?: string;
  caption?: string;
  url_document?: string;
  media?: string;
}

export class EvolutionV2 {
  static async sendButton(reqObj: ISendButton): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let telefone = reqObj.telefone;
      //ROBINHO - 04/01/2025 13:05
      //Comentando a funcionalidade de incluir o 55 de telefone que não tem
      //Pois temos numeros fora do BR e atualmente a api está retornando corretamente o 55
      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }

      const data = JSON.stringify({
        number: telefone,
        delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
        presence: 'composing',
        title: reqObj.title,
        description: reqObj.description,
        footer: reqObj.footer,
        buttons: reqObj.buttons,
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/message/sendButtons/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      // console.log('config at line 226 in services/EvolutionApiService.ts:', config);
      const response = await axios.request(config);

      logger.debug('response.data at line 228 in services/EvolutionApiService.ts: ' + JSON.stringify(response.data));
      if (response.status === 201) {
        return sucesso(response.data);
        /*const resp: IListWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          title: response.data.message.listMessage.title,
          description: response.data.message.listMessage.description,
          buttonText: response.data.message.listMessage.buttonText,
          listType: response.data.message.listMessage.listType,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
        };
        return sucesso([resp]);*/
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      logger.error('Erro na função sendList:' + JSON.stringify(error.response.data));
      //return { statuscode: 500, message: 'Erro na função sendText', data: error.response.data?.response?.message };
      return erroApiWhats(error);
    }
  }
  static async sendList(reqObj: ISendList): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      //console.log('respEst at line 350 in evolution/EvolutionV2.ts:', respEst);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      // console.log('hotsApi at line 354 in evolution/EvolutionV2.ts:', hotsApi);
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let telefone = reqObj.telefone;
      //ROBINHO - 04/01/2025 13:05
      //Comentando a funcionalidade de incluir o 55 de telefone que não tem
      //Pois temos numeros fora do BR e atualmente a api está retornando corretamente o 55
      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }
      // console.log('urlApiWhats at line 357 in evolution/EvolutionV2.ts:', urlApiWhats);
      const data = JSON.stringify({
        number: telefone,
        delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
        presence: 'composing',
        title: reqObj.title,
        description: reqObj.description,
        buttonText: reqObj.buttonText,
        footerText: '', //.reqObj.footerText,
        sections: reqObj.sections,
      });
      // console.log('data at line 343 in evolution/EvolutionV2.ts:', data);

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/message/sendList/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      logger.debug('config at line 367 in evolution/EvolutionV2.ts:' + JSON.stringify(config));
      const response = await axios.request(config);
      logger.debug('response at line 368 in evolution/EvolutionV2.ts:' + JSON.stringify(response.data));

      //console.log('response.data at line 228 in services/EvolutionApiService.ts:', response.data);
      if (response.status === 201) {
        const resp: IListWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          title: response.data.message.listMessage.title,
          description: response.data.message.listMessage.description,
          buttonText: response.data.message.listMessage.buttonText,
          listType: response.data.message.listMessage.listType,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
        };
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função sendList:', error.response.data);
      //return { statuscode: 500, message: 'Erro na função sendText', data: error.response.data?.response?.message };
      return erroApiWhats(error);
    }
  }
  static async sendAudio(reqObj: ISendAudio): Promise<IRetorno> {
    try {
      let data = JSON.stringify({
        number: reqObj.telefone,
        options: {
          delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
          presence: 'recording',
          encoding: true,
        },
        audioMessage: {
          audio: reqObj.url_midia,
        },
      });

      const req = {
        query: {},
      };
      //const respEstabelecimento = await EstabelecimentoInstanciasDB.listarEstabelecimentoInstancias();
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/message/sendWhatsAppAudio/' + reqObj.instance,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //  console.log('sendDocument > reqObj', reqObj);
      //  console.log('sendDocument > data', data);
      //  console.error('sendDocument > config', config);

      const response = await axios.request(config);
      // console.error('sendDocument', response.data);

      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função sendDocument');
    }
  }
  static async createInstanceRabbitMQ(instance: string): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      let hotsApi;
      let inHabilitaProxy = false;
      let proxyHost;
      let proxyPort;
      let proxyUsername;
      let proxyPassword;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
        inHabilitaProxy = respEst.data[0].in_habilitaproxy != undefined ? respEst.data[0].in_habilitaproxy : false;
        // proxyHost = respEst.data[0].host_proxy;
        // proxyPort = respEst.data[0].porta_proxy;
        // proxyUsername = respEst.data[0].username_proxy;
        // proxyPassword = respEst.data[0].password_proxy;
        proxyHost = process.env.PROXY_URL;
        proxyPort = process.env.PROXY_PORT;
        proxyUsername = process.env.PROXY_USER_NAME;
        proxyPassword = process.env.PROXY_PASSWORD;
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;

      let data = {
        instanceName: instance,
        //token: instance,
        qrcode: true,
        integration: 'WHATSAPP-BAILEYS',
        //readMessages: process.env.READ_MESSAGES == 'true' ? true : false,
        proxyHost: '',
        proxyPort: '',
        proxyProtocol: '',
        proxyUsername: '',
        proxyPassword: '',
        groupsIgnore: true,
        rabbitmq: {
          enabled: true,
          events: [
            'QRCODE_UPDATED',
            //'MESSAGES_SET',
            'MESSAGES_UPSERT',
            'MESSAGES_UPDATE',
            'MESSAGES_DELETE',
            'MESSAGES_EDITED',
            'CONTACTS_UPSERT',
            'CONTACTS_UPDATE',
            'SEND_MESSAGE',
            'CONNECTION_UPDATE',
            'PRESENCE_UPDATE',
          ],
        },
      };

      if (inHabilitaProxy) {
        data.proxyHost = proxyHost || '';
        data.proxyPort = proxyPort || '';
        data.proxyProtocol = 'http';
        data.proxyUsername = proxyUsername || '';
        data.proxyPassword = proxyPassword || '';
      }
      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/create',
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: JSON.stringify(data),
      };

      // console.log('config at line 423 in evolution/EvolutionV2.ts:', config);
      logger.debug('createInstanceRabbitMQ > config:' + JSON.stringify(config));
      const response = await axios.request(config);
      logger.debug('createInstanceRabbitMQ > response:', response.data);
      // console.log('response.data at line 443 in evolution/EvolutionV2.ts:', response.data);

      if (response.status === 201) {
        //logger.debug('createInstanceRabbitMQ > response:' + JSON.stringify(response.data));
        const data = {
          nameinstance: response.data.instance.instanceName,
          status: response.data.instance.status,
          status_connection: Funcoes.trataStatus(response.data.instance.status),
          code: response.data.qrcode.code,
          base64: response.data.qrcode.base64,
          count: response.data.qrcode.count,
        };

        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      // console.log('error at line 415 in evolution/EvolutionV2.ts:', error);
      logger.error('createInstanceRabbitMQ > createInstanceRabbitMQ: ' + JSON.stringify(error));
      return erroApiWhats(error);
    }
  }
  static async connectionState(instance: string): Promise<IRetorno> {
    // console.log('connectionState', instance);
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      //console.log('respEst at line 520 in evolution/EvolutionV2.ts:', respEst);
      let hotsApi;

      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }

      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      console.log('🚀 ~ EvolutionV2.ts:514 ~ connectionState ~ urlApiWhats:', urlApiWhats);
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/connectionState/' + instance,
        headers: {
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      logger.debug('connectionState > config:' + JSON.stringify(config));
      const response = await axios.request(config);
      logger.debug('connectionState > response.data:' + JSON.stringify(response.data));

      if (response.status === 200) {
        const data = {
          nameinstance: response.data.instance.instanceName,
          status: response.data.instance.state,
          status_connection: Funcoes.trataStatus(response.data.instance.state),
        };
        // console.log('data at line 522:', response.data.instance.state, data);
        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        //throw new Error('Erro na requisição da API.');
        return erroInterno(response.data, 'Erro na função connectionState');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função connectionState');
      //console.error('Erro na função connectionState:', message ? message : error.response.data);
    }
  }
  static async deleteInstance(instance: string): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let config = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/delete/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      // console.log(config);

      const response = await axios.request(config);

      //console.log('response', 'status= ' + response?.status, response?.data);

      if (response.status === 200) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função deleteInstance');
    }
  }
  static async logoutInstance(instance: string): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let config = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/logout/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      const response = await axios.request(config);

      if (response.status == 200) {
        return {
          statuscode: 200,
          message: 'Sucesso',
          data: [
            {
              status: 'Desconectado',
              message: 'Instancia desconectada',
            },
          ],
        };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função logoutInstance');
    }
  }
  static async connectInstance(instance: string): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/connect/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };
      logger.debug('connectInstance > config:' + JSON.stringify(config));
      //console.log('config at line 597 in evolution/EvolutionV2.ts:', config);
      const response = await axios.request(config);
      //console.log('response at line 598 in evolution/EvolutionV2.ts:', response.data);
      logger.debug('connectInstance > response.data:' + JSON.stringify(response.data));
      //console.log('response', 'status= ' + response?.status, response.data);
      if (response.status === 200) {
        const data = {
          nameinstance: instance,
          code: response.data.code,
          base64: response.data.base64,
          count: response.data.count,
        };
        return { statuscode: 200, message: 'Sucesso', data: data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função connectInstance');
    }
  }
  static async fetchInstances(instance: string): Promise<IRetorno> {
    //console.log('Fetching instances', instance);
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      //console.log('🚀 ~ EvolutionV2.ts:680 ~ fetchInstances ~ respEst:', respEst);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      // console.log('urlApiWhats at line 675 in evolution/EvolutionV2.ts:', urlApiWhats);
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/fetchInstances?instanceName=' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      // console.log('config at line 686 in evolution/EvolutionV2.ts:', config);
      logger.debug('config at line 686 in evolution/EvolutionV2.ts: ' + JSON.stringify(config));
      const response = await axios.request(config);
      logger.debug('response at line 687 in evolution/EvolutionV2.ts: ' + JSON.stringify(response.data));
      // console.log('response.data at line 688 in evolution/EvolutionV2.ts:', response.data);

      if (response.status === 200) {
        const dataInstance = response.data[0];
        if (!dataInstance) {
          return { statuscode: 404, message: 'Dados não encontrado!', data: [] };
        }

        let nrTelefone = dataInstance?.ownerJid;

        if (nrTelefone != undefined) {
          nrTelefone = nrTelefone.replace('@s.whatsapp.net', '').replace('55', '');
        }

        const dataRet = {
          nameinstance: dataInstance?.name,
          status: dataInstance?.connectionStatus,
          status_connection: Funcoes.trataStatus(dataInstance?.connectionStatus),
          nr_telefone: nrTelefone,
          nm_perfil: dataInstance?.profileName,
          url_foto: dataInstance?.profilePicUrl,
          qt_messages: dataInstance?._count?.Message,
          qt_contact: dataInstance?._count?.Contact,
          qt_chat: dataInstance?._count?.Chat,
        };
        //console.log('data at line 726 in evolution/EvolutionV2.ts:', dataRet);
        return sucesso(dataRet); // { statuscode: 200, message: 'Sucesso', data: dataRet };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      if (error?.status == 404 || error.response?.status == 404) {
        return { statuscode: 404, message: 'Dados não encontrado!', data: [] };
      }
      console.log('error at line 671 in evolution/EvolutionV2.ts:', error);
      return erroInterno(error, 'Erro na função fetchInstances');
    }
  }
  static async restartInstance(instance: string): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/instance/restart/' + instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
      };

      const response = await axios.request(config);
      //console.log('response at line 692 in evolution/EvolutionV2.ts:', response);

      if (response.status === 200) {
        const resp: IRestartInstance = {
          instance: response.data.instance.instanceName,
          status: response.data.instance.state,
          status_connection: Funcoes.trataStatus(response.data.instance.state),
        };
        //console.log('resp at line 700 in evolution/EvolutionV2.ts:', resp);
        return sucesso([resp]);
        //  return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função restartInstance');
    }
  }
  static async setProxy(reqObj: any): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;

      let data = {
        enabled: reqObj.in_habilitaproxy,
        host: reqObj.host_proxy,
        port: reqObj.porta_proxy,
        protocol: 'http',
        username: reqObj.username_proxy,
        password: reqObj.password_proxy,
      };

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/proxy/set/' + reqObj.instance,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: JSON.stringify(data),
      };

      const response = await axios.request(config);

      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      return erroInterno(error, 'Erro na função setProxy');
    }
  }
  static async fetchProfilePictureUrl(reqObj: ReqObj): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      const data = JSON.stringify({
        number: reqObj.telefone,
      });

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/chat/fetchProfilePictureUrl/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      const response = await axios.request(config);

      // console.log(' response.data at line 366 in services/EvolutionApiService.ts:', response.data);
      if (response.status === 200) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      console.error('Erro na função fetchProfilePictureUrl:', error.response.data);
      return { statuscode: 500, message: 'Erro na função fetchProfilePictureUrl', data: error.response.data };
    }
  }
  static async fetchProfile(reqObj: ISendProfile): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      const data = JSON.stringify({
        number: reqObj.telefone,
      });
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/chat/fetchProfile/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      const response = await axios.request(config);
      logger.debug('response at line 798 in evolution/EvolutionV2.ts:' + JSON.stringify(response.data));
      // console.log('response.data at line 396 in services/EvolutionApiService.ts:', response.status, response.data);

      if (response.status === 200) {
        // let base64;
        // const respPicture = await convertPdfUrlToBase64(response.data.picture);
        // if (respPicture.statuscode == 200) {
        //   base64 = respPicture.data[0].base64;
        // }

        const resp: IProfileWhats = {
          telefone: reqObj.telefone,
          name: response.data.name,
          numberExists: response.data.numberExists,
          picture: response.data.picture,
          pictureBase64: '', //base64,
          status: response.data.status,
          isBusiness: response.data.isBusiness,
        };
        return sucesso([resp]);

        // return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      //console.error('Erro na função fetchProfile:', error.response.data);
      return { statuscode: 500, message: 'Erro na função fetchProfile', data: error.response.data };
    }
  }
  static async sendText(reqObj: ISendMessage): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      //logger.debug('urlApiWhats at line 785 in evolution/EvolutionV2.ts: ' + urlApiWhats);

      let telefone = reqObj.telefone;

      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }
      const data = JSON.stringify({
        number: telefone,
        delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
        presence: 'composing',
        linkPreview: reqObj.linkpreview,
        text: reqObj.mensagem,
      });
      //logger.debug('data at line 844 in evolution/EvolutionV2.ts: ' + data);

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/message/sendText/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };
      logger.debug('🚀 ~ EvolutionV2.ts:984 ~ sendText ~ config:' + JSON.stringify(config));

      // console.log('sendText > config:', config);

      const response = await axios.request(config);
      //console.log('🚀 ~ EvolutionV2.ts:882 ~ sendText ~ response:', response);
      // logger.info('EvolutionV2 > sendText > response :' + JSON.stringify(response.data));

      if (response.status === 201) {
        const resp: IMessageWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
        };
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      logger.error('Erro na função sendText: ' + JSON.stringify(error.response.data));
      //return { statuscode: 500, message: 'Erro na função sendText', data: error.response.data?.response?.message };
      return erroApiWhats(error);
    }
  }
  static async sendImage(reqObj: ISendImage): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let telefone = reqObj.telefone;
      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }
      let dataImage: any = {
        number: telefone,
        mediatype: 'image',
        mimetype: 'image/png',
        caption: reqObj.mensagem,
        delay: reqObj.timer,
      };

      if (reqObj?.ds_base64 != undefined) {
        dataImage.media = reqObj.ds_base64;
      } else {
        if (reqObj.url_midiacompleta != undefined) {
          dataImage.media = reqObj.url_midiacompleta;
        } else {
          dataImage.media = reqObj.url_midia;
        }
      }
      const data = JSON.stringify(dataImage);
      // console.log('data at line 1014 in evolution/EvolutionV2.ts:', data);

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/message/sendMedia/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          apikey: process.env.GLOBAL_APIKEY_WHATS as string,
        },
        data,
      };

      //console.log('config at line 148 in evolution/EvolutionV2.ts:', config);
      const response = await axios.request(config);
      //console.log('response at line 144 in evolution/EvolutionV2.ts:', response);
      /*
      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
*/
      if (response.status === 201) {
        // console.log('response.data at line 268 in services/EvolutionApiService.ts:', response.data);
        const resp: IImageWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          url_midia: reqObj.url_midia || '',
          url_midiacompleta: reqObj.url_midiacompleta || '',
          ds_base64: reqObj?.ds_base64 || '',
          file_length: response.data.message.imageMessage.fileLength,
          file_sha256: response.data.message.imageMessage.fileSha256,
          media_key: response.data.message.imageMessage.mediaKey,
          nr_height: response.data.message.imageMessage.height,
          nr_width: response.data.message.imageMessage.width,
          mimetype: response.data.message.imageMessage.mimetype,
        };
        //console.log('resp at line 348 in services/EvolutionApiService.ts:', resp);
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error: any) {
      //console.error('Erro na função sendImage:', error.response.data);
      //return { statuscode: 500, message: 'Erro na função sendImage', data: error.response.data };
      return erroApiWhats(error);
      //return erroInterno(error, 'Erro na função sendImage');
    }
  }
  static async sendLocation(reqObj: ISendLocation): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
      let latitude: number = Number(reqObj.latitude);
      let longitude: number = Number(reqObj.longitude);
      let telefone = reqObj.telefone;
      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }
      let data = JSON.stringify({
        number: telefone,
        delay: reqObj.timer == undefined ? 1200 : reqObj.timer == 0 ? 1200 : reqObj.timer,
        presence: 'composing',
        name: reqObj.estabelecimento,
        address: reqObj.address,
        latitude: latitude,
        longitude: longitude,
      });
      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${urlApiWhats}/message/sendLocation/${reqObj.instance}`,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //console.log('sendLocation > reqObj', reqObj);
      // console.log('sendLocation > data', data);
      // console.error('sendLocation > config', config);

      const response = await axios.request(config);
      //console.log('sendLocation', response.data);

      if (response.status === 201) {
        const resp: ILocationWhats = {
          telefone: reqObj.telefone,
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          estabelecimento: reqObj.estabelecimento,
          address: reqObj.address,
          latitude: reqObj.latitude,
          longitude: reqObj.longitude,
        };

        return sucesso([resp]);

        //return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }
    } catch (error) {
      //console.error('Erro na função sendLocation:', error.response.data);
      return erroInterno(error, 'Erro na função sendLocation');
      //return { statuscode: 500, message: 'Erro na função sendLocation', data: error.response.data };
    }
  }
  // static async downloadAudio(id: any, outputPath: any, instance: any) {
  //   console.log('downloadAudio - FAZENDO O DOWNLOAD ID ' + id, outputPath);
  //   try {
  //     const data = JSON.stringify({
  //       message: {
  //         key: {
  //           id: id,
  //         },
  //       },
  //       convertToMp4: false,
  //     });

  //     console.log('downloadAudio - data', data);

  //     const config = {
  //       method: 'post',
  //       maxBodyLength: Infinity,
  //       url: `${HOST_APIWHATS}/chat/getBase64FromMediaMessage/${instance}`,
  //       headers: {
  //         'Content-Type': 'application/json',
  //         apikey: `${process.env.APIKEY_WHATS}`,
  //       },
  //       data: data,
  //     };

  //     console.log('downloadAudio - config', config);

  //     const response = await axios.request(config);
  //     console.log('downloadAudio - response', response);
  //     const audio64 = response.data.base64;
  //     console.log('downloadAudio - audio64', audio64);

  //     // Use await para esperar a escrita do arquivo ser concluída
  //     await fs.promises.writeFile(outputPath, Buffer.from(audio64, 'base64'));

  //     // Agora você pode prosseguir com o próximo passo após o salvamento do arquivo
  //     console.log('Download e salvamento do arquivo concluídos com sucesso.');
  //     return { statuscode: 200, message: 'OK', data: [] };
  //   } catch (error) {
  //     //console.log('Erro ao baixar e salvar o arquivo de áudio:', error.response.data)
  //     return { statuscode: 500, message: error, data: [] };
  //   }
  // }
  static async donwloadMedia(reqObj: any, type: any): Promise<any> {
    const data = reqObj;
    //TEMOS QUE TIRAR DE DENTRO DO DATA.DATA

    try {
      const message = data.data;
      let fileName;
      const instance = reqObj.instance;

      const currentTimeStamp = new Date().getTime(); // Obtém um carimbo de data/hora exclusivo

      let pathMida;
      let urlMidia;

      if (os.platform() === 'win32') {
        pathMida = process.env.PATH_MIDIA_WINDOWS + instance + '\\' + type + '\\';
        urlMidia = process.env.URL_MIDIA_WINDOWS + instance + '\\' + type + '\\';
      } else if (os.platform() === 'linux') {
        pathMida =
          process.env.AMBIENTE == 'SANDBOX'
            ? process.env.PATH_MIDIA_SANDBOX + instance + '/' + type + '/'
            : process.env.PATH_MIDIA + instance + '/' + type + '/';
        urlMidia =
          process.env.AMBIENTE == 'SANDBOX'
            ? process.env.URL_MIDIA_SANDBOX + instance + '/' + type + '/'
            : process.env.URL_MIDIA + instance + '/' + type + '/';
      } else {
      }

      //if ((message && message.message.audioMessage) || message.message?.documentMessage) {
      if (type == 'audioMessage') {
        /* if (message.message?.documentMessage) {
          if (message.message?.documentMessage.mimetype.includes('audio')) {
            fileName = `${currentTimeStamp}_${message.message?.documentMessage.fileSha256}.oga`; // Use uma extensão adequada, como .oga
            path = 'audio';
          } else {
            fileName = `audio_${currentTimeStamp}.oga`;
            path = 'audio';
          }
        } else {
         */

        fileName = `audio_${currentTimeStamp}.oga`; // Use uma extensão adequada, como .oga
        urlMidia = pathMida;
        //}
        // } else if (message.message.imageMessage) {
      } else if (type == 'imageMessage') {
        //const fileName = message.message.audioMessage.fileName;
        //console.log(fileName);
        fileName = `imagem_${currentTimeStamp}.jpeg`;
        urlMidia += fileName;
        // console.log('urlMidia at line 774 in services/EvolutionApiService.ts:', urlMidia);

        //console.log(pdfBase64);
      } else {
        return { statuscode: 200, message: 'Mensagem sem mídia', data: [] };
      }

      const pdfBase64 = await getMediaBase64(message);
      await saveBase64ToFile(pdfBase64, pathMida, fileName);
      return {
        statuscode: 200,
        message: 'Arquivo salvo com sucesso',
        data: [{ url_midia: urlMidia, file_name: fileName }],
      };
    } catch (error) {
      return erroInterno(error, 'Erro na função donwloadMedia');
      //console.error('Erro ao processar a requisição:', error.message);
      //return { statuscode: 500, message: error.message, data: [] };
    }
  }
  static async sendDocument(reqObj: ReqObj): Promise<IRetorno> {
    try {
      const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
      let hotsApi;
      if (respEst.statuscode == 200) {
        if (
          respEst.data[0].ds_hostapi != undefined &&
          respEst.data[0].ds_hostapi != null &&
          respEst.data[0].ds_hostapi != '' &&
          respEst.data[0].in_apidedicada == true
        ) {
          hotsApi = respEst.data[0].ds_hostapi;
        }
      }
      const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;

      let telefone = reqObj.telefone || '';
      // let codigo = telefone.substr(0, 2);
      // if (codigo != '55') {
      //   logger.error('telefone ' + telefone + ' NÃO possui o codigo 55 ');
      //   telefone = '55' + telefone;
      // }

      let dataDocument: any = {
        number: telefone,
        mediatype: 'document',
        mimetype: 'application/pdf',
        //caption: reqObj.mensagem,
        delay: reqObj.timer,
        fileName: reqObj.fileName,
      };

      let urlPDF = '';
      if (reqObj?.ds_base64 != undefined) {
        dataDocument.media = reqObj.ds_base64;
      } else {
        if (reqObj.url_midiacompleta != undefined) {
          urlPDF = reqObj.url_midiacompleta;
        } else {
          urlPDF = reqObj.url_midia || '';
        }
        const respPDF = await convertPdfUrlToBase64(urlPDF);
        if (respPDF.statuscode != 200) {
          return respPDF;
        } else {
          dataDocument.media = respPDF.data[0].base64;
        }
      }
      const data = JSON.stringify(dataDocument);

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: urlApiWhats + '/message/sendMedia/' + reqObj.instance,
        headers: {
          'Content-Type': 'application/json',
          //apikey: instance,
          apikey: process.env.GLOBAL_APIKEY_WHATS,
        },
        data: data,
      };

      //  console.log('sendDocument > reqObj', reqObj);
      //console.log('sendDocument > data', data);
      //console.error('sendDocument > config', config);

      const response = await axios.request(config);
      // console.error('sendDocument', response.data);

      if (response.status === 201) {
        //console.log('response.data at line 268 in services/EvolutionApiService.ts:', response.data);
        const resp: IDocumentWhats = {
          telefone: reqObj.telefone || '',
          instance: reqObj.instance,
          message_id: response.data.key.id,
          remote_jid: response.data.key.remoteJid,
          timestamp: Funcoes.dateTime(response.data.messageTimestamp, 'datetime'),
          date: Funcoes.dateTime(response.data.messageTimestamp, 'date'),
          time: Funcoes.dateTime(response.data.messageTimestamp, 'time'),
          status: Funcoes.trataStatus(response.data.status),
          url_midia: reqObj.url_midia || '',
          url_midiacompleta: reqObj.url_midiacompleta || '',
          ds_base64: dataDocument.media,
          file_length: response.data.message.documentMessage.fileLength,
          file_sha256: response.data.message.documentMessage.fileSha256,
          file_name: response.data.message.documentMessage.fileName,
          media_key: response.data.message.documentMessage.mediaKey,
          nr_height: response.data.message.documentMessage.height,
          nr_width: response.data.message.documentMessage.width,
          mimetype: response.data.message.documentMessage.mimetype,
        };

        //console.log('resp at line 348 in services/EvolutionApiService.ts:', resp);
        return sucesso([resp]);
      } else {
        throw new Error('Erro na requisição da API.');
      }
      /*
      if (response.status === 201) {
        return { statuscode: 200, message: 'Sucesso', data: response.data };
      } else {
        throw new Error('Erro na requisição da API.');
      }*/
    } catch (error) {
      return erroApiWhats(error);
      //return erroInterno(error, 'Erro na função sendDocument');
      //return { statuscode: 500, message: 'Erro na função sendDocument', data: error.response.data };
    }
  }
  static async findContacts(reqObj: ReqObj): Promise<IRetorno> {
    const respEst = await EstabelecimentoInstanciasDB.buscaEstabelecimentoInstancias(reqObj.instance, undefined);
    let hotsApi;
    if (respEst.statuscode == 200) {
      if (
        respEst.data[0].ds_hostapi != undefined &&
        respEst.data[0].ds_hostapi != null &&
        respEst.data[0].ds_hostapi != '' &&
        respEst.data[0].in_apidedicada == true
      ) {
        hotsApi = respEst.data[0].ds_hostapi;
      }
    }
    const urlApiWhats = hotsApi ? hotsApi : HOST_APIWHATS;
    const data = JSON.stringify({
      where: reqObj.telefone ? { id: reqObj.telefone } : {},
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${urlApiWhats}/chat/findContacts/${reqObj.instance}`,
      headers: {
        'Content-Type': 'application/json',
        apikey: process.env.GLOBAL_APIKEY_WHATS as string,
      },
      data: undefined,
    };
    if (reqObj.telefone) {
      config.data = data;
    }

    const response = await axios.request(config);

    // console.log(' response.data at line 366 in services/EvolutionApiService.ts:', response.data);
    if (response.status === 200) {
      return { statuscode: 200, message: 'Sucesso', data: response.data };
    } else {
      throw new Error('Erro na requisição da API.');
    }
  }
  catch(error: any) {
    console.error('Erro na função findContacts:', error.response.data);
    return { statuscode: 500, message: 'Erro na função findContacts', data: error.response.data };
  }
}
