import { Request, Response, NextFunction } from 'express';
import { BAD_REQUEST, parametrosInvalidos } from '../interfaces/IRetorno';

export const RegrasGlobais = (req: Request, res: Response, next: NextFunction) => {
  console.log('🔍 RegrasGlobais - Rota acessada:', req.method, req.path);
  
  const errors: string[] = [];
  /*const { SISTEMA } = req.query;
  if (!SISTEMA) {
    errors.push("parametro SISTEMA não pode ser nulo");
  }
  */

  if (errors.length > 0) {
    console.log('❌ RegrasGlobais - Erros encontrados:', errors);
    return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
  } else {
    console.log('✅ RegrasGlobais - Passando para próxima função');
    next();
  }
};
