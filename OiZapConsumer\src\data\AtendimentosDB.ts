require('dotenv').config();
import { IRetorno, erroInterno } from '../interfaces/IRetorno';
import { Funcoes } from '../services/Funcoes';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';
const logger = Logger.getLogger();
export class AtendimentosDB {
  static async getStatusAtendimento(req: any): Promise<IRetorno> {
    try {
      let sql = `select a.tp_status from atendimentos a 
      where a.cd_estabelecimento = ${req.cd_estabelecimento} and a.cd_atendimento= ${req.cd_atendimento}`;

      // console.log('sql at line 16 in data/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarAtendimentos(req: any): Promise<IRetorno> {
    try {
      let telefone = req.telefone;
      //ROBINHO - 04/01/2025 13:05
      //Comentando a funcionalidade de incluir o 55 de telefone que não tem
      //Pois temos numeros fora do BR e atualmente a api está retornando corretamente o 55
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      if (telefone.startsWith('55')) {
        telefone = telefone.slice(2);
      } else {
        telefone = telefone;
      }

      let oitoUltimosDigitos = telefone.slice(-8);

      //sql += `and (nr_telefonezap  = '${req.query.nr_telefonezap}' or nr_telefonezap  = '${telefoneSemDDD}')`;

      // let sql = `select a.*,p.tp_situacao tp_situacao_pedido from atendimentos a
      // left join pedidos p on p.cd_atendimento = a.cd_atendimento and p.cd_estabelecimento = a.cd_estabelecimento
      // where a.cd_estabelecimento = ${req.cd_estabelecimento} and (a.nr_telefone= '${telefone}' or a.nr_telefone like '%${oitoUltimosDigitos}')
      // and (a.tp_situacao not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
      //      and a.tp_status not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
      //      or a.tp_situacao is null) `;

      /**
       *  -- e.hr_tempo_novo_atendimento, COALESCE(a.dt_cadastro, a.dt_alteracao) AS dt_ultimostatus,
    --COALESCE(a.dt_cadastro, a.dt_alteracao) + CAST(e.hr_tempo_novo_atendimento || ' hours' AS INTERVAL) AS dt_limite,
    --NOW() AS data_hora_atual,
    -- Retorna TRUE se já pode iniciar novo atendimento (horário atual passou do limite)
    --CURRENT_TIMESTAMP > COALESCE(a.dt_cadastro, a.dt_alteracao) + CAST(e.hr_tempo_novo_atendimento || ' hours' AS INTERVAL) AS pode_iniciar_novo_atendimento,
  
       */
      let sql = `SELECT a.*, p.tp_situacao AS tp_situacao_pedido,d.ds_departamento,au.ds_nome nm_atendente
FROM atendimentos a 
LEFT JOIN pedidos p ON p.cd_atendimento = a.cd_atendimento AND p.cd_estabelecimento = a.cd_estabelecimento
left join departamentos d on d.cd_departamento = a.cd_departamento and d.cd_estabelecimento = a.cd_estabelecimento
left join adm_usuarios au on au.cd_usuario = a.cd_atendente 
INNER JOIN parametros e ON e.cd_estabelecimento = a.cd_estabelecimento
WHERE a.cd_estabelecimento = ${req.cd_estabelecimento} AND  (a.nr_telefone= '${telefone}' or a.nr_telefone like '%${oitoUltimosDigitos}') `;
      if (req.instance) {
        sql += ` AND a.instance = '${req.instance}'`;
      }
      sql += `
  AND (a.tp_situacao IN ('Pedido Cancelado', 'Pedido Entregue', 'Atendimento Finalizado', 'Atendimento Cancelado')
  AND a.tp_status IN ('Em Pedido','Pedido Cancelado', 'Pedido Entregue', 'Atendimento Finalizado', 'Atendimento Cancelado')
  and (CURRENT_TIMESTAMP > COALESCE(a.dt_cadastro, a.dt_alteracao) + CAST(e.hr_tempo_novo_atendimento || ' hours' AS INTERVAL)) = false
       or  (a.tp_situacao not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
           and a.tp_status not in ('Pedido Cancelado','Pedido Entregue','Atendimento Finalizado','Atendimento Cancelado')
           or a.tp_situacao is null)) ORDER BY a.cd_atendimento DESC LIMIT 1`;

      // console.log('🚀 ~ AtendimentosDB.ts:72 ~ listarAtendimentos ~ sql:', sql);
      const result = await new PostgreSQLServices().query(sql);
      // console.log('🚀 ~ AtendimentosDB.ts:73 ~ listarAtendimentos ~ result:', result);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async atualizaStatusAtendimento(req: any): Promise<IRetorno> {
    // console.log('req at line 25 in data/AtendimentosDB.ts:', req);
    try {
      let status: string = req.tp_status_atendimento;
      let etapa: string = req.tp_status_atendimento;
      const dsComentario = req.ds_comentario || '';

      if (status == 'Inicio') {
        status = 'Em Atendimento';
        etapa = 'Inicio';
      } else if (status == 'Endereço') {
        status = 'Em Atendimento';
        etapa = 'Endereço';
      }

      let sql = `update atendimentos set tp_etapachat = '${etapa}',ds_comentario = '${dsComentario}',dt_alteracao=current_timestamp `;
      if (req.in_finalizaatendimento) {
        sql += `, tp_status ='${status}' `;
      }
      if (req.ds_evento == 'INTEGRACAO') {
        sql += `, tp_status = '${status}' `;
      }
      if (req.tp_status_atendimento == 'Falar com Atendente') {
        sql += `, in_stop_bot=true,dt_start_stop_bot=current_timestamp`;
      }
      sql += ` where cd_atendimento = ${req.cd_atendimento} returning * `;

      //console.log('sql at line 50 in data/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      const dados = {
        cd_pedido: req.cd_pedido,
        cd_atendimento: req.cd_atendimento,
        cd_estabelecimento: req.cd_estabelecimento,
        tp_status: etapa,
        ds_comentario: dsComentario,
      };

      await this.incluiHistoricoAtendimento(dados);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async incluiHistoricoAtendimento(req: any): Promise<IRetorno> {
    try {
      let sql = `insert into atendimento_historicos (cd_atendimento,cd_estabelecimento,tp_status`;

      if (req.cd_pedido != undefined) {
        sql += `,cd_pedido`;
      }

      if (req.ds_comentario != undefined) {
        sql += `,ds_comentario`;
      }

      sql += `) values (${req.cd_atendimento},${req.cd_estabelecimento},'${req.tp_status}'`;

      if (req.cd_pedido != undefined) {
        sql += `,${req.cd_pedido}`;
      }

      if (req.ds_comentario != undefined) {
        sql += `,'${req.ds_comentario}'`;
      }

      sql += `)`;

      return await new PostgreSQLServices().query(sql);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async incluirAtendimento(req: any): Promise<IRetorno> {
    try {
      const hash: String = Funcoes.gerarHashUnica(req.telefone, 20);
      let telefone = req.telefone;
      // if (!telefone?.startsWith('55')) {
      //   telefone = '55' + telefone;
      // }

      let sql = ` INSERT INTO atendimentos (cd_estabelecimento,instance,nr_telefone,ds_hash,tp_status,tp_situacao,tp_etapachat
        ,ds_contato,ds_foto,dt_alteracao`;
      if (req.cd_cliente != undefined) {
        sql += `,cd_cliente`;
      }
      sql += `) 
VALUES (${req.cd_estabelecimento},'${req.instance}', '${telefone}', '${hash}','${req.tp_status}','Em Atendimento','Inicio'
,'${req.ds_contato}','${req.ds_foto}',current_timestamp`;

      //,profile_picture_base64,'${req.profile_picture_base64}'

      if (req.cd_cliente != undefined) {
        sql += `,${req.cd_cliente}`;
      }
      sql += `) returning *;`;

      logger.debug('incluirAtendimento>insert>atendimentos ' + sql);

      //    console.log('sql at line 111 in data/AtendimentosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      //  console.log('result at line 112 in data/AtendimentosDB.ts:', result);
      logger.debug('incluirAtendimento>result ' + JSON.stringify(result));

      if (result.statuscode == 200) {
        sql = ` update messages set cd_atendimento = '${result.data[0].cd_atendimento}'
              where cd_estabelecimento=${req.cd_estabelecimento} and telefone='${telefone}'and message_id='${req.message_id}'`;
        logger.debug('incluirAtendimento>uppdate>messages ' + sql);
        await new PostgreSQLServices().query(sql);
      }

      const dados = {
        cd_atendimento: result.data[0].cd_atendimento,
        cd_estabelecimento: req.cd_estabelecimento,
        tp_status: 'Em Atendimento',
      };

      await this.incluiHistoricoAtendimento(dados);

      return result;
    } catch (error: any) {
      logger.error('incluirAtendimento ' + JSON.stringify(error));
      return erroInterno(error);
    }
  }

  static async alterarAtendimentos(req: any): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['atendimentos'],
        chaves: { cd_atendimento: req.cd_atendimento },
        dados: req,
        retorno: '*',
      };

      console.log('🚀 ~ AtendimentosDB.ts:219 ~ alterarAtendimentos ~ opDb:', opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async consultaAtendimento(req: any): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'select',
        tabelas: ['atendimentos'],
        chaves: { cd_atendimento: req.cd_atendimento },
        dados: req,
        retorno: '*',
      };

      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
