<template>
  <div>
    <ShowLoading ref="loading" />
    <ShowNotifications ref="showNotifications" />
    <div class="intro-y flex items-center mt-2">
      <h2 class="text-lg font-medium mr-auto">Cadastro de Tela</h2>
      <button @click="router.push({ name: 'listaTelas' })" class="btn btn-primary-soft col-span-4 mt-2">Voltar</button>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-3">
      <div class="intro-y col-span-12">
        <PreviewComponent class="intro-y box">
          <div class="p-5">
            <form class="validate-form" @submit.prevent="save">
              <div class="grid grid-cols-3 gap-2 mt-2">
                <div class="form-inline col-span-3">
                  <label class="forceLeft form-label sm:w-20 self-center">Nome da Tela:</label>
                  <input v-model.trim="formData.nm_tela" type="text" class="form-control col-span-2" required />
                </div>
                <div class="form-inline col-span-3 mt-2">
                  <label class="forceLeft form-label sm:w-20 self-center">Rota (path):</label>
                  <input v-model.trim="formData.ds_rota" type="text" class="form-control col-span-2" required />
                </div>
              </div>
              <button type="submit" class="btn btn-primary mt-5">Salvar</button>
            </form>
          </div>
        </PreviewComponent>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import ShowNotifications from '@/components/show-notifications/Main.vue'
import ShowLoading from '@/components/show-loading/Main.vue'
import api from '@/utils/api'
const router = useRouter()
const showNotifications = ref()
const loading = ref()
const formData = ref({ nm_tela: '', ds_rota: '' })

async function save() {
  loading.value.show()
  try {
    await api.post('/telas', formData.value)
    showNotifications.value.showSuccessNotification('Tela cadastrada!')
    router.push({ name: 'listaTelas' })
  } catch (e) {
    showNotifications.value.showErrorNotification('Erro ao salvar tela')
  }
  loading.value.hide()
}
</script> 