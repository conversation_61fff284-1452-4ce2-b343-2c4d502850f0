import { IRetorno, erroInterno, generico } from '../interfaces/IRetorno';

import { ClientesDB } from '../data/ClientesDB';
import { IMessage } from '../interfaces/IMessage';

import dotenv, { config } from 'dotenv';
import path from 'path';
import { AtendimentosDB } from '../data/AtendimentosDB';
import { ContactsDB } from '../data/ContactsDB';
import { InstanceDB } from '../data/InstanceDB';
import MessagesDB from '../data/MessagesDB';
import { AudioServices } from '../services/AudioServices';
import { EvolutionApiService } from '../services/evolution/EvolutionApiService';
import { Funcoes } from '../services/Funcoes';
import Logger from '../services/Logger';
const logger = Logger.getLogger();
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

interface ReqObj {
  data: any; // Defina uma interface adequada para a estrutura da mensagem, se disponível
  instance: string;
  event: string;
}

export default class MessagesRabbitMQ {
  private data: ReqObj;

  constructor(data: ReqObj) {
    this.data = data;
  }

  public async messageUpSert(): Promise<IRetorno> {
    let reqObj = this.data;

    try {
      let mensagem: string | undefined;
      let mimetype: string | undefined;
      let filesha256: string | undefined;
      let file_length: number | undefined;
      let nr_height: number | undefined;
      let nr_width: number | undefined;
      let media_key: string | undefined;
      let jpeg_thumbnail: string | undefined;
      //let ds_base64: string | undefined;
      let file_name: string | undefined;
      let url_midia: string | undefined;
      let url_midiacompleta: string | undefined;
      let url_midia_quotedcompleta: string | undefined;
      let message_id_quoted: string | undefined;
      let quotedmessage: string | undefined;
      let mimetype_quoted: string | undefined;
      let filesha256_quoted: string | undefined;
      let file_length_quoted: number | undefined;
      let nr_height_quoted: number | undefined;
      let nr_width_quoted: number | undefined;
      let media_key_quoted: string | undefined;
      let jpeg_thumbnail_quoted: string | undefined;
      //let ds_base64_quoted: string | undefined;
      let file_name_quoted: string | undefined;
      let url_midia_quoted: string | undefined;
      let type_message_quoted: string | undefined;

      let URL_ARQUIVOS: string = '';
      if (process.env.AMBIENTE == 'PROD') {
        URL_ARQUIVOS = process.env.URL_ARQUIVOS || '';
      } else if (process.env.AMBIENTE == 'CRM') {
        URL_ARQUIVOS = process.env.URL_ARQUIVOS || '';
      } else {
        URL_ARQUIVOS = process.env.URL_ARQUIVOS_SANDBOX || '';
      }

      /// console.log('reqObj.data at line 28 in models/MessagesRabbitMQ.ts:', reqObj.data);
      let type_message = reqObj.data.messageType;

      if (type_message === 'extendedTextMessage') {
        type_message = 'textMessage';
        mensagem = reqObj.data.message?.extendedTextMessage?.text;
      } else if (type_message === 'conversation') {
        type_message = 'textMessage';
        mensagem = reqObj.data.message?.conversation;
      } else if (type_message === 'ephemeralMessage') {
        type_message = 'textMessage';
        mensagem = reqObj.data?.message?.ephemeralMessage?.message?.extendedTextMessage?.text;
      } else if (type_message === 'listResponseMessage') {
        type_message = 'textMessage';
        mensagem = reqObj.data?.message?.listResponseMessage?.title;
      } else if (type_message === 'imageMessage') {
        mimetype = reqObj.data.message?.imageMessage?.mimetype;
        filesha256 = reqObj.data.message?.imageMessage?.fileSha256;
        file_length = reqObj.data.message?.imageMessage?.fileLength;
        nr_height = reqObj.data.message?.imageMessage?.height;
        nr_width = reqObj.data.message?.imageMessage?.width;
        media_key = reqObj.data.message?.imageMessage?.mediaKey;
        jpeg_thumbnail = reqObj.data.message?.imageMessage?.jpegThumbnail;
        // ds_base64 = reqObj.data.message?.imageMessage?.base64;
      } else if (type_message === 'documentMessage') {
        mimetype = reqObj.data.message?.documentMessage?.mimetype;
        filesha256 = reqObj.data.message?.documentMessage?.fileSha256;
        file_length = reqObj.data.message?.documentMessage?.fileLength;
        media_key = reqObj.data.message?.documentMessage?.mediaKey;
      } else if (type_message === 'audioMessage') {
        mimetype = reqObj.data.message?.audioMessage?.mimetype;
        filesha256 = reqObj.data.message?.audioMessage?.fileSha256;
        file_length = reqObj.data.message?.audioMessage?.fileLength;
        media_key = reqObj.data.message?.audioMessage?.mediaKey;
      }

      if (reqObj.data?.contextInfo?.quotedMessage?.listMessage) {
        type_message_quoted = 'textMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage = reqObj.data?.contextInfo?.quotedMessage?.listMessage?.title.replace(/'/g, '');
      } else if (reqObj.data?.contextInfo?.quotedMessage?.conversation) {
        type_message_quoted = 'textMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage = reqObj.data?.contextInfo?.quotedMessage?.conversation.replace(/'/g, '');
      } else if (reqObj.data?.message?.messageContextInfo?.contextInfo?.quotedMessage?.conversation) {
        type_message_quoted = 'textMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage = reqObj.data?.message?.extendedTextMessage?.contextInfo?.quotedMessage?.conversation.replace(
          /'/g,
          '',
        );
      } else if (reqObj.data?.contextInfo?.quotedMessage?.imageMessage) {
        type_message_quoted = 'imageMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage = reqObj.data?.contextInfo?.quotedMessage?.imageMessage.caption;
        quotedmessage = quotedmessage == undefined ? mensagem : quotedmessage;
        mimetype_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.mimetype;
        filesha256_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.fileSha256;
        file_length_quoted = reqObj.data.contextInfo?.quotedMessage?.imageMessage?.fileLength;
        nr_height_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.height;
        nr_width_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.width;
        media_key_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.mediaKey;
        jpeg_thumbnail_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.jpegThumbnail;
        // ds_base64_quoted = reqObj.data?.contextInfo?.quotedMessage?.imageMessage?.base64;
      } else if (reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage) {
        type_message_quoted = 'documentMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage =
          reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage?.message?.documentMessage?.caption;
        quotedmessage = quotedmessage == undefined ? mensagem : quotedmessage;
        mimetype_quoted =
          reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage?.message?.documentMessage?.mimetype;
        filesha256_quoted =
          reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage?.message?.documentMessage?.fileSha256;
        file_length_quoted =
          reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage?.message?.documentMessage?.fileLength;
        media_key_quoted =
          reqObj.data?.contextInfo?.quotedMessage?.documentWithCaptionMessage?.message?.documentMessage?.mediaKey;
      } else if (reqObj.data?.contextInfo?.quotedMessage?.audioMessage) {
        type_message_quoted = 'audioMessage';
        message_id_quoted = reqObj.data?.contextInfo?.stanzaId;
        quotedmessage = reqObj.data?.contextInfo?.quotedMessage?.audioMessage.caption;
        quotedmessage = quotedmessage == undefined ? mensagem : quotedmessage;
        mimetype_quoted = reqObj.data?.contextInfo?.quotedMessage?.audioMessage?.mimetype;
        filesha256_quoted = reqObj.data?.contextInfo?.quotedMessage?.audioMessage?.fileSha256;
        file_length_quoted = reqObj.data?.contextInfo?.quotedMessage?.audioMessage?.fileLength;
        media_key_quoted = reqObj.data?.contextInfo?.quotedMessage?.audioMessage?.mediaKey;
      }

      if (type_message == 'documentMessage' || type_message == 'audioMessage' || type_message == 'imageMessage') {
        //VAMOS ENVIAR OS DADOS NECESSARIO QUE PRECISA PARA FAZER O DOWNLOAD DA IMAGEM, DOCUMENTO, AUDIO
        //const respMedia = await new EvolutionApi().donwloadMedia(reqObj);
        const respMedia = await EvolutionApiService.donwloadMedia(reqObj, type_message);
        // console.log('respMedia at line 170 in models/MessagesRabbitMQ.ts:', respMedia);
        //logger.debug('respMedia at line 117 in models/MessagesRabbitMQ.ts: ' + JSON.stringify(respMedia));
        if (respMedia.statuscode === 200) {
          file_name = respMedia.data[0].file_name;
          url_midia = respMedia.data[0].url_midia;
          url_midiacompleta = URL_ARQUIVOS + respMedia.data[0].url_midia;
        }
      }

      if (message_id_quoted != undefined) {
        const filtro = {
          instance: reqObj.instance,
          telefone: reqObj.data.key.remoteJid.replace('@s.whatsapp.net', ''),
          message_id: message_id_quoted,
        };
        const respQuoted = await MessagesDB.getUrlMidiaMessageQuoted(filtro);
        //console.log('respQuoted at line 160 in models/MessagesRabbitMQ.ts:', respQuoted);
        if (respQuoted.statuscode === 200) {
          url_midia_quoted = respQuoted.data[0]?.url_midia;
          file_name_quoted = respQuoted.data[0]?.file_name;
          type_message_quoted = respQuoted.data[0]?.type_message;
          url_midia_quotedcompleta = URL_ARQUIVOS + respQuoted.data[0]?.url_midia;
        }
      }
      let telefone = reqObj.data.key.remoteJid.replace('@s.whatsapp.net', '');

      const dataInstance = await InstanceDB.listarInstancias({ instance: reqObj.instance });
      let cd_estabelecimento = undefined;
      let in_stop_bot = false;
      if (dataInstance.statuscode == 200) {
        cd_estabelecimento = dataInstance.data[0].cd_estabelecimento;
        in_stop_bot = dataInstance.data[0].in_stop_bot;
      }
      //console.log('dataInstance :', dataInstance);

      const dataAtendimento = await AtendimentosDB.listarAtendimentos({
        telefone: telefone,
        cd_estabelecimento: cd_estabelecimento,
        instance: reqObj.instance,
      });
      //console.log('dataAtendimento at line 191 in models/MessagesRabbitMQ.ts:', dataAtendimento);

      telefone = telefone ? telefone.toString() : '';

      if (telefone == '') {
        telefone = reqObj.data.key.remoteJid.replace('@s.whatsapp.net', '');
        telefone = telefone.toString();
      }
      let filtros = {
        telefone: telefone,
        cd_estabelecimento: cd_estabelecimento,
        instance: reqObj.instance,
      };
      //const respCliente = await new Clientes().consultaCliente(filtros);
      const respCliente = await ClientesDB.listarClientes(filtros);
      // console.log('respCliente at line 258 in models/MessagesRabbitMQ.ts:', respCliente);
      let cdCliente = 0;
      let nmCliente = '';
      if (respCliente.statuscode == 200) {
        cdCliente = respCliente.data[0].cd_cliente;
        nmCliente = respCliente.data[0].ds_nome;
      }

      if (cdCliente == 0 || cdCliente == undefined) {
        cdCliente = dataAtendimento.data[0]?.cd_cliente;
      }

      if (nmCliente == '' || nmCliente == undefined) {
        nmCliente = dataAtendimento.data[0]?.ds_contato;
      }

      const nome = reqObj.data.pushName.replace(/'/g, '');

      if (mensagem) mensagem = mensagem.replace(/'/g, '');
      const data: IMessage = {
        instance: reqObj.instance,
        event: reqObj.event,
        nome: nome,
        from_me: reqObj.data.key.fromMe,
        type_message: type_message,
        mimetype: mimetype,
        file_sha256: filesha256,
        file_length: file_length,
        nr_height: nr_height,
        nr_width: nr_width,
        media_key: media_key,
        jpeg_thumbnail: jpeg_thumbnail,
        //ds_base64: ds_base64,
        file_name: file_name,
        url_midia: url_midia,
        url_midiacompleta: url_midiacompleta,
        mensagem: mensagem || '',
        message_timestamp: Funcoes.dateTime(reqObj.data.messageTimestamp, 'datetime'),
        horario: Funcoes.dateTime(reqObj.data.messageTimestamp, 'time'),
        telefone: telefone,
        message_id: reqObj.data.key.id,
        remote_jid: reqObj.data.key.remoteJid,
        sender_lid: reqObj.data?.key?.senderLid,
        url_profile_picture: '',
        profile_picture_base64: '',
        quotedmessage: quotedmessage,
        mimetype_quoted: mimetype_quoted,
        file_sha256_quoted: filesha256_quoted,
        file_length_quoted: file_length_quoted,
        nr_height_quoted: nr_height_quoted,
        nr_width_quoted: nr_width_quoted,
        media_key_quoted: media_key_quoted,
        jpeg_thumbnail_quoted: jpeg_thumbnail_quoted,
        //ds_base64_quoted: ds_base64_quoted,
        file_name_quoted: file_name_quoted,
        url_midia_quoted: url_midia_quoted,
        url_midia_quotedcompleta: url_midia_quotedcompleta,
        message_id_quoted: message_id_quoted,
        type_message_quoted: type_message_quoted,
        cd_estabelecimento: cd_estabelecimento,
        in_stop_bot_geral: in_stop_bot,
        // atendimento: dataAtendimento.data,
        cd_atendimento: dataAtendimento.data[0]?.cd_atendimento,
        cd_cliente: cdCliente ? cdCliente : 0,
        nm_cliente: nmCliente ? nmCliente : '',
        ds_hash: dataAtendimento.data[0]?.ds_hash,
        in_stop_bot: dataAtendimento.data[0]?.in_stop_bot,
        tp_status_atendimento: dataAtendimento.data[0]?.tp_status,
        tp_situacao_atendimento: dataAtendimento.data[0]?.tp_situacao,
        tp_situacao_pedido: dataAtendimento.data[0]?.tp_situacao_pedido,
        tp_etapachat: dataAtendimento.data[0]?.tp_etapachat,
        in_forahorario: dataAtendimento.data[0]?.in_forahorario,
        cd_departamento: dataAtendimento.data[0]?.cd_departamento,
        ds_departamento: dataAtendimento.data[0]?.ds_departamento,
        cd_atendente: dataAtendimento.data[0]?.cd_atendente,
        nm_atendente: dataAtendimento.data[0]?.nm_atendente,
      };

      // console.log('data at line 250 in models/MessagesRabbitMQ.ts:', data);
      //AQUI VAMOS AJUSTAR PARA CHAMAR O AUDIOSERVICES
      if (type_message === 'audioMessage') {
        const respAudio = await AudioServices.transcreveAudio(data);
        //console.log('respAudio at line 124 in models/MessagesRabbitMQ.ts:', respAudio);
        if (respAudio.statuscode == 200) {
          data.mensagem = respAudio.data[0].texto;
        }
      }

      let filtrosContato = {
        instance: reqObj.instance,
        telefone: reqObj.data.key.remoteJid,
        lid: reqObj.data?.key?.senderLid, //ainda nao esta em uso no consulta contato
        pushName: nome,
      };
      //const respCont = await new Contacts().consultaContato(filtrosContato);

      if (dataAtendimento.statuscode == 404) {
        //console.log('filtrosContato at line 295 in models/MessagesRabbitMQ.ts:', filtrosContato);
        const respCont = await ContactsDB.consultaContato(filtrosContato);
        // console.log('respCont at line 296 in models/MessagesRabbitMQ.ts:', respCont);
        logger.debug('respCont at line 293 in models/MessagesRabbitMQ.ts:' + JSON.stringify(respCont));

        if (respCont.statuscode == 200) {
          const dataArray = respCont.data as any[];
          if (dataArray.length > 0) {
            data.url_profile_picture = dataArray[0].url_profile_picture;
            data.profile_picture_base64 = dataArray[0].profile_picture_base64;
          }
        }
      }

      let send = {
        statuscode: 200,
        message: 'Sucesso',
        event: reqObj.event,
        instance: reqObj.instance,
        data: [data],
      };

      //console.log('send at line 316 in models/MessagesRabbitMQ.ts:', send);
      return generico(send);
    } catch (error) {
      console.error('Erro ao processar a mensagem:', error);
      return erroInterno(error);
    }
  }
}
