-- Migration: add_departamento_atendimentos
-- Created: 2025-06-02T03:26:15.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar coluna cd_departamento na tabela atendimentos
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'atendimentos' 
        AND column_name = 'cd_departamento'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE atendimentos ADD COLUMN cd_departamento INTEGER;
        COMMENT ON COLUMN atendimentos.cd_departamento IS 'Indica o departamento responsável pelo atendimento';
        RAISE NOTICE 'Coluna cd_departamento adicionada à tabela atendimentos';
    ELSE
        RAISE NOTICE 'Coluna cd_departamento já existe na tabela atendimentos';
    END IF;
END $$; 