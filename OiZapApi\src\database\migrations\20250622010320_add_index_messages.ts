import { Knex } from 'knex';
import { safeCreateIndex } from '../../scripts/indexUtils';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('Iniciando criação dos índices na tabela messages');

    await safeCreateIndex(knex, 'messages', 'cd_departamento', 'messages_cd_departamento_idx');
    console.log('Índice cd_departamento criado com sucesso na tabela messages');

    await safeCreateIndex(knex, 'messages', 'cd_atendente', 'messages_cd_atendente_idx');
    console.log('Índice cd_atendente criado com sucesso na tabela messages');

    await safeCreateIndex(knex, 'messages', 'cd_motivo', 'messages_cd_motivo_idx');
    console.log('Índice cd_motivo criado com sucesso na tabela messages');
    return;
  } catch (error) {
    console.error('Erro ao criar indice cd_motivo:', error);
  }
}

export async function down(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  try {
    await trx.schema.alterTable('messages_cd_departamento_idx', (table) => {
      table.dropIndex([], 'messages_cd_atendente_idx');
      table.dropIndex([], 'messages_cd_motivo_idx');
    });
    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}
