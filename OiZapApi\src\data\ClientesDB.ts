require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno, parametrosInvalidos, sucessoErro } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { OperationObject, PostgreSQLServices } from '../services/PostgreSQLServices';

const logger = Logger.getLogger();
export class ClientesDB {
  static async incluirCliente(req: Request): Promise<IRetorno> {
    try {
      let campos: string;
      let values: string;
      let update: string = '';
      campos = `insert into clientes (cd_estabelecimento,ds_nome,nr_telefonezap`;
      values = `${req.body.cd_estabelecimento},'${req.body.ds_nome}','${req.body.nr_telefonezap}'`;

      if (req.body.cd_clientepdv != undefined) {
        campos += `,cd_clientepdv`;
        values += `,'${req.body.cd_clientepdv}'`;
        update += `,cd_clientepdv='${req.body.cd_clientepdv}'`;
      }
      if (req.body.ds_endereco != undefined) {
        campos += `,ds_endereco`;
        values += `,'${req.body.ds_endereco}'`;
        update += `,ds_endereco='${req.body.ds_endereco}'`;
      }
      if (req.body.nr_endereco != undefined) {
        campos += `,nr_endereco`;
        values += `,'${req.body.nr_endereco}'`;
        update += `,nr_endereco='${req.body.nr_endereco}'`;
      }
      if (req.body.ds_bairro != undefined) {
        campos += `,ds_bairro`;
        values += `,'${req.body.ds_bairro}'`;
        update += `,ds_bairro='${req.body.ds_bairro}'`;
      }
      if (req.body.ds_complemento != undefined) {
        campos += `,ds_complemento`;
        values += `,'${req.body.ds_complemento}'`;
        update += `,ds_complemento='${req.body.ds_complemento}'`;
      }
      if (req.body.ds_obscliente != undefined) {
        campos += `,ds_obscliente`;
        values += `,'${req.body.ds_obscliente}'`;
        update += `,ds_obscliente='${req.body.ds_obscliente}'`;
      }
      if (req.body.nr_diasprazo != undefined) {
        campos += `,nr_diasprazo`;
        values += `,'${req.body.nr_diasprazo}'`;
        update += `,nr_diasprazo='${req.body.nr_diasprazo}'`;
      }

      let sql = `${campos}) values (${values})`;

      sql += ` ON CONFLICT (cd_estabelecimento,cd_clientepdv) DO UPDATE SET 
      ds_nome='${req.body.ds_nome}',nr_telefonezap='${req.body.nr_telefonezap}'
      ${update} returning * `;

      //  console.log('sql at line 79 in data/ClientesDB.ts:', sql);
      const resp = await new PostgreSQLServices().query(sql);

      return resp;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterarCliente(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['clientes'],
        chaves: { cd_cliente: req.body.cd_cliente, cd_estabelecimento: req.body.cd_estabelecimento },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listarClientes(req: Request): Promise<IRetorno> {
    try {
      //Analisar para usar o redis, porém temos que lembrar que o cliente por ter
      //em outros estabelecimentos, podendo então ser estabeleciemnto e telefone como
      //chave, mas esses dois nem sempre estão na query para usar como chave
      //const keyRedis = `${req.query.nameinstance}:instances-estabelecimento_instancias:${JSON.stringify(req.query)}`;
      //const resultRedis = await new RedisServices().get(keyRedis);
      //console.log('resultRedis :', resultRedis);
      //if (resultRedis) return sucesso(resultRedis);
      /*
      const opDb: OperationObject = {
        operacao: 'select',
        tabelas: ['clientes'],
        chaves: req.query,
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      */
      let sql = `select c.cd_cliente,c.cd_clientepdv,c.ds_nome,c.nr_telefonezap,c.ds_endereco,c.nr_endereco,c.ds_bairro
,c.ds_complemento,c.ds_obscliente,c.cd_estabelecimento,c.nr_latitude,c.nr_longitude,c.ds_cidade,c.ds_uf
,c.ds_pais,c.ds_estado,c.nr_diasprazo
 `;

      if (req.query.cd_contato) {
        sql += ` ,ct.cd_contato from clientes c left join cliente_contatos ct on c.cd_estabelecimento = ct.cd_estabelecimento and c.cd_cliente = ct.cd_cliente and ct.cd_contato = ${req.query.cd_contato} `;
      } else {
        sql += ` from clientes c `;
      }

      sql += `  where 1=1 `;

      if (req.query.cd_estabelecimento) {
        sql += ` and c.cd_estabelecimento = ${req.query.cd_estabelecimento} `;
      }
      if (req.query.cd_cliente) {
        sql += ` and c.cd_cliente = ${req.query.cd_cliente} `;
      }
      if (req.query.cd_clientepdv) {
        sql += ` and c.cd_clientepdv = ${req.query.cd_clientepdv} `;
      }
      if (req.query.ds_nome) {
        sql += ` and c.ds_nome like '%${req.query.ds_nome}%'`;
      }
      if (req.query.nr_telefonezap) {
        let telefone = req.query.nr_telefonezap as string; // Convertemos explicitamente para string

        // if (telefoneSemDDD && telefoneSemDDD.length > 2) {
        //   telefoneSemDDD = telefoneSemDDD.slice(2); // Executa o slice se for seguro
        // }

        // let telefoneSemDDD = telefone.slice(2);
        // let DDD = telefone.substr(0, 2);
        // //console.log('telefoneSemDDD at line 13 in data/ClientesDB.ts:', telefoneSemDDD);
        // let telefoneSem9DDD = telefoneSemDDD.slice(1);
        // let telefoneSem9 = DDD + telefoneSem9DDD;
        //console.log('telefoneSem9 at line 15 in data/ClientesDB.ts:', telefoneSem9);

        let oitoUltimosDigitos = telefone.slice(-8);

        //sql += `and (nr_telefonezap  = '${req.query.nr_telefonezap}' or nr_telefonezap  = '${telefoneSemDDD}')`;
        sql += ` and (trim(replace(c.nr_telefonezap,'-',''))  = '${telefone}' or 
                     trim(replace(c.nr_telefonezap,'-',''))  like '%${oitoUltimosDigitos}') `;
      }
      if (req.query.ds_endereco) {
        sql += ` and upper(c.ds_endereco) like upper('%${req.query.ds_endereco}%')`;
      }
      if (req.query.ds_cidade) {
        sql += ` and upper(c.ds_cidade) like upper('%${req.query.ds_cidade}%')`;
      }
      if (req.query.ds_uf) {
        sql += ` and upper(c.ds_uf) like upper('%${req.query.ds_uf}%')`;
      }
      if (req.query.ds_pais) {
        sql += ` and upper(c.ds_pais) like upper('%${req.query.ds_pais}%')`;
      }
      if (req.query.ds_estado) {
        sql += ` and upper(c.ds_estado) like upper('%${req.query.ds_estado}%')`;
      }

      if (req.query.search) {
        const search = req.query.search as string;
        const isName = isNaN(Number(search));
        if (isName && search.trim() !== '') {
          sql += ` and upper(c.ds_nome) like upper('%${req.query.search}%')`;
        }
        if (!isName) {
          let telefone = req.query.search as string;
          let oitoUltimosDigitos = telefone.slice(-8);
          sql += ` and (trim(replace(c.nr_telefonezap,'-',''))  = '${telefone}' or 
                     trim(replace(c.nr_telefonezap,'-',''))  like '%${oitoUltimosDigitos}') `;
        }
      }
      if (req.query.limit && req.query.page) {
        const offsetPage = (parseInt(req.query.page as string) - 1) * parseInt(req.query.limit as string);
        sql += `  order by c.ds_nome asc, c.ds_cidade asc limit ${req.query.limit} offset ${offsetPage}`;
      }

      //  logger.debug('sql at line 142 in data/ClientesDB.ts: ' + sql);
      // console.log('sql at line 138 in data/ClientesDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);
      //  console.log('result at line 139 in data/ClientesDB.ts:', result);
      // logger.debug('sql at line 142 in data/ClientesDB.ts: ' + JSON.stringify(result));

      //console.log('result:', result);

      //  if (result.statuscode == 200) {
      //    await new RedisServices().set(keyRedis, result.data, REDIS_TIMER);
      //  }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async removerCliente(req: Request): Promise<IRetorno> {
    try {
      // const resultInst = await new PostgreSQLServices().query(
      //   `select nr_telefonezap from clientes where id = ${req.body.cd_cliente}`,
      // );
      // const keyRedis = {
      //   keyword: 'clientes',
      //   id: resultInst.data[0].nr_telefonezap,
      // };
      // await new RedisServices().removeCacheCriteria(keyRedis);

      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['clientes'],
        chaves: {
          cd_cliente: req.body.cd_cliente,
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async limpaClientes(req: Request): Promise<IRetorno> {
    try {
      if (req.body.cd_estabelecimento == undefined) {
        return parametrosInvalidos(
          'O campo "cd_estabelecimento" é obrigatório.',
          'O campo "cd_estabelecimento" é obrigatório.',
        );
      }
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['clientes'],
        chaves: {
          cd_estabelecimento: req.body.cd_estabelecimento,
        },
        retorno: '*',
      };
      const result = await new PostgreSQLServices().executar(opDb);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async importaClientes(req: Request): Promise<IRetorno> {
    try {
      const dataCliente = req.body;
      //console.log('dataCliente at line 203 in data/ClientesDB.ts:', dataCliente);
      let respClienteSucesso = [];
      let respClienteErro = [];

      for (const dado of dataCliente) {
        let campos: string;
        let values: string;
        let update: string = '';
        campos = `insert into clientes (cd_estabelecimento,cd_clientepdv,ds_nome,nr_telefonezap`;
        values = `${req.query.cd_estabelecimento},${dado.cd_clientepdv},'${dado.ds_nome}','${dado.nr_telefonezap}'`;

        if (dado.ds_endereco != undefined) {
          campos += `,ds_endereco`;
          values += `,'${dado.ds_endereco}'`;
          update += `,ds_endereco='${dado.ds_endereco}'`;
        }
        if (dado.nr_endereco != undefined) {
          campos += `,nr_endereco`;
          values += `,'${dado.nr_endereco}'`;
          update += `,nr_endereco='${dado.nr_endereco}'`;
        }
        if (dado.ds_bairro != undefined) {
          campos += `,ds_bairro`;
          values += `,'${dado.ds_bairro}'`;
          update += `,ds_bairro='${dado.ds_bairro}'`;
        }
        if (dado.ds_complemento != undefined) {
          campos += `,ds_complemento`;
          values += `,'${dado.ds_complemento}'`;
          update += `,ds_complemento='${dado.ds_complemento}'`;
        }
        if (dado.ds_obscliente != undefined) {
          campos += `,ds_obscliente`;
          values += `,'${dado.ds_obscliente}'`;
          update += `,ds_obscliente='${dado.ds_obscliente}'`;
        }

        if (dado.nr_diasprazo != undefined) {
          campos += `,nr_diasprazo`;
          values += `,'${dado.nr_diasprazo}'`;
          update += `,nr_diasprazo='${dado.nr_diasprazo}'`;
        }

        let sql = `${campos}) values (${values})`;

        sql += ` ON CONFLICT (cd_estabelecimento,cd_clientepdv) DO UPDATE SET 
      ds_nome='${dado.ds_nome}',nr_telefonezap='${dado.nr_telefonezap}'
      ${update} returning * `;

        const resp = await new PostgreSQLServices().query(sql);

        if (resp.statuscode == 200) {
          respClienteSucesso.push(resp.data[0]);
        } else {
          respClienteErro.push(resp.data[0]);
        }
      }

      return sucessoErro(respClienteSucesso, respClienteErro, 'Importação concluida.');
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listaClientePorAtendimento(req: Request): Promise<IRetorno> {
    try {
      let sql = `select b.* from atendimentos a,clientes b 
where b.cd_cliente = a.cd_cliente 
and b.cd_estabelecimento = a.cd_estabelecimento 
and a.cd_atendimento = ${req.query.cd_atendimento}  
and a.cd_estabelecimento = ${req.query.cd_estabelecimento} `;

      //console.log('sql at line 66 in pedidos/PedidosDB.ts:', sql);
      const result = await new PostgreSQLServices().query(sql);

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
