import { config } from 'dotenv';
import { Request } from 'express';
import { IRetorno, erroInterno, servicoIndisponivel, sucesso } from '../interfaces/IRetorno';
import { ISendMessageWhats } from '../interfaces/ISendMessageWhats';
const axios = require('axios');
config();

let API_CONSUMER_SERVICES_URL: String;
//console.log('🚀 ~ WhatsServices.ts:10 ~ process.env.AMBIENTE:', process.env.AMBIENTE);
if (process.env.AMBIENTE == 'SANDBOX') {
  API_CONSUMER_SERVICES_URL = process.env.API_CONSUMER_SERVICES_URL_SANDBOX || '';
} else if (process.env.AMBIENTE == 'PROD') {
  API_CONSUMER_SERVICES_URL = process.env.API_CONSUMER_SERVICES_URL_SERVER || '';
} else if (process.env.AMBIENTE == 'CRM') {
  API_CONSUMER_SERVICES_URL = process.env.API_CONSUMER_SERVICES_URL_SERVER || '';
} else if (process.env.AMBIENTE == 'DEV') {
  API_CONSUMER_SERVICES_URL = process.env.API_CONSUMER_SERVICES_URL_LOCAL || '';
} else {
  API_CONSUMER_SERVICES_URL = process.env.API_CONSUMER_SERVICES_URL_LOCAL || '';
}
//console.log('🚀 ~ WhatsServices.ts:19 ~ API_CONSUMER_SERVICES_URL:', API_CONSUMER_SERVICES_URL);

export class WhatsServices {
  private static async api(
    method: string,
    url: string,
    headers?: object | null,
    params?: object | null,
    data?: object | null,
  ) {
    try {
      const result = await axios({
        url: url,
        method: method,
        headers: headers,
        params: params,
        data: data,
      });

      let resp = result.data.data;
      resp = resp ? resp : result.data;

      return sucesso(resp); //{ statuscode: 200, message: "OK", data: result.data };
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.statuscode) {
        return error.response.data;
      } else if (error.response && error.response.data) {
        return erroInterno(error.response.data);
      } else if (error.code && error.code == 'ECONNREFUSED') {
        return servicoIndisponivel(error);
      } else {
        return erroInterno(error);
      }
    }
  }
  static async fetchInstances(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash, //req.headers['authorization'],
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/fetchInstances/v1`;

      const result = await this.api('get', url, headers, req.query, null);
      return result;
    } catch (error) {
      // console.log('error at line 63 in services/WhatsServices.ts:', error);
      return erroInterno(error);
    }
  }
  static async restartInstance(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash, //req.headers['authorization'],
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/restartInstance/v1`;
      //console.log('url at line 53 in services/WhatsServices.ts:', url);
      const result = await this.api('post', url, headers, req.query, null);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async connectionState(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash, //req.headers['authorization'],
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/connectionState/v1`;
      const result = await this.api('get', url, headers, req.query, null);
      return result;
    } catch (error) {
      console.log('error at line 97 in services/WhatsServices.ts:', error);
      return erroInterno(error);
    }
  }
  static async logoutInstance(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/logoutInstance/v1`;
      //console.log('url at line 53 in services/WhatsServices.ts:', url);
      const result = await this.api('post', url, headers, req.query, req.body);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async deleteInstance(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/deleteInstance/v1`;
      //console.log('url at line 53 in services/WhatsServices.ts:', url);
      const result = await this.api('post', url, headers, req.query, req.body);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async removeFilas(req: Request): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + req.query.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/consumer/removeFilas/v1`;
      //console.log('url at line 53 in services/WhatsServices.ts:', url);
      const result = await this.api('delete', url, headers, req.query, req.body);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async emitEventSocket(req: Request): Promise<IRetorno> {
    try {
      const headers = undefined;
      const url = API_CONSUMER_SERVICES_URL + `/consumer/emitEventSocket/v1`;
      const result = await this.api('post', url, headers, req.query, req.body);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async publishToQueue(req: Request): Promise<IRetorno> {
    try {
      const headers = undefined;
      const url = API_CONSUMER_SERVICES_URL + `/consumer/publishToQueue/v1`;
      const result = await this.api('post', url, headers, req.query, req.body);
      console.log('result at line 155 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async sendMessage(message: ISendMessageWhats): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + message.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/sendMessage/v1`;
      const result = await this.api('post', url, headers, null, message);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async sendImage(message: ISendMessageWhats): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + message.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/sendImage/v1`;
      const result = await this.api('post', url, headers, null, message);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async sendList(message: ISendMessageWhats): Promise<IRetorno> {
    try {
      const headers = {
        authorization: 'Bearer ' + message.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/sendList/v1`;
      const result = await this.api('post', url, headers, null, message);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
  static async enviarMensagem(dados: any): Promise<IRetorno> {
    try {
      // console.log('dados at line 106 in services/WhatsServices.ts:', dados);
      const headers = {
        authorization: 'Bearer ' + dados.nr_hash,
      };

      const url = API_CONSUMER_SERVICES_URL + `/whats/enviarMensagem/v1`;
      const result = await this.api('post', url, headers, null, dados);
      //console.log('result at line 67 in services/WhatsServices.ts:', result);
      return result;
    } catch (error) {
      return erroInterno(error);
    }
  }
}
