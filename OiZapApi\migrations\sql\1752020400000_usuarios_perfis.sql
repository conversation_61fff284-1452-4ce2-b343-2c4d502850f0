-- Migration: usuarios_perfis
-- Created: 2025-01-09T00:00:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'adm_usuarios' 
        AND constraint_name = 'unique_cd_usuario'
    ) THEN
        ALTER TABLE adm_usuarios ADD CONSTRAINT unique_cd_usuario UNIQUE (cd_usuario);
    END IF;
END $$;

-- Exemplo: Criar tabela usuarios_perfis
CREATE TABLE IF NOT EXISTS usuarios_perfis (
  id SERIAL PRIMARY KEY,
  cd_usuario INTEGER NOT NULL,
  cd_perfil INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Chave estrangeira para usuários
  CONSTRAINT fk_usuarios_perfis_usuario 
    FOREIGN KEY (cd_usuario) 
    REFERENCES adm_usuarios(cd_usuario) 
    ON DELETE CASCADE,
    
  -- Chave estrangeira para perfis
  CONSTRAINT fk_usuarios_perfis_perfil 
    FOREIGN KEY (cd_perfil) 
    REFERENCES perfis(cd_perfil) 
    ON DELETE CASCADE,
    
  -- Evitar duplicação
  UNIQUE(cd_usuario, cd_perfil)
);

-- Comentários nas tabelas e colunas
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'usuarios_perfis') THEN
    COMMENT ON TABLE usuarios_perfis IS 'Tabela de relacionamento entre usuários e perfis - Sistema de permissões';
    COMMENT ON COLUMN usuarios_perfis.cd_usuario IS 'Código do usuário';
    COMMENT ON COLUMN usuarios_perfis.cd_perfil IS 'Código do perfil';
    COMMENT ON COLUMN usuarios_perfis.created_at IS 'Data de criação do registro';
    COMMENT ON COLUMN usuarios_perfis.updated_at IS 'Data da última atualização';
  END IF;
END $$;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_usuarios_perfis_usuario 
  ON usuarios_perfis(cd_usuario);

CREATE INDEX IF NOT EXISTS idx_usuarios_perfis_perfil 
  ON usuarios_perfis(cd_perfil);

-- ========================================
-- Adicione seu SQL personalizado aqui:
-- ======================================== 