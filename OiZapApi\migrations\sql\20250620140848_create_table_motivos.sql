-- Migration: create_table_motivos
-- Created: 2025-06-20T14:08:48.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar tabela motivos_atendimento
-- ========================================

CREATE TABLE IF NOT EXISTS motivos_atendimento (
    cd_motivo SERIAL PRIMARY KEY,
    ds_motivo VARCHAR(60) NOT NULL,
    in_ativo BOOLEAN DEFAULT TRUE,
    cd_usucad INTEGER NULL,
    dt_cadastro TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Coment<PERSON><PERSON>s nas colunas
COMMENT ON COLUMN motivos_atendimento.cd_motivo IS 'Código do motivo de atendimento';
COMMENT ON COLUMN motivos_atendimento.ds_motivo IS 'Descrição do motivo de atendimento';
COMMENT ON COLUMN motivos_atendimento.in_ativo IS 'Indica se o motivo está ativo';
COMMENT ON COLUMN motivos_atendimento.cd_usucad IS 'Código do usuário que cadastrou';
COMMENT ON COLUMN motivos_atendimento.dt_cadastro IS 'Data de cadastro do motivo'; 