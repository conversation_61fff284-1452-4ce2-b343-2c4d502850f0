import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { UsuarioModel } from '../../models/usuarios/UsuarioModel';

export class UsuarioController {
  static async login(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.iduser == undefined) {
        if (req.body.ds_login === undefined) {
          errors.push('O campo "ds_login" é obrigatório.');
        }

        if (req.body.ds_senha === undefined) {
          errors.push('O campo "ds_senha" é obrigatório.');
        }

        if (errors.length > 0) {
          return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
        }
      }

      const result = await new UsuarioModel().login(req);
      //console.log('result:', result);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaUsuarios(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listaUsuarios(req);
      //console.log('result:', result);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaUsuariosPorDepartamento(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listaUsuariosPorDepartamento(req);
      //console.log('result:', result);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaUsuariosDepartamentos(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listaUsuariosDepartamentos(req);
      //console.log('result:', result);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaUsuariosTodos(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listaUsuariosTodos(req);
      //console.log('result:', result);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async incluirUsuario(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      // if (req.body.cd_estabelecimento === undefined) {
      //   errors.push('O campo "cd_estabelecimento" é obrigatório.');
      // }
      if (req.body.ds_nome === undefined) {
        errors.push('O campo "ds_nome" é obrigatório.');
      }
      if (req.body.ds_login === undefined) {
        errors.push('O campo "ds_login" é obrigatório.');
      }
      if (req.body.ds_email === undefined) {
        errors.push('O campo "ds_email" é obrigatório.');
      }
      if (req.body.tp_privilegio === undefined) {
        errors.push('O campo "tp_privilegio" é obrigatório.');
      }
      if (req.body.tp_status === undefined) {
        errors.push('O campo "tp_status" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new UsuarioModel().incluirUsuario(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterarUsuario(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      // if (req.body.cd_estabelecimento === undefined) {
      //   errors.push('O campo "cd_estabelecimento" é obrigatório.');
      // }
      if (req.body.cd_usuario == undefined) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new UsuarioModel().alterarUsuario(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alteraSenha(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_usuario == undefined) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new UsuarioModel().alteraSenha(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarUsuario(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listarUsuario(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  /* static async listarUsuarioSQL(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuarioModel().listarUsuarioSQL(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async removerUsuario(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.cd_usuario == undefined) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new UsuarioModel().removerUsuario(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  */
}
