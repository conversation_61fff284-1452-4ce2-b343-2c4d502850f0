<template>
    <div
        @click="$emit('chat-selected', chat, index)"
        :class="[
            'relative px-2 py-1.5 cursor-pointer transition-all duration-150 hover:bg-gray-50 dark:hover:bg-darkmode-400',
            isActive ? 'bg-emerald-50 dark:bg-emerald-900/20 border-l-4 border-l-emerald-500' : '',
            chat?.in_stop_bot ? 'border-l-4 border-l-orange-400' : '',
        ]"
    >
        <div class="flex items-start space-x-2">
            <!-- Avatar -->
            <div class="relative flex-shrink-0" :class="qtInstancias > 1 ? 'mt-2' : 'mt-1.5'">
                <div class="w-12 h-12 rounded-full overflow-hidden">
                    <img
                        alt="avatar"
                        class="w-full h-full object-cover"
                        :src="chat?.url_profile_picture"
                        @error="handleImageError"
                    />
                </div>
                <!-- Indicador Online -->
                <div
                    v-if="chat?.online"
                    class="absolute -bottom-0 -right-0 w-2.5 h-2.5 bg-green-500 border border-white dark:border-darkmode-800 rounded-full"
                ></div>
            </div>

            <!-- Conteúdo -->
            <div class="flex-1 min-w-0">
                <!-- Linha 1: Nome + Horário + Contador -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-1 flex-1 min-w-0">
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-slate-300 truncate">
                            {{ chat?.nome || 'Nome não disponível' }}
                        </h3>

                        <!-- Ícones -->
                        <Tippy v-show="chat?.in_acessoulink" :content="chat?.tp_etapachat || ''">
                            <LinkIcon
                                class="w-3 h-3"
                                :class="chat?.tp_etapachat == 'Acessou' ? 'text-orange-500' : 'text-green-600'"
                            />
                        </Tippy>

                        <Tippy
                            v-show="chat.tp_etapachat == 'Falar com Atendente'"
                            content="Falar com Atendente"
                            class="mr-1"
                        >
                            <HeadsetIcon
                                v-show="chat.tp_etapachat == 'Falar com Atendente'"
                                class="w-4 h-4 ml-1 -mt-0.5 animate-ping absolute inline-flex opacity-75"
                                style="stroke-width: 3.5; color: #16a34a"
                            />
                            <HeadsetIcon class="w-4 h-4 ml-1 -mt-0.5" style="stroke-width: 3.5; color: #16a34a" />
                        </Tippy>
                    </div>

                    <div class="flex items-center space-x-1">
                        <!-- Horário -->
                        <div v-if="chat?.horario">
                            <Tippy
                                v-if="chat?.minutos_atendimento >= 10"
                                :content="`Acima de ${chat.minutos_atendimento} minutos`"
                                class="tooltip"
                            >
                                <span
                                    :class="[
                                        'text-xs px-1.5 py-0.5 rounded font-medium',
                                        chat.minutos_atendimento >= 10
                                            ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                                            : chat.minutos_atendimento >= 5
                                            ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300'
                                            : 'text-gray-500 dark:text-slate-400',
                                    ]"
                                >
                                    {{ chat.horario }}
                                </span>
                            </Tippy>

                            <span
                                v-else
                                :class="[
                                    'text-xs',
                                    chat?.minutos_atendimento >= 5
                                        ? 'text-amber-600 dark:text-amber-400 font-medium'
                                        : 'text-gray-500 dark:text-slate-400',
                                ]"
                            >
                                {{ chat.horario }}
                            </span>
                        </div>

                        <!-- Badge Mensagens -->
                        <div
                            v-if="chat?.qt_messages_notread > 0"
                            class="bg-emerald-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold leading-none"
                        >
                            {{ chat.qt_messages_notread > 9 ? '9+' : chat.qt_messages_notread }}
                        </div>
                    </div>
                </div>

                <!-- Linha 2: Mensagem -->
                <div class="mt-0.5">
                    <div
                        v-if="!chat?.presence && chat?.mensagem"
                        :class="[
                            'text-xs text-gray-600 dark:text-slate-400 truncate leading-tight',
                            chat?.in_novamsg ? 'font-semibold text-gray-900 dark:text-slate-300' : '',
                            chat?.in_delete ? 'line-through' : '',
                        ]"
                        v-html="chat.mensagem"
                    ></div>
                    <div v-else-if="chat?.presence" class="text-xs text-green-600 dark:text-green-400 italic">
                        {{ chat.presence }}
                    </div>
                    <div v-else class="text-xs text-gray-400 dark:text-slate-500 italic">Sem mensagens</div>
                </div>

                <!-- Linha 3: Status + Departamento -->
                <div class="flex items-center justify-between mt-0.5">
                    <span
                        v-if="chat?.tp_status"
                        :class="['px-1.5 py-0.5 text-xs font-medium rounded', getColorForStatus(chat.tp_status)]"
                    >
                        {{ chat.tp_status }}
                    </span>
                    <span
                        v-else
                        class="px-1.5 py-0.5 text-xs font-medium rounded bg-gray-100 dark:bg-darkmode-400 text-gray-600 dark:text-slate-300"
                    >
                        Sem status
                    </span>

                    <div class="flex items-center justify-between mt-0.5" v-show="modulosOiZap.in_crm">
                        <Tippy v-show="chat?.cd_campanha" :content="`Lead ${chat?.ds_campanha}`" class="mr-1">
                            <MagnetIcon
                                v-show="chat?.cd_campanha"
                                class="w-4 h-4 ml-1 -mt-0.5 animate-ping absolute inline-flex opacity-75"
                                style="stroke-width: 3.5; color: #1b6ba6"
                            />
                            <MagnetIcon class="w-4 h-4 ml-1 -mt-0.5" style="stroke-width: 3.5; color: #1b6ba6" />
                        </Tippy>
                        <span
                            class="text-xs text-gray-700 ml-auto mr-2 bg-cyan-400 rounded px-2 flex items-center relative p-0.5"
                            v-show="chat?.ds_departamento"
                        >
                            <Tippy content="Departamento" class="tooltip">
                                {{ chat?.ds_departamento }}
                            </Tippy>
                        </span>

                        <span
                            class="text-xs bg-emerald-400 text-gray-700 rounded px-2 flex items-center relative ml-auto p-0.5"
                            v-show="chat?.nm_atendente"
                        >
                            <HeadsetIcon class="w-4 h-4 mr-1" style="stroke-width: 1.5" />
                            <Tippy content="Atendente" class="tooltip">
                                {{ chat?.nm_atendente }}
                            </Tippy>
                        </span>

                        <Tippy
                            v-if="
                                chat?.tp_status != 'Atendimento Finalizado' &&
                                chat?.tp_status != 'Atendimento Cancelado'
                            "
                            content="Transferir Atendimento"
                            class="tooltip"
                            @click.stop="$emit('transferir', chat, index)"
                        >
                            <ArrowRightLeftIcon
                                class="w-4 h-4 ml-1 text-gray-800 dark:text-white"
                                style="stroke-width: 2.5"
                            />
                        </Tippy>

                        <!-- <div class="text-xs text-gray-500 dark:text-slate-400 truncate max-w-[100px]">
                        {{ chat?.ds_departamento || '' }}{{ chat?.nm_atendente ? ' • ' + chat.nm_atendente : '' }}
                    </div> -->
                    </div>
                </div>
            </div>
        </div>
        <div class="flex items-center justify-between mt-0.5" v-if="qtInstancias > 1">
            <span
                class="text-[9px] text-gray-700 mr-auto mr-2 bg-gray-500/20 rounded px-2 flex items-center relative p-0.5"
                v-if="modulosOiZap.in_crm"
            >
                <Tippy content="Instância" class="tooltip">
                    {{ chat?.nameinstance }}
                </Tippy>
            </span>
        </div>
    </div>
</template>

<script setup>
    import { LinkIcon, HeadsetIcon } from 'lucide-vue-next';
    import notFoundImage from '@/assets/images/notfound.png';

    // Props
    const props = defineProps({
        chat: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        isActive: {
            type: Boolean,
            default: false,
        },
        getColorForStatus: {
            type: Function,
            required: true,
        },
        modulosOiZap: {
            type: Object,
            default: () => ({}),
        },
        qtInstancias: {
            type: Number,
            default: 0,
        },
    });

    // Emits
    const emit = defineEmits(['chat-selected', 'transferir']);

    // Methods
    const handleImageError = (event) => {
        event.target.src = notFoundImage;
    };
</script>
