import { Channel, connect, Connection, ChannelModel } from 'amqplib';
import Logger from '../Logger';

const logger = Logger.getLogger();

interface ServerConnection {
  connection: Connection;
  channels: Map<string, Channel>; // channelId -> Channel
}

class RabbitMQConnectionManager {
  private static instance: RabbitMQConnectionManager;
  private connections: Map<string, ServerConnection> = new Map(); // serverName -> ServerConnection
  private reconnectInterval: number = 5000;
  private maxChannelsPerConnection: number = 100; // Limite de canais por conexão

  private constructor() {}

  public static getInstance(): RabbitMQConnectionManager {
    if (!RabbitMQConnectionManager.instance) {
      RabbitMQConnectionManager.instance = new RabbitMQConnectionManager();
    }
    return RabbitMQConnectionManager.instance;
  }

  /**
   * Conecta a um servidor RabbitMQ (uma única conexão por servidor)
   */
  public async connectToServer(serverName: string, url: string): Promise<void> {
    console.log('🚀 ~ RabbitMQConnectionManager.ts:30 ~ connectToServer ~ url:', url);
    console.log('🚀 ~ RabbitMQConnectionManager.ts:30 ~ connectToServer ~ serverName:', serverName);
    if (this.connections.has(serverName)) {
      logger.debug(`Conexão já existe para o servidor: ${serverName}`);
      return;
    }

    try {
      const connection = await connect(url) as unknown as Connection;
      logger.info(`✅ Conectado ao RabbitMQ servidor: ${serverName}`);

      // Configurar listeners de reconexão
      this.setupReconnectionListeners(connection, serverName, url);

      // Armazenar a conexão
      this.connections.set(serverName, {
        connection,
        channels: new Map(),
      });
    } catch (error: any) {
      logger.error(`❌ Erro ao conectar no servidor ${serverName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cria um canal para uma instância específica
   */
  public async createChannelForInstance(serverName: string, instanceName: string): Promise<Channel> {
    const serverConnection = this.connections.get(serverName);

    if (!serverConnection) {
      throw new Error(`Conexão não encontrada para o servidor: ${serverName}`);
    }

    const channelId = `${serverName}-${instanceName}`;

    // Verificar se já existe canal para esta instância
    if (serverConnection.channels.has(channelId)) {
      logger.debug(`Canal já existe para ${channelId}`);
      return serverConnection.channels.get(channelId)!;
    }

    // Verificar limite de canais
    if (serverConnection.channels.size >= this.maxChannelsPerConnection) {
      logger.warn(`Limite de canais atingido para ${serverName} (${this.maxChannelsPerConnection})`);
      throw new Error(`Limite de canais atingido para ${serverName}`);
    }

    try {
      const channel = await (serverConnection.connection as any).createChannel();

      // Configurar listeners do canal
      this.setupChannelListeners(channel, channelId, serverName, instanceName);

      // Armazenar o canal
      serverConnection.channels.set(channelId, channel);

      logger.info(`📡 Canal criado para ${channelId}`);
      return channel;
    } catch (error: any) {
      logger.error(`❌ Erro ao criar canal para ${channelId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Obtém um canal existente para uma instância
   */
  public getChannel(serverName: string, instanceName: string): Channel | null {
    const serverConnection = this.connections.get(serverName);
    if (!serverConnection) {
      return null;
    }

    const channelId = `${serverName}-${instanceName}`;
    return serverConnection.channels.get(channelId) || null;
  }

  /**
   * Remove um canal específico
   */
  public async removeChannel(serverName: string, instanceName: string): Promise<void> {
    const serverConnection = this.connections.get(serverName);
    if (!serverConnection) {
      return;
    }

    const channelId = `${serverName}-${instanceName}`;
    const channel = serverConnection.channels.get(channelId);

    if (channel) {
      try {
        await channel.close();
        serverConnection.channels.delete(channelId);
        logger.info(`🗑️ Canal removido: ${channelId}`);
      } catch (error: any) {
        logger.error(`❌ Erro ao fechar canal ${channelId}: ${error.message}`);
      }
    }
  }

  /**
   * Obtém estatísticas das conexões
   */
  public getConnectionStats(): any {
    const stats: any = {};

    for (const [serverName, serverConnection] of this.connections) {
      stats[serverName] = {
        connected: serverConnection.connection ? true : false,
        channelsCount: serverConnection.channels.size,
        channels: Array.from(serverConnection.channels.keys()),
      };
    }

    return stats;
  }

  /**
   * Configura listeners de reconexão para a conexão
   */
  private setupReconnectionListeners(connection: Connection, serverName: string, url: string): void {
    connection.on('error', (error) => {
      logger.error(`🔥 Erro na conexão ${serverName}: ${error.message}`);
      this.handleReconnection(serverName, url);
    });

    connection.on('close', () => {
      logger.warn(`🔌 Conexão fechada para ${serverName}`);
      this.handleReconnection(serverName, url);
    });
  }

  /**
   * Configura listeners para um canal
   */
  private setupChannelListeners(channel: Channel, channelId: string, serverName: string, instanceName: string): void {
    channel.on('error', (error) => {
      logger.error(`🔥 Erro no canal ${channelId}: ${error.message}`);
      this.recreateChannel(serverName, instanceName);
    });

    channel.on('close', () => {
      logger.warn(`🔌 Canal fechado: ${channelId}`);
      // Canal será recriado quando necessário
    });
  }

  /**
   * Tenta reconectar ao servidor
   */
  private async handleReconnection(serverName: string, url: string): Promise<void> {
    // Remover conexão atual
    this.connections.delete(serverName);

    logger.info(`🔄 Tentando reconectar ao ${serverName} em ${this.reconnectInterval}ms...`);

    setTimeout(async () => {
      try {
        await this.connectToServer(serverName, url);
        logger.info(`✅ Reconectado com sucesso ao ${serverName}`);
      } catch (error: any) {
        logger.error(`❌ Falha na reconexão ao ${serverName}: ${error.message}`);
        // Tentar novamente
        this.handleReconnection(serverName, url);
      }
    }, this.reconnectInterval);
  }

  /**
   * Recria um canal específico
   */
  private async recreateChannel(serverName: string, instanceName: string): Promise<void> {
    try {
      // Remove canal antigo
      await this.removeChannel(serverName, instanceName);

      // Cria novo canal
      await this.createChannelForInstance(serverName, instanceName);

      logger.info(`🔄 Canal recriado com sucesso: ${serverName}-${instanceName}`);
    } catch (error: any) {
      logger.error(`❌ Erro ao recriar canal ${serverName}-${instanceName}: ${error.message}`);
    }
  }

  /**
   * Fecha todas as conexões
   */
  public async closeAllConnections(): Promise<void> {
    for (const [serverName, serverConnection] of this.connections) {
      try {
        // Fechar todos os canais
        for (const [channelId, channel] of serverConnection.channels) {
          await channel.close();
          logger.debug(`Canal fechado: ${channelId}`);
        }

        // Fechar conexão
        await (serverConnection.connection as any).close();
        logger.info(`✅ Conexão fechada: ${serverName}`);
      } catch (error: any) {
        logger.error(`❌ Erro ao fechar conexão ${serverName}: ${error.message}`);
      }
    }

    this.connections.clear();
    logger.info('🏁 Todas as conexões RabbitMQ foram fechadas');
  }
}

export default RabbitMQConnectionManager;
