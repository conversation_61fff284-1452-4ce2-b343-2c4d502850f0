#VALORES = DEV, SANDBOX, PROD
AMBIENTE='DEV'
#VALORES = V1, V2
VERSAO_EVOLUTION='V2'
LOG_LEVEL=info,warn,error,debug

PORT='3200'
TOKEN='n0d3j5vu3'
JWT_SECRET_KEY = 'n0d3j5vu3'

# CONFIGURACOES DO PM2 #################################
PM2_APPNAME='3200_Consumer'
PM2_APPNAMESANDBOX='4200_Sandbox-Consumer'
PM2_APPNAMEPROD='5200_Prod-Consumer'
PM2_APPNAMECRM='6200_CRM-Consumer'
# CONFIGURACOES DO PM2 #################################

# CONFIGURACOES DO BANCO ###############################
USER_DATABASE='postgres'
DATABASE='oizap'
DATABASE_CRM='oizap-crm'
#DATABASE='oizap-sandbox'
#DATABASE_SANDBOX='oizap-sandbox'
DATABASE_SANDBOX='oizap-crm'
#DATABASE_SANDBOX='oizap'
#HOST_DATABASE='*************'
HOST_DATABASE='*************'
HOST_DATABASEDEV='*************'
#HOST_DATABASEDEV='*************'
PORT_DATABASE='5831'
PORT_DATABASEDEV='5831'
#PASSWORD_DATABASE='01z4p'
PASSWORD_DATABASE='p0$tgr3$ql'
PASSWORD_DATABASEDEV='p0$tgr3$ql'
#PASSWORD_DATABASEDEV='01z4p'
# CONFIGURACOES DO BANCO ###############################

# CONFIGURACOES DO RABBITMQ  ##########################
HOST_SOCKET ="http://localhost:4200"
EXCHANGE_RABBITMQ = 'evolution_exchange'
HOST_RABBITMQ = 'amqp://admin:<EMAIL>:5673/default'
##HOST_RABBITMQSANDBOX = 'amqp://admin:<EMAIL>/default'
HOST_RABBITMQSANDBOX = 'amqp://admin:<EMAIL>:5673/default'
#FILA_RABBITMQ_DEV = 'c674fa7332a608ebcbd5dd849','2378f6183a13e63d1125bruno'
FILA_RABBITMQ_DEV = "'2378f6183a13e63d1592f12ef','59af048b92128ccb58b11286e','2378f6183a13e63d1125bruno'"
API_RABBITMQ='http://filas-evo.oizap.com.br:5673/api/queues'
##API_RABBITMQSANDBOX='http://rabbitmq-sandbox.oizap.com.br/api/queues'
API_RABBITMQSANDBOX='http://filas-oizap.oizap.com.br:5673/api/queues'
#API_RABBITMQOIZAP='http://oizap.com.br:15672/api/queues'
#API_RABBITMQOIZAP='http://********:15672/api/queues'
##API_RABBITMQOIZAP='https://filas-evo.oizap.com.br/api/queues'
API_RABBITMQOIZAP='https://filas-oizap.oizap.com.br:5673/api/queues'
USER_RABBITMQ='admin'
##PASS_RABBITMQ='r1R2r3R4r5rrR'
PASS_RABBITMQ='r1R2r3R4r5'
#USER_RABBITMQ='admin'
#PASS_RABBITMQ='01z4p'
#PASS_RABBITMQ='r@bb1tmq)'
#HOST_RABBITMQ_OIZAP = '*******************************************/'
#HOST_RABBITMQ_OIZAP = '************************************/'
##HOST_RABBITMQ_OIZAP = 'amqp://admin:<EMAIL>/default'
HOST_RABBITMQ_OIZAP = 'amqp://admin:<EMAIL>:5673/default'
# CONFIGURACOES DO RABBITMQ  ##########################

# CONFIGURACOES EVOLUTION  ############################
APIKEY_WHATS='70f22a40'
READ_MESSAGES='false'
GLOBAL_APIKEY_WHATS='c94e57eb79f4b761111eb1549a0abc2f'
HOST_APIWHATS='https://evo.oizap.com.br'
HOST_APIWHATS_SANDBOX='https://evov2-sandbox.oizap.com.br'
#EVO V2
HOST_APIWHATS_V2='https://evo.oizap.com.br'
#HOST_APIWHATS_SANDBOX_V2='https://evohmllite.oizap.com.br'
#HOST_APIWHATS_SANDBOX_V2='https://evolite-sandbox.oizap.com.br'
##HOST_APIWHATS_SANDBOX_V2='https://evov2-sandbox.oizap.com.br'
HOST_APIWHATS_SANDBOX_V2='https://evocrm.oizap.com.br'
#LIMITA_NUMEROS=5521992676573,5521992685661,5521994649353,5521972289930,554499495450,554499249526,5521993786972
# CONFIGURACOES EVOLUTION  ############################

# REDIS #################################
REDIS_ATIVO='S'
#REDIS_HOST = "**************"
REDIS_HOST = "redis-oizap.oizap.com.br"
REDIS_PORT = "6379"
REDIS_PASSWORD = "r3d1s01z4p"
# REDIS #################################

# CONFIGURACAO CAMINHOS ###############################
URL_MIDIA = 'https://app-api.oizap.com.br/midia/'
PATH_AUDIOS = '/var/www/consumer/audios/'
URL_MIDIA_SANDBOX = 'https://app-apisandbox.oizap.com.br/midia/'
URL_MIDIA_DEV = 'https://app-apidev.oizap.com.br/midia/'
PATH_AUDIOS_SANDBOX = '/var/www/sandbox-consumer/audios/'
URL_MIDIA_WINDOWS = 'D:\\ProjetosHTML\\ArthySis\\OiZapConsumer\\midia\\'
PATH_AUDIOSWINDOWS = 'D:\\ProjetosHTML\\ArthySis\\wapiConsumer\\audios\\'
URL_AUDIOS = '/api/audios/'
URL_AUDIOS_SANDBOX = '/sandbox-oizap-api/audios/'

PATH_MIDIA = '/var/www/oizap-api/midia/'
PATH_MIDIA_SANDBOX = '/var/www/sandbox-oizap-api/midia/'
#PATH_MIDIA_SANDBOX = '/midia/'
PATH_MIDIA_WINDOWS = 'D:\\ProjetosHTML\\ArthySis\\wapiconsumer\\midia\\'
PATH_IMAGENSFLUXO = '/var/www/api/uploads/'
PATH_IMAGENSFLUXO_SANDBOX = '/var/www/sandbox-oizap-api/uploads/'
PATH_IMAGENSFLUXO_WINDOWS = 'D:\\ProjetosHTML\\ArthySis\\wapiapi\\uploads\\'
PATH_PYTHON = '/root/venv/bin/python3'
#PATH_PYTHON = '/usr/bin/python3'
PATH_PYTHON_WINDOWS = 'python'
FFMPEG_PATH = '/usr/bin/ffmpeg'
FFMPEG_WORKDIR = '/tmp'

URL_IMAGENSFLUXO = 'https://app.oizap.com.br/api/uploads/'
URL_IMAGENSFLUXO_SANDBOX = 'https://app-sandbox.oizap.com.br/api/uploads/'
URL_ARQUIVOS = 'https://app-api.oizap.com.br'
URL_ARQUIVOS_SANDBOX = 'https://app-apisandbox.oizap.com.br'
# CONFIGURACAO CAMINHOS ###############################

#WEBSHARE - PROXY #################################
WEBSHARE_TOKEN_API='rgoepn7feko4cqmcfws50se5sn8ae54j9hnr9wbm'
WEBSHAR_URL_API='https://proxy.webshare.io'
PROXY_URL='p.webshare.io'
PROXY_PORT='80'
PROXY_USER_NAME='oizaproxy-rotate'
PROXY_PASSWORD='01z4pr0xy'
#WEBSHARE - PROXY #################################

# SERVIDORES DESTINO #################################
HOST_SERVERAPI ="*************"
PORT_SERVERAPI ="22"
USER_SERVERAPI ="root"
#HOST_SERVERAPI ="*************"
#PORT_SERVERAPI ="2229"
#USER_SERVERAPI ="resulti"
PASSWORD_SERVERAPI ="01z4p"
#PASSWORD_SERVERAPI = "rd29012011"
HOST_SERVERAPI_GET ="*************"
PORT_SERVERAPI_GET ="2229"
USER_SERVERAPI_GET ="resulti"
PASSWORD_SERVERAPI_GET = "rd29012011"
DESTINO_SERVERAPI = "/var/www/consumer"
DESTINO_SANDBOX_SERVERAPI = "/var/www/sandbox-consumer"
DESTINO_DEV_SERVERAPI = "/var/www/dev-consumer"
DESTINO_CRM_SERVERAPI = "/var/www/crm-consumer"
# SERVIDORES DESTINO #################################