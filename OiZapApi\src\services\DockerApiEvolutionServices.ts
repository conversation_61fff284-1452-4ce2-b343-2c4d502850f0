import dotenv from 'dotenv';
import fs from 'fs-extra';
import yaml from 'js-yaml';
import { erroInterno, IRetorno, sucesso } from '../interfaces/IRetorno';
import { CloudFlareServices } from './CloudFlareServices';
import Logger from './Logger';
import { PortainerServices } from './PortainerServices';

const logger = Logger.getLogger();
dotenv.config();

// Função para ler e alterar o arquivo docker-compose.yml em memória
async function generateDockerCompose(establishmentName: string): Promise<string> {
  try {
    const subdomain = `${establishmentName}.oizap.com.br`;

    let rolePortainer = '';
    if (process.env.AMBIENTE == 'PROD') {
      rolePortainer = 'manager';
    } else {
      rolePortainer = 'OiZap-SandBox';
    }

    // Lê o arquivo docker-compose.yml
    const filePath = './utils/docker/evolutionv2.yml'; // Caminho do arquivo
    let fileContents = await fs.readFile(filePath, 'utf8');

    // Substitui as ocorrências de #nmEstabelecimento pelo nome do estabelecimento
    fileContents = fileContents.replace(/#nmEstabelecimento/g, establishmentName);
    fileContents = fileContents.replace(/#hostName/g, subdomain);
    fileContents = fileContents.replace(/#rolePortainer/g, rolePortainer);

    // Converte o conteúdo YAML para um objeto JS
    let yamlContent = yaml.load(fileContents) as unknown;

    // Converte de volta para YAML
    const updatedYaml = yaml.dump(yamlContent, { lineWidth: -1 });

    return updatedYaml; // Retorna o conteúdo modificado do YAML como uma string
  } catch (error) {
    logger.error('Erro ao ler ou modificar o arquivo docker-compose.yml: ' + JSON.stringify(error));
    throw erroInterno(error);
  }
}

export class DockerApiEvolutionServices {
  // Função para rodar o docker-compose com o arquivo modificado
  static async runDockerCompose(nmEstabelecimento: string): Promise<IRetorno> {
    try {
      let respDNS;
      let respStack;
      let establishmentName = nmEstabelecimento
        .toLowerCase()
        .replace(/\s/g, '')
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '');

      //Cria DNS
      respDNS = await CloudFlareServices.criarDominio(establishmentName);
      console.debug('runDockerCompose > respDNS' + JSON.stringify(respDNS));
      if (respDNS.statuscode > 200 && respDNS.statuscode != 409) {
        logger.error('Erro ao criar DNS ' + JSON.stringify(respDNS));
        return respDNS;
      } else if (respDNS.statuscode == 409) {
        respDNS = await CloudFlareServices.listaDominios(establishmentName + '.oizap.com.br');
        console.debug('runDockerCompose > respDNS' + JSON.stringify(respDNS));
      }

      // Modifica o arquivo docker-compose em memória
      const modifiedYaml = await generateDockerCompose(establishmentName);

      respStack = await PortainerServices.createStack(establishmentName, modifiedYaml);
      console.debug('runDockerCompose > respStack' + JSON.stringify(respStack));
      if (respStack.statuscode == 409) {
        respStack = await PortainerServices.getStacks(establishmentName);
        console.debug('runDockerCompose > respStack' + JSON.stringify(respStack));
      } else if (respStack.statuscode != 200) {
        return respStack;
      }

      const respDados = {
        id_host_cloudflare: respDNS.data[0].id,
        ds_hostapi: respDNS.data[0].name,
        id_stack: respStack.data[0].Id,
        nm_stack: respStack.data[0].Name,
      };

      //const tokenPortainer = await PortainerServices.logout(tokenPortainer.data[0].jwt);
      //console.log('tokenPortainer at line 49 in services/DockerApiEvolutionServices.ts:', tokenPortainer);
      return sucesso([respDados]);
    } catch (error) {
      logger.error('Erro ao rodar o docker-compose: ' + JSON.stringify(error));
      return erroInterno(error);
    }
  }

  static async getStacks(stackName: any): Promise<IRetorno> {
    try {
      const respStack = await PortainerServices.getStacks(stackName);
      return respStack;
    } catch (error) {
      logger.error('Erro ao rodar o docker-compose: ' + JSON.stringify(error));
      return erroInterno(error);
    }
  }
}
