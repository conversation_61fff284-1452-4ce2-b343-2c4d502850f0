import dotenv, { config } from 'dotenv';
import path from 'path';
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

import amqp, { Channel, Connection, Message } from 'amqplib';
import MessagesRabbitMQ from '../../models/MessagesRabbitMQ';
//import Messages from '../../models/Messages.js';

//import Bot from '../../models/Bot';
import Logger from '../../logs/Logger';
import { MessagesModel } from '../../models/MessagesModel';
import { BotServices } from '../BotServices';
const logger = Logger.getLogger();

export default class MessagesUpSertConsumer {
  private queueName: string;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private messagesHandler = new MessagesModel();
  //private botHandler = new Bot();
  private processedMessages = 1;
  private totalMessages = 0;
  private CONTINUE = false;

  constructor(queueName: string) {
    this.queueName = queueName;
  }

  private async getQueueMessageCount(channel: Channel, queueName: string): Promise<number> {
    const result = await channel.checkQueue(queueName);
    return result.messageCount;
  }

  private async ensureQueueExists(channel: Channel, queueName: string): Promise<void> {
    try {
      await channel.assertQueue(queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });
    } catch (error) {
      logger.error('Error ensuring queue exists:', error);
    }
  }

  public async connect(): Promise<void> {
    try {
      let room = this.queueName.replace('.messages.upsert', '');
      // if (process.env.AMBIENTE === 'DEV' && process.env.FILA_RABBITMQ_DEV === room) {
      //   this.CONTINUE = true;
      // } else if (process.env.AMBIENTE !== 'DEV' && process.env.FILA_RABBITMQ_DEV !== room) {
      //   this.CONTINUE = true;
      // } else {
      //   this.CONTINUE = false;
      // }

      if (
        process.env.AMBIENTE === 'DEV' &&
        (process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else if (
        process.env.AMBIENTE !== 'DEV' &&
        !(process.env.FILA_RABBITMQ_DEV || '') // Garante que não seja undefined
          .split(',')
          .map((s) => s.replace(/['"]/g, ''))
          .includes(room)
      ) {
        this.CONTINUE = true;
      } else {
        this.CONTINUE = false;
      }

      let HOST_RABBITMQ: string = '';

      if (process.env.AMBIENTE == 'PROD' || process.env.AMBIENTE === 'CRM') {
        HOST_RABBITMQ = process.env.HOST_RABBITMQ as string;
      } else {
        HOST_RABBITMQ = process.env.HOST_RABBITMQSANDBOX as string;
      }
      logger.debug('MessagesUpSertConsumer > HOST_RABBITMQ:' + HOST_RABBITMQ);

      this.connection = await amqp.connect(HOST_RABBITMQ);
      this.channel = await this.connection.createChannel();

      await this.channel.assertQueue(this.queueName, { durable: true, arguments: { 'x-queue-type': 'quorum' } });

      await this.ensureQueueExists(this.channel, this.queueName);

      const messageCount = await this.getQueueMessageCount(this.channel, this.queueName);
      this.totalMessages = messageCount;

      this.channel.prefetch(1);

      this.channel.consume(this.queueName, async (message: Message | null) => {
        if (!this.CONTINUE) {
          return;
        }

        if (message !== null) {
          const messageContent = JSON.parse(message.content.toString());
          logger.debug('MessagesUpSertConsumer > messageContent: ' + JSON.stringify(messageContent));
          //console.log('messageContent at line 67 in rabbitmq/MessagesUpSertConsumer.ts:', messageContent);
          const queue = this.queueName;

          if (queue.includes('messages.upsert')) {
            //console.log(' messageContent.data:', messageContent.data);
            if (
              messageContent.data.key.remoteJid.includes('status@broadcast') ||
              messageContent.data.key.remoteJid.includes('@g.us') ||
              messageContent.data.key.remoteJid.includes('@g.us')
            ) {
              if (this.channel) {
                this.channel.ack(message);
              }

              this.processedMessages++;
              return;
            }

            //console.log('messageContent at line 83 in rabbitmq/MessagesUpSertConsumer.ts:', messageContent);
            const messageRabbitMQ = new MessagesRabbitMQ(messageContent);
            //logger.debug('MessagesUpSertConsumer > messageRabbitMQ: ' + JSON.stringify(messageRabbitMQ));
            const processedData = await messageRabbitMQ.messageUpSert();
            //logger.debug('MessagesUpSertConsumer > processedData :' + JSON.stringify(processedData));

            try {
              //salva a mensagem no banco
              const respMessage = await this.messagesHandler.incluiMessages(processedData);
              //logger.debug('MessagesUpSertConsumer > incluiMessages > respMessage :' + JSON.stringify(respMessage));

              if (respMessage && respMessage.statuscode === 200) {
                const dataArray = respMessage.data as any[];
                let dataMensagem: any;
                if (dataArray.length > 0) {
                  dataMensagem = dataArray[0];
                  // processedData.data[0].atendimento = dataMensagem.atendimento;
                }

                //const messageJSON = JSON.stringify(processedData);
                //console.log('messageJSON at line 99 in rabbitmq/MessagesUpSertConsumer.ts:', messageJSON);
                // const messageBuffer = Buffer.from(messageJSON);
                //console.log('messageBuffer at line 100 in rabbitmq/MessagesUpSertConsumer.ts:', messageBuffer);

                if (this.channel) {
                  //AGORA QUE ENVIA PARA  A FILA DE PROCESSADOS É O METODO DE INCLUIMESSAGES
                  //ASSIM FUNCIONA PARA OUTRAS SITUAÇÕES
                  // this.channel.sendToQueue(`${this.queueName}.processed`, messageBuffer);
                  //console.log('line 102 in rabbitmq/MessagesUpSertConsumer.ts:  HABILITAR QUANDO PARAR DE DEBUGAR');
                  ///
                  /*
                  console.log(`ATENÇÃO
                   PARAMETRO MOCADO ===> this.channel.ack(message); <===
                   TEM QUE HABILITAR QUANDO PARAR DE TESTAR`);
                    */
                  this.channel.ack(message);
                }

                //console.log('dataMensagem :', dataMensagem);
                const respBot = await new BotServices().interpetra(dataMensagem);
                //console.log('respBot :', respBot);

                //console.log('dataMensagem at line 103 in rabbitmq/MessagesUpSertConsumer.ts:', dataMensagem);
                //const respBot = await this.botHandler.fluxoAtendimento(dataMensagem);
              } else {
                logger.error(`Error insert message PostgreSQL ${JSON.stringify(respMessage)}`);
                if (this.channel) {
                  this.channel.reject(message, false);
                }
              }
            } catch (error) {
              logger.error('Error handling the message:' + error);
              if (this.channel) {
                this.channel.reject(message, false);
              }
            }

            this.processedMessages++;
          }
        }
      });
    } catch (error) {
      logger.error('Error connecting to RabbitMQ-MessagesUpSert: ' + JSON.stringify(error));
    }
  }

  public getStats() {
    return {
      processedMessages: this.processedMessages,
      totalMessages: this.totalMessages,
    };
  }

  public close(): void {
    if (this.connection) {
      this.connection.close();
    }
  }
}
