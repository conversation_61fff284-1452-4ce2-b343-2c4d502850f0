import { Router } from 'express';
import { CampanhasController } from '../../controllers/campanhas/CampanhasController';
import authApi from '../../middleware/authApi';

export const CampanhasRoutes = Router();

CampanhasRoutes.post('/campanhas/v1', authApi, CampanhasController.incluir);
CampanhasRoutes.put('/campanhas/v1', authApi, CampanhasController.alterar);
CampanhasRoutes.get('/campanhas/v1', authApi, CampanhasController.listar);
CampanhasRoutes.get('/campanhas/capturar-clique/v1', CampanhasController.capturarClique);
CampanhasRoutes.delete('/campanhas/v1', authApi, CampanhasController.excluir);
