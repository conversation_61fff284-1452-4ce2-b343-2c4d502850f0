import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { AtendimentosModel } from '../../models/atendimentos/AtendimentosModel';

export class AtendimentosController {
  static async incluirAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      if (req.body.nr_telefone === undefined) {
        errors.push('O campo "nr_telefone" é obrigatório.');
      }
      if (req.body.ds_hash === undefined || req.body.ds_hash.trim() === '') {
        errors.push('O campo "ds_hash" é obrigatório.');
      }
      if (req.body.tp_status === undefined || req.body.tp_status.trim() === '') {
        errors.push('O campo "tp_status" é obrigatório.');
      }
      if (req.body.ds_contato === undefined || req.body.ds_contato.trim() === '') {
        errors.push('O campo "ds_contato" é obrigatório.');
      }
      if (req.body.tp_situacao === undefined || req.body.tp_situacao.trim() === '') {
        errors.push('O campo "tp_situacao" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new AtendimentosModel().incluirAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterarAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().alterarAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async atendido(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().atendido(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async novoAtendimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (req.body.cd_atendente === undefined) {
        errors.push('O campo "cd_atendente" é obrigatório.');
      }

      if (req.body.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }

      if (req.body.nr_telefone === undefined) {
        errors.push('O campo "nr_telefone" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().novoAtendimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async iniciarAtendimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (req.body.cd_atendente === undefined) {
        errors.push('O campo "cd_atendente" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().iniciarAtendimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async transferirAtendimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (req.body.cd_departamento === undefined) {
        errors.push('O campo "cd_departamento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().transferirAtendimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async encerraAtendimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }
      if (req.body.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().encerraAtendimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async statusAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.hash === undefined) {
        errors.push('O campo "hash" é obrigatório.');
      }

      if (req.body.status === undefined) {
        errors.push('O campo "status" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new AtendimentosModel().statusAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new AtendimentosModel().listarAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarAtendimentoPorTelefone(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.cd_estabelecimento === undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.query.telefone === undefined) {
        errors.push('O campo "telefone" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new AtendimentosModel().listarAtendimentoPorTelefone(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaAtendimentosHistorico(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.query.instance === undefined) {
        errors.push('O campo "instance" é obrigatório.');
      }
      if (req.query.telefone === undefined) {
        errors.push('O campo "telefone" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new AtendimentosModel().listaAtendimentosHistorico(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarAtendimentosFluxo(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new AtendimentosModel().listarAtendimentosFluxo(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async removerAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.cd_atendimento === undefined) {
        errors.push('O campo "cd_atendimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new AtendimentosModel().removerAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async stopAtendimentos(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new AtendimentosModel().stopAtendimentos(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaAtendimentoData(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new AtendimentosModel().listaAtendimentoData(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaAtendimentoHora(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new AtendimentosModel().listaAtendimentoHora(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
