import dotenv, { config } from 'dotenv';
import { Request } from 'express';
import path from 'path';
import { IRetorno, erroInterno, sucesso } from '../interfaces/IRetorno';
import Logger from '../services/Logger';
import { PostgreSQLServices } from '../services/PostgreSQLServices';
import { REDIS_TIMER, RedisServices } from '../services/RedisServices';
const logger = Logger.getLogger();
config();
// Carrega o .env com caminho explícito em desenvolvimento, mas usa o padrão em produção
const envPath = path.join(__dirname, '..', '.env'); // Garante que sobe exatamente um nível
if (process.env.NODE_ENV === 'PROD') {
  dotenv.config(); // Padrão em produção
} else {
  dotenv.config({ path: envPath, override: true }); // Força o .env correto em desenvolvimento
}

export class EstabelecimentoInstanciasDB {
  static async listarEstabelecimentoInstancias(req: Request): Promise<IRetorno> {
    try {
      return this.buscaEstabelecimentoInstancias(req.query.instance, req.query.nr_hash);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async buscaEstabelecimentoInstancias(instance: any, nr_hash: any): Promise<IRetorno> {
    try {
      //console.log('req.query at line 9 in data/EstabelecimentoInstanciasDB.ts:', req.query);
      let filtro;
      let chave;
      if (instance) {
        chave = `${instance}_${process.env.AMBIENTE}`;
        filtro = { instance: instance };
      } else {
        chave = `${nr_hash}_${process.env.AMBIENTE}`;
        filtro = { nr_hash: nr_hash };
      }

      // TENHO QUE PENSAR MELHOR NESSE PONTO, POIS NOS METODOS DE SALVAR E REMOVER O ESTABELECIMENTO EU TENHO
      // QUE REMOVER A ESSA KEY  DO REDIS

      // const keyRedis = `estabelecimento-instancia-valida-${JSON.stringify(filtro)}`;
      // const resultRedis = await new RedisServices().get(keyRedis);
      // if (resultRedis) return sucesso(resultRedis);

      const keyRedis = `${chave}:estabelecimento-instancia-valida:${JSON.stringify(filtro)}`;
      const resultRedis = await new RedisServices().get(keyRedis);
      if (resultRedis) return sucesso(resultRedis);

      let sql = `select e.cd_estabelecimento,ei.nr_hash,i.nameinstance,e.in_apidedicada,e.ds_hostapi
      --,(select ex.ds_hostapi from estabelecimento ex where ex.cd_estabelecimento = e.cd_estabelecimento and in_apidedicada = true limit 1) ds_hostapi 
,e.in_habilitaproxy,e.host_proxy,e.porta_proxy,e.username_proxy,e.password_proxy
 from estabelecimento e, estabelecimento_instancias ei,instances i 
 where ei.cd_estabelecimento = e.cd_estabelecimento and i.id = ei.id_instancia `;

      if (instance != undefined) {
        sql += ` and i.nameinstance='${instance}'`;
      } else {
        sql += ` and ei.nr_hash='${nr_hash}' `;
      }

      logger.debug('sql at line 44 in data/EstabelecimentoInstanciasDB.ts:' + JSON.stringify(sql));
      const result = await new PostgreSQLServices().query(sql);

      if (result.statuscode == 200) {
        await new RedisServices().set(keyRedis, result.data, REDIS_TIMER);
      }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
