import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { MotivosAtendimentoModel } from '../../models/motivos-atendimento/MotivosAtendimentoModel';

export class MotivosAtendimentoController {
  static async incluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.ds_motivo) {
        errors.push('O campo "ds_motivo" é obrigatório.');
      }
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (typeof req.body.in_ativo !== 'boolean') {
        errors.push('O campo "in_ativo" deve ser um booleano.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MotivosAtendimentoModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (!req.query.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MotivosAtendimentoModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async alterar(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_motivo) {
        errors.push('O campo "cd_motivo" é obrigatório.');
      }
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (!req.body.ds_motivo) {
        errors.push('O campo "ds_motivo" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MotivosAtendimentoModel().alterar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async excluir(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (!req.body.cd_motivo) {
        errors.push('O campo "cd_motivo" é obrigatório.');
      }
      if (!req.body.cd_estabelecimento) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new MotivosAtendimentoModel().excluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
