import { useRouter } from 'vue-router';
import hosts from '@/utils/hosts';

/**
 * Middleware de autorização para o frontend
 * Verifica se o usuário tem permissão para acessar a rota
 */
export function useAuthMiddleware() {
  const router = useRouter();

  /**
   * Verifica se o usuário está autenticado
   */
  const isAuthenticated = () => {
    const token = localStorage.getItem('token');
    return !!token;
  };

  /**
   * Verifica se o usuário tem permissão para acessar uma rota específica
   */
  const hasPermission = async (route) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return false;
      }

      // Fazer requisição para verificar permissão
      const response = await fetch(`${hosts.apiOiZap}/perfis/check-permission`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ route })
      });

      if (response.ok) {
        const result = await response.json();
        return result.hasPermission;
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar permissão:', error);
      return false;
    }
  };

  /**
   * Middleware para proteger rotas
   */
  const requireAuth = async (to, from, next) => {
    if (!isAuthenticated()) {
      // Redirecionar para login se não estiver autenticado
      next({ path: '/login', query: { redirect: to.fullPath } });
      return;
    }

    // Verificar permissão para a rota
    const hasAccess = await hasPermission(to.path);
    if (!hasAccess) {
      // Redirecionar para página de acesso negado
      next({ path: '/access-denied' });
      return;
    }

    next();
  };

  /**
   * Logout do usuário
   */
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  return {
    isAuthenticated,
    hasPermission,
    requireAuth,
    logout
  };
}

/**
 * Guarda de rota para verificar autenticação
 */
export function authGuard(to, from, next) {
  const { requireAuth } = useAuthMiddleware();
  return requireAuth(to, from, next);
} 