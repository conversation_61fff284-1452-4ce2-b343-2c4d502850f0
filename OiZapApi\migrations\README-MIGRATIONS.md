# 🚀 GUIA COMPLETO DE MIGRATIONS - Sistema Híbrido

## 🎯 **MÉTODO RECOMENDADO: MIGRATIONS SIMPLES** ⭐

### ✅ **Por que usar o método simples?**

- 🔥 **NUNCA trava** (diferente do Knex/node-pg-migrate)
- 🛡️ **Nunca dá erro** de tabela existente
- 🚀 **Execução automática** no deploy
- 📊 **Controle inteligente** - não repete migrations
- 🌍 **Multi-ambiente** - DEV, CRM, SANDBOX, PROD

Para usar corretamente o sistema:
Criar nova migration: npm run migration:create nome_da_migration
Executar migrations: npm run migration:run (ou :crm, :sandbox, :prod)
Comandos disponíveis:
migration:create - Cria novo arquivo SQL
migration:run - Executa migrations no DEV
migration:run:crm - Executa migrations no CRM
migration:run:sandbox - Executa migrations no SANDBOX
migration:run:prod - Executa migrations no PROD

### 🔧 **Como funciona:**

O sistema executa **SQL direto** durante o deploy, com controle próprio de execução:

```javascript
// simple-migrator.js
const migrations = [
  {
    name: 'create_perfis_table',
    sql: `
      CREATE TABLE IF NOT EXISTS perfis (
        id SERIAL PRIMARY KEY,
        cd_perfil INTEGER NOT NULL,
        nm_perfil VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_perfis_cd_perfil ON perfis(cd_perfil);
    `,
  },
  {
    name: 'create_perfis_telas_table',
    sql: `
      CREATE TABLE IF NOT EXISTS perfis_telas (
        id SERIAL PRIMARY KEY,
        cd_perfil INTEGER NOT NULL,
        cd_tela INTEGER NOT NULL,
        in_visualizar BOOLEAN DEFAULT TRUE,
        in_inserir BOOLEAN DEFAULT TRUE,
        in_alterar BOOLEAN DEFAULT TRUE,
        in_excluir BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_perfis_telas_cd_perfil ON perfis_telas(cd_perfil);
      CREATE INDEX IF NOT EXISTS idx_perfis_telas_cd_tela ON perfis_telas(cd_tela);
    `,
  },
];
```

### 📋 **Comandos Disponíveis (Método Simples)**

```bash
# Executar migrations simples por ambiente
npm run migrate:simple          # DEV
npm run migrate:simple:crm      # CRM
npm run migrate:simple:sandbox  # SANDBOX
npm run migrate:simple:prod     # PROD
```

### 🔄 **Execução Automática no Deploy**

O sistema **executa automaticamente** durante o deploy via `enviarServidor.js`:

```javascript
// Fluxo do Deploy:
1. Compilação do código TS → JS
2. Cópia dos arquivos para servidor
3. 🚀 Execução automática das migrations SQL
4. Reinicialização do PM2

// O SQL é executado diretamente no servidor:
psql "postgresql://postgres:senha@host:porta/database" -c "SQL_MIGRATIONS"
```

### ✅ **Como adicionar novas migrations:**

1. **Edite o arquivo `simple-migrator.js`**
2. **Adicione nova migration ao array:**

```javascript
const migrations = [
  // ... migrations existentes ...
  {
    name: 'create_nova_tabela', // Nome único
    sql: `
      CREATE TABLE IF NOT EXISTS nova_tabela (
        id SERIAL PRIMARY KEY,
        nome VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_nova_tabela_nome ON nova_tabela(nome);
    `,
  },
];
```

3. **Deploy normalmente** - a migration será executada automaticamente!

### 🛡️ **Sistema de Controle:**

```sql
-- Tabela criada automaticamente para controle
CREATE TABLE IF NOT EXISTS simple_migrations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cada migration verifica se já foi executada
IF NOT EXISTS (SELECT 1 FROM simple_migrations WHERE name = 'create_perfis_table') THEN
  -- Executa a migration
  CREATE TABLE perfis (...)
  -- Registra como executada
  INSERT INTO simple_migrations (name) VALUES ('create_perfis_table')
END IF
```

### 🎯 **Vantagens do Método Simples:**

| Característica             | Método Simples | node-pg-migrate | Knex |
| -------------------------- | -------------- | --------------- | ---- |
| **Nunca trava**            | ✅             | ❌              | ❌   |
| **Execução automática**    | ✅             | ⚠️              | ⚠️   |
| **Multi-ambiente**         | ✅             | ✅              | ✅   |
| **Controle de execução**   | ✅             | ✅              | ✅   |
| **Sintaxe simples**        | ✅             | ⚠️              | ⚠️   |
| **Sem problemas de ordem** | ✅             | ❌              | ❌   |

---

## 🔧 **MÉTODOS ALTERNATIVOS (node-pg-migrate)**

### 📋 Comandos Disponíveis

```bash
# Criar nova migration
npm run migrate:create nome_da_migration

# Executar migrations por ambiente
npm run migrate:dev        # DEV (manual)
npm run migrate:crm        # CRM (automático)
npm run migrate:sandbox    # SANDBOX (automático)
npm run migrate:prod       # PROD (automático)

# Comandos auxiliares
npm run migrate:up         # Executar próxima migration
npm run migrate:down       # Reverter última migration
```

## 🔧 SCHEMA BUILDER (parecido com Knex)

### ✅ Criar Tabela Básica

```javascript
exports.up = (pgm) => {
  pgm.createTable('usuarios', {
    id: 'id', // SERIAL PRIMARY KEY
    nome: { type: 'varchar(100)', notNull: true },
    email: { type: 'varchar(255)', notNull: true, unique: true },
    senha: { type: 'varchar(255)', notNull: true },
    ativo: { type: 'boolean', default: true },
    created_at: { type: 'timestamp', default: pgm.func('current_timestamp') },
    updated_at: { type: 'timestamp', default: pgm.func('current_timestamp') },
  });
};

exports.down = (pgm) => {
  pgm.dropTable('usuarios');
};
```

### ✅ Tipos de Campos Comuns

```javascript
exports.up = (pgm) => {
  pgm.createTable('produtos', {
    id: 'id', // AUTO INCREMENT PRIMARY KEY
    nome: { type: 'varchar(200)', notNull: true }, // VARCHAR(200) NOT NULL
    descricao: { type: 'text' }, // TEXT
    preco: { type: 'decimal(10,2)', notNull: true }, // DECIMAL(10,2) NOT NULL
    estoque: { type: 'integer', default: 0 }, // INTEGER DEFAULT 0
    peso: { type: 'float' }, // FLOAT
    ativo: { type: 'boolean', default: true }, // BOOLEAN DEFAULT TRUE
    data_cadastro: { type: 'date' }, // DATE
    created_at: { type: 'timestamp', default: pgm.func('current_timestamp') }, // TIMESTAMP
    config: { type: 'json' }, // JSON
    categoria_id: {
      // FOREIGN KEY
      type: 'integer',
      references: 'categorias(id)',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  });
};
```

### ✅ Adicionar Colunas

```javascript
exports.up = (pgm) => {
  pgm.addColumn('produtos', {
    codigo_barras: { type: 'varchar(50)', unique: true },
    observacoes: { type: 'text' },
    desconto_maximo: { type: 'decimal(5,2)', default: 0 },
  });
};

exports.down = (pgm) => {
  pgm.dropColumn('produtos', ['codigo_barras', 'observacoes', 'desconto_maximo']);
};
```

### ✅ Criar Índices

```javascript
exports.up = (pgm) => {
  // Índice simples
  pgm.createIndex('produtos', 'categoria_id');

  // Índice composto
  pgm.createIndex('produtos', ['nome', 'ativo']);

  // Índice único
  pgm.createIndex('produtos', 'codigo_barras', { unique: true });

  // Índice com nome customizado
  pgm.createIndex('produtos', 'created_at', { name: 'idx_produtos_data' });
};

exports.down = (pgm) => {
  pgm.dropIndex('produtos', 'categoria_id');
  pgm.dropIndex('produtos', ['nome', 'ativo']);
  pgm.dropIndex('produtos', 'codigo_barras');
  pgm.dropIndex('produtos', 'created_at', { name: 'idx_produtos_data' });
};
```

### ✅ Adicionar Constraints

```javascript
exports.up = (pgm) => {
  // Check constraint
  pgm.addConstraint('produtos', 'produtos_preco_check', 'CHECK (preco > 0)');

  // Unique constraint
  pgm.addConstraint('produtos', 'produtos_codigo_unique', 'UNIQUE (codigo_barras)');

  // Foreign key constraint
  pgm.addConstraint('produtos', 'produtos_categoria_fk', 'FOREIGN KEY (categoria_id) REFERENCES categorias(id)');
};

exports.down = (pgm) => {
  pgm.dropConstraint('produtos', 'produtos_preco_check');
  pgm.dropConstraint('produtos', 'produtos_codigo_unique');
  pgm.dropConstraint('produtos', 'produtos_categoria_fk');
};
```

### ✅ Alterar Colunas

```javascript
exports.up = (pgm) => {
  // Alterar tipo
  pgm.alterColumn('produtos', 'nome', { type: 'varchar(300)' });

  // Alterar para NOT NULL
  pgm.alterColumn('produtos', 'codigo_barras', { notNull: true });

  // Alterar default
  pgm.alterColumn('produtos', 'ativo', { default: false });

  // Renomear coluna
  pgm.renameColumn('produtos', 'descricao', 'descricao_produto');
};

exports.down = (pgm) => {
  pgm.alterColumn('produtos', 'nome', { type: 'varchar(200)' });
  pgm.alterColumn('produtos', 'codigo_barras', { notNull: false });
  pgm.alterColumn('produtos', 'ativo', { default: true });
  pgm.renameColumn('produtos', 'descricao_produto', 'descricao');
};
```

## 🔧 SQL PURO (mais flexível)

### ✅ Criar Tabela com Verificações

```javascript
exports.up = (pgm) => {
  pgm.sql(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'clientes') THEN
        CREATE TABLE clientes (
          id SERIAL PRIMARY KEY,
          nome VARCHAR(200) NOT NULL,
          email VARCHAR(255) UNIQUE,
          telefone VARCHAR(20),
          cpf_cnpj VARCHAR(20) UNIQUE,
          ativo BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS clientes_email_idx ON clientes(email);
        CREATE INDEX IF NOT EXISTS clientes_ativo_idx ON clientes(ativo);
        
        COMMENT ON TABLE clientes IS 'Tabela de clientes do sistema';
        COMMENT ON COLUMN clientes.cpf_cnpj IS 'CPF ou CNPJ do cliente';
        
        RAISE NOTICE 'Tabela clientes criada com sucesso';
      ELSE
        RAISE NOTICE 'Tabela clientes já existe, pulando criação';
      END IF;
    END
    $$;
  `);
};

exports.down = (pgm) => {
  pgm.sql('DROP TABLE IF EXISTS clientes CASCADE;');
};
```

### ✅ Adicionar Coluna com Verificação

```javascript
exports.up = (pgm) => {
  pgm.sql(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                     WHERE table_name = 'clientes' AND column_name = 'data_nascimento') THEN
        ALTER TABLE clientes ADD COLUMN data_nascimento DATE;
        RAISE NOTICE 'Coluna data_nascimento adicionada com sucesso';
      ELSE
        RAISE NOTICE 'Coluna data_nascimento já existe, pulando adição';
      END IF;
    END
    $$;
  `);
};

exports.down = (pgm) => {
  pgm.sql('ALTER TABLE clientes DROP COLUMN IF EXISTS data_nascimento;');
};
```

### ✅ Criar Índice com Verificação

```javascript
exports.up = (pgm) => {
  pgm.sql(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace 
                     WHERE c.relname = 'clientes_telefone_idx' AND n.nspname = 'public') THEN
        CREATE INDEX clientes_telefone_idx ON clientes(telefone);
        RAISE NOTICE 'Índice clientes_telefone_idx criado com sucesso';
      ELSE
        RAISE NOTICE 'Índice clientes_telefone_idx já existe, pulando criação';
      END IF;
    END
    $$;
  `);
};

exports.down = (pgm) => {
  pgm.sql('DROP INDEX IF EXISTS clientes_telefone_idx;');
};
```

### ✅ Adicionar Constraint com Verificação

```javascript
exports.up = (pgm) => {
  pgm.sql(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT 1 FROM information_schema.constraint_column_usage 
                     WHERE constraint_name = 'clientes_cpf_cnpj_unique') THEN
        ALTER TABLE clientes ADD CONSTRAINT clientes_cpf_cnpj_unique UNIQUE (cpf_cnpj);
        RAISE NOTICE 'Constraint clientes_cpf_cnpj_unique criada com sucesso';
      ELSE
        RAISE NOTICE 'Constraint clientes_cpf_cnpj_unique já existe, pulando criação';
      END IF;
    END
    $$;
  `);
};

exports.down = (pgm) => {
  pgm.sql('ALTER TABLE clientes DROP CONSTRAINT IF EXISTS clientes_cpf_cnpj_unique;');
};
```

## 🎯 EXEMPLOS PRÁTICOS COMPLETOS

### 📋 Migration de Pedidos

```javascript
exports.up = (pgm) => {
  // Criar tabela pedidos
  pgm.createTable('pedidos', {
    id: 'id',
    cliente_id: {
      type: 'integer',
      references: 'clientes(id)',
      onDelete: 'RESTRICT',
      notNull: true,
    },
    numero_pedido: { type: 'varchar(50)', notNull: true, unique: true },
    total: { type: 'decimal(10,2)', notNull: true },
    status: { type: 'varchar(20)', notNull: true, default: 'pendente' },
    data_pedido: { type: 'timestamp', default: pgm.func('current_timestamp') },
    observacoes: { type: 'text' },
  });

  // Criar índices
  pgm.createIndex('pedidos', 'cliente_id');
  pgm.createIndex('pedidos', 'status');
  pgm.createIndex('pedidos', 'data_pedido');
  pgm.createIndex('pedidos', 'numero_pedido', { unique: true });

  // Adicionar constraint
  pgm.addConstraint('pedidos', 'pedidos_total_check', 'CHECK (total > 0)');
  pgm.addConstraint(
    'pedidos',
    'pedidos_status_check',
    "CHECK (status IN ('pendente', 'confirmado', 'enviado', 'entregue', 'cancelado'))",
  );
};

exports.down = (pgm) => {
  pgm.dropTable('pedidos');
};
```

### 📋 Migration de Itens do Pedido

```javascript
exports.up = (pgm) => {
  pgm.createTable('pedido_itens', {
    id: 'id',
    pedido_id: {
      type: 'integer',
      references: 'pedidos(id)',
      onDelete: 'CASCADE',
      notNull: true,
    },
    produto_id: {
      type: 'integer',
      references: 'produtos(id)',
      onDelete: 'RESTRICT',
      notNull: true,
    },
    quantidade: { type: 'integer', notNull: true },
    preco_unitario: { type: 'decimal(10,2)', notNull: true },
    preco_total: { type: 'decimal(10,2)', notNull: true },
  });

  pgm.createIndex('pedido_itens', 'pedido_id');
  pgm.createIndex('pedido_itens', 'produto_id');
  pgm.createIndex('pedido_itens', ['pedido_id', 'produto_id'], { unique: true });

  pgm.addConstraint('pedido_itens', 'pedido_itens_quantidade_check', 'CHECK (quantidade > 0)');
  pgm.addConstraint('pedido_itens', 'pedido_itens_preco_check', 'CHECK (preco_unitario > 0 AND preco_total > 0)');
};

exports.down = (pgm) => {
  pgm.dropTable('pedido_itens');
};
```

## 🔗 RELACIONAMENTOS E FOREIGN KEYS

### ✅ Criar Tabelas com Relacionamentos

```javascript
exports.up = (pgm) => {
  // Tabela pai
  pgm.createTable('categorias', {
    id: 'id',
    nome: { type: 'varchar(100)', notNull: true, unique: true },
    descricao: { type: 'text' },
    ativo: { type: 'boolean', default: true },
  });

  // Tabela filha
  pgm.createTable('subcategorias', {
    id: 'id',
    categoria_id: {
      type: 'integer',
      references: 'categorias(id)',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
      notNull: true,
    },
    nome: { type: 'varchar(100)', notNull: true },
    ativo: { type: 'boolean', default: true },
  });

  // Índices para performance
  pgm.createIndex('subcategorias', 'categoria_id');
  pgm.createIndex('subcategorias', ['categoria_id', 'nome'], { unique: true });
};

exports.down = (pgm) => {
  pgm.dropTable('subcategorias');
  pgm.dropTable('categorias');
};
```

## 🚀 DICAS E BOAS PRÁTICAS

### ✅ Sempre use verificações em SQL puro:

```javascript
// ✅ CORRETO
pgm.sql(`
  DO $$
  BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'minha_tabela') THEN
      CREATE TABLE minha_tabela (...);
    END IF;
  END
  $$;
`);

// ❌ EVITE (pode dar erro se já existir)
pgm.sql('CREATE TABLE minha_tabela (...);');
```

### ✅ Use nomes descritivos para índices:

```javascript
// ✅ CORRETO
pgm.createIndex('produtos', 'categoria_id', { name: 'idx_produtos_categoria' });

// ❌ EVITE
pgm.createIndex('produtos', 'categoria_id');
```

### ✅ Sempre implemente rollback:

```javascript
exports.up = (pgm) => {
  pgm.createTable('teste', { id: 'id' });
};

exports.down = (pgm) => {
  pgm.dropTable('teste'); // Sempre implemente o rollback
};
```

### ✅ Use comentários para documentar:

```javascript
pgm.addComment('usuarios', 'Tabela de usuários do sistema');
pgm.addComment('usuarios', 'email', 'Email único do usuário');
```

## 🏆 QUANDO USAR CADA MÉTODO

### 🔧 **MÉTODO SIMPLES** - Use quando:

- ⭐ **Precisa que funcione sempre** (recomendado)
- 🚀 **Quer execução automática** no deploy
- 🛡️ **Não quer problemas** de ordem/sintaxe
- 🎯 **Operações básicas** (CREATE, INDEX, INSERT)

### 🔧 **SCHEMA BUILDER** - Use quando:

- Operações simples (criar tabela, adicionar coluna)
- Quer sintaxe parecida com Knex
- Precisa de rollback automático
- Quer validação de sintaxe

### 🔧 **SQL PURO** - Use quando:

- Precisa de verificações `IF NOT EXISTS`
- Operações complexas
- Quer mensagens de log personalizadas
- Precisa de máximo controle

---

## 📋 **RESUMO EXECUTIVO**

### 🚀 **RECOMENDAÇÃO PRINCIPAL:**

**Use o MÉTODO SIMPLES** para 90% dos casos. É mais confiável, automático e nunca trava.

### 🔧 **WORKFLOWS:**

**Para desenvolvimento rápido:**

```bash
1. Edite simple-migrator.js
2. npm run migrate:simple (teste local)
3. npm run deploy (execução automática)
```

**Para casos complexos:**

```bash
1. npm run migrate:create nome_migration
2. Edite arquivo gerado com SQL puro
3. npm run migrate:dev
```

---

**💡 Dica:** O **MÉTODO SIMPLES** resolve 90% dos problemas e nunca falha. Use-o como padrão!

---

**📚 Documentação oficial node-pg-migrate:** https://salsita.github.io/node-pg-migrate/
