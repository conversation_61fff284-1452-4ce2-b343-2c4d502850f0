import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  console.log('Starting migration to add ds_comentario to atendimentos table');

  try {
    const hasColumn = await knex.schema.hasColumn('atendimentos', 'ds_comentario');
    console.log('🚀 ds_comentario exists:', hasColumn);

    if (!hasColumn) {
      console.log('Adding ds_comentario column...');
      await knex.schema.alterTable('atendimentos', (table) => {
        table.text('ds_comentario').comment('Indica um comentário sobre o atendimento');
      });
      console.log('✅ ds_comentario added successfully');
    } else {
      console.log('ℹ️ ds_comentario already exists, skipping');
    }

    // SEMPRE retorna - sem transação manual
    return;
  } catch (error) {
    console.error('❌ Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    console.log('Dropping ds_comentario column...');
    await knex.schema.alterTable('atendimentos', (table) => {
      table.dropColumn('ds_comentario');
    });
    console.log('✅ ds_comentario dropped successfully');
  } catch (error) {
    console.error('❌ Erro no rollback:', error);
    throw error;
  }
}
