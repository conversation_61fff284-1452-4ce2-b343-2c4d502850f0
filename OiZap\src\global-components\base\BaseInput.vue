<!-- filepath: src/components/base/BaseInput.vue -->
<template>
    <div class="relative">
        <!-- Label -->
        <label v-if="label" :for="inputId" class="block text-sm font-medium text-gray-700 mb-2">
            {{ label }}
            <span v-if="required" class="text-red-500 ml-1">*</span>
        </label>

        <!-- Input Container -->
        <div class="relative">
            <!-- Icon Left -->

            <div
                v-if="$slots.iconLeft || iconLeft"
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
                <slot name="iconLeft">
                    <component :is="iconLeft" class="h-5 w-5 text-gray-400" />
                </slot>
            </div>

            <!-- Input -->
            <input
                :id="inputId"
                ref="inputRef"
                :type="type"
                :placeholder="placeholder"
                :required="required"
                :disabled="disabled"
                :readonly="readonly"
                :maxlength="maxlength"
                :minlength="minlength"
                :min="min"
                :max="max"
                :step="step"
                :value="modelValue"
                @input="handleInput"
                @blur="$emit('blur', $event)"
                @focus="$emit('focus', $event)"
                @keydown="$emit('keydown', $event)"
                @keyup="$emit('keyup', $event)"
                @change="$emit('change', $event)"
                autocomplete="one-time-code"
                autocorrect="off"
                autocapitalize="off"
                spellcheck="false"
                data-form-type="other"
                data-lpignore="true"
                data-1p-ignore="true"
                :name="randomName"
                role="textbox"
                aria-autocomplete="none"
                :class="[
                    'dark:bg-darkmode-800 dark:border-darkmode-400 dark:text-slate-300 block w-full border border-gray-200 rounded focus:ring-1 focus:ring-emerald-500 focus:border-transparent transition-all placeholder-gray-400 text-xs',
                    iconLeft || $slots.iconLeft ? 'pl-10' : withoutIconPadding ? 'pl-10' : 'pl-3',
                    iconRight || $slots.iconRight ? 'pr-10' : 'pr-3',
                    disabled ? 'bg-gray-100 cursor-not-allowed text-gray-500' : 'bg-white',
                    error
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',
                    size === 'sm' ? 'text-sm py-1.5' : size === 'lg' ? 'text-lg py-3' : 'text-sm py-2',
                ]"
            />

            <!-- Icon Right -->
            <div
                v-if="$slots.iconRight || iconRight"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                :class="{ 'pointer-events-none': !clickableIconRight }"
                @click="$emit('icon-click', $event)"
            >
                <slot name="iconRight">
                    <component :is="iconRight" class="h-5 w-5 text-gray-400" />
                </slot>
            </div>
        </div>

        <!-- Error message -->
        <p v-if="error" class="mt-1 text-sm text-red-600 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                />
            </svg>
            {{ error }}
        </p>

        <!-- Help text -->
        <p v-if="help && !error" class="mt-1 text-sm text-gray-500">
            {{ help }}
        </p>
    </div>
</template>

<script setup>
    import { ref, computed, onMounted } from 'vue';

    const props = defineProps({
        modelValue: {
            type: [String, Number],
            default: '',
        },
        type: {
            type: String,
            default: 'text',
        },
        label: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '',
        },
        required: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        error: {
            type: String,
            default: '',
        },
        help: {
            type: String,
            default: '',
        },
        maxlength: {
            type: [String, Number],
            default: null,
        },
        minlength: {
            type: [String, Number],
            default: null,
        },
        min: {
            type: [String, Number],
            default: null,
        },
        max: {
            type: [String, Number],
            default: null,
        },
        step: {
            type: [String, Number],
            default: null,
        },
        size: {
            type: String,
            default: 'md', // sm, md, lg
            validator: (value) => ['sm', 'md', 'lg'].includes(value),
        },
        iconLeft: {
            type: String,
            default: null,
        },
        iconRight: {
            type: String,
            default: null,
        },
        clickableIconRight: {
            type: Boolean,
            default: false,
        },
        withoutIconPadding: {
            type: Boolean,
            default: false,
            description: 'Adiciona padding esquerdo mesmo sem ícone',
        },
    });

    const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'keydown', 'keyup', 'change', 'icon-click']);

    const inputRef = ref(null);

    // Generate random name to prevent autocomplete
    const randomName = computed(() => `input_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`);

    // Generate unique ID
    const inputId = computed(() => `input_${Math.random().toString(36).substr(2, 9)}`);

    const handleInput = (event) => {
        emit('update:modelValue', event.target.value);
    };

    // Focus method (exposed)
    const focus = () => {
        inputRef.value?.focus();
    };

    // Blur method (exposed)
    const blur = () => {
        inputRef.value?.blur();
    };

    defineExpose({
        focus,
        blur,
        inputRef,
    });
</script>
