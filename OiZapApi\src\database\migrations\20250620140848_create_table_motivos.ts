import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    let hasColumn = await knex.schema.hasTable('motivos_atendimento');

    if (!hasColumn) {
      console.log('Tabela não existe, criando...');
      await knex.schema.createTable('motivos_atendimento', (table) => {
        table.increments('cd_motivo').primary();
        table.string('ds_motivo', 60).notNullable();
        table.boolean('in_ativo').defaultTo(true);
        table.integer('cd_usucad').nullable();
        table.timestamp('dt_cadastro').nullable();
        table.timestamps(true, true);
      });
    }
  } catch (error) {
    console.error('Erro na migration:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.dropTable('motivos_atendimento');
  } catch (error) {
    throw error;
  }
}
