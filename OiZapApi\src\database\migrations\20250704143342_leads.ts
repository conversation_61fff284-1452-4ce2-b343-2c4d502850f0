import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('leads', (table) => {
    table.increments('cd_lead').primary();
    table.integer('cd_disparo').unsigned().references('cd_disparo').inTable('disparos_pixel').onDelete('CASCADE');
    table.string('numero', 30).notNullable();
    table.string('mensagem', 500).notNullable();
    table.timestamp('data_mensagem').notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('leads');
}
