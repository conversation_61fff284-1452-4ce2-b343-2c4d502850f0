require('dotenv').config();
import { Request } from 'express';
import { erroInterno, IRetorno } from '../../interfaces/IRetorno';
import { OperationObject, PostgreSQLServices } from '../../services/PostgreSQLServices';
export class DepartamentosDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['departamentos'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      //console.log(opDb);
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async listar(req: Request): Promise<IRetorno> {
    try {
      let xSQL = `select * from departamentos where 1=1`;
      if (req.query.ds_departamento) {
        xSQL += ` and ds_departamento like '%${req.query.ds_departamento}%'`;
      }
      if (req.query.cd_departamento) {
        xSQL += ` and cd_departamento = ${req.query.cd_departamento}`;
      }
      xSQL += ` order by ds_departamento`;
      return await new PostgreSQLServices().query(xSQL);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['departamentos'],
        chaves: { cd_departamento: req.body.cd_departamento },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
  static async excluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['departamentos'],
        chaves: { cd_departamento: req.body.cd_departamento },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
