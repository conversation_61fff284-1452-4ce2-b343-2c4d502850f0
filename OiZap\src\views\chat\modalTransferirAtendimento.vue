<template>
    <ShowLoading ref="loading" />
    <Modal :show="modalTransferir" @hidden="closeModal">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Transferir Atendimento</h2>
        </ModalHeader>
        <ModalBody class="p-2">
            <!-- Campos fake para enganar o autocomplete -->
            <input type="text" style="position: absolute; left: -9999px; opacity: 0" autocomplete="username" />
            <input
                type="password"
                style="position: absolute; left: -9999px; opacity: 0"
                autocomplete="current-password"
            />

            <div class="mb-4">
                <label for="departamento" class="form-label">Transferir para o departamento</label>
                <Multiselect
                    class="-mt-2"
                    :customHeight="32"
                    v-model="formData.cd_departamento"
                    placeholder="Selecione o Departamento"
                    mode="single"
                    :close-on-select="false"
                    spellcheck="false"
                    :options="departamentos"
                    ref="selectDepartamento"
                    @emitEvent="selecionaDepartamento"
                />
            </div>

            <div class="mb-4">
                <label for="atendente" class="form-label">Transferir para o atendente </label>
                <label for="atendente" class="form-label text-xs text-slate-600 italic">(opcional)</label>

                <Multiselect
                    class="-mt-2"
                    :customHeight="32"
                    v-model="formData.cd_atendente"
                    placeholder="Selecione o Atendente"
                    mode="single"
                    :close-on-select="false"
                    :searchable="true"
                    :createOption="false"
                    :options="atendentes"
                    ref="selectAtendente"
                    @emitEvent="selecionaAtendente"
                    spellcheck="false"
                />
            </div>

            <div class="mb-1">
                <div class="flex items-center gap-1.5 w-max">
                    <label>Adicionar comentário</label>
                </div>
                <p class="text-xs text-slate-600 dark:text-slate-400 italic">Não será encaminhado para o cliente</p>

                <textarea
                    id="comentario"
                    class="form-control mt-1"
                    rows="4"
                    v-model="formData.ds_comentario"
                    placeholder="Digite um comentário sobre a transferência"
                ></textarea>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-between">
            <button type="button" class="btn btn-danger-soft w-24" @click="closeModal">Cancelar</button>
            <button type="button" class="btn btn-primary-soft w-24" @click="confirmarTransferencia">Confirmar</button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
    import { ref, defineExpose, onMounted, reactive, defineEmits } from 'vue';

    import AtendimentoServices from '@/services/chat/AtendimentoServices';
    // import AtendimentoHistoricosServices from '@/services/chat/AtendimentoHistoricosServices';
    import DepartamentosServices from '@/services/administracao/DepartamentosServices';
    import { useToast } from '@/global-components/toastify/useToast';
    import UsuariosServices from '../../services/administracao/UsuariosServices';

    const toast = useToast();
    const modalTransferir = ref(false);
    const selectedAtendimento = ref(null);

    const formData = reactive({
        cd_atendimento: undefined,
        cd_estabelecimento: undefined,
        cd_departamento: null,
        ds_departamento: undefined,
        cd_atendente: null,
        nm_atendente: undefined,
        ds_comentario: undefined,
        instance: undefined,
        nr_telefone: undefined,
        ds_nome: undefined,
        mensagem: undefined,
        url_profile_picture: undefined,
        cd_cliente: undefined,
        nm_cliente: undefined,
    });

    // Lista de departamentos (substitua com sua fonte de dados real)
    const departamentos = ref([]);
    // Lista de atendentes (substitua com sua fonte de dados real)
    const atendentes = ref([]);

    const emit = defineEmits(['transferenciaConfirmada']);

    // Método para desabilitar autocomplete após o modal abrir
    const disableAutocomplete = () => {
        setTimeout(() => {
            // Desabilita em todos os inputs do modal
            const modal = document.querySelector('.modal');
            if (modal) {
                const inputs = modal.querySelectorAll('input');
                inputs.forEach((input) => {
                    if (!input.style.position.includes('absolute')) {
                        // Não mexe nos campos fake
                        input.setAttribute('autocomplete', 'chrome-off');
                        input.setAttribute('data-form-type', 'other');
                        input.setAttribute('data-lpignore', 'true');
                        input.setAttribute('data-1p-ignore', 'true');
                        input.setAttribute('data-bwignore', 'true');
                        input.setAttribute('role', 'combobox');
                        input.removeAttribute('name');

                        // Trick para remover focus temporariamente
                        input.addEventListener('focus', (e) => {
                            e.target.setAttribute('readonly', 'readonly');
                            setTimeout(() => {
                                e.target.removeAttribute('readonly');
                            }, 100);
                        });
                    }
                });
            }
        }, 200);
    };

    const selecionaDepartamento = (selected) => {
        formData.cd_departamento = undefined;
        formData.ds_departamento = undefined;
        formData.cd_atendente = undefined;
        formData.nm_atendente = undefined;

        if (selected.event == 'Clear') {
            formData.cd_departamento = undefined;
            formData.ds_departamento = undefined;
            formData.cd_atendente = undefined;
            formData.nm_atendente = undefined;
            atendentes.value = [];
        } else if (selected.event == 'Select') {
            formData.cd_departamento = selected.select.value;
            formData.ds_departamento = selected.select.label;
            carregaAtendentes(formData.cd_departamento);
        }
    };
    const selecionaAtendente = (selected) => {
        formData.cd_atendente = undefined;
        formData.nm_atendente = undefined;

        if (selected.event == 'Clear') {
            formData.cd_atendente = undefined;
            formData.nm_atendente = undefined;
        } else if (selected.event == 'Select') {
            formData.cd_atendente = selected.select.value;
            formData.nm_atendente = selected.select.label;
        }
    };

    // Carregar departamentos e atendentes
    const carregaDepartamentos = async () => {
        try {
            let result = await DepartamentosServices.listar();
            //console.log('result at line 1400 in chat/Chat.vue:', result);
            if (result.statuscode == 200) {
                departamentos.value = result.data.map((item) => ({
                    value: item.cd_departamento,
                    label: item.ds_departamento,
                }));
            } else if (result.statuscode == 404) {
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        }
    };

    const carregaAtendentes = async (cdDepartamento) => {
        try {
            atendentes.value = []; // Limpa a lista de atendentes antes de carregar novos dados
            let result = await UsuariosServices.listaUsuariosPorDepartamento({ cd_departamento: cdDepartamento });

            if (result.statuscode == 200) {
                atendentes.value = result.data.map((item) => ({
                    value: item.cd_usuario,
                    label: item.ds_nome,
                }));
            } else if (result.statuscode == 404) {
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        }
    };

    async function abreModalTransferir(atendimento) {
        selectedAtendimento.value = atendimento;
        console.log('atendimento:', atendimento);
        // atendimentoAtual.value = atendimento;
        atendentes.value = [];
        departamentos.value = [];
        formData.cd_atendimento = atendimento.cd_atendimento;
        formData.cd_estabelecimento = atendimento.cd_estabelecimento;
        formData.ds_comentario = atendimento.ds_comentario;
        formData.instance = atendimento.instance;
        formData.nr_telefone = atendimento.telefone;
        formData.ds_nome = atendimento.nome;
        formData.url_profile_picture = atendimento.url_profile_picture;
        formData.cd_cliente = atendimento.cd_cliente;
        formData.nm_cliente = atendimento.nm_cliente;
        formData.mensagem = atendimento.mensagem;

        // Carrega os dados dos selects
        await carregaDepartamentos();

        formData.cd_departamento = atendimento.cd_departamento;
        formData.ds_departamento = atendimento.ds_departamento;
        if (atendimento.cd_departamento) {
            await carregaAtendentes(atendimento.cd_departamento);
        }
        formData.cd_atendente = atendimento.cd_atendente;
        formData.nm_atendente = atendimento.nm_atendente;

        modalTransferir.value = true;

        // Múltiplas tentativas para desabilitar
        disableAutocomplete();
        setTimeout(disableAutocomplete, 300);
        setTimeout(disableAutocomplete, 500);
    }

    // Função para fechar o modal
    const closeModal = () => {
        modalTransferir.value = false;
    };

    // Função para salvar a transferência
    const confirmarTransferencia = async () => {
        try {
            if (!formData.cd_departamento) {
                // Validar se pelo menos o departamento foi selecionado
                toast.warning('Selecione um departamento para transferir');
                return;
            }

            let isAlterado = false;
            if (formData.cd_departamento != selectedAtendimento.value.cd_departamento) {
                isAlterado = true;
            }
            if (formData.cd_atendente != selectedAtendimento.value.cd_atendente) {
                isAlterado = true;
            }
            if (formData.ds_comentario != selectedAtendimento.value.ds_comentario) {
                isAlterado = true;
            }

            if (!isAlterado) {
                closeModal();
                return;
            }

            let result = await AtendimentoServices.transferir(formData);
            //console.log('result at line 1400 in chat/Chat.vue:', result);
            if (result.statuscode == 200) {
                toast.success('Atendimento transferido com sucesso!');
                emit('transferenciaConfirmada', formData);
                closeModal();
            } else if (result.statuscode == 404) {
                toast.warning(result.message);
            } else {
                toast.error(result.message);
            }

            // Emitir evento de sucesso ou recarregar atendimentos
        } catch (error) {
            toast.error('Erro ao transferir atendimento:' + error);
        }
    };

    defineExpose({
        abreModalTransferir,
    });

    onMounted(() => {
        // Carregar dados iniciais se necessário
    });
</script>
