import { Request, Response } from 'express';
import { BAD_REQUEST, erroInterno, INTERNAL_SERVER_ERROR, parametrosInvalidos } from '../../interfaces/IRetorno';
import { EstabelecimentoModel } from '../../models/estabelecimento/EstabelecimentoModel';

export class EstabelecimentoController {
  static async incluirEstabelecimentoIntegracao(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.nm_estabelecimento === undefined && req.body.nm_estabelecimento == '') {
        errors.push('nm_estabelecimento não pode ser nulo');
      }
      if (req.body.nr_cnpj === undefined && req.body.nr_cnpj == '') {
        errors.push('nr_cnpj não pode ser nulo');
      }
      if (req.body.ds_email === undefined && req.body.ds_email == '') {
        errors.push('ds_email não pode ser nulo');
      }
      if (req.body.ds_hostentregadochef === undefined && req.body.ds_hostentregadochef == '') {
        errors.push('ds_hostentregadochef não pode ser nulo');
      }
      if (req.body.ds_endereco === undefined && req.body.ds_endereco == '') {
        errors.push('ds_endereco não pode ser nulo');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().incluirEstabelecimentoIntegracao(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async incluirEstabelecimentoCadastro(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.nm_estabelecimento === undefined && req.body.nm_estabelecimento == '') {
        errors.push('nm_estabelecimento não pode ser nulo');
      }
      if (req.body.ds_email === undefined && req.body.ds_email == '') {
        errors.push('ds_email não pode ser nulo');
      }
      if (req.body.nm_modulo === undefined && req.body.nm_modulo == '') {
        errors.push('nm_modulo não pode ser nulo');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().incluirEstabelecimentoCadastro(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async incluirEstabelecimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.nm_estabelecimento === undefined && req.body.nm_estabelecimento == '') {
        errors.push('nm_estabelecimento não pode ser nulo');
      }
      if (req.body.nr_cnpj === undefined && req.body.nr_cnpj == '') {
        errors.push('nr_cnpj não pode ser nulo');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().incluirEstabelecimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async alterarEstabelecimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.cd_estabelecimento == undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (req.body.nm_estabelecimento === undefined && req.body.nm_estabelecimento == '') {
        errors.push('nm_estabelecimento não pode ser nulo');
      }
      if (req.body.nr_cnpj === undefined && req.body.nr_cnpj == '') {
        errors.push('nr_cnpj não pode ser nulo');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().alterarEstabelecimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async criarApiDedicada(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.nm_estabelecimento === undefined && req.body.nm_estabelecimento == '') {
        errors.push('nm_estabelecimento não pode ser nulo');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().criarApiDedicada(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarEstabelecimento(req: Request, res: Response): Promise<Response> {
    try {
      //console.log('req:', req.query);
      const result = await new EstabelecimentoModel().listarEstabelecimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listarEstabelecimentoIntegracao(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new EstabelecimentoModel().listarEstabelecimentoIntegracao(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async removerEstabelecimento(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.cd_estabelecimento == undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().removerEstabelecimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async limpaDados(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];

      if (req.body.cd_estabelecimento == undefined) {
        errors.push('O campo "cd_estabelecimento" é obrigatório.');
      }
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }
      const result = await new EstabelecimentoModel().limpaDados(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaEstabelecimentoDirect(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      if (req.body.ds_server === undefined && req.body.ds_server == '') {
        errors.push('ds_server não pode ser nulo');
      }

      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new EstabelecimentoModel().listaEstabelecimentoDirect(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
  static async listaInstanciasPorEstabelecimento(req: Request, res: Response): Promise<Response> {
    try {
      // const errors: string[] = [];
      // if (req.body.ds_server === undefined && req.body.ds_server == '') {
      //   errors.push('ds_server não pode ser nulo');
      // }

      // if (errors.length > 0) {
      //   return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      // }

      const result = await new EstabelecimentoModel().listaInstanciasPorEstabelecimento(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
