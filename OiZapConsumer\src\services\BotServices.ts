import { AtendimentosDB } from '../data/AtendimentosDB';
import { InstanceDB } from '../data/InstanceDB';
//import { InstanceDB } from '../data/InstanceDB';
import { DepartamentosDB } from '../data/DepartamentosDB';
import { EstabelecimentosDB } from '../data/EstabelecimentosDB';
import { FluxoMensagensDB } from '../data/FluxoMensagensDB';
import MessagesDB from '../data/MessagesDB';
import { IRetorno, erroInterno, naoAutenticado, sucesso } from '../interfaces/IRetorno';
import { MessagesModel } from '../models/MessagesModel';
import { WhatsAppModel } from '../models/WhatsAppModel';
import { Funcoes } from './Funcoes';
import Logger from './Logger';
import NLP from './NLP';
import { emitToRoom } from './Socket';

//import { emitToRoom } from './Socket';

const logger = Logger.getLogger();

interface Dados {
  cd_atendimento?: number;
  cd_estabelecimento: number;
  telefone: string;
  nome: string;
  cd_cliente?: number;
  url_profile_picture?: string;
  profile_picture_base64?: string;
  message_id?: string;
  in_stop_bot_geral?: boolean;
  in_stop_bot?: boolean;
  mensagem?: string;
  ds_hash?: string;
  tp_status_atendimento?: string;
  tp_situacao_atendimento?: string;
  tp_situacao_pedido?: string;
  instance?: string;
  origem?: string;
  from_me?: boolean;
  nm_cliente?: string;
  tp_etapachat?: string;
  in_forahorario?: boolean;
  cd_departamento?: number;
  ds_departamento?: string;
  cd_atendente?: number;
  nm_atendente?: string;
}
// Função para converter horário HH:MM:SS para minutos desde a meia-noite
function horarioParaMinutos(horario: string): number {
  const [horas, minutos, segundos = 0] = horario.split(':').map(Number);
  return horas * 60 + minutos + segundos / 60;
}

// Função para adicionar 1 minuto ao horário
function adicionarMinuto(horario: string): string {
  const [horas, minutos, segundos = '00'] = horario.split(':');
  const date = new Date();
  date.setHours(parseInt(horas), parseInt(minutos), parseInt(segundos));
  date.setMinutes(date.getMinutes() + 1);

  return date.toTimeString().slice(0, 8);
}

export class BotServices {
  constructor() {
    // this.io = global.io;
  }

  async verificaHorarioAtendimento(dados: Dados): Promise<any> {
    let retorno: any = {
      foraHorario: false,
      mensagem_enviada: undefined,
      tipo: undefined,
      horario_funcionamento: undefined,
      mensagem: undefined,
    };

    try {
      const respForaHorario = await InstanceDB.getMensagemForaHorario(dados.cd_estabelecimento);

      if (respForaHorario.statuscode === 200) {
        const agora = new Date();
        const diaAtual = agora.getDay(); // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
        const horaAtual = agora.toTimeString().slice(0, 8); // Formato HH:MM:SS

        // Verificar horário do dia atual
        let horarioHoje = respForaHorario.data.find((horario: any) => horario.nr_diasemana == diaAtual);

        // Se não encontrou horário para hoje, ou se o horário atual é muito cedo (antes das 6h),
        // verificar se estamos no final do expediente do dia anterior
        if (!horarioHoje || horaAtual < '06:00:00') {
          const diaAnterior = diaAtual === 0 ? 6 : diaAtual - 1; // Se for domingo (0), dia anterior é sábado (6)
          const horarioDiaAnterior = respForaHorario.data.find((horario: any) => horario.nr_diasemana == diaAnterior);

          // Se o dia anterior tem horário que passa da meia-noite
          if (horarioDiaAnterior && horarioDiaAnterior.hr_final < horarioDiaAnterior.hr_inicial) {
            horarioHoje = horarioDiaAnterior;
          }
        }

        if (horarioHoje) {
          let dentroHorario;
          const horarioFinalAjustado = adicionarMinuto(horarioHoje.hr_final);

          // Tratamento especial para horários que passam da meia-noite
          if (horarioHoje.hr_final < horarioHoje.hr_inicial) {
            // Caso especial: horário final é menor que inicial (passa pela meia-noite)
            // Se estamos no mesmo dia do início do expediente
            if (diaAtual === horarioHoje.nr_diasemana) {
              dentroHorario = horaAtual >= horarioHoje.hr_inicial;
            } else {
              // Se estamos no dia seguinte (após meia-noite)
              dentroHorario = horaAtual < horarioFinalAjustado;
            }
          } else {
            // Caso normal: horário final é maior que inicial
            dentroHorario = horaAtual >= horarioHoje.hr_inicial && horaAtual < horarioFinalAjustado;
          }

          if (!dentroHorario) {
            // Está fora do horário - disparar mensagem personalizada
            const mensagemForaHorario = horarioHoje.ds_mensagem
              .replace('{horarioInicial}', horarioHoje.hr_inicial.slice(0, 5))
              .replace('{horarioFinal}', horarioHoje.hr_final.slice(0, 5));

            // Montar dados para envio da mensagem
            const dadosEnvio = {
              instance: dados.instance,
              telefone: dados.telefone,
              mensagem: mensagemForaHorario,
              cd_estabelecimento: dados.cd_estabelecimento,
            };

            const query = {
              cd_atendimento: dados.cd_atendimento,
              cd_estabelecimento: dados.cd_estabelecimento,
            };

            //console.log('req at line 71 in services/BotServices.ts:', req);
            let respAtendimento = await AtendimentosDB.consultaAtendimento(query);

            if (respAtendimento.statuscode == 200 && respAtendimento.data.length > 0) {
              dados.in_forahorario = respAtendimento.data[0].in_forahorario;
            }

            //console.log('dadosEnvio at line 187 in services/BotServices.ts:', dadosEnvio);

            // Retornar sem processar mais o bot
            retorno = {
              foraHorario: true,
              mensagem_enviada: true,
              tipo: 'fora_horario',
              horario_funcionamento: `${horarioHoje.hr_inicial} - ${horarioHoje.hr_final}`,
              mensagem: mensagemForaHorario,
            };

            if (!dados.in_forahorario) {
              const body = {
                cd_atendimento: dados.cd_atendimento,
                in_forahorario: true,
                cd_estabelecimento: dados.cd_estabelecimento,
              };

              //console.log('req at line 71 in services/BotServices.ts:', req);
              respAtendimento = await AtendimentosDB.alterarAtendimentos(body);
              // Enviar mensagem de fora do horário após 5 segundos
              setTimeout(async () => {
                const resultEnvio = await new WhatsAppModel().sendMessage(dadosEnvio);

                // MessagesDB.incluiMessages(dados,undefined)
                const payloadData = {
                  data: [
                    {
                      instance: dados.instance,
                      event: 'messages.upsert',
                      nome: dados.nome,
                      from_me: true,
                      type_message: 'textMessage',
                      mensagem: mensagemForaHorario,
                      message_timestamp: Funcoes.dateTimestamp('datetime'),
                      horario: Funcoes.dateTimestamp('time'),
                      telefone: dados.telefone,
                      remote_jid: dados.telefone + '@s.whatsapp.net',
                      cd_estabelecimento: dados.cd_estabelecimento,
                      in_stop_bot_geral: false,
                      cd_atendimento: dados.cd_atendimento,
                      cd_cliente: dados.cd_cliente,
                      nm_cliente: dados.nm_cliente,
                      ds_hash: dados.ds_hash,
                      in_stop_bot: false,
                      tp_status_atendimento: dados.tp_status_atendimento,
                      tp_situacao_atendimento: dados,
                      tp_etapachat: dados,
                      origem: 'bot',
                      tp_status: resultEnvio.statuscode == 200 ? 'Enviado' : 'Erro',
                    },
                  ],
                };

                await new MessagesModel().incluiMessages(payloadData, undefined);

                // O código que vem abaixo (construção do payloadData) deve ser movido
                // para dentro do setTimeout se precisar usar o resultEnvio
              }, 3000); // 3000 milissegundos = 3 segundos
            }
          }
        }
      }
      return retorno;
    } catch (error) {
      return retorno;
    }
  }

  async interpetra(dados: Dados): Promise<IRetorno> {
    // console.log('dados at line 35 in services/BotServices.ts:', dados);
    try {
      if (dados.cd_atendimento === undefined) {
        const respAtendimento = await AtendimentosDB.listarAtendimentos({
          cd_estabelecimento: dados.cd_estabelecimento,
          telefone: dados.telefone,
          instance: dados.instance,
        });
        // console.log('respAtendimento at line 217 in services/BotServices.ts:', respAtendimento);
        if (respAtendimento.statuscode == 200) {
          dados.cd_atendimento = respAtendimento.data[0].cd_atendimento;
          dados.ds_hash = respAtendimento.data[0].ds_hash;
          dados.in_stop_bot = respAtendimento.data[0].in_stop_bot;
          dados.tp_status_atendimento = respAtendimento.data[0].tp_status;
          dados.tp_etapachat = respAtendimento.data[0].tp_etapachat;
          dados.in_forahorario = respAtendimento.data[0].in_forahorario;
          dados.cd_departamento = respAtendimento.data[0].cd_departamento;
          dados.ds_departamento = respAtendimento.data[0].ds_departamento;
          dados.cd_cliente = respAtendimento.data[0].cd_cliente;
          dados.nm_cliente = respAtendimento.data[0].nm_cliente;
          dados.cd_atendente = respAtendimento.data[0].cd_atendente;
          dados.nm_atendente = respAtendimento.data[0].nm_atendente;
        }
      }

      let req: any;
      let dataMensagem: any;
      let tpFuncionalidade: string;
      let nome;
      if (dados.origem == 'bot') {
        if (dados.nm_cliente != '' || dados.nome != undefined) {
          nome = dados.nm_cliente;
        } else {
          nome = dados.nome;
        }
      } else {
        nome = dados.nome;
      }

      if (nome == '') {
        nome = dados.nm_cliente;
      }
      if (nome == '') {
        nome = dados.nome;
      }

      const respModulosAtivos = await EstabelecimentosDB.modulosAtivos({
        nameinstance: dados.instance,
      });

      const inCRM = respModulosAtivos.statuscode == 200 ? respModulosAtivos.data.in_crm : false;
      let cdDepartamentoSelecionado = undefined;
      let dsDepartamentoSelecionado = undefined;
      if (inCRM && dados.tp_status_atendimento == 'Fila') {
        const respAtendimento = await FluxoMensagensDB.listarFluxoAtendimentos({
          cd_estabelecimento: dados.cd_estabelecimento,
          ds_token: dados.mensagem,
        });
        if (respAtendimento.statuscode == 200 && respAtendimento.data.length > 0) {
          const respDepartamento = await DepartamentosDB.listar({
            cd_departamento: respAtendimento.data[0].cd_departamento,
          });

          cdDepartamentoSelecionado = respAtendimento.data[0].cd_departamento;
          dsDepartamentoSelecionado =
            respDepartamento.statuscode == 200 ? respDepartamento.data[0].ds_departamento : undefined;
        }
        // console.log('🚀 ~ BotServices.ts:226 ~ interpetra ~ respAtendimento:', respAtendimento);
      }

      if (dados.cd_atendimento === undefined) {
        const respAtendimento = await AtendimentosDB.listarAtendimentos({
          cd_estabelecimento: dados.cd_estabelecimento,
          telefone: dados.telefone,
        });
        // console.log('respAtendimento at line 217 in services/BotServices.ts:', respAtendimento);
        if (respAtendimento.statuscode == 200) {
          dados.cd_atendimento = respAtendimento.data[0].cd_atendimento;
          dados.ds_hash = respAtendimento.data[0].ds_hash;
          dados.in_stop_bot = respAtendimento.data[0].in_stop_bot;
          dados.tp_status_atendimento = respAtendimento.data[0].tp_status;
          dados.tp_etapachat = respAtendimento.data[0].tp_etapachat;
          dados.in_forahorario = respAtendimento.data[0].in_forahorario;
          dados.cd_departamento =
            respAtendimento.data[0].cd_departamento == undefined
              ? cdDepartamentoSelecionado
              : respAtendimento.data[0].cd_departamento;
          dados.ds_departamento =
            respAtendimento.data[0].ds_departamento == undefined
              ? dsDepartamentoSelecionado
              : respAtendimento.data[0].ds_departamento;
          dados.cd_cliente = respAtendimento.data[0].cd_cliente;
          dados.nm_cliente = respAtendimento.data[0].nm_cliente;
          dados.cd_atendente = respAtendimento.data[0].cd_atendente;
          dados.nm_atendente = respAtendimento.data[0].nm_atendente;
        }
      } else {
        if (inCRM && dados.tp_status_atendimento == 'Fila' && cdDepartamentoSelecionado != undefined) {
          req = {
            cd_atendimento: dados.cd_atendimento,
            cd_estabelecimento: dados.cd_estabelecimento,
            cd_departamento: cdDepartamentoSelecionado,
          };

          //console.log('req at line 71 in services/BotServices.ts:', req);
          await AtendimentosDB.alterarAtendimentos(req);
          // console.log('🚀 ~ BotServices.ts:306 ~ interpetra ~ respAtendimento:', respAtendimento);
        }
      }

      if (dados.cd_atendimento === undefined) {
        tpFuncionalidade = 'Inicio';
        req = {
          instance: dados.instance,
          telefone: dados.telefone,
          ds_contato: nome, //dados.nome,
          tp_status: inCRM ? 'Fila' : 'Em Atendimento',
          tp_funcionalidade: 'Inicio',
          cd_cliente: dados.cd_cliente === 0 ? undefined : dados.cd_cliente,
          ds_cliente: nome,
          ds_foto: dados.url_profile_picture,
          profile_picture_base64: dados.profile_picture_base64,
          cd_estabelecimento: dados.cd_estabelecimento,
          message_id: dados.message_id,
          cd_departamento: dados.cd_departamento,
        };

        //console.log('req at line 71 in services/BotServices.ts:', req);
        const respAtendimento = await AtendimentosDB.incluirAtendimento(req);
        //console.log('respAtendimento at line 71 in services/BotServices.ts:', respAtendimento);
        //  logger.debug('respAtendimento ' + JSON.stringify(respAtendimento));

        if (respAtendimento.statuscode === 200) {
          req.hash = respAtendimento.data[0].ds_hash;
          dados.ds_hash = respAtendimento.data[0].ds_hash;
          dados.cd_atendimento = respAtendimento.data[0].cd_atendimento;
          dados.cd_cliente = respAtendimento.data[0]?.cd_cliente;
          dados.in_stop_bot = respAtendimento.data[0]?.in_stop_bot;
          dados.tp_status_atendimento = respAtendimento.data[0]?.tp_status;

          const respLast = await MessagesDB.updateLastMessage({
            instance: dados.instance,
            telefone: dados.telefone,
            mensagem: dados.mensagem,
            cd_atendimento: respAtendimento.data[0].cd_atendimento,
            nome: nome,
            from_me: dados.origem == 'bot' ? true : false,
          });
          //console.log('🚀 ~ BotServices.ts:302 ~ interpetra ~ respLast:', respLast);

          // emitToRoom(req.instance, 'status.atendimento', dados);
        }

        const respForaHorario = await this.verificaHorarioAtendimento(dados);
        if (respForaHorario.foraHorario) {
          return sucesso([respForaHorario]);
        }
        if (dados.in_stop_bot_geral === true) {
          return naoAutenticado('Bot Geral parado', [{ atendimento: dados }]);
        }
      } else {
        const respForaHorario = await this.verificaHorarioAtendimento(dados);
        if (respForaHorario.foraHorario) {
          return sucesso([respForaHorario]);
        }

        if (dados.in_stop_bot_geral === true || dados.in_stop_bot === true) {
          return naoAutenticado('Bot Geral parado', [{ atendimento: dados }]);
        }

        req = {
          instance: dados.instance,
          telefone: dados.telefone,
          ds_contato: nome,
          hash: dados.ds_hash,
          tp_status: dados.mensagem,
          tp_funcionalidade: dados.mensagem,
          ds_cliente: nome,
          cd_estabelecimento: dados.cd_estabelecimento,
          tp_etapachat: dados.tp_etapachat,
        };
        // console.log('req at line 106 in services/BotServices.js:', req);
      }

      const respTokens = await new NLP().tokenizar(dados);
      // logger.debug('respTokens at line 88 in services/BotServices.js:' + JSON.stringify(respTokens));

      if (respTokens.statuscode === 200) {
        req.nr_controle = respTokens.data[0].nr_contfluxoatendimento;
        req.tp_funcionalidade = undefined;
      }

      if (inCRM && dados.tp_status_atendimento == 'Fila' && cdDepartamentoSelecionado) {
        const pay = {
          event: 'status.atendimento',
          nr_telefone: dados.telefone,
          instance: dados.instance,
          cd_atendimento: dados.cd_atendimento,
          tp_status_atendimento: dados?.tp_status_atendimento,
          tp_situacao_atendimento: dados?.tp_situacao_atendimento,
          tp_situacao_pedido: dados?.tp_situacao_pedido,
          tp_etapachat: dados?.tp_etapachat,
          in_stop_bot: dados.in_stop_bot,
          ds_hash: dados.ds_hash,
          cd_estabelecimento: dados.cd_estabelecimento,
          cd_departamento: cdDepartamentoSelecionado,
          ds_departamento: dsDepartamentoSelecionado,
        };
        console.log('🚀 ~ BotServices.ts:402 ~ interpetra ~ pay:', pay);
        emitToRoom(req.instance, 'status.atendimento', pay);
      }

      req.hash = dados.ds_hash;
      req.cd_atendimento = dados.cd_atendimento;
      req.cd_cliente = dados?.cd_cliente;
      req.tp_status_atendimento = dados?.tp_status_atendimento;
      req.tp_situacao_atendimento = dados?.tp_situacao_atendimento;
      req.tp_situacao_pedido = dados?.tp_situacao_pedido;
      req.tokens = respTokens.data;
      req.tp_etapachat = dados?.tp_etapachat;

      const reqBody = {
        body: req,
      };

      //console.log('dataMensagem at line 144 in services/BotServices.ts:', dataMensagem);
      const result = await new WhatsAppModel().enviarMensagem(reqBody);
      // console.log('result at line 141 in services/BotServices.ts:', result);
      if (result.statuscode == 200) {
        dataMensagem = {
          tp_etapachat: result.data[0].tp_etapachat,
          in_stop_bot: result.data[0].in_stop_bot,
        };
      }
      return sucesso([dataMensagem]);
    } catch (error) {
      return erroInterno(error);
    }
  }
}
