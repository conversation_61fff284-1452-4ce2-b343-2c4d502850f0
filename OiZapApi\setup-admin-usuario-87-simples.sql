-- ========================================
-- Setup Simples: Perfil Admin para usuário 87
-- Execute os comandos na ordem apresentada
-- ========================================

-- 1. <PERSON>riar perfil Administrador
INSERT INTO perfis (nm_perfil, created_at) 
VALUES ('Administrador', CURRENT_TIMESTAMP);

-- 2. Criar telas administrativas
INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES ('Cadastro de Perfil', '/cadPerfil', CURRENT_TIMESTAMP);

INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES ('Cadastro de Tela', '/cadTela', CURRENT_TIMESTAMP);

INSERT INTO telas (nm_tela, ds_rota, created_at) 
VALUES ('Associação Perfis x Telas', '/cadPerfisTelas', CURRENT_TIMESTAMP);

-- 3. Associar usuário 87 ao perfil Administrador (assumindo cd_perfil = 1)
INSERT INTO usuarios_perfis (cd_usuario, cd_perfil, created_at, updated_at)
VALUES (87, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 4. Associar telas ao perfil Administrador (assumindo cd_tela = 1, 2, 3)
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
VALUES (1, 1, true, true, true, true, CURRENT_TIMESTAMP);

INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
VALUES (1, 2, true, true, true, true, CURRENT_TIMESTAMP);

INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir, created_at)
VALUES (1, 3, true, true, true, true, CURRENT_TIMESTAMP);

-- 5. Verificar se foi criado corretamente
SELECT 'Perfil criado:' as info, cd_perfil, nm_perfil FROM perfis WHERE nm_perfil = 'Administrador';
SELECT 'Telas criadas:' as info, cd_tela, nm_tela, ds_rota FROM telas WHERE ds_rota IN ('/cadPerfil', '/cadTela', '/cadPerfisTelas');
SELECT 'Usuário 87 associado:' as info, cd_usuario, cd_perfil FROM usuarios_perfis WHERE cd_usuario = 87;
SELECT 'Telas associadas:' as info, cd_perfil, cd_tela FROM perfis_telas WHERE cd_perfil = 1; 