import { IToken } from '@/interfaces/IToken';
import crypto from 'crypto';
import { Request, Response } from 'express';
import { CampanhasDB } from '../../data/campanhas/CampanhasDB';
//import { DisparosPixelDB } from '../../data/campanhas/DisparosPixelDB';
import { CliquesDB } from '../../data/campanhas/CliquesDB';
import { LeadsDB } from '../../data/campanhas/LeadsDB';
import { BAD_REQUEST, IRetorno, erroInterno } from '../../interfaces/IRetorno';

export class CampanhasModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      const token = req.query.token as unknown as IToken;
      req.body.cd_usucad = token?.cd_usuario || 0;
      const body = req.body;
      //console.log('🚀 ~ CampanhasModel.ts:14 ~ incluir ~ body:', body);

      const nr_hash = crypto.randomBytes(16).toString('hex').substring(0, 5);
      const baseUrl = process.env.BASE_URL;
      const ds_url = `${baseUrl}/campanhas/capturar-clique/v1?id=${nr_hash}&utm_source=${body.tp_campanha}`;

      req.body = {
        ...body,
        ds_url,
        nr_hash,
        dt_cadastro: new Date(),
      };
      return await CampanhasDB.incluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async capturarClique(req: Request, res: Response) {
    try {
      //console.log('🚀 ~ CampanhasModel.ts:35 ~ capturarClique ~ req.query:', req.query);
      const { id, utm_source } = req.query;
      const disparo = await CampanhasDB.findByHash(id as string);
      // console.log('🚀 ~ CampanhasModel.ts:37 ~ capturarClique ~ disparo:', disparo);
      if (!disparo) {
        return res.status(404).send('Campanha não encontrada');
      }
      const respClique = await CliquesDB.criarClique({
        cd_campanha: disparo.cd_campanha,
        tp_campanha: utm_source || 'utm_source_not_found',
        dt_clique: new Date(),
        cd_estabelecimento: disparo.cd_estabelecimento,
      });
      // console.log('🚀 ~ CampanhasModel.ts:46 ~ capturarClique ~ respClique:', respClique);
      //obter whatsapp number from DisparoPixelDB.listarDisparo
      const whatsappNumber = disparo.nr_whatsapp;
      // console.log('🚀 ~ CampanhasModel.ts:39 ~ capturarClique ~ whatsappNumber:', whatsappNumber);

      const mensagem = encodeURIComponent(`${disparo.ds_mensagem}\n ID: ${id}`);
      return res.redirect(`https://wa.me/${whatsappNumber}?text=${mensagem}`);
    } catch (error: any) {
      return res.status(500).send('Erro ao registrar clique');
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      return await CampanhasDB.listar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async alterar(req: Request): Promise<IRetorno> {
    //  console.log('🚀 ~ CampanhasModel.ts:67 ~ alterar ~ req:', req.body);
    try {
      return await CampanhasDB.alterar(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async excluir(req: Request): Promise<IRetorno> {
    try {
      const leads = await LeadsDB.listarLeads({
        cd_campanha: req.body.cd_campanha,
        cd_estabelecimento: req.body.cd_estabelecimento,
      });
      if (leads.data && leads.data.length > 0) {
        return {
          statuscode: BAD_REQUEST,
          message: 'Não é possível excluir a campanha, pois ela possui leads',
          data: [],
          errors: ['Não é possível excluir a campanha, pois ela possui leads'],
        };
      }
      const cliques = await CliquesDB.listarCliques({
        cd_campanha: req.body.cd_campanha,
        cd_estabelecimento: req.body.cd_estabelecimento,
      });
      if (cliques.data && cliques.data.length > 0) {
        return {
          statuscode: BAD_REQUEST,
          message: 'Não é possível excluir a campanha, pois ela possui cliques',
          data: [],
          errors: ['Não é possível excluir a campanha, pois ela possui cliques'],
        };
      }

      return await CampanhasDB.excluir(req);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
