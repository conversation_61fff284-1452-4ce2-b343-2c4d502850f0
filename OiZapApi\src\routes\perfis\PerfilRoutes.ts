import { Router } from 'express';
import { PerfilController } from '../../controllers/perfis/PerfilController';

const router = Router();

// Rota de teste para verificar se as rotas estão funcionando
router.get('/teste', (req, res) => {
  res.json({ message: 'Rota de teste funcionando', timestamp: new Date().toISOString() });
});

router.get('/', PerfilController.listarPerfis);
router.post('/', PerfilController.criarPerfil);
router.put('/:cd_perfil', PerfilController.atualizarPerfil);
router.delete('/:cd_perfil', PerfilController.deletarPerfil);

export default router; 