<template>
    <ShowLoading ref="loading" />
    <Modal :show="modalEncerrar" @hidden="closeModal">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Encerrar Atendimento</h2>
        </ModalHeader>
        <ModalBody class="p-2">
            <!-- <PERSON>s fake para enganar o autocomplete -->
            <input type="text" style="position: absolute; left: -9999px; opacity: 0" autocomplete="username" />
            <input
                type="password"
                style="position: absolute; left: -9999px; opacity: 0"
                autocomplete="current-password"
            />

            <div class="mb-4">
                <label for="departamento" class="form-label">Assunto do Atendimento</label>
                <Multiselect
                    class="-mt-2"
                    :customHeight="32"
                    v-model="formData.cd_assunto"
                    placeholder="Selecione o Assunto"
                    :close-on-select="false"
                    spellcheck="false"
                    :options="assuntos"
                    ref="selectAssunto"
                    mode="single"
                    @emitEvent="selecionaAssunto"
                />
            </div>

            <div class="mb-1">
                <div class="flex items-center gap-1.5 w-max">
                    <label>Resumo do atendimento</label>
                </div>

                <textarea
                    id="comentario"
                    class="form-control mt-1"
                    rows="4"
                    v-model="formData.ds_resumo"
                    placeholder="Digite um resumo sobre o atendimento"
                ></textarea>
            </div>
        </ModalBody>
        <ModalFooter class="flex justify-between">
            <button type="button" class="btn btn-danger-soft w-24" @click="closeModal">Cancelar</button>
            <button type="button" class="btn btn-primary-soft w-24" @click="confirmarEncerramento">Encerrar</button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
    import { ref, defineExpose, onMounted, reactive, defineEmits } from 'vue';

    import { useToast } from '@/global-components/toastify/useToast';
    import MotivosAtendimentoServices from '../../services/chat/MotivosAtendimentoServices';

    const toast = useToast();
    const modalEncerrar = ref(false);

    const formData = reactive({
        cd_assunto: undefined,
        cd_estabelecimento: undefined,
        ds_assunto: undefined,
        ds_resumo: undefined,
    });

    const assuntos = ref([]);

    const atendentes = ref([]);

    const emit = defineEmits(['encerramentoConfirmado']);

    // Método para desabilitar autocomplete após o modal abrir
    const disableAutocomplete = () => {
        setTimeout(() => {
            // Desabilita em todos os inputs do modal
            const modal = document.querySelector('.modal');
            if (modal) {
                const inputs = modal.querySelectorAll('input');
                inputs.forEach((input) => {
                    if (!input.style.position.includes('absolute')) {
                        // Não mexe nos campos fake
                        input.setAttribute('autocomplete', 'chrome-off');
                        input.setAttribute('data-form-type', 'other');
                        input.setAttribute('data-lpignore', 'true');
                        input.setAttribute('data-1p-ignore', 'true');
                        input.setAttribute('data-bwignore', 'true');
                        input.setAttribute('role', 'combobox');
                        input.removeAttribute('name');

                        // Trick para remover focus temporariamente
                        input.addEventListener('focus', (e) => {
                            e.target.setAttribute('readonly', 'readonly');
                            setTimeout(() => {
                                e.target.removeAttribute('readonly');
                            }, 100);
                        });
                    }
                });
            }
        }, 200);
    };

    // Carregar departamentos e atendentes
    const carregaAssuntoAtendimento = async (cdEstabelecimento) => {
        try {
            let result = await MotivosAtendimentoServices.listar({
                in_ativo: true,
                cd_estabelecimento: cdEstabelecimento,
            });

            if (result.statuscode == 200) {
                assuntos.value = result.data.map((item) => ({
                    value: item.cd_motivo,
                    label: item.ds_motivo,
                }));
            } else if (result.statuscode == 404) {
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        }
    };

    const selecionaAssunto = (selected) => {
        if (selected.event == 'Select') {
            formData.ds_assunto = selected.select.label;
        } else if (selected.event == 'Clear') {
            formData.cd_assunto = undefined;
            formData.ds_assunto = undefined;
        }
    };

    async function abreModalEncerrar(atendimento) {
        //console.log('atendimento:', atendimento);
        // atendimentoAtual.value = atendimento;
        // Limpa os campos ao abrir o modal
        formData.cd_atendimento = atendimento.cd_atendimento;
        formData.cd_estabelecimento = atendimento.cd_estabelecimento;
        formData.cd_assunto = undefined;
        formData.ds_assunto = undefined;
        formData.ds_resumo = undefined;

        // Carrega os dados dos selects
        await carregaAssuntoAtendimento(atendimento.cd_estabelecimento);

        modalEncerrar.value = true;

        // Múltiplas tentativas para desabilitar
        disableAutocomplete();
        setTimeout(disableAutocomplete, 300);
        setTimeout(disableAutocomplete, 500);
    }

    // Função para fechar o modal
    const closeModal = () => {
        modalEncerrar.value = false;
    };

    // Função para salvar o encerramento
    const confirmarEncerramento = async () => {
        if (!formData.cd_assunto) {
            // Validar se pelo menos o assunto foi selecionado
            toast.warning('Selecione um assunto para encerrar');
            return;
        }

        emit('encerramentoConfirmado', formData);
        closeModal();
    };

    defineExpose({
        abreModalEncerrar,
    });

    onMounted(() => {
        // Carregar dados iniciais se necessário
    });
</script>
