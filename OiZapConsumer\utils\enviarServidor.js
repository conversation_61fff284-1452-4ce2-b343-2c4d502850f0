const utils = require('./util');
const Sftp = require('./sftpService');
const deploy = require('./deploy');
const inquirer = require('inquirer');

const { execSync } = require('child_process');
require('dotenv').config();

// Função simples para colorir texto
const colors = {
  black: (text) => `\x1b[38;2;0;0;0m${text}\x1b[0m`, // Preto (RGB: 0,0,0)
  white: (text) => `\x1b[38;2;255;255;255m${text}\x1b[0m`, // Branco (RGB: 255,255,255)
  gray: (text) => `\x1b[38;2;128;128;128m${text}\x1b[0m`, // Cinza (RGB: 128,128,128)
  red: (text) => `\x1b[38;2;255;0;0m${text}\x1b[0m`, // Vermelho (RGB: 255,0,0)
  green: (text) => `\x1b[38;2;0;255;0m${text}\x1b[0m`, // Verde (RGB: 0,255,0)
  blue: (text) => `\x1b[38;2;0;0;255m${text}\x1b[0m`, // Azul (RGB: 0,0,255)
  yellow: (text) => `\x1b[38;2;255;255;0m${text}\x1b[0m`, // Amarelo (RGB: 255,255,0)
  orange: (text) => `\x1b[38;2;255;165;0m${text}\x1b[0m`, // Laranja (RGB: 255,165,0)
  cyan: (text) => `\x1b[38;2;0;255;255m${text}\x1b[0m`, // Ciano (RGB: 0,255,255)
  magenta: (text) => `\x1b[38;2;255;0;255m${text}\x1b[0m`, // Magenta (RGB: 255,0,255)
  pink: (text) => `\x1b[38;2;255;192;203m${text}\x1b[0m`, // Rosa (RGB: 255,192,203)
  purple: (text) => `\x1b[38;2;128;0;128m${text}\x1b[0m`, // Roxo (RGB: 128,0,128)
  brown: (text) => `\x1b[38;2;165;42;42m${text}\x1b[0m`, // Marrom (RGB: 165,42,42)
  lime: (text) => `\x1b[38;2;50;205;50m${text}\x1b[0m`, // Verde-limão (RGB: 50,205,50)
  gold: (text) => `\x1b[38;2;255;215;0m${text}\x1b[0m`, // Dourado (RGB: 255,215,0)
  silver: (text) => `\x1b[38;2;192;192;192m${text}\x1b[0m`, // Prata (RGB: 192,192,192)
};

async function getConfiguracoes() {
  const questions = [
    {
      type: 'list',
      name: 'ambiente',
      message: 'Selecione o ambiente:',
      choices: [
        {
          name: colors.green('sandbox'),
          value: 'sandbox',
        },
        {
          name: colors.blue('oizap'),
          value: 'oizap',
        },
        {
          name: colors.yellow('devzap'),
          value: 'devzap',
        },
        {
          name: colors.orange('crm'),
          value: 'crm',
        },
      ],
    },
    {
      type: 'list',
      name: 'compilar',
      message: 'Deseja compilar antes de enviar:',
      choices: [
        {
          name: colors.green('Sim'),
          value: true,
        },
        {
          name: colors.red('Não'),
          value: false,
        },
      ],
      default: true,
    },
    {
      type: 'list',
      name: 'servidor',
      message: 'Selecione o servidor:',
      choices: [
        {
          name: colors.cyan('get'),
          value: 'get',
        },
        {
          name: colors.magenta('hetzner'),
          value: 'hetzner',
        },
      ],
      default: 'get',
    },
    {
      type: 'list',
      name: 'versao',
      message: 'Deseja alterar a versão?',
      choices: [
        {
          name: colors.red('Não'),
          value: 'nao',
        },
        {
          name: colors.green('Patch (correções)'),
          value: 'patch',
        },
        {
          name: colors.yellow('Minor (funcionalidades)'),
          value: 'minor',
        },
        {
          name: colors.blue('Major (mudanças grandes)'),
          value: 'major',
        },
      ],
      default: 'nao',
    },
    {
      type: 'list',
      name: 'confirmar', // Mudando o nome aqui para corresponder à verificação
      message: (answers) => {
        // Define as cores para cada tipo de ambiente
        const ambienteColors = {
          sandbox: colors.green,
          oizap: colors.blue,
          dev: colors.yellow,
          crm: colors.orange,
        };

        // Define as cores para cada tipo de versão
        const versaoColors = {
          nao: colors.red,
          patch: colors.green,
          minor: colors.yellow,
          major: colors.blue,
        };

        return colors.white(
          '\nConfirma o envio para:\n' +
            'Ambiente: ' +
            ambienteColors[answers.ambiente](answers.ambiente) +
            '\n' +
            'Compilar: ' +
            (answers.compilar ? colors.green('Sim') : colors.red('Não')) +
            '\n' +
            'Servidor: ' +
            (answers.servidor === 'get' ? colors.cyan('get') : colors.magenta('hetzner')) +
            '\n' +
            'Alterar versão: ' +
            (answers.versao !== 'nao' ? versaoColors[answers.versao](answers.versao) : colors.red('Não')) +
            '\n',
        );
      },
      choices: [
        {
          name: colors.green('Sim'),
          value: true,
        },
        {
          name: colors.red('Não'),
          value: false,
        },
      ],
      default: true,
    },
  ];

  return await inquirer.prompt(questions);
}

async function start() {
  try {
    const config = await getConfiguracoes();
    if (!config.confirmar) {
      console.log(colors.red('Operação cancelada pelo usuário'));
      return;
    }

    const ambiente = config.ambiente;
    const compila = config.compilar ? 'compila' : '';
    const servidor = config.servidor;

    console.log(colors.cyan('Preparando build para ' + ambiente + '...'));

    let host, user, password, port, destino, pm2APPName;

    if (servidor == 'get') {
      host = process.env.HOST_SERVERAPI_GET;
      port = process.env.PORT_SERVERAPI_GET;
      user = process.env.USER_SERVERAPI_GET;
      password = process.env.PASSWORD_SERVERAPI_GET;
    } else {
      host = process.env.HOST_SERVERAPI;
      port = process.env.PORT_SERVERAPI;
      user = process.env.USER_SERVERAPI;
      password = process.env.PASSWORD_SERVERAPI;
    }
    console.log('servidor :', servidor);
    console.log('password :', password);
    console.log('user :', user);
    console.log('port :', port);
    console.log('host :', host);
    console.log('ambiente :', ambiente);
    console.log('compila :', compila);

    if (ambiente == 'oizap') {
      destino = process.env.DESTINO_SERVERAPI;
      pm2APPName = process.env.PM2_APPNAME;
    } else if (ambiente == 'sandbox') {
      destino = process.env.DESTINO_SANDBOX_SERVERAPI;
      pm2APPName = process.env.PM2_APPNAMESANDBOX;
    } else if (ambiente == 'devzap') {
      destino = process.env.DESTINO_DEV_SERVERAPI;
      pm2APPName = process.env.PM2_APPNAMEDEV;
    } else if (ambiente == 'crm') {
      destino = process.env.DESTINO_CRM_SERVERAPI;
      pm2APPName = process.env.PM2_APPNAMECRM;
    } else {
      console.log(colors.red('Ambiente não informado'));
      return;
    }

    if (compila == 'compila') {
      await deploy.start(ambiente);

      console.log(colors.yellow('\nLimpando Build...'));
      try {
        var resultLimpar = await utils.executar('tsc --build --clean');
        if (resultLimpar.statuscode != 200) {
          console.log(resultLimpar.message);
          return;
        }
        console.log('Executando transpile...');
        var resultCompilar = await utils.executar('tsc');
        if (resultCompilar.statuscode != 200) {
          console.log(resultCompilar.message);
          return;
        }

        console.log('Copiando arquivos de configuração...');
        var resultCompilar = await utils.executar('npm run copy-files');
        if (resultCompilar.statuscode != 200) {
          console.log(resultCompilar.message);
          return;
        }
      } catch (err) {
        console.error(colors.red('Erro durante a compilação:'), err);
        return;
      }
    } else {
      console.log(colors.blue('Compilação não solicitada, utilizando a versão já compilada'));
    }

    console.log(
      colors.cyan('\nEnviando para o ambiente ' + ambiente + ' no servidor ' + servidor + ' ... aguarde ...'),
    );

    // // Adiciona barra de progresso ao método de envio do SFTP
    // const sftp = new Sftp(host, port, user, password);
    // await sftp.enviar('./dist', destino, true, (progress) => {
    //   const percentage = ((progress.current / progress.total) * 100).toFixed(2);
    //   const progressBar =
    //     '='.repeat(Math.floor((50 * progress.current) / progress.total)) +
    //     (progress.current < progress.total ? '>' : '') +
    //     ' '.repeat(Math.max(0, 50 - Math.floor((50 * progress.current) / progress.total)));

    //   process.stdout.write(`\r[${progressBar}] ${percentage}% | ${progress.file}`);
    // });

    // if (resultSftp.statuscode != 200) {
    //   console.log(colors.red('\nErro ao enviar arquivos:', resultSftp.message));
    //   return;
    // }

    const resultSftp = await new Sftp(host, port, user, password).enviar('./dist', destino, true);

    if (resultSftp.statuscode != 200) {
      console.log(resultSftp.message);
      return;
    }

    // console.log(colors.yellow('\n\nReiniciando ' + pm2APPName));
    // const resultRestartPM2 = await sftp.restartPM2(pm2APPName);
    // if (resultRestartPM2.statuscode != 200) {
    //   console.log(colors.red('Erro ao reiniciar PM2:', resultRestartPM2.message));
    //   return;
    // }

    console.log('Reiniciando ' + pm2APPName);

    const resultRestartPM2 = await new Sftp(host, port, user, password).restartPM2(pm2APPName);
    if (resultRestartPM2.statuscode != 200) {
      console.log(resultRestartPM2.message);
      return;
    }
    console.log('Fim');

    console.log(colors.green('\nDeployment concluído com sucesso! ✨'));
  } catch (error) {
    console.error(colors.red('Erro na execução:'), error);
  }
}

start();
